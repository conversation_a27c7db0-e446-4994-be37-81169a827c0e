.id                 920
.int_stack_count    2
.string_stack_count 0
.int_var_count      5
.string_var_count   0
; callback "forceStackStones"
;   Used by the InterfaceStylesPlugin to enable it's Always stack bottom bar option
;   Toggle the option when you have the bottom line top level interface on and your screen is large enough for the stones to be in a single line
   iconst                 0
   istore                 2
   iconst                 0
   istore                 3
   iconst                 -1
   istore                 4
   iload                  1
   switch                
      1131: LABEL9
      1130: LABEL107
      1129: LABEL149
      1745: LABEL169
   jump                   LABEL244
LABEL9:
   iconst                 10747996
   if_getwidth           
   iconst                 33
   sub                   
   iconst                 10747996
   if_getheight          
   istore                 3
   istore                 2
   iload                  0
   if_getwidth           
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551390
   enum                  
   if_getwidth           
   sub                   
   iconst                 429
   if_icmplt              LABEL29
   iconst                 0                  ; should resizable stones be forced to stack
   sconst                 "forceStackStones" ; push event name
   runelite_callback     ; invoke callback
   iconst                 0                  ; if 0 is returned, continue normal layout
   if_icmpne              LABEL29
   jump                   LABEL49
LABEL29:
   iconst                 0
   iload                  3
   iconst                 10747997
   if_getheight          
   add                   
   iconst                 2
   iconst                 2
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10747998
   enum                  
   if_setposition        
   iconst                 0
   iload                  3
   iconst                 2
   iconst                 2
   iconst                 10747997
   if_setposition        
   jump                   LABEL65
LABEL49:
   iconst                 0
   iload                  3
   iconst                 2
   iconst                 2
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10747998
   enum                  
   if_setposition        
   iload                  2
   iconst                 0
   iconst                 2
   iconst                 2
   iconst                 10747997
   if_setposition        
LABEL65:
   get_varbit             4084
   iconst                 1
   if_icmpeq              LABEL69
   jump                   LABEL77
LABEL69:
   iconst                 1178
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
   jump                   LABEL96
LABEL77:
   get_varbit             13037
   iconst                 1
   if_icmpeq              LABEL81
   jump                   LABEL89
LABEL81:
   iconst                 3513
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
   jump                   LABEL96
LABEL89:
   iconst                 2154
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
LABEL96:
   clientclock           
   set_varc_int           384
   invoke                 1445
   iconst                 1
   if_icmpeq              LABEL102
   jump                   LABEL106
LABEL102:
   get_varbit             12986
   invoke                 633
   iconst                 10747925
   if_sethide            
LABEL106:
   jump                   LABEL244
LABEL107:
   get_varbit             4084
   iconst                 1
   if_icmpeq              LABEL111
   jump                   LABEL119
LABEL111:
   iconst                 1178
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
   jump                   LABEL138
LABEL119:
   get_varbit             13037
   iconst                 1
   if_icmpeq              LABEL123
   jump                   LABEL131
LABEL123:
   iconst                 3513
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
   jump                   LABEL138
LABEL131:
   iconst                 2154
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
LABEL138:
   clientclock           
   set_varc_int           384
   invoke                 1445
   iconst                 1
   if_icmpeq              LABEL144
   jump                   LABEL148
LABEL144:
   get_varbit             12986
   invoke                 633
   iconst                 10551317
   if_sethide            
LABEL148:
   jump                   LABEL244
LABEL149:
   invoke                 3297
   iconst                 1
   if_icmpeq              LABEL153
   jump                   LABEL161
LABEL153:
   iconst                 2422
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
   jump                   LABEL168
LABEL161:
   iconst                 1200
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551325
   enum                  
   2122                  
LABEL168:
   jump                   LABEL244
LABEL169:
   get_varbit             6257
   iconst                 1
   if_icmpeq              LABEL176
   get_varbit             542
   iconst                 1
   if_icmpeq              LABEL176
   jump                   LABEL180
LABEL176:
   iconst                 1
   iconst                 39387162
   if_sethide            
   jump                   LABEL235
LABEL180:
   iconst                 0
   iconst                 39387162
   if_sethide            
   iconst                 1
   iconst                 39387162
   2308                  
   get_varbit             6255
   switch                
      2: LABEL189
      1: LABEL197
      3: LABEL205
   jump                   LABEL213
LABEL189:
   iconst                 1718
   iconst                 39387181
   if_setgraphic         
   iconst                 1
   sconst                 "Toggle single-tap mode"
   iconst                 39387162
   if_setop              
   jump                   LABEL220
LABEL197:
   iconst                 1717
   iconst                 39387181
   if_setgraphic         
   iconst                 1
   sconst                 "Toggle tap-to-drop mode"
   iconst                 39387162
   if_setop              
   jump                   LABEL220
LABEL205:
   iconst                 1716
   iconst                 39387181
   if_setgraphic         
   iconst                 1
   sconst                 "Show Keyboard"
   iconst                 39387162
   if_setop              
   jump                   LABEL220
LABEL213:
   iconst                 1715
   iconst                 39387181
   if_setgraphic         
   iconst                 1
   sconst                 ""
   iconst                 39387162
   if_setop              
LABEL220:
   get_varbit             6255
   iconst                 3
   if_icmpne              LABEL224
   jump                   LABEL232
LABEL224:
   get_varbit             6256
   iconst                 0
   if_icmpeq              LABEL228
   jump                   LABEL232
LABEL228:
   iconst                 155
   iconst                 39387181
   if_settrans           
   jump                   LABEL235
LABEL232:
   iconst                 0
   iconst                 39387181
   if_settrans           
LABEL235:
   invoke                 2581
   get_varbit             6254
   invoke                 633
   iconst                 39387173
   if_sethide            
   invoke                 2526
   pop_int               
   clientclock           
   set_varc_int           384
LABEL244:
   return                
