/*
 * Copyright (c) 2019, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.mining;

import com.google.common.collect.ImmutableMap;
import java.time.Duration;
import java.util.Map;
import lombok.AccessLevel;
import lombok.Getter;
import net.runelite.client.TickConstants;

import static net.runelite.api.ObjectID.*;
import static net.runelite.client.util.RSTimeUnit.*;

enum Rock
{
	TIN(Duration.of(TickConstants.TIN, GAME_TICKS), 0, ROCKS_11360, ROCKS_11361, ROCKS_10080),
	COPPER(Duration.of(TickConstants.COPPER, GAME_TICKS), 0, ROCKS_10943, ROCKS_11161, ROCKS_10079),
	IRON(Duration.of(TickConstants.IRON, GAME_TICKS), 0, ROCKS_11364, ROCKS_11365, ROCKS_36203),
	COAL(Duration.of(TickConstants.COAL, GAME_TICKS), 0, ROCKS_11366, ROCKS_11367, ROCKS_36204, ROCKS_4676),
	SILVER(Duration.of(TickConstants.SILVER, GAME_TICKS), 0, ROCKS_11368, ROCKS_11369, ROCKS_36205),
	SANDSTONE(Duration.of(TickConstants.SANDSTONE, GAME_TICKS), 0, ROCKS_11386),
	GOLD(Duration.of(TickConstants.GOLD, GAME_TICKS), 0, ROCKS_11370, ROCKS_11371, ROCKS_36206),
	GRANITE(Duration.of(TickConstants.GRANITE, GAME_TICKS), 0, ROCKS_11387),
	MITHRIL(Duration.of(TickConstants.MITHRIL, GAME_TICKS), 0, ROCKS_11372, ROCKS_11373, ROCKS_36207),
	LOVAKITE(Duration.of(MiningRocksOverlay.LOVAKITE_ORE_MAX_RESPAWN_TIME, GAME_TICKS), 0, ROCKS_28596, ROCKS_28597),
	ADAMANTITE(Duration.of(TickConstants.ADAMANTITE, GAME_TICKS), 0, ROCKS_11374, ROCKS_11375, ROCKS_36208) {
		@Override
		Duration getRespawnTime(int region) {
			return region == MINING_GUILD || region == WILDERNESS_RESOURCE_AREA ? Duration.of(200, GAME_TICKS) : super.respawnTime;
		}
	},
	RUNITE(Duration.of(TickConstants.RUNITE, GAME_TICKS), 0, ROCKS_11376, ROCKS_11377, ROCKS_36209) {
		@Override
		Duration getRespawnTime(int region) {
			return region == MINING_GUILD ? Duration.of(600, GAME_TICKS) : super.respawnTime;
		}
	},
	ORE_VEIN(Duration.of(MiningRocksOverlay.ORE_VEIN_MAX_RESPAWN_TIME, GAME_TICKS), 150),
	AMETHYST(Duration.of(TickConstants.AMETHYST, GAME_TICKS), 120),
	ASH_VEIN(Duration.of(TickConstants.ASH_VEIN, GAME_TICKS), 0, ASH_PILE),
	GEM_ROCK(Duration.of(TickConstants.GEM_ROCK, GAME_TICKS), 0, ROCKS_11380, ROCKS_11381),
	URT_SALT(Duration.of(TickConstants.URT_SALT, GAME_TICKS), 0, ROCKS_33254),
	EFH_SALT(Duration.of(TickConstants.EFH_SALT, GAME_TICKS), 0, ROCKS_33255),
	TE_SALT(Duration.of(TickConstants.TE_SALT, GAME_TICKS), 0, ROCKS_33256),
	BASALT(Duration.of(TickConstants.BASALT, GAME_TICKS), 0, ROCKS_33257),
	DAEYALT_ESSENCE(Duration.of(MiningRocksOverlay.DAEYALT_MAX_RESPAWN_TIME, GAME_TICKS), 0, DAEYALT_ESSENCE_39095),
	BARRONITE(Duration.of(TickConstants.BARRONITE, GAME_TICKS), 140),
	MINERAL_VEIN(Duration.of(TickConstants.MINERAL_VEIN, GAME_TICKS), 150);

	private static final int WILDERNESS_RESOURCE_AREA = 12605;
	private static final int MISCELLANIA = 10044;
	private static final int MINING_GUILD = 12183;
	private static final Map<Integer, Rock> ROCKS;

	static
	{
		ImmutableMap.Builder<Integer, Rock> builder = new ImmutableMap.Builder<>();
		for (Rock rock : values())
		{
			for (int id : rock.ids)
			{
				builder.put(id, rock);
			}
		}
		ROCKS = builder.build();
	}

	private final Duration respawnTime;
	@Getter(AccessLevel.PACKAGE)
	private final int zOffset;
	private final int[] ids;

	Rock(Duration respawnTime, int zOffset, int... ids)
	{
		this.respawnTime = respawnTime;
		this.zOffset = zOffset;
		this.ids = ids;
	}

	Duration getRespawnTime(int region)
	{
		return respawnTime;
	}

	static Rock getRock(int id)
	{
		return ROCKS.get(id);
	}
}
