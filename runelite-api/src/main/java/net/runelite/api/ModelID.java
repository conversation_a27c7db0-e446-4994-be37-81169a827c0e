/*
 * Copyright (c) 2018 Magic fTail
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

public final class ModelID
{
	public static final int ANVIL = 1251;
	public static final int WILLOW_TREE = 1570;
	public static final int SLAYER_SKILL_MODEL = 1733;
	public static final int FIREMAKING_SKILL_MODEL = 2260;
	public static final int STEEL_KITESHIELD = 2339;
	public static final int PURE_ESSENCE = 2349;
	public static final int RAW_TUNA = 2355;
	public static final int CLEAN_HERB = 2364;
	public static final int HAMMER = 2376;
	public static final int BLUE_WIZARD_HAT = 2483;
	public static final int CHISEL = 2489;
	public static final int HIGHWAYMAN_MASK = 2500;
	public static final int STEEL_PICKAXE = 2529;
	public static final int SHORTBOW = 2562;
	public static final int STEEL_LONGSWORD = 2602;
	public static final int STEEL_SWORD = 2604;
	public static final int STEEL_ARROW = 2711;
	public static final int PRAYER_SKILL_MODEL = 3325;
	public static final int STRENGTH_SKILL_MODEL = 3327;
	public static final int AGILITY_SKILL_MODEL = 3328;
	public static final int HEARTH = 3326;
	public static final int WATERING_CAN = 7331;
	public static final int SAW = 12309;
	public static final int FOOTPRINT = 19980;
	public static final int COOKING_SKILL_MODEL = 27611;
}