/*
 * Copyright (c) 2021, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.skillcalculator.skills;

import java.util.Arrays;
import java.util.EnumSet;
import lombok.Getter;
import net.runelite.api.ItemID;

@Getter
public enum PrayerAction implements ItemSkillAction
{
	ENSOULED_GOBLIN_HEAD(ItemID.ENSOULED_GOBLIN_HEAD, 1, 130),
	ENSOULED_MONKEY_HEAD(ItemID.ENSOULED_MONKEY_HEAD, 1, 182),
	ENSOULED_IMP_HEAD(ItemID.ENSOULED_IMP_HEAD, 1, 286),
	ENSOULED_MINOTAUR_HEAD(ItemID.ENSOULED_MINOTAUR_HEAD, 1, 364),
	ENSOULED_SCORPION_HEAD(ItemID.ENSOULED_SCORPION_HEAD, 1, 454),
	ENSOULED_BEAR_HEAD(ItemID.ENSOULED_BEAR_HEAD, 1, 480),
	ENSOULED_UNICORN_HEAD(ItemID.ENSOULED_UNICORN_HEAD, 1, 494),
	ENSOULED_DOG_HEAD(ItemID.ENSOULED_DOG_HEAD, 1, 520),
	ENSOULED_CHAOS_DRUID_HEAD(ItemID.ENSOULED_CHAOS_DRUID_HEAD, 1, 584),
	ENSOULED_GIANT_HEAD(ItemID.ENSOULED_GIANT_HEAD, 1, 650),
	ENSOULED_OGRE_HEAD(ItemID.ENSOULED_OGRE_HEAD, 1, 716),
	ENSOULED_ELF_HEAD(ItemID.ENSOULED_ELF_HEAD, 1, 754),
	ENSOULED_TROLL_HEAD(ItemID.ENSOULED_TROLL_HEAD, 1, 780),
	ENSOULED_HORROR_HEAD(ItemID.ENSOULED_HORROR_HEAD, 1, 832),
	ENSOULED_KALPHITE_HEAD(ItemID.ENSOULED_KALPHITE_HEAD, 1, 884),
	ENSOULED_DAGANNOTH_HEAD(ItemID.ENSOULED_DAGANNOTH_HEAD, 1, 936),
	ENSOULED_BLOODVELD_HEAD(ItemID.ENSOULED_BLOODVELD_HEAD, 1, 1040),
	ENSOULED_TZHAAR_HEAD(ItemID.ENSOULED_TZHAAR_HEAD, 1, 1104),
	ENSOULED_DEMON_HEAD(ItemID.ENSOULED_DEMON_HEAD, 1, 1170),
	ENSOULED_HELLHOUND_HEAD(ItemID.ENSOULED_HELLHOUND_HEAD, 1, 1200),
	ENSOULED_AVIANSIE_HEAD(ItemID.ENSOULED_AVIANSIE_HEAD, 1, 1234),
	ENSOULED_ABYSSAL_HEAD(ItemID.ENSOULED_ABYSSAL_HEAD, 1, 1300),
	ENSOULED_DRAGON_HEAD(ItemID.ENSOULED_DRAGON_HEAD, 1, 1560),
	FIENDISH_ASHES(ItemID.FIENDISH_ASHES, 1, 10, PrayerBonus.DEMONIC_OFFERING),
	VILE_ASHES(ItemID.VILE_ASHES, 1, 25, PrayerBonus.DEMONIC_OFFERING),
	MALICIOUS_ASHES(ItemID.MALICIOUS_ASHES, 1, 65, PrayerBonus.DEMONIC_OFFERING),
	ABYSSAL_ASHES(ItemID.ABYSSAL_ASHES, 1, 85, PrayerBonus.DEMONIC_OFFERING),
	INFERNAL_ASHES(ItemID.INFERNAL_ASHES, 1, 110, PrayerBonus.DEMONIC_OFFERING),
	BONES(ItemID.BONES, 1, 4.5f, PrayerBonus.BONE_BONUSES),
	WOLF_BONES(ItemID.WOLF_BONES, 1, 4.5f, PrayerBonus.BONE_BONUSES),
	LOAR_REMAINS(ItemID.LOAR_REMAINS, 1, 33, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	BURNT_BONES(ItemID.BURNT_BONES, 1, 4.5f, PrayerBonus.BONE_BONUSES),
	MONKEY_BONES(ItemID.MONKEY_BONES, 1, 5, PrayerBonus.BONE_BONUSES),
	BAT_BONES(ItemID.BAT_BONES, 1, 5.3f, PrayerBonus.BONE_BONUSES),
	JOGRE_BONES(ItemID.JOGRE_BONES, 1, 15, PrayerBonus.BONE_BONUSES),
	BIG_BONES(ItemID.BIG_BONES, 1, 15, PrayerBonus.BONE_BONUSES),
	ZOGRE_BONES(ItemID.ZOGRE_BONES, 1, 22.5f, PrayerBonus.BONE_BONUSES),
	SHAIKAHAN_BONES(ItemID.SHAIKAHAN_BONES, 1, 25, PrayerBonus.BONE_BONUSES),
	BABYDRAGON_BONES(ItemID.BABYDRAGON_BONES, 1, 30, PrayerBonus.BONE_BONUSES),
	PHRIN_REMAINS(ItemID.PHRIN_REMAINS, 1, 46.5f, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	WYRM_BONES(ItemID.WYRM_BONES, 1, 50, PrayerBonus.BONE_BONUSES),
	RIYL_REMAINS(ItemID.RIYL_REMAINS, 1, 59.5f, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	WYVERN_BONES(ItemID.WYVERN_BONES, 1, 72, PrayerBonus.BONE_BONUSES),
	DRAGON_BONES(ItemID.DRAGON_BONES, 1, 72, PrayerBonus.BONE_BONUSES),
	DRAKE_BONES(ItemID.DRAKE_BONES, 1, 80, PrayerBonus.BONE_BONUSES),
	ASYN_REMAINS(ItemID.ASYN_REMAINS, 1, 82.5f, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	FAYRG_BONES(ItemID.FAYRG_BONES, 1, 84, PrayerBonus.BONE_BONUSES),
	FIYR_REMAINS(ItemID.FIYR_REMAINS, 1, 84, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	LAVA_DRAGON_BONES(ItemID.LAVA_DRAGON_BONES, 1, 85, PrayerBonus.BONE_BONUSES),
	RAURG_BONES(ItemID.RAURG_BONES, 1, 96, PrayerBonus.BONE_BONUSES),
	HYDRA_BONES(ItemID.HYDRA_BONES, 1, 110, PrayerBonus.BONE_BONUSES),
	DAGANNOTH_BONES(ItemID.DAGANNOTH_BONES, 1, 125, PrayerBonus.BONE_BONUSES),
	OURG_BONES(ItemID.OURG_BONES, 1, 140, PrayerBonus.BONE_BONUSES),
	URIUM_REMAINS(ItemID.URIUM_REMAINS, 1, 120, PrayerBonus.MORYTANIA_DIARY_3_SHADES),
	GUPPY(ItemID.GUPPY, 1, 4),
	CAVEFISH(ItemID.CAVEFISH, 1, 7),
	TETRA(ItemID.TETRA, 1, 10),
	CATFISH(ItemID.CATFISH, 1, 16),
	SUPERIOR_DRAGON_BONES(ItemID.SUPERIOR_DRAGON_BONES, 70, 150, PrayerBonus.BONE_BONUSES),
	;

	private final int itemId;
	private final int level;
	private final float xp;
	private final EnumSet<PrayerBonus> applicableBonuses;

	PrayerAction(int itemId, int level, float xp, PrayerBonus... applicableBonuses)
	{
		this.itemId = itemId;
		this.level = level;
		this.xp = xp;
		this.applicableBonuses = EnumSet.noneOf(PrayerBonus.class);
		this.applicableBonuses.addAll(Arrays.asList(applicableBonuses));
	}

	@Override
	public boolean isBonusApplicable(SkillBonus skillBonus)
	{
		return skillBonus instanceof PrayerBonus && applicableBonuses.contains(skillBonus);
	}
}
