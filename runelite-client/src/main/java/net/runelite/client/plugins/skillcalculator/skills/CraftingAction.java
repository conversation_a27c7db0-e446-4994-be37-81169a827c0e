/*
 * Copyright (c) 2021, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.skillcalculator.skills;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.runelite.api.ItemID;
import net.runelite.client.game.ItemManager;

@AllArgsConstructor
@Getter
public enum CraftingAction implements ItemSkillAction
{
	BALL_OF_WOOL(ItemID.BALL_OF_WOOL, 1, 2.5f),
	UNFIRED_POT(ItemID.UNFIRED_POT, 1, 6.3f),
	POT(ItemID.POT, 1, 6.3f),
	LEATHER_GLOVES(ItemID.LEATHER_GLOVES, 1, 13.8f),
	OPAL(ItemID.OPAL, 1, 15),
	OPAL_RING(ItemID.OPAL_RING, 1, 10),
	MOLTEN_GLASS(ItemID.MOLTEN_GLASS, 1, 20),
	BEER_GLASS(ItemID.BEER_GLASS, 1, 17.5f, true),
	EMPTY_CANDLE_LANTERN(ItemID.EMPTY_CANDLE_LANTERN, 4, 19),
	GOLD_RING(ItemID.GOLD_RING, 5, 15),
	BIRD_HOUSE(ItemID.BIRD_HOUSE, 5, 15),
	GOLD_NECKLACE(ItemID.GOLD_NECKLACE, 6, 20),
	LEATHER_BOOTS(ItemID.LEATHER_BOOTS, 7, 16.3f),
	UNFIRED_PIE_DISH(ItemID.UNFIRED_PIE_DISH, 7, 15),
	PIE_DISH(ItemID.PIE_DISH, 7, 10),
	GOLD_BRACELET(ItemID.GOLD_BRACELET_11069, 7, 25),
	UNFIRED_BOWL(ItemID.UNFIRED_BOWL, 8, 18),
	BOWL(ItemID.BOWL, 8, 15),
	GOLD_AMULET_U(ItemID.GOLD_AMULET_U, 8, 30),
	COWL(ItemID.LEATHER_COWL, 9, 18.5f),
	CROSSBOW_STRING(ItemID.CROSSBOW_STRING, 10, 15),
	BOW_STRING(ItemID.BOW_STRING, 10, 15),
	LEATHER_VAMBRACES(ItemID.LEATHER_VAMBRACES, 11, 22),
	EMPTY_OIL_LAMP(ItemID.EMPTY_OIL_LAMP, 12, 25),
	JADE(ItemID.JADE, 13, 20),
	JADE_RING(ItemID.JADE_RING, 13, 32),
	LEATHER_BODY(ItemID.LEATHER_BODY, 14, 25),
	OAK_BIRD_HOUSE(ItemID.OAK_BIRD_HOUSE, 15, 20),
	RED_TOPAZ(ItemID.RED_TOPAZ, 16, 25),
	TOPAZ_RING(ItemID.TOPAZ_RING, 16, 35),
	HOLY_SYMBOL(ItemID.HOLY_SYMBOL, 16, 50),
	OPAL_NECKLACE(ItemID.OPAL_NECKLACE, 16, 35),
	UNHOLY_SYMBOL(ItemID.UNHOLY_SYMBOL, 17, 50),
	LEATHER_CHAPS(ItemID.LEATHER_CHAPS, 18, 27),
	UNFIRED_PLANT_POT(ItemID.UNFIRED_PLANT_POT, 19, 20),
	EMPTY_PLANT_POT(ItemID.EMPTY_PLANT_POT, 19, 17.5f),
	MAGIC_STRING(ItemID.MAGIC_STRING, 19, 30),
	SAPPHIRE(ItemID.SAPPHIRE, 20, 50),
	SAPPHIRE_RING(ItemID.SAPPHIRE_RING, 20, 40),
	EMPTY_SACK(ItemID.EMPTY_SACK, 21, 38),
	SAPPHIRE_NECKLACE(ItemID.SAPPHIRE_NECKLACE, 22, 55),
	OPAL_BRACELET(ItemID.OPAL_BRACELET, 22, 45),
	SAPPHIRE_BRACELET(ItemID.SAPPHIRE_BRACELET_11072, 23, 60),
	TIARA(ItemID.TIARA, 23, 52.5f),
	SAPPHIRE_AMULET_U(ItemID.SAPPHIRE_AMULET_U, 24, 65),
	UNFIRED_POT_LID(ItemID.UNFIRED_POT_LID, 25, 20),
	POT_LID(ItemID.POT_LID, 25, 20),
	JADE_NECKLACE(ItemID.JADE_NECKLACE, 25, 54),
	WILLOW_BIRD_HOUSE(ItemID.WILLOW_BIRD_HOUSE, 25, 25),
	DRIFT_NET(ItemID.DRIFT_NET, 26, 55),
	EMERALD(ItemID.EMERALD, 27, 67.5f),
	EMERALD_RING(ItemID.EMERALD_RING, 27, 55),
	OPAL_AMULET_U(ItemID.OPAL_AMULET_U, 27, 55),
	HARDLEATHER_BODY(ItemID.HARDLEATHER_BODY, 28, 35),
	EMERALD_NECKLACE(ItemID.EMERALD_NECKLACE, 29, 60),
	JADE_BRACELET(ItemID.JADE_BRACELET, 29, 60),
	EMERALD_BRACELET(ItemID.EMERALD_BRACELET, 30, 65),
	ROPE(ItemID.ROPE, 30, 25, true),
	EMERALD_AMULET_U(ItemID.EMERALD_AMULET_U, 31, 70),
	SPIKY_VAMBRACES(ItemID.SPIKY_VAMBRACES, 32, 6),
	TOPAZ_NECKLACE(ItemID.TOPAZ_NECKLACE, 32, 70),
	VIAL(ItemID.VIAL, 33, 35, true),
	RUBY(ItemID.RUBY, 34, 85),
	RUBY_RING(ItemID.RUBY_RING, 34, 70),
	JADE_AMULET_U(ItemID.JADE_AMULET_U, 34, 70),
	BROODOO_SHIELD(ItemID.BROODOO_SHIELD, 35, 100),
	TEAK_BIRD_HOUSE(ItemID.TEAK_BIRD_HOUSE, 35, 30),
	BASKET(ItemID.BASKET, 36, 56),
	COIF(ItemID.COIF, 38, 37, true),
	TOPAZ_BRACELET(ItemID.TOPAZ_BRACELET, 38, 75),
	RUBY_NECKLACE(ItemID.RUBY_NECKLACE, 40, 75),
	HARD_LEATHER_SHIELD(ItemID.HARD_LEATHER_SHIELD, 41, 70),
	RUBY_BRACELET(ItemID.RUBY_BRACELET, 42, 80),
	FISHBOWL(ItemID.FISHBOWL, 42, 42.5f),
	DIAMOND(ItemID.DIAMOND, 43, 107.5f),
	DIAMOND_RING(ItemID.DIAMOND_RING, 43, 85),
	TOPAZ_AMULET_U(ItemID.TOPAZ_AMULET_U, 45, 80),
	SNAKESKIN_BOOTS(ItemID.SNAKESKIN_BOOTS, 45, 30),
	MAPLE_BIRD_HOUSE(ItemID.MAPLE_BIRD_HOUSE, 45, 35),
	UNPOWERED_ORB(ItemID.UNPOWERED_ORB, 46, 52.5f),
	SNAKESKIN_VAMBRACES(ItemID.SNAKESKIN_VAMBRACES, 47, 35),
	SNAKESKIN_BANDANA(ItemID.SNAKESKIN_BANDANA, 48, 45),
	LANTERN_LENS(ItemID.LANTERN_LENS, 49, 55),
	RUBY_AMULET_U(ItemID.RUBY_AMULET_U, 50, 85),
	MAHOGANY_BIRD_HOUSE(ItemID.MAHOGANY_BIRD_HOUSE, 50, 40),
	SNAKESKIN_CHAPS(ItemID.SNAKESKIN_CHAPS, 51, 50),
	SNAKESKIN_BODY(ItemID.SNAKESKIN_BODY, 53, 55),
	WATER_BATTLESTAFF(ItemID.WATER_BATTLESTAFF, 54, 100),
	DRAGONSTONE(ItemID.DRAGONSTONE, 55, 137.5f),
	DRAGONSTONE_RING(ItemID.DRAGONSTONE_RING, 55, 100),
	DIAMOND_NECKLACE(ItemID.DIAMOND_NECKLACE, 56, 90),
	SNAKESKIN_SHIELD(ItemID.SNAKESKIN_SHIELD, 56, 100),
	GREEN_DHIDE_VAMB(ItemID.GREEN_DHIDE_VAMBRACES, 57, 62, true),
	DIAMOND_BRACELET(ItemID.DIAMOND_BRACELET, 58, 95),
	EARTH_BATTLESTAFF(ItemID.EARTH_BATTLESTAFF, 58, 112.5f),
	GREEN_DHIDE_CHAPS(ItemID.GREEN_DHIDE_CHAPS, 60, 124, true),
	YEW_BIRD_HOUSE(ItemID.YEW_BIRD_HOUSE, 60, 45),
	FIRE_BATTLESTAFF(ItemID.FIRE_BATTLESTAFF, 62, 125),
	GREEN_DHIDE_SHIELD(ItemID.GREEN_DHIDE_SHIELD, 62, 124),
	GREEN_DHIDE_BODY(ItemID.GREEN_DHIDE_BODY, 63, 186, true),
	AIR_BATTLESTAFF(ItemID.AIR_BATTLESTAFF, 66, 137.5f),
	BLUE_DHIDE_VAMB(ItemID.BLUE_DHIDE_VAMBRACES, 66, 70),
	ONYX_RING(ItemID.ONYX_RING, 67, 115),
	ONYX(ItemID.ONYX, 67, 167.5f),
	BLUE_DHIDE_CHAPS(ItemID.BLUE_DHIDE_CHAPS, 68, 140),
	BLUE_DHIDE_SHIELD(ItemID.BLUE_DHIDE_SHIELD, 69, 140),
	DIAMOND_AMULET_U(ItemID.DIAMOND_AMULET_U, 70, 100),
	BLUE_DHIDE_BODY(ItemID.BLUE_DHIDE_BODY, 71, 210),
	DRAGONSTONE_NECKLACE(ItemID.DRAGON_NECKLACE, 72, 105),
	RED_DHIDE_VAMB(ItemID.RED_DHIDE_VAMBRACES, 73, 78),
	DRAGONSTONE_BRACELET(ItemID.DRAGONSTONE_BRACELET, 74, 110),
	RED_DHIDE_CHAPS(ItemID.RED_DHIDE_CHAPS, 75, 156),
	MAGIC_BIRD_HOUSE(ItemID.MAGIC_BIRD_HOUSE, 75, 50),
	RED_DHIDE_SHIELD(ItemID.RED_DHIDE_SHIELD, 76, 156),
	RED_DHIDE_BODY(ItemID.RED_DHIDE_BODY, 77, 234),
	BLACK_DHIDE_VAMB(ItemID.BLACK_DHIDE_VAMBRACES, 79, 86),
	DRAGONSTONE_AMULET_U(ItemID.DRAGONSTONE_AMULET_U, 80, 150),
	BLACK_DHIDE_CHAPS(ItemID.BLACK_DHIDE_CHAPS, 82, 172),
	ONYX_NECKLACE(ItemID.ONYX_NECKLACE, 82, 120),
	AMETHYST_BOLT_TIPS(ItemID.AMETHYST_BOLT_TIPS, 83, 4),
	BLACK_DHIDE_SHIELD(ItemID.BLACK_DHIDE_SHIELD, 83, 172),
	BLACK_DHIDE_BODY(ItemID.BLACK_DHIDE_BODY, 84, 258),
	ONYX_BRACELET(ItemID.ONYX_BRACELET, 84, 125),
	AMETHYST_ARROWTIPS(ItemID.AMETHYST_ARROWTIPS, 85, 4),
	AMETHYST_JAVELIN_HEADS(ItemID.AMETHYST_JAVELIN_HEADS, 87, 12),
	LIGHT_ORB(ItemID.LIGHT_ORB, 87, 70),
	AMETHYST_DART_TIP(ItemID.AMETHYST_DART_TIP, 89, 7.5f),
	ZENYTE(ItemID.ZENYTE, 89, 200),
	ZENYTE_RING(ItemID.ZENYTE_RING, 89, 150),
	ONYX_AMULET_U(ItemID.ONYX_AMULET_U, 90, 165),
	REDWOOD_BIRD_HOUSE(ItemID.REDWOOD_BIRD_HOUSE, 90, 55),
	ZENYTE_NECKLACE(ItemID.ZENYTE_NECKLACE, 92, 165),
	ZENYTE_BRACELET(ItemID.ZENYTE_BRACELET_19532, 95, 180),
	ZENYTE_AMULET_U(ItemID.ZENYTE_AMULET_U, 98, 200),
	;

	private final int itemId;
	private final int level;
	private final float xp;
	private final boolean isMembersOverride;

	CraftingAction(int itemId, int level, float xp)
	{
		this(itemId, level, xp, false);
	}

	@Override
	public boolean isMembers(final ItemManager itemManager)
	{
		return isMembersOverride() || ItemSkillAction.super.isMembers(itemManager);
	}
}
