import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("qs")
public interface class451 extends Iterable {
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(IB)I",
      garbageValue = "69"
   )
   int vmethod8375(int var1);

   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "(ILjava/lang/Object;I)V",
      garbageValue = "-1706605993"
   )
   void vmethod8378(int var1, Object var2);
}
