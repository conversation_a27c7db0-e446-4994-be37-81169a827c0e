[{"class": "Formatting", "name": "cy", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Task", "name": "fm", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "intArgument", "owner": "fm", "name": "g", "access": 1, "descriptor": "I"}, {"field": "next", "owner": "fm", "name": "m", "access": 0, "descriptor": "Lfm;"}, {"field": "objectArgument", "owner": "fm", "name": "l", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "result", "owner": "fm", "name": "e", "access": 65, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "status", "owner": "fm", "name": "o", "access": 65, "descriptor": "I"}, {"field": "type", "owner": "fm", "name": "u", "access": 0, "descriptor": "I", "decoder": -1097728571}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GzipDecompressor", "name": "go", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "inflater", "owner": "go", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/zip/Inflater;"}], "methods": [{"method": "decompress", "owner": "go", "name": "m", "access": 1, "descriptor": "(Lgr;[BB)V"}], "constructors": [{"access": 1, "descriptor": "()V"}, {"access": 0, "descriptor": "(III)V"}]}, {"class": "AbstractRasterProvider", "name": "lp", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [{"field": "height", "owner": "lp", "name": "o", "access": 1, "descriptor": "I", "decoder": -239960145}, {"field": "pixels", "owner": "lp", "name": "q", "access": 1, "descriptor": "[I"}, {"field": "width", "owner": "lp", "name": "w", "access": 1, "descriptor": "I", "decoder": -560298363}], "methods": [{"method": "apply", "owner": "lp", "name": "b", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "draw", "owner": "lp", "name": "q", "access": 1025, "parameters": ["x", "y", "width", "height"], "descriptor": "(IIIIB)V"}, {"method": "drawFull", "owner": "lp", "name": "f", "access": 1025, "parameters": ["x", "y"], "descriptor": "(III)V"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "Clock", "name": "fa", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [], "methods": [{"method": "mark", "owner": "fa", "name": "m", "access": 1025, "parameters": [], "descriptor": "(B)V"}, {"method": "wait", "owner": "fa", "name": "f", "access": 1025, "parameters": ["cycleMs", "minSleepMs"], "descriptor": "(III)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ByteArrayPool", "name": "gi", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "DevicePcmPlayer", "name": "ax", "super": "co", "access": 33, "interfaces": [], "fields": [{"field": "byteSamples", "owner": "ax", "name": "w", "access": 0, "descriptor": "[B"}, {"field": "capacity2", "owner": "ax", "name": "q", "access": 0, "descriptor": "I", "decoder": -106586673}, {"field": "format", "owner": "ax", "name": "m", "access": 0, "descriptor": "Ljavax/sound/sampled/AudioFormat;"}, {"field": "line", "owner": "ax", "name": "f", "access": 0, "descriptor": "Ljavax/sound/sampled/SourceDataLine;"}], "methods": [{"method": "close", "owner": "ax", "name": "o", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "discard", "owner": "ax", "name": "u", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "init", "owner": "ax", "name": "m", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "open", "owner": "ax", "name": "f", "access": 4, "parameters": ["bufferSize"], "descriptor": "(II)V"}, {"method": "position", "owner": "ax", "name": "q", "access": 4, "parameters": [], "descriptor": "(B)I"}, {"method": "write", "owner": "ax", "name": "w", "access": 4, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON>", "name": "ao", "super": "java.awt.Canvas", "access": 49, "interfaces": [], "fields": [{"field": "component", "owner": "ao", "name": "m", "access": 0, "descriptor": "L<PERSON><PERSON>/awt/Component;"}], "methods": [{"method": "paint", "owner": "ao", "name": "paint", "access": 17, "parameters": ["g"], "descriptor": "(Ljava/awt/Graphics;)V"}, {"method": "update", "owner": "ao", "name": "update", "access": 17, "parameters": ["g"], "descriptor": "(Ljava/awt/Graphics;)V"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/awt/Component;)V"}]}, {"class": "ClientParameter", "name": "ji", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "id", "owner": "ji", "name": "y", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}]}, {"class": "MenuAction", "name": "cs", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "action", "owner": "cs", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "argument0", "owner": "cs", "name": "w", "access": 0, "descriptor": "I", "decoder": -2057883817}, {"field": "argument1", "owner": "cs", "name": "m", "access": 0, "descriptor": "I", "decoder": 305977315}, {"field": "argument2", "owner": "cs", "name": "f", "access": 0, "descriptor": "I", "decoder": 1764645917}, {"field": "opcode", "owner": "cs", "name": "q", "access": 0, "descriptor": "I", "decoder": -1483856253}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ClientPacket", "name": "gs", "super": "java.lang.Object", "access": 33, "interfaces": ["gb"], "fields": [{"field": "id", "owner": "gs", "name": "cd", "access": 16, "descriptor": "I", "decoder": -286923745}, {"field": "length", "owner": "gs", "name": "cx", "access": 16, "descriptor": "I", "decoder": -1759434223}], "methods": [], "constructors": [{"access": 0, "descriptor": "(II)V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "eb", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "is<PERSON><PERSON>", "owner": "eb", "name": "u", "access": 0, "descriptor": "Z"}, {"field": "neColor", "owner": "eb", "name": "q", "access": 0, "descriptor": "I", "decoder": -1668262341}, {"field": "nwColor", "owner": "eb", "name": "w", "access": 0, "descriptor": "I", "decoder": -1057776035}, {"field": "rgb", "owner": "eb", "name": "g", "access": 0, "descriptor": "I", "decoder": 2097947337}, {"field": "seColor", "owner": "eb", "name": "f", "access": 0, "descriptor": "I", "decoder": -1875844515}, {"field": "swColor", "owner": "eb", "name": "m", "access": 0, "descriptor": "I", "decoder": 181759449}, {"field": "texture", "owner": "eb", "name": "o", "access": 0, "descriptor": "I", "decoder": 463308567}], "methods": [], "constructors": [{"access": 0, "descriptor": "(IIIIIIZ)V"}]}, {"class": "AbstractByteArrayCopier", "name": "gu", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [], "methods": [{"method": "get", "owner": "gu", "name": "m", "access": 1024, "parameters": [], "descriptor": "(I)[B"}, {"method": "set", "owner": "gu", "name": "f", "access": 1024, "parameters": ["array"], "descriptor": "([BI)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ModelData0", "name": "ed", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON>", "name": "hv", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Strings", "name": "id", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "WorldMapCacheName", "name": "aw", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "name", "owner": "aw", "name": "u", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;)V"}]}, {"class": "TriBool", "name": "kd", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Decimator", "name": "dp", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "inputRate", "owner": "dp", "name": "w", "access": 0, "descriptor": "I", "decoder": -1376358157}, {"field": "outputRate", "owner": "dp", "name": "o", "access": 0, "descriptor": "I", "decoder": -876645877}, {"field": "table", "owner": "dp", "name": "u", "access": 0, "descriptor": "[[I"}], "methods": [{"method": "resample", "owner": "dp", "name": "m", "access": 0, "descriptor": "([BI)[B"}, {"method": "scalePosition", "owner": "dp", "name": "q", "access": 0, "parameters": ["position"], "descriptor": "(IB)I"}, {"method": "scaleRate", "owner": "dp", "name": "f", "access": 0, "parameters": ["rate"], "descriptor": "(II)I"}], "constructors": [{"access": 1, "descriptor": "(II)V"}]}, {"class": "DirectByteArrayCopier", "name": "gv", "super": "gu", "access": 33, "interfaces": [], "fields": [{"field": "directBuffer", "owner": "gv", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/nio/<PERSON>te<PERSON>er;"}], "methods": [{"method": "get", "owner": "gv", "name": "m", "access": 0, "parameters": [], "descriptor": "(I)[B"}, {"method": "set", "owner": "gv", "name": "f", "access": 0, "parameters": ["array"], "descriptor": "([BI)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "V<PERSON><PERSON>", "name": "ch", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "intsPersistence", "owner": "ch", "name": "q", "access": 0, "descriptor": "[Z"}, {"field": "lastWriteTimeMs", "owner": "ch", "name": "g", "access": 0, "descriptor": "J", "decoder": 4836824701467349417}, {"field": "map", "owner": "ch", "name": "w", "access": 0, "descriptor": "Ljava/util/Map;"}, {"field": "strings", "owner": "ch", "name": "o", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "unwrittenChanges", "owner": "ch", "name": "u", "access": 0, "descriptor": "Z"}], "methods": [{"method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "ch", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "getInt", "owner": "ch", "name": "f", "access": 0, "parameters": ["index"], "descriptor": "(IB)I"}, {"method": "getPreferencesFile", "owner": "ch", "name": "l", "access": 0, "descriptor": "(ZB)Ldk;"}, {"method": "getString", "owner": "ch", "name": "w", "access": 0, "parameters": ["index"], "descriptor": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "getStringOld", "owner": "ch", "name": "u", "access": 0, "parameters": ["index"], "descriptor": "(IB)Ljava/lang/String;"}, {"method": "hasUnwrittenChanges", "owner": "ch", "name": "a", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "read", "owner": "ch", "name": "x", "access": 0, "descriptor": "(I)V"}, {"method": "setInt", "owner": "ch", "name": "m", "access": 0, "parameters": ["index", "n"], "descriptor": "(III)V"}, {"method": "setString", "owner": "ch", "name": "q", "access": 0, "parameters": ["index", "s"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "setStringOld", "owner": "ch", "name": "o", "access": 0, "parameters": ["index", "s"], "descriptor": "(<PERSON>java/lang/String;B)V"}, {"method": "tryWrite", "owner": "ch", "name": "d", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "write", "owner": "ch", "name": "e", "access": 0, "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON>rna<PERSON>", "name": "ky", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "username", "owner": "ky", "name": "kr", "access": 1025, "descriptor": "(B)Lkp;"}], "constructors": []}, {"class": "SecureRandomFuture", "name": "cn", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "executor", "owner": "cn", "name": "m", "access": 0, "descriptor": "Ljava/util/concurrent/ExecutorService;"}, {"field": "future", "owner": "cn", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/concurrent/Future;"}], "methods": [{"method": "get", "owner": "cn", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)Ljava/security/SecureRandom;"}, {"method": "isDone", "owner": "cn", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "shutdown", "owner": "cn", "name": "m", "access": 0, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ServerPacket", "name": "ge", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "id", "owner": "ge", "name": "cu", "access": 17, "descriptor": "I", "decoder": 1117211673}, {"field": "length", "owner": "ge", "name": "cb", "access": 17, "descriptor": "I", "decoder": 947096829}], "methods": [], "constructors": [{"access": 0, "descriptor": "(II)V"}]}, {"class": "UrlRequest", "name": "ey", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "isDone0", "owner": "ey", "name": "f", "access": 64, "descriptor": "Z"}, {"field": "response0", "owner": "ey", "name": "q", "access": 64, "descriptor": "[B"}, {"field": "url", "owner": "ey", "name": "m", "access": 16, "descriptor": "Ljava/net/URL;"}], "methods": [{"method": "getResponse", "owner": "ey", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)[B"}, {"method": "isDone", "owner": "ey", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "(Ljava/net/URL;)V"}]}, {"class": "WorldMapLabel", "name": "aj", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "height", "owner": "aj", "name": "q", "access": 0, "descriptor": "I", "decoder": -85027761}, {"field": "size", "owner": "aj", "name": "w", "access": 0, "descriptor": "Ls;"}, {"field": "text", "owner": "aj", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "width", "owner": "aj", "name": "f", "access": 0, "descriptor": "I", "decoder": -471061617}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IILs;)V"}]}, {"class": "RunException", "name": "fx", "super": "java.lang.RuntimeException", "access": 33, "interfaces": [], "fields": [{"field": "string", "owner": "fx", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "throwable", "owner": "fx", "name": "u", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Throwable;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/Throwable;<PERSON><PERSON><PERSON>/lang/String;)V"}]}, {"class": "Node", "name": "gw", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "key", "owner": "gw", "name": "cl", "access": 1, "descriptor": "J"}, {"field": "next", "owner": "gw", "name": "cz", "access": 1, "descriptor": "Lgw;"}, {"field": "previous", "owner": "gw", "name": "ct", "access": 1, "descriptor": "Lgw;"}], "methods": [{"method": "hasNext", "owner": "gw", "name": "eg", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "remove", "owner": "gw", "name": "es", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "DefaultsGroup", "name": "ka", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "group", "owner": "ka", "name": "f", "access": 16, "descriptor": "I", "decoder": -800980069}], "methods": [], "constructors": [{"access": 0, "descriptor": "(I)V"}]}, {"class": "SoundEnvelope", "name": "de", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "amplitude", "owner": "de", "name": "d", "access": 0, "descriptor": "I"}, {"field": "durations", "owner": "de", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "end", "owner": "de", "name": "o", "access": 0, "descriptor": "I"}, {"field": "form", "owner": "de", "name": "u", "access": 0, "descriptor": "I"}, {"field": "max", "owner": "de", "name": "k", "access": 0, "descriptor": "I"}, {"field": "phaseIndex", "owner": "de", "name": "l", "access": 0, "descriptor": "I"}, {"field": "phases", "owner": "de", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "segments", "owner": "de", "name": "m", "access": 0, "descriptor": "I"}, {"field": "start", "owner": "de", "name": "w", "access": 0, "descriptor": "I"}, {"field": "step", "owner": "de", "name": "e", "access": 0, "descriptor": "I"}, {"field": "ticks", "owner": "de", "name": "g", "access": 0, "descriptor": "I"}], "methods": [{"method": "decode", "owner": "de", "name": "m", "access": 16, "parameters": ["buffer"], "descriptor": "(Lgr;)V"}, {"method": "decodeSegments", "owner": "de", "name": "f", "access": 16, "parameters": ["buffer"], "descriptor": "(Lgr;)V"}, {"method": "doStep", "owner": "de", "name": "w", "access": 16, "parameters": ["n"], "descriptor": "(I)I"}, {"method": "reset", "owner": "de", "name": "q", "access": 16, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "FriendLoginUpdate", "name": "kn", "super": "gh", "access": 33, "interfaces": [], "fields": [{"field": "time", "owner": "kn", "name": "q", "access": 1, "descriptor": "I", "decoder": -1390885889}, {"field": "username", "owner": "kn", "name": "w", "access": 1, "descriptor": "Lkp;"}, {"field": "world", "owner": "kn", "name": "o", "access": 1, "descriptor": "S"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(Lkp;I)V"}]}, {"class": "GrandExchangeEvents", "name": "g", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "events", "owner": "g", "name": "m", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/util/List;"}], "methods": [{"method": "sort", "owner": "g", "name": "m", "access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/util/Comparator;ZI)V"}], "constructors": [{"access": 1, "descriptor": "(Lgr;Z)V"}]}, {"class": "ServerBuild", "name": "iy", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "id", "owner": "iy", "name": "u", "access": 17, "descriptor": "I", "decoder": -947415971}, {"field": "name", "owner": "iy", "name": "o", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "bc", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "ids", "owner": "bc", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "quantities", "owner": "bc", "name": "q", "access": 0, "descriptor": "[I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "StudioGame", "name": "is", "super": "java.lang.Object", "access": 33, "interfaces": ["gc"], "fields": [{"field": "id", "owner": "is", "name": "l", "access": 16, "descriptor": "I", "decoder": -1688917715}, {"field": "name", "owner": "is", "name": "g", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [{"method": "ordinal", "owner": "is", "name": "f", "access": 1, "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}]}, {"class": "ByteArrayNode", "name": "gp", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "byteArray", "owner": "gp", "name": "m", "access": 1, "descriptor": "[B"}], "methods": [], "constructors": [{"access": 1, "descriptor": "([B)V"}]}, {"class": "Nan<PERSON><PERSON><PERSON>", "name": "fp", "super": "fa", "access": 33, "interfaces": [], "fields": [{"field": "lastTimeNano", "owner": "fp", "name": "m", "access": 0, "descriptor": "J", "decoder": 7586221391557635221}], "methods": [{"method": "mark", "owner": "fp", "name": "m", "access": 1, "parameters": [], "descriptor": "(B)V"}, {"method": "wait", "owner": "fp", "name": "f", "access": 1, "parameters": ["cycleMs", "minSleepMs"], "descriptor": "(III)I"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "ai", "super": "lp", "access": 49, "interfaces": [], "fields": [{"field": "component0", "owner": "ai", "name": "m", "access": 0, "descriptor": "L<PERSON><PERSON>/awt/Component;"}, {"field": "image", "owner": "ai", "name": "f", "access": 0, "descriptor": "Ljava/awt/Image;"}], "methods": [{"method": "draw", "owner": "ai", "name": "q", "access": 17, "parameters": ["x", "y", "width", "height"], "descriptor": "(IIIIB)V"}, {"method": "draw0", "owner": "ai", "name": "o", "access": 16, "parameters": ["graphics", "x", "y", "width", "height"], "descriptor": "(Ljava/awt/Graphics;IIIIB)V"}, {"method": "drawFull", "owner": "ai", "name": "f", "access": 17, "parameters": ["x", "y"], "descriptor": "(III)V"}, {"method": "drawFull0", "owner": "ai", "name": "w", "access": 16, "parameters": ["graphics", "x", "y"], "descriptor": "(Ljava/awt/Graphics;III)V"}, {"method": "setComponent", "owner": "ai", "name": "m", "access": 16, "descriptor": "(<PERSON><PERSON><PERSON>/awt/Component;B)V"}], "constructors": [{"access": 0, "descriptor": "(IILjava/awt/Component;)V"}]}, {"class": "MouseW<PERSON><PERSON>", "name": "fv", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "useRotation", "owner": "fv", "name": "q", "access": 1025, "parameters": [], "descriptor": "(I)I"}], "constructors": []}, {"class": "WorldMapArchiveLoader", "name": "ls", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "archive", "owner": "ls", "name": "u", "access": 0, "descriptor": "Lir;"}, {"field": "cacheName", "owner": "ls", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "isLoaded0", "owner": "ls", "name": "l", "access": 0, "descriptor": "Z"}, {"field": "percentLoaded0", "owner": "ls", "name": "g", "access": 0, "descriptor": "I", "decoder": 624113115}], "methods": [{"method": "isLoaded", "owner": "ls", "name": "q", "access": 0, "parameters": [], "descriptor": "(S)Z"}, {"method": "load", "owner": "ls", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "percentLoaded", "owner": "ls", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "reset", "owner": "ls", "name": "m", "access": 0, "parameters": ["cacheName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}], "constructors": [{"access": 0, "descriptor": "(Lir;)V"}]}, {"class": "ClientPacketMarker", "name": "gb", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "AbstractSound", "name": "dd", "super": "gw", "access": 1057, "interfaces": [], "fields": [{"field": "position", "owner": "dd", "name": "u", "access": 0, "descriptor": "I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "NodeDeque", "name": "je", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "current", "owner": "je", "name": "f", "access": 0, "descriptor": "Lgw;"}, {"field": "sentinel", "owner": "je", "name": "m", "access": 1, "descriptor": "Lgw;"}], "methods": [{"method": "add<PERSON><PERSON><PERSON>", "owner": "je", "name": "f", "access": 1, "parameters": ["node"], "descriptor": "(Lgw;)V"}, {"method": "addLast", "owner": "je", "name": "q", "access": 1, "parameters": ["node"], "descriptor": "(Lgw;)V"}, {"method": "clear", "owner": "je", "name": "m", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "first", "owner": "je", "name": "l", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "last", "owner": "je", "name": "g", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "next", "owner": "je", "name": "x", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "previous", "owner": "je", "name": "e", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "remove<PERSON><PERSON><PERSON>", "owner": "je", "name": "u", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "removeLast", "owner": "je", "name": "o", "access": 1, "parameters": [], "descriptor": "()Lgw;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "UserList", "name": "km", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [{"field": "array", "owner": "km", "name": "u", "access": 0, "descriptor": "[Ljs;"}, {"field": "capacity", "owner": "km", "name": "w", "access": 16, "descriptor": "I", "decoder": -2119868987}, {"field": "comparator", "owner": "km", "name": "e", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}, {"field": "previousUsernamesMap", "owner": "km", "name": "l", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "size0", "owner": "km", "name": "o", "access": 0, "descriptor": "I", "decoder": -280101247}, {"field": "usernamesMap", "owner": "km", "name": "g", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}], "methods": [{"method": "addComparator", "owner": "km", "name": "al", "access": 17, "parameters": ["c"], "descriptor": "(<PERSON><PERSON><PERSON>/util/Comparator;B)V"}, {"method": "addLast", "owner": "km", "name": "ag", "access": 0, "parameters": ["username", "previousUsername"], "descriptor": "(Lkp;Lkp;I)Ljs;"}, {"method": "addLastNoPreviousUsername", "owner": "km", "name": "v", "access": 0, "parameters": ["username"], "descriptor": "(Lkp;I)Ljs;"}, {"method": "arrayAddLast", "owner": "km", "name": "ay", "access": 16, "parameters": ["user"], "descriptor": "(Ljs;I)V"}, {"method": "arrayRemove", "owner": "km", "name": "ak", "access": 16, "parameters": ["index"], "descriptor": "(II)V"}, {"method": "changeName", "owner": "km", "name": "av", "access": 16, "parameters": ["user", "username", "previousUsername"], "descriptor": "(Ljs;Lkp;Lkp;B)V"}, {"method": "clear", "owner": "km", "name": "z", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "contains", "owner": "km", "name": "t", "access": 1, "parameters": ["username"], "descriptor": "(Lkp;I)Z"}, {"method": "get", "owner": "km", "name": "aq", "access": 17, "parameters": ["index"], "descriptor": "(II)Ljs;"}, {"method": "getByCurrentUsername", "owner": "km", "name": "h", "access": 0, "parameters": ["username"], "descriptor": "(Lkp;I)Ljs;"}, {"method": "getByPreviousUsername", "owner": "km", "name": "b", "access": 0, "parameters": ["previousUsername"], "descriptor": "(Lkp;I)Ljs;"}, {"method": "getByUsername", "owner": "km", "name": "y", "access": 1, "parameters": ["username"], "descriptor": "(Lkp;I)Ljs;"}, {"method": "indexOf", "owner": "km", "name": "ar", "access": 16, "parameters": ["user"], "descriptor": "(<PERSON><PERSON><PERSON>;<PERSON>)I"}, {"method": "isFull", "owner": "km", "name": "s", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "mapPut", "owner": "km", "name": "ah", "access": 16, "parameters": ["user"], "descriptor": "(Ljs;B)V"}, {"method": "mapRemove", "owner": "km", "name": "ac", "access": 16, "parameters": ["user"], "descriptor": "(Ljs;S)V"}, {"method": "newInstance", "owner": "km", "name": "m", "access": 1024, "parameters": [], "descriptor": "(I)Ljs;"}, {"method": "newTypedArray", "owner": "km", "name": "f", "access": 1024, "parameters": ["size"], "descriptor": "(II)[Ljs;"}, {"method": "remove", "owner": "km", "name": "p", "access": 16, "parameters": ["user"], "descriptor": "(Ljs;I)V"}, {"method": "removeByUsername", "owner": "km", "name": "c", "access": 17, "parameters": ["username"], "descriptor": "(Lkp;S)Z"}, {"method": "removeComparator", "owner": "km", "name": "aw", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "size", "owner": "km", "name": "j", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "sort", "owner": "km", "name": "aj", "access": 17, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "(I)V"}]}, {"class": "Skeleton", "name": "ek", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "count", "owner": "ek", "name": "f", "access": 0, "descriptor": "I", "decoder": 1875841677}, {"field": "id", "owner": "ek", "name": "m", "access": 0, "descriptor": "I", "decoder": -1634889691}, {"field": "labels", "owner": "ek", "name": "w", "access": 0, "descriptor": "[[I"}, {"field": "transformTypes", "owner": "ek", "name": "q", "access": 0, "descriptor": "[I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(I[B)V"}]}, {"class": "IntegerNode", "name": "gn", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "integer", "owner": "gn", "name": "m", "access": 1, "descriptor": "I"}], "methods": [], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "ObjectNode", "name": "fk", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "obj", "owner": "fk", "name": "m", "access": 17, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}], "methods": [], "constructors": [{"access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)V"}]}, {"class": "DualNode", "name": "fn", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "keyDual", "owner": "fn", "name": "cg", "access": 1, "descriptor": "J"}, {"field": "nextDual", "owner": "fn", "name": "cp", "access": 1, "descriptor": "Lfn;"}, {"field": "previousDual", "owner": "fn", "name": "cf", "access": 1, "descriptor": "Lfn;"}], "methods": [{"method": "removeDual", "owner": "fn", "name": "cg", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "InterfaceParent", "name": "bx", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "itf", "owner": "bx", "name": "m", "access": 0, "descriptor": "I", "decoder": -707461797}, {"field": "keep", "owner": "bx", "name": "q", "access": 0, "descriptor": "Z"}, {"field": "type", "owner": "bx", "name": "f", "access": 0, "descriptor": "I", "decoder": 1712037041}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ParamDefinition", "name": "jb", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "autodisable", "owner": "jb", "name": "u", "access": 0, "descriptor": "Z"}, {"field": "defaultint", "owner": "jb", "name": "w", "access": 1, "descriptor": "I", "decoder": 1133042753}, {"field": "defaultstr", "owner": "jb", "name": "o", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "type", "owner": "jb", "name": "q", "access": 0, "descriptor": "C"}], "methods": [{"method": "decode", "owner": "jb", "name": "q", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jb", "name": "w", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "isString", "owner": "jb", "name": "o", "access": 1, "descriptor": "(I)Z"}, {"method": "postDecode", "owner": "jb", "name": "f", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Animation", "name": "dz", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "hasAlphaTransform", "owner": "dz", "name": "d", "access": 0, "descriptor": "Z"}, {"field": "skeleton", "owner": "dz", "name": "o", "access": 0, "descriptor": "Lek;"}, {"field": "transformCount", "owner": "dz", "name": "u", "access": 0, "descriptor": "I"}, {"field": "transformSkeletonLabels", "owner": "dz", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "transformXs", "owner": "dz", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "transformYs", "owner": "dz", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "transformZs", "owner": "dz", "name": "x", "access": 0, "descriptor": "[I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "([BLek;)V"}]}, {"class": "PcmPlayerProvider", "name": "cz", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "player", "owner": "cz", "name": "m", "access": 1025, "descriptor": "(B)Lco;"}], "constructors": []}, {"class": "Enumerated", "name": "gc", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "ordinal", "owner": "gc", "name": "f", "access": 1025, "parameters": [], "descriptor": "(B)I"}], "constructors": []}, {"class": "NetFileRequest", "name": "iz", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "archive", "owner": "iz", "name": "m", "access": 1, "descriptor": "Lit;"}, {"field": "crc", "owner": "iz", "name": "f", "access": 1, "descriptor": "I", "decoder": **********}, {"field": "padding", "owner": "iz", "name": "q", "access": 1, "descriptor": "B"}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON>", "name": "ke", "super": "js", "access": 33, "interfaces": [], "fields": [{"field": "int2", "owner": "ke", "name": "u", "access": 1, "descriptor": "I", "decoder": 120689297}, {"field": "rank", "owner": "ke", "name": "g", "access": 1, "descriptor": "I", "decoder": -304954565}, {"field": "world0", "owner": "ke", "name": "o", "access": 1, "descriptor": "I", "decoder": 429472919}], "methods": [{"method": "hasWorld", "owner": "ke", "name": "au", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "set", "owner": "ke", "name": "an", "access": 0, "descriptor": "(IIS)V"}, {"method": "world", "owner": "ke", "name": "az", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "HealthBarUpdate", "name": "bg", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "cycle", "owner": "bg", "name": "m", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "cycleOffset", "owner": "bg", "name": "w", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "health", "owner": "bg", "name": "f", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "health2", "owner": "bg", "name": "q", "access": 0, "descriptor": "I", "decoder": -820353645}], "methods": [{"method": "set", "owner": "bg", "name": "m", "access": 0, "descriptor": "(IIIII)V"}], "constructors": [{"access": 0, "descriptor": "(IIII)V"}]}, {"class": "HealthBar", "name": "cr", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "definition", "owner": "cr", "name": "q", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "updates", "owner": "cr", "name": "w", "access": 0, "descriptor": "Lja;"}], "methods": [{"method": "get", "owner": "cr", "name": "f", "access": 0, "parameters": ["cycle"], "descriptor": "(II)Lbg;"}, {"method": "isEmpty", "owner": "cr", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "put", "owner": "cr", "name": "m", "access": 0, "descriptor": "(IIIIB)V"}], "constructors": [{"access": 0, "descriptor": "(Lii;)V"}]}, {"class": "IntHashTable", "name": "lv", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "array", "owner": "lv", "name": "m", "access": 0, "descriptor": "[I"}], "methods": [{"method": "get", "owner": "lv", "name": "m", "access": 1, "descriptor": "(II)I"}], "constructors": [{"access": 1, "descriptor": "([I)V"}]}, {"class": "FloorUnderlayDefinition", "name": "ih", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "hue", "owner": "ih", "name": "w", "access": 1, "descriptor": "I", "decoder": -1465372343}, {"field": "hueMultiplier", "owner": "ih", "name": "g", "access": 1, "descriptor": "I", "decoder": 1484542081}, {"field": "lightness", "owner": "ih", "name": "u", "access": 1, "descriptor": "I", "decoder": -1691813081}, {"field": "rgb", "owner": "ih", "name": "q", "access": 0, "descriptor": "I", "decoder": -635678213}, {"field": "saturation", "owner": "ih", "name": "o", "access": 1, "descriptor": "I", "decoder": 2031118785}], "methods": [{"method": "decode", "owner": "ih", "name": "w", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "decode0", "owner": "ih", "name": "o", "access": 0, "descriptor": "(Lgr;III)V"}, {"method": "postDecode", "owner": "ih", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "setHsl", "owner": "ih", "name": "u", "access": 0, "parameters": ["rgb"], "descriptor": "(IB)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Wrapper", "name": "eu", "super": "fn", "access": 1057, "interfaces": [], "fields": [{"field": "size", "owner": "eu", "name": "f", "access": 16, "descriptor": "I"}], "methods": [{"method": "get", "owner": "eu", "name": "m", "access": 1024, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "isSoft", "owner": "eu", "name": "f", "access": 1024, "parameters": [], "descriptor": "()Z"}], "constructors": [{"access": 0, "descriptor": "(I)V"}]}, {"class": "DualNodeDeque", "name": "jp", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "sentinel", "owner": "jp", "name": "m", "access": 0, "descriptor": "Lfn;"}], "methods": [{"method": "add<PERSON><PERSON><PERSON>", "owner": "jp", "name": "m", "access": 1, "descriptor": "(Lfn;)V"}, {"method": "addLast", "owner": "jp", "name": "f", "access": 1, "descriptor": "(Lfn;)V"}, {"method": "clear", "owner": "jp", "name": "u", "access": 1, "descriptor": "()V"}, {"method": "last", "owner": "jp", "name": "o", "access": 1, "descriptor": "()Lfn;"}, {"method": "removeLast", "owner": "jp", "name": "w", "access": 1, "descriptor": "()Lfn;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "NodeHashTable", "name": "ld", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "buckets", "owner": "ld", "name": "f", "access": 0, "descriptor": "[Lgw;"}, {"field": "current", "owner": "ld", "name": "w", "access": 0, "descriptor": "Lgw;"}, {"field": "currentGet", "owner": "ld", "name": "q", "access": 0, "descriptor": "Lgw;"}, {"field": "index", "owner": "ld", "name": "o", "access": 0, "descriptor": "I"}, {"field": "size", "owner": "ld", "name": "m", "access": 0, "descriptor": "I"}], "methods": [{"method": "clear", "owner": "ld", "name": "q", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "first", "owner": "ld", "name": "w", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "get", "owner": "ld", "name": "m", "access": 1, "parameters": ["key"], "descriptor": "(J)Lgw;"}, {"method": "next", "owner": "ld", "name": "o", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "put", "owner": "ld", "name": "f", "access": 1, "parameters": ["node", "key"], "descriptor": "(Lgw;J)V"}], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "ReflectionCheck", "name": "lw", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "arguments", "owner": "lw", "name": "m", "access": 0, "descriptor": "[[[B"}, {"field": "creationErrors", "owner": "lw", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "fields", "owner": "lw", "name": "g", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/reflect/Field;"}, {"field": "id", "owner": "lw", "name": "q", "access": 0, "descriptor": "I", "decoder": -1955146989}, {"field": "intReplaceValues", "owner": "lw", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "methods", "owner": "lw", "name": "l", "access": 0, "descriptor": "[<PERSON>ja<PERSON>/lang/reflect/Method;"}, {"field": "operations", "owner": "lw", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "size", "owner": "lw", "name": "w", "access": 0, "descriptor": "I", "decoder": -1091722075}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "AbstractSocket", "name": "fu", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [], "methods": [{"method": "available", "owner": "fu", "name": "q", "access": 1025, "parameters": [], "descriptor": "(I)I"}, {"method": "close", "owner": "fu", "name": "g", "access": 1025, "parameters": [], "descriptor": "(I)V"}, {"method": "isAvailable", "owner": "fu", "name": "f", "access": 1025, "parameters": ["length"], "descriptor": "(II)Z"}, {"method": "read", "owner": "fu", "name": "o", "access": 1025, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIII)I"}, {"method": "readUnsignedByte", "owner": "fu", "name": "w", "access": 1025, "parameters": [], "descriptor": "(I)I"}, {"method": "write", "owner": "fu", "name": "u", "access": 1025, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "VarbitDefinition", "name": "ia", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "baseVar", "owner": "ia", "name": "q", "access": 1, "descriptor": "I", "decoder": 1552575189}, {"field": "endBit", "owner": "ia", "name": "o", "access": 1, "descriptor": "I", "decoder": -1710935305}, {"field": "startBit", "owner": "ia", "name": "w", "access": 1, "descriptor": "I", "decoder": -870948181}], "methods": [{"method": "decode", "owner": "ia", "name": "q", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "ia", "name": "w", "access": 0, "descriptor": "(Lgr;IB)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "InvDefinition", "name": "ib", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "size", "owner": "ib", "name": "q", "access": 1, "descriptor": "I", "decoder": 1358868971}], "methods": [{"method": "decode", "owner": "ib", "name": "f", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "ib", "name": "q", "access": 0, "descriptor": "(Lgr;II)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "LoginPacket", "name": "gl", "super": "java.lang.Object", "access": 33, "interfaces": ["gb"], "fields": [{"field": "id", "owner": "gl", "name": "u", "access": 17, "descriptor": "I", "decoder": 1743806243}], "methods": [], "constructors": [{"access": 0, "descriptor": "(II)V"}]}, {"class": "WorldMapEvent", "name": "ap", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "coord1", "owner": "ap", "name": "f", "access": 1, "descriptor": "<PERSON><PERSON>;"}, {"field": "coord2", "owner": "ap", "name": "q", "access": 1, "descriptor": "<PERSON><PERSON>;"}, {"field": "mapElement", "owner": "ap", "name": "m", "access": 1, "descriptor": "I", "decoder": 264384233}], "methods": [], "constructors": [{"access": 1, "descriptor": "(ILhu;Lhu;)V"}]}, {"class": "WorldMapSection", "name": "ab", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "containsCoord", "owner": "ab", "name": "f", "access": 1025, "parameters": ["plane", "x", "y"], "descriptor": "(IIIB)Z"}, {"method": "containsPosition", "owner": "ab", "name": "q", "access": 1025, "parameters": ["x", "y"], "descriptor": "(IIB)Z"}, {"method": "coord", "owner": "ab", "name": "o", "access": 1025, "parameters": ["x", "y"], "descriptor": "(III)<PERSON>hu;"}, {"method": "expandBounds", "owner": "ab", "name": "m", "access": 1025, "parameters": ["area"], "descriptor": "(Lag;I)V"}, {"method": "position", "owner": "ab", "name": "w", "access": 1025, "parameters": ["plane", "x", "y"], "descriptor": "(IIII)[I"}, {"method": "read", "owner": "ab", "name": "u", "access": 1025, "parameters": ["buffer"], "descriptor": "(Lgr;B)V"}], "constructors": []}, {"class": "Frames", "name": "en", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "frames", "owner": "en", "name": "m", "access": 0, "descriptor": "[Ldz;"}], "methods": [{"method": "hasAlphaTransform", "owner": "en", "name": "m", "access": 1, "parameters": ["frame"], "descriptor": "(II)Z"}], "constructors": [{"access": 1, "descriptor": "(Lir;Lir;IZ)V"}]}, {"class": "Skills", "name": "hc", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "SoftWrapper", "name": "ef", "super": "eu", "access": 33, "interfaces": [], "fields": [{"field": "ref", "owner": "ef", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/ref/SoftReference;"}], "methods": [{"method": "get", "owner": "ef", "name": "m", "access": 0, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "isSoft", "owner": "ef", "name": "f", "access": 0, "parameters": [], "descriptor": "()Z"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}]}, {"class": "DirectWrapper", "name": "ew", "super": "eu", "access": 33, "interfaces": [], "fields": [{"field": "obj", "owner": "ew", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}], "methods": [{"method": "get", "owner": "ew", "name": "m", "access": 0, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "isSoft", "owner": "ew", "name": "f", "access": 0, "parameters": [], "descriptor": "()Z"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}]}, {"class": "SoundCache", "name": "dt", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "musicSampleIndex", "owner": "dt", "name": "f", "access": 0, "descriptor": "Lir;"}, {"field": "musicSamples", "owner": "dt", "name": "q", "access": 0, "descriptor": "Lld;"}, {"field": "rawSounds", "owner": "dt", "name": "w", "access": 0, "descriptor": "Lld;"}, {"field": "soundEffectIndex", "owner": "dt", "name": "m", "access": 0, "descriptor": "Lir;"}], "methods": [{"method": "getMusicSample", "owner": "dt", "name": "w", "access": 1, "descriptor": "(I[II)Lcx;"}, {"method": "getMusicSample0", "owner": "dt", "name": "f", "access": 0, "descriptor": "(II[II)Lcx;"}, {"method": "getSoundEffect", "owner": "dt", "name": "q", "access": 1, "descriptor": "(I[IB)Lcx;"}, {"method": "getSoundEffect0", "owner": "dt", "name": "m", "access": 0, "descriptor": "(II[IS)Lcx;"}], "constructors": [{"access": 1, "descriptor": "(Lir;Lir;)V"}]}, {"class": "SpriteMask", "name": "hp", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "height", "owner": "hp", "name": "f", "access": 17, "descriptor": "I", "decoder": -193097093}, {"field": "width", "owner": "hp", "name": "m", "access": 17, "descriptor": "I", "decoder": 574541221}, {"field": "xStarts", "owner": "hp", "name": "w", "access": 17, "descriptor": "[I"}, {"field": "xWidths", "owner": "hp", "name": "q", "access": 17, "descriptor": "[I"}], "methods": [{"method": "contains", "owner": "hp", "name": "m", "access": 1, "parameters": ["x", "y"], "descriptor": "(III)Z"}], "constructors": [{"access": 0, "descriptor": "(II[I[II)V"}]}, {"class": "StructDefinition", "name": "jq", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "params", "owner": "jq", "name": "q", "access": 0, "descriptor": "Llh;"}], "methods": [{"method": "decode", "owner": "jq", "name": "q", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jq", "name": "w", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "getIntParam", "owner": "jq", "name": "o", "access": 1, "descriptor": "(IIB)I"}, {"method": "getStringParam", "owner": "jq", "name": "u", "access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"method": "postDecode", "owner": "jq", "name": "f", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Rasterizer2D", "name": "lf", "super": "fn", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "Bzip2Decompressor", "name": "gk", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Interpreter", "name": "bv", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "IgnoreList", "name": "kj", "super": "km", "access": 33, "interfaces": [], "fields": [{"field": "loginType", "owner": "kj", "name": "q", "access": 16, "descriptor": "Llx;"}], "methods": [{"method": "newInstance", "owner": "kj", "name": "m", "access": 0, "descriptor": "(I)Ljs;"}, {"method": "newTypedArray", "owner": "kj", "name": "f", "access": 0, "descriptor": "(II)[Ljs;"}, {"method": "read", "owner": "kj", "name": "q", "access": 1, "parameters": ["buffer", "n"], "descriptor": "(Lgr;IB)V"}], "constructors": [{"access": 1, "descriptor": "(Llx;)V"}]}, {"class": "Message", "name": "bm", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "count", "owner": "bm", "name": "m", "access": 0, "descriptor": "I", "decoder": 1597147817}, {"field": "cycle", "owner": "bm", "name": "f", "access": 0, "descriptor": "I", "decoder": 1164592791}, {"field": "isFromFriend0", "owner": "bm", "name": "u", "access": 0, "descriptor": "Lkd;"}, {"field": "isFromIgnored0", "owner": "bm", "name": "g", "access": 0, "descriptor": "Lkd;"}, {"field": "prefix", "owner": "bm", "name": "l", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "sender", "owner": "bm", "name": "w", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "senderUsername", "owner": "bm", "name": "o", "access": 0, "descriptor": "Lkp;"}, {"field": "text", "owner": "bm", "name": "e", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "type", "owner": "bm", "name": "q", "access": 0, "descriptor": "I", "decoder": 1223792995}], "methods": [{"method": "clearIsFromFriend", "owner": "bm", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "clearIsFromIgnored", "owner": "bm", "name": "o", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "fillIsFromFriend", "owner": "bm", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "fillIsFromIgnored", "owner": "bm", "name": "g", "access": 0, "parameters": [], "descriptor": "(S)V"}, {"method": "fillSenderUsername", "owner": "bm", "name": "l", "access": 16, "parameters": [], "descriptor": "(B)V"}, {"method": "isFromFriend", "owner": "bm", "name": "q", "access": 16, "parameters": [], "descriptor": "(I)Z"}, {"method": "isFromIgnored", "owner": "bm", "name": "u", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "set", "owner": "bm", "name": "m", "access": 0, "parameters": ["type", "sender", "prefix", "text"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;B)V"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;)V"}]}, {"class": "Entity", "name": "ex", "super": "fn", "access": 1057, "interfaces": [], "fields": [{"field": "height", "owner": "ex", "name": "cw", "access": 1, "descriptor": "I", "decoder": 849583845}], "methods": [{"method": "draw", "owner": "ex", "name": "cy", "access": 0, "parameters": ["yaw", "cameraPitchSine", "cameraPitchCosine", "cameraYawSine", "cameraYawCosine", "x", "y", "z", "tag"], "descriptor": "(IIIIIIIIJ)V"}, {"method": "getModel", "owner": "ex", "name": "e", "access": 4, "parameters": [], "descriptor": "(I)Ldu;"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "NetCache", "name": "iq", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "EnumDefinition", "name": "jw", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "defaultint", "owner": "jw", "name": "u", "access": 1, "descriptor": "I", "decoder": 1948115527}, {"field": "defaultstr", "owner": "jw", "name": "o", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "inputtype", "owner": "jw", "name": "q", "access": 1, "descriptor": "C"}, {"field": "intvals", "owner": "jw", "name": "e", "access": 1, "descriptor": "[I"}, {"field": "keys", "owner": "jw", "name": "l", "access": 1, "descriptor": "[I"}, {"field": "outputcount", "owner": "jw", "name": "g", "access": 1, "descriptor": "I", "decoder": -12324053}, {"field": "outputtype", "owner": "jw", "name": "w", "access": 1, "descriptor": "C"}, {"field": "strvals", "owner": "jw", "name": "x", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [{"method": "decode", "owner": "jw", "name": "f", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jw", "name": "q", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "size", "owner": "jw", "name": "w", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON>", "name": "cu", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "AttackOption", "name": "cj", "super": "java.lang.Object", "access": 33, "interfaces": ["gc"], "fields": [{"field": "id", "owner": "cj", "name": "o", "access": 16, "descriptor": "I", "decoder": -1461217963}], "methods": [{"method": "ordinal", "owner": "cj", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(I)V"}]}, {"class": "Messages", "name": "cw", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Rasterizer3D", "name": "et", "super": "lf", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "AbstractFont", "name": "kt", "super": "lf", "access": 1057, "interfaces": [], "fields": [{"field": "advances", "owner": "kt", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "ascent", "owner": "kt", "name": "g", "access": 1, "descriptor": "I"}, {"field": "heights", "owner": "kt", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "kerning", "owner": "kt", "name": "d", "access": 0, "descriptor": "[B"}, {"field": "leftBearings", "owner": "kt", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "maxAscent", "owner": "kt", "name": "l", "access": 1, "descriptor": "I"}, {"field": "maxDescent", "owner": "kt", "name": "e", "access": 1, "descriptor": "I"}, {"field": "pixels", "owner": "kt", "name": "m", "access": 0, "descriptor": "[[B"}, {"field": "topBearings", "owner": "kt", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "widths", "owner": "kt", "name": "q", "access": 0, "descriptor": "[I"}], "methods": [{"method": "breakLines", "owner": "kt", "name": "a", "access": 1, "parameters": ["s", "lineWidths", "linesDst"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;[I[Ljava/lang/String;)I"}, {"method": "calculateLineJustification", "owner": "kt", "name": "ar", "access": 0, "parameters": ["s", "lineWidth"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "char<PERSON><PERSON><PERSON>", "owner": "kt", "name": "x", "access": 0, "parameters": ["c"], "descriptor": "(C)I"}, {"method": "decodeTag", "owner": "kt", "name": "av", "access": 0, "parameters": ["s"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;)V"}, {"method": "draw", "owner": "kt", "name": "t", "access": 1, "parameters": ["s", "x", "y", "color", "shadow"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIII)V"}, {"method": "draw0", "owner": "kt", "name": "ac", "access": 0, "parameters": ["s", "x", "y"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;II)V"}, {"method": "drawAl<PERSON>", "owner": "kt", "name": "y", "access": 1, "parameters": ["s", "x", "y", "color", "shadow", "alpha"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIII)V"}, {"method": "drawCentered", "owner": "kt", "name": "b", "access": 1, "parameters": ["s", "x", "y", "color", "shadow"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIII)V"}, {"method": "drawCenteredShake", "owner": "kt", "name": "ag", "access": 1, "parameters": ["s", "x", "y", "color", "shadow", "seed", "seed2"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIIII)V"}, {"method": "drawCenteredWave", "owner": "kt", "name": "p", "access": 1, "parameters": ["s", "x", "y", "color", "shadow", "seed"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIII)V"}, {"method": "drawCenteredWave2", "owner": "kt", "name": "v", "access": 1, "parameters": ["s", "x", "y", "color", "shadow", "seed"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIII)V"}, {"method": "drawGlyph", "owner": "kt", "name": "m", "access": 1024, "parameters": ["pixels", "x", "y", "width", "height", "color"], "descriptor": "([BIIIII)V"}, {"method": "drawGlyphAlpha", "owner": "kt", "name": "f", "access": 1024, "parameters": ["pixels", "x", "y", "width", "height", "color", "alpha"], "descriptor": "([BIIIIII)V"}, {"method": "drawLines", "owner": "kt", "name": "c", "access": 1, "parameters": ["s", "x", "y", "width", "height", "color", "shadow", "xAlignment", "yAlignment", "lineHeight"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIIIIIII)I"}, {"method": "drawRandomAlphaAndSpacing", "owner": "kt", "name": "aq", "access": 1, "parameters": ["s", "x", "y", "color", "shadow", "seed"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIIII)V"}, {"method": "drawRightAligned", "owner": "kt", "name": "h", "access": 1, "parameters": ["s", "x", "y", "color", "shadow"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;IIII)V"}, {"method": "drawWithOffsets0", "owner": "kt", "name": "ay", "access": 0, "parameters": ["s", "x", "y", "xs", "ys"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;II[I[I)V"}, {"method": "lineCount", "owner": "kt", "name": "j", "access": 1, "parameters": ["s", "lineWidth"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"method": "lineWidth", "owner": "kt", "name": "z", "access": 1, "parameters": ["s", "lineWidth"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"method": "readMetrics", "owner": "kt", "name": "l", "access": 0, "parameters": ["bytes"], "descriptor": "([B)V"}, {"method": "reset", "owner": "kt", "name": "aj", "access": 0, "parameters": ["color", "shadow"], "descriptor": "(II)V"}, {"method": "stringWidth", "owner": "kt", "name": "d", "access": 1, "parameters": ["s"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;)I"}], "constructors": [{"access": 0, "descriptor": "([B[I[I[I[I[I[[B)V"}, {"access": 0, "descriptor": "([B)V"}]}, {"class": "DevicePcmPlayerProvider", "name": "az", "super": "java.lang.Object", "access": 33, "interfaces": ["cz"], "fields": [], "methods": [{"method": "player", "owner": "az", "name": "m", "access": 1, "descriptor": "(B)Lco;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ArchiveDisk", "name": "ff", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "archive", "owner": "ff", "name": "w", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "datFile", "owner": "ff", "name": "f", "access": 0, "descriptor": "Ldy;"}, {"field": "idxFile", "owner": "ff", "name": "q", "access": 0, "descriptor": "Ldy;"}, {"field": "maxEntrySize", "owner": "ff", "name": "o", "access": 0, "descriptor": "I", "decoder": -748616567}], "methods": [{"method": "read", "owner": "ff", "name": "m", "access": 1, "parameters": ["entry"], "descriptor": "(II)[B"}, {"method": "write", "owner": "ff", "name": "f", "access": 1, "descriptor": "(I[BII)Z"}, {"method": "write0", "owner": "ff", "name": "q", "access": 0, "descriptor": "(I[BIZI)Z"}], "constructors": [{"access": 1, "descriptor": "(ILdy;Ldy;I)V"}]}, {"class": "TileItem", "name": "ck", "super": "ex", "access": 49, "interfaces": [], "fields": [{"field": "id", "owner": "ck", "name": "m", "access": 0, "descriptor": "I", "decoder": 893970699}, {"field": "quantity", "owner": "ck", "name": "f", "access": 0, "descriptor": "I", "decoder": 454094667}], "methods": [{"method": "getModel", "owner": "ck", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "VarpDefinition", "name": "ix", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "type", "owner": "ix", "name": "w", "access": 1, "descriptor": "I", "decoder": -1792568421}], "methods": [{"method": "decode", "owner": "ix", "name": "f", "access": 0, "descriptor": "(Lgr;B)V"}, {"method": "decode0", "owner": "ix", "name": "q", "access": 0, "descriptor": "(Lgr;II)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Font", "name": "kk", "super": "kt", "access": 49, "interfaces": [], "fields": [], "methods": [{"method": "drawGlyph", "owner": "kk", "name": "m", "access": 16, "descriptor": "([BIIIII)V"}, {"method": "drawGlyphAlpha", "owner": "kk", "name": "f", "access": 16, "descriptor": "([BIIIIII)V"}], "constructors": [{"access": 1, "descriptor": "([B[I[I[I[I[I[[B)V"}, {"access": 1, "descriptor": "([B)V"}]}, {"class": "ChatChannel", "name": "cp", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "count", "owner": "cp", "name": "q", "access": 0, "descriptor": "I", "decoder": 612486877}, {"field": "messages", "owner": "cp", "name": "f", "access": 0, "descriptor": "[Lbm;"}], "methods": [{"method": "addMessage", "owner": "cp", "name": "m", "access": 0, "parameters": ["type", "sender", "text", "prefix"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON>ja<PERSON>/lang/String;I)Lbm;"}, {"method": "getMessage", "owner": "cp", "name": "f", "access": 0, "parameters": ["index"], "descriptor": "(II)Lbm;"}, {"method": "size", "owner": "cp", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "PlatformInfoProvider", "name": "lm", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "get", "owner": "lm", "name": "m", "access": 1025, "parameters": [], "descriptor": "(I)Llb;"}], "constructors": []}, {"class": "DynamicObject", "name": "cf", "super": "ex", "access": 33, "interfaces": [], "fields": [{"field": "cycleStart", "owner": "cf", "name": "e", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "frame", "owner": "cf", "name": "l", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "id", "owner": "cf", "name": "m", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "orientation", "owner": "cf", "name": "q", "access": 0, "descriptor": "I", "decoder": 141031459}, {"field": "plane", "owner": "cf", "name": "w", "access": 0, "descriptor": "I", "decoder": 844945979}, {"field": "sequenceDefinition", "owner": "cf", "name": "g", "access": 0, "descriptor": "Ljh;"}, {"field": "type", "owner": "cf", "name": "f", "access": 0, "descriptor": "I", "decoder": 1713856147}, {"field": "x", "owner": "cf", "name": "o", "access": 0, "descriptor": "I", "decoder": -351084819}, {"field": "y", "owner": "cf", "name": "u", "access": 0, "descriptor": "I", "decoder": -126308963}], "methods": [{"method": "getModel", "owner": "cf", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}], "constructors": [{"access": 0, "descriptor": "(IIIIIIIZLex;)V"}]}, {"class": "ClanMate", "name": "jk", "super": "ke", "access": 33, "interfaces": [], "fields": [{"field": "isFriend0", "owner": "jk", "name": "m", "access": 0, "descriptor": "Lkd;"}, {"field": "isIgnored0", "owner": "jk", "name": "f", "access": 0, "descriptor": "Lkd;"}], "methods": [{"method": "clearIsFriend", "owner": "jk", "name": "m", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "clearIsIgnored", "owner": "jk", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "fillIsFriend", "owner": "jk", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "fillIsIgnored", "owner": "jk", "name": "u", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "isFriend", "owner": "jk", "name": "f", "access": 17, "parameters": [], "descriptor": "(I)Z"}, {"method": "isIgnored", "owner": "jk", "name": "o", "access": 17, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "DesktopPlatformInfoProvider", "name": "lu", "super": "java.lang.Object", "access": 33, "interfaces": ["lm"], "fields": [], "methods": [{"method": "get", "owner": "lu", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)Llb;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "TileItemPile", "name": "dr", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "first", "owner": "dr", "name": "o", "access": 0, "descriptor": "Lex;"}, {"field": "height", "owner": "dr", "name": "l", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "second", "owner": "dr", "name": "m", "access": 0, "descriptor": "Lex;"}, {"field": "tag", "owner": "dr", "name": "g", "access": 0, "descriptor": "J", "decoder": -3696674445500472687}, {"field": "third", "owner": "dr", "name": "u", "access": 0, "descriptor": "Lex;"}, {"field": "tileHeight", "owner": "dr", "name": "f", "access": 0, "descriptor": "I", "decoder": -325682933}, {"field": "x", "owner": "dr", "name": "q", "access": 0, "descriptor": "I", "decoder": -1363136655}, {"field": "y", "owner": "dr", "name": "w", "access": 0, "descriptor": "I", "decoder": 968072997}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "DemotingHashTable", "name": "ec", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "capacity", "owner": "ec", "name": "m", "access": 0, "descriptor": "I"}, {"field": "hashTable", "owner": "ec", "name": "q", "access": 0, "descriptor": "Llh;"}, {"field": "queue", "owner": "ec", "name": "w", "access": 0, "descriptor": "Ljc;"}, {"field": "remaining", "owner": "ec", "name": "f", "access": 0, "descriptor": "I"}], "methods": [{"method": "clear", "owner": "ec", "name": "u", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "demote", "owner": "ec", "name": "o", "access": 1, "parameters": ["softeningLevel"], "descriptor": "(I)V"}, {"method": "get", "owner": "ec", "name": "m", "access": 1, "parameters": ["key"], "descriptor": "(J)<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "put", "owner": "ec", "name": "w", "access": 1, "parameters": ["value", "key", "size"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;JI)V"}, {"method": "remove", "owner": "ec", "name": "f", "access": 0, "parameters": ["key"], "descriptor": "(J)V"}, {"method": "removeWrapper", "owner": "ec", "name": "q", "access": 0, "parameters": ["wrapper"], "descriptor": "(Leu;)V"}], "constructors": [{"access": 1, "descriptor": "(II)V"}]}, {"class": "ScriptFrame", "name": "bn", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "intLocals", "owner": "bn", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "pc", "owner": "bn", "name": "f", "access": 0, "descriptor": "I", "decoder": -1715361849}, {"field": "script", "owner": "bn", "name": "m", "access": 0, "descriptor": "Lcg;"}, {"field": "stringLocals", "owner": "bn", "name": "w", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "BoundaryObject", "name": "ej", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "entity1", "owner": "ej", "name": "u", "access": 1, "descriptor": "Lex;"}, {"field": "entity2", "owner": "ej", "name": "g", "access": 1, "descriptor": "Lex;"}, {"field": "flags", "owner": "ej", "name": "e", "access": 0, "descriptor": "I", "decoder": -693868549}, {"field": "orientationA", "owner": "ej", "name": "w", "access": 0, "descriptor": "I", "decoder": 1724480965}, {"field": "orientationB", "owner": "ej", "name": "o", "access": 0, "descriptor": "I", "decoder": -969316473}, {"field": "tag", "owner": "ej", "name": "l", "access": 1, "descriptor": "J", "decoder": -4401157754236673285}, {"field": "tileHeight", "owner": "ej", "name": "m", "access": 0, "descriptor": "I", "decoder": -9307913}, {"field": "x", "owner": "ej", "name": "f", "access": 0, "descriptor": "I", "decoder": 860688417}, {"field": "y", "owner": "ej", "name": "q", "access": 0, "descriptor": "I", "decoder": -260081891}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "WallDecoration", "name": "eq", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "entity1", "owner": "eq", "name": "l", "access": 1, "descriptor": "Lex;"}, {"field": "entity2", "owner": "eq", "name": "e", "access": 1, "descriptor": "Lex;"}, {"field": "flags", "owner": "eq", "name": "d", "access": 0, "descriptor": "I", "decoder": -1164153171}, {"field": "int7", "owner": "eq", "name": "o", "access": 0, "descriptor": "I", "decoder": -303827911}, {"field": "orientation", "owner": "eq", "name": "w", "access": 0, "descriptor": "I", "decoder": -183359389}, {"field": "tag", "owner": "eq", "name": "x", "access": 1, "descriptor": "J", "decoder": 3167575053900974839}, {"field": "tileHeight", "owner": "eq", "name": "m", "access": 0, "descriptor": "I", "decoder": 1781638945}, {"field": "x", "owner": "eq", "name": "f", "access": 0, "descriptor": "I", "decoder": 1411967127}, {"field": "xOffset", "owner": "eq", "name": "u", "access": 0, "descriptor": "I", "decoder": 1927462659}, {"field": "y", "owner": "eq", "name": "q", "access": 0, "descriptor": "I", "decoder": -629971645}, {"field": "yOffset", "owner": "eq", "name": "g", "access": 0, "descriptor": "I", "decoder": -1531207607}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ArchiveDiskAction", "name": "ik", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "archive", "owner": "ik", "name": "w", "access": 0, "descriptor": "Lit;"}, {"field": "archiveDisk", "owner": "ik", "name": "q", "access": 0, "descriptor": "Lff;"}, {"field": "data", "owner": "ik", "name": "f", "access": 0, "descriptor": "[B"}, {"field": "type", "owner": "ik", "name": "m", "access": 0, "descriptor": "I", "decoder": -1942326733}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Projectile", "name": "cb", "super": "ex", "access": 49, "interfaces": [], "fields": [{"field": "accelerationZ", "owner": "cb", "name": "y", "access": 0, "descriptor": "D"}, {"field": "cycleEnd", "owner": "cb", "name": "l", "access": 0, "descriptor": "I", "decoder": 2121946763}, {"field": "cycleStart", "owner": "cb", "name": "g", "access": 0, "descriptor": "I", "decoder": 358196599}, {"field": "frame", "owner": "cb", "name": "r", "access": 0, "descriptor": "I", "decoder": -377214997}, {"field": "frameCycle", "owner": "cb", "name": "p", "access": 0, "descriptor": "I", "decoder": -742871951}, {"field": "id", "owner": "cb", "name": "m", "access": 0, "descriptor": "I", "decoder": 2111080001}, {"field": "int3", "owner": "cb", "name": "e", "access": 0, "descriptor": "I", "decoder": -769539171}, {"field": "int4", "owner": "cb", "name": "x", "access": 0, "descriptor": "I", "decoder": -2074328969}, {"field": "int5", "owner": "cb", "name": "u", "access": 0, "descriptor": "I", "decoder": -675081925}, {"field": "isMoving", "owner": "cb", "name": "k", "access": 0, "descriptor": "Z"}, {"field": "pitch", "owner": "cb", "name": "b", "access": 0, "descriptor": "I", "decoder": -2072120189}, {"field": "plane", "owner": "cb", "name": "f", "access": 0, "descriptor": "I", "decoder": -1038876505}, {"field": "sequenceDefinition", "owner": "cb", "name": "c", "access": 0, "descriptor": "Ljh;"}, {"field": "sourceX", "owner": "cb", "name": "q", "access": 0, "descriptor": "I", "decoder": 1652455041}, {"field": "sourceY", "owner": "cb", "name": "w", "access": 0, "descriptor": "I", "decoder": -1421996173}, {"field": "sourceZ", "owner": "cb", "name": "o", "access": 0, "descriptor": "I", "decoder": -4446033}, {"field": "speed", "owner": "cb", "name": "s", "access": 0, "descriptor": "D"}, {"field": "speedX", "owner": "cb", "name": "z", "access": 0, "descriptor": "D"}, {"field": "speedY", "owner": "cb", "name": "j", "access": 0, "descriptor": "D"}, {"field": "speedZ", "owner": "cb", "name": "t", "access": 0, "descriptor": "D"}, {"field": "targetIndex", "owner": "cb", "name": "d", "access": 0, "descriptor": "I", "decoder": 551413977}, {"field": "x", "owner": "cb", "name": "n", "access": 0, "descriptor": "D"}, {"field": "y", "owner": "cb", "name": "i", "access": 0, "descriptor": "D"}, {"field": "yaw", "owner": "cb", "name": "h", "access": 0, "descriptor": "I", "decoder": 1281680175}, {"field": "z", "owner": "cb", "name": "a", "access": 0, "descriptor": "D"}], "methods": [{"method": "advance", "owner": "cb", "name": "f", "access": 16, "parameters": ["cycles"], "descriptor": "(II)V"}, {"method": "getModel", "owner": "cb", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}, {"method": "setDestination", "owner": "cb", "name": "m", "access": 16, "parameters": ["x", "y", "height", "cycle"], "descriptor": "(IIIII)V"}], "constructors": [{"access": 0, "descriptor": "(IIIIIIIIIII)V"}]}, {"class": "FloorDecoration", "name": "do", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "entity", "owner": "do", "name": "o", "access": 1, "descriptor": "Lex;"}, {"field": "flags", "owner": "do", "name": "u", "access": 0, "descriptor": "I", "decoder": -1241982749}, {"field": "tag", "owner": "do", "name": "m", "access": 1, "descriptor": "J", "decoder": -5063688075016130813}, {"field": "tileHeight", "owner": "do", "name": "f", "access": 0, "descriptor": "I", "decoder": -1358154703}, {"field": "x", "owner": "do", "name": "q", "access": 0, "descriptor": "I", "decoder": 1309467119}, {"field": "y", "owner": "do", "name": "w", "access": 0, "descriptor": "I", "decoder": 318503957}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GraphicsObject", "name": "bw", "super": "ex", "access": 49, "interfaces": [], "fields": [{"field": "cycleStart", "owner": "bw", "name": "f", "access": 0, "descriptor": "I", "decoder": -1245488067}, {"field": "frame", "owner": "bw", "name": "l", "access": 0, "descriptor": "I", "decoder": 1541221595}, {"field": "frameCycle", "owner": "bw", "name": "e", "access": 0, "descriptor": "I", "decoder": -1950327625}, {"field": "height", "owner": "bw", "name": "u", "access": 0, "descriptor": "I", "decoder": 1156757009}, {"field": "id", "owner": "bw", "name": "m", "access": 0, "descriptor": "I", "decoder": -289084031}, {"field": "isFinished", "owner": "bw", "name": "x", "access": 0, "descriptor": "Z"}, {"field": "plane", "owner": "bw", "name": "q", "access": 0, "descriptor": "I", "decoder": 432847573}, {"field": "sequenceDefinition", "owner": "bw", "name": "g", "access": 0, "descriptor": "Ljh;"}, {"field": "x", "owner": "bw", "name": "w", "access": 0, "descriptor": "I", "decoder": -1189549567}, {"field": "y", "owner": "bw", "name": "o", "access": 0, "descriptor": "I", "decoder": 1416438985}], "methods": [{"method": "advance", "owner": "bw", "name": "m", "access": 16, "parameters": ["cycles"], "descriptor": "(II)V"}, {"method": "getModel", "owner": "bw", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}], "constructors": [{"access": 0, "descriptor": "(IIIIIII)V"}]}, {"class": "ViewportMouse", "name": "dm", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Tiles", "name": "bq", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "Players", "name": "cq", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [], "methods": [], "constructors": []}, {"class": "WorldComparator", "name": "u", "super": "java.lang.Object", "access": 48, "interfaces": ["java.util.Comparator"], "fields": [], "methods": [{"method": "__m_0", "owner": "u", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Ll;Ll;I)I"}, {"method": "__compare_1", "owner": "u", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_2", "owner": "u", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GrandExchangeEvent", "name": "l", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "grandExchange<PERSON>ffer", "owner": "l", "name": "q", "access": 17, "descriptor": "Lx;"}, {"field": "string1", "owner": "l", "name": "w", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "string2", "owner": "l", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "world", "owner": "l", "name": "m", "access": 17, "descriptor": "I", "decoder": -633089807}, {"field": "__f", "owner": "l", "name": "f", "access": 17, "descriptor": "J", "decoder": -6503890758391257651}], "methods": [{"method": "__m_3", "owner": "l", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "__f_4", "owner": "l", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}], "constructors": [{"access": 0, "descriptor": "(Lgr;BI)V"}]}, {"class": "TotalQuantityComparator", "name": "e", "super": "java.lang.Object", "access": 48, "interfaces": ["java.util.Comparator"], "fields": [], "methods": [{"method": "__m_5", "owner": "e", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Ll;Ll;I)I"}, {"method": "__compare_6", "owner": "e", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_7", "owner": "e", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GrandExchangeOffer", "name": "x", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "currentPrice", "owner": "x", "name": "u", "access": 1, "descriptor": "I", "decoder": 396420073}, {"field": "currentQuantity", "owner": "x", "name": "o", "access": 1, "descriptor": "I", "decoder": 1413132419}, {"field": "id", "owner": "x", "name": "f", "access": 1, "descriptor": "I", "decoder": 1136727241}, {"field": "state", "owner": "x", "name": "m", "access": 0, "descriptor": "B"}, {"field": "totalQuantity", "owner": "x", "name": "w", "access": 1, "descriptor": "I", "decoder": 562564821}, {"field": "unitPrice", "owner": "x", "name": "q", "access": 1, "descriptor": "I", "decoder": -1627453165}], "methods": [{"method": "status", "owner": "x", "name": "q", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "type", "owner": "x", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_8", "owner": "x", "name": "f", "access": 0, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Integer;I)V"}, {"method": "__o_9", "owner": "x", "name": "o", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__u_10", "owner": "x", "name": "u", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}], "constructors": [{"access": 1, "descriptor": "()V"}, {"access": 1, "descriptor": "(Lgr;Z)V"}]}, {"class": "UnitPriceComparator", "name": "d", "super": "java.lang.Object", "access": 48, "interfaces": ["java.util.Comparator"], "fields": [], "methods": [{"method": "__m_11", "owner": "d", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Ll;Ll;I)I"}, {"method": "__compare_12", "owner": "d", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_13", "owner": "d", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "WorldMapLabelSize", "name": "s", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__o", "owner": "s", "name": "o", "access": 16, "descriptor": "I", "decoder": 707828941}, {"field": "__u", "owner": "s", "name": "u", "access": 16, "descriptor": "I", "decoder": -422937451}, {"field": "__w", "owner": "s", "name": "w", "access": 16, "descriptor": "I", "decoder": 1252293513}], "methods": [{"method": "__f_14", "owner": "s", "name": "f", "access": 0, "parameters": ["arg0"], "descriptor": "(FI)Z"}], "constructors": [{"access": 0, "descriptor": "(III)V"}]}, {"class": "WorldMapIcon1", "name": "t", "super": "ak", "access": 33, "interfaces": [], "fields": [{"field": "label0", "owner": "t", "name": "f", "access": 16, "descriptor": "<PERSON><PERSON>;"}, {"field": "__m", "owner": "t", "name": "m", "access": 16, "descriptor": "I", "decoder": -1722323621}, {"field": "__q", "owner": "t", "name": "q", "access": 16, "descriptor": "I", "decoder": 364253793}, {"field": "__w", "owner": "t", "name": "w", "access": 16, "descriptor": "I", "decoder": -362031241}], "methods": [{"method": "__m_15", "owner": "t", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_16", "owner": "t", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)<PERSON>j;"}, {"method": "__q_17", "owner": "t", "name": "q", "access": 0, "parameters": [], "descriptor": "(B)I"}, {"method": "__w_18", "owner": "t", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)I"}], "constructors": [{"access": 0, "descriptor": "(<PERSON>hu;<PERSON><PERSON>;<PERSON>aj;)V"}]}, {"class": "WorldMapSection0", "name": "y", "super": "java.lang.Object", "access": 33, "interfaces": ["ab"], "fields": [{"field": "__d", "owner": "y", "name": "d", "access": 0, "descriptor": "I", "decoder": -799353317}, {"field": "__e", "owner": "y", "name": "e", "access": 0, "descriptor": "I", "decoder": -1022177315}, {"field": "__f", "owner": "y", "name": "f", "access": 0, "descriptor": "I", "decoder": 1299225715}, {"field": "__g", "owner": "y", "name": "g", "access": 0, "descriptor": "I", "decoder": -150870205}, {"field": "__i", "owner": "y", "name": "i", "access": 0, "descriptor": "I", "decoder": 1345061881}, {"field": "__k", "owner": "y", "name": "k", "access": 0, "descriptor": "I", "decoder": -1388959491}, {"field": "__l", "owner": "y", "name": "l", "access": 0, "descriptor": "I", "decoder": 1009718107}, {"field": "__m", "owner": "y", "name": "m", "access": 0, "descriptor": "I", "decoder": -2011838881}, {"field": "__n", "owner": "y", "name": "n", "access": 0, "descriptor": "I", "decoder": -1657956317}, {"field": "__o", "owner": "y", "name": "o", "access": 0, "descriptor": "I", "decoder": -671345527}, {"field": "__q", "owner": "y", "name": "q", "access": 0, "descriptor": "I", "decoder": 82600709}, {"field": "__u", "owner": "y", "name": "u", "access": 0, "descriptor": "I", "decoder": 529427495}, {"field": "__w", "owner": "y", "name": "w", "access": 0, "descriptor": "I", "decoder": -477370575}, {"field": "__x", "owner": "y", "name": "x", "access": 0, "descriptor": "I", "decoder": -1397517463}], "methods": [{"method": "__m_19", "owner": "y", "name": "m", "access": 1, "parameters": ["arg0"], "descriptor": "(Lag;I)V"}, {"method": "__f_20", "owner": "y", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)Z"}, {"method": "__q_21", "owner": "y", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)Z"}, {"method": "__w_22", "owner": "y", "name": "w", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)[I"}, {"method": "__o_23", "owner": "y", "name": "o", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)<PERSON>hu;"}, {"method": "__u_24", "owner": "y", "name": "u", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgr;B)V"}, {"method": "__g_25", "owner": "y", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "WorldMapSectionType", "name": "h", "super": "java.lang.Object", "access": 33, "interfaces": ["gc"], "fields": [{"field": "id", "owner": "h", "name": "u", "access": 16, "descriptor": "B"}, {"field": "type", "owner": "h", "name": "o", "access": 16, "descriptor": "I", "decoder": 738759853}], "methods": [{"method": "__f_26", "owner": "h", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(IB)V"}]}, {"class": "WorldMapIcon2", "name": "r", "super": "ak", "access": 33, "interfaces": [], "fields": [{"field": "label0", "owner": "r", "name": "w", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "__o", "owner": "r", "name": "o", "access": 0, "descriptor": "I", "decoder": 927871683}, {"field": "__q", "owner": "r", "name": "q", "access": 0, "descriptor": "I", "decoder": -25914375}, {"field": "__u", "owner": "r", "name": "u", "access": 0, "descriptor": "I", "decoder": -1431936619}, {"field": "__f", "owner": "r", "name": "f", "access": 16, "descriptor": "Lav;"}, {"field": "__m", "owner": "r", "name": "m", "access": 16, "descriptor": "I", "decoder": -985436813}], "methods": [{"method": "__z_27", "owner": "r", "name": "z", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__m_28", "owner": "r", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_29", "owner": "r", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)<PERSON>j;"}, {"method": "__q_30", "owner": "r", "name": "q", "access": 0, "parameters": [], "descriptor": "(B)I"}, {"method": "__w_31", "owner": "r", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)I"}], "constructors": [{"access": 0, "descriptor": "(Lhu;<PERSON>hu;ILav;)V"}]}, {"class": "WorldMapSection3", "name": "p", "super": "java.lang.Object", "access": 33, "interfaces": ["ab"], "fields": [{"field": "__e", "owner": "p", "name": "e", "access": 0, "descriptor": "I", "decoder": 395786901}, {"field": "__f", "owner": "p", "name": "f", "access": 0, "descriptor": "I", "decoder": -1622972483}, {"field": "__g", "owner": "p", "name": "g", "access": 0, "descriptor": "I", "decoder": 1016557381}, {"field": "__l", "owner": "p", "name": "l", "access": 0, "descriptor": "I", "decoder": 884736611}, {"field": "__m", "owner": "p", "name": "m", "access": 0, "descriptor": "I", "decoder": 1389733201}, {"field": "__o", "owner": "p", "name": "o", "access": 0, "descriptor": "I", "decoder": 1257151629}, {"field": "__q", "owner": "p", "name": "q", "access": 0, "descriptor": "I", "decoder": 702057599}, {"field": "__u", "owner": "p", "name": "u", "access": 0, "descriptor": "I", "decoder": -1351623663}, {"field": "__w", "owner": "p", "name": "w", "access": 0, "descriptor": "I", "decoder": -1068088609}, {"field": "__x", "owner": "p", "name": "x", "access": 0, "descriptor": "I", "decoder": -627081155}], "methods": [{"method": "__m_32", "owner": "p", "name": "m", "access": 1, "parameters": ["arg0"], "descriptor": "(Lag;I)V"}, {"method": "__f_33", "owner": "p", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)Z"}, {"method": "__q_34", "owner": "p", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)Z"}, {"method": "__w_35", "owner": "p", "name": "w", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)[I"}, {"method": "__o_36", "owner": "p", "name": "o", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)<PERSON>hu;"}, {"method": "__u_37", "owner": "p", "name": "u", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgr;B)V"}, {"method": "__g_38", "owner": "p", "name": "g", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "WorldMapArea", "name": "ag", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "archiveName0", "owner": "ag", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "id0", "owner": "ag", "name": "m", "access": 0, "descriptor": "I", "decoder": 1232579503}, {"field": "isMain0", "owner": "ag", "name": "d", "access": 0, "descriptor": "Z"}, {"field": "maxX0", "owner": "ag", "name": "l", "access": 0, "descriptor": "I", "decoder": 225384859}, {"field": "maxY0", "owner": "ag", "name": "x", "access": 0, "descriptor": "I", "decoder": 1025105175}, {"field": "minX0", "owner": "ag", "name": "g", "access": 0, "descriptor": "I", "decoder": 1238532539}, {"field": "minY0", "owner": "ag", "name": "e", "access": 0, "descriptor": "I", "decoder": 963380367}, {"field": "name0", "owner": "ag", "name": "q", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "origin0", "owner": "ag", "name": "u", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "sections", "owner": "ag", "name": "k", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/LinkedList;"}, {"field": "zoom0", "owner": "ag", "name": "o", "access": 0, "descriptor": "I", "decoder": 196514055}, {"field": "__w", "owner": "ag", "name": "w", "access": 0, "descriptor": "I", "decoder": 986239133}], "methods": [{"method": "archiveName", "owner": "ag", "name": "x", "access": 1, "parameters": [], "descriptor": "(B)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "containsCoord", "owner": "ag", "name": "q", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(IIII)Z"}, {"method": "containsPosition", "owner": "ag", "name": "w", "access": 1, "parameters": ["x", "y"], "descriptor": "(III)Z"}, {"method": "coord", "owner": "ag", "name": "u", "access": 1, "parameters": ["x", "y"], "descriptor": "(III)<PERSON>hu;"}, {"method": "id", "owner": "ag", "name": "l", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "is<PERSON><PERSON>", "owner": "ag", "name": "e", "access": 1, "parameters": [], "descriptor": "(B)Z"}, {"method": "maxX", "owner": "ag", "name": "s", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "maxY", "owner": "ag", "name": "y", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "minX", "owner": "ag", "name": "j", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "minY", "owner": "ag", "name": "t", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "name", "owner": "ag", "name": "d", "access": 1, "parameters": [], "descriptor": "(B)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "origin", "owner": "ag", "name": "p", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON>;"}, {"method": "originPlane", "owner": "ag", "name": "b", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "originX", "owner": "ag", "name": "h", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "originY", "owner": "ag", "name": "c", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "position", "owner": "ag", "name": "o", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(IIII)[I"}, {"method": "read", "owner": "ag", "name": "m", "access": 1, "descriptor": "(Lgr;II)V"}, {"method": "readWorldMapSection", "owner": "ag", "name": "f", "access": 0, "descriptor": "(Lgr;S)Lab;"}, {"method": "setBounds", "owner": "ag", "name": "g", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "zoom", "owner": "ag", "name": "z", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__a_39", "owner": "ag", "name": "a", "access": 0, "parameters": [], "descriptor": "(I)I"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "WorldMapRegion", "name": "av", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "fonts", "owner": "av", "name": "i", "access": 16, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "iconsList", "owner": "av", "name": "k", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/List;"}, {"field": "iconsMap", "owner": "av", "name": "n", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "x", "owner": "av", "name": "u", "access": 0, "descriptor": "I", "decoder": -1061363609}, {"field": "y", "owner": "av", "name": "g", "access": 0, "descriptor": "I", "decoder": 2099407363}, {"field": "__d", "owner": "av", "name": "d", "access": 0, "descriptor": "I", "decoder": 415972873}, {"field": "__x", "owner": "av", "name": "x", "access": 0, "descriptor": "I", "decoder": -120515153}, {"field": "__e", "owner": "av", "name": "e", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/LinkedList;"}], "methods": [{"method": "icons", "owner": "av", "name": "at", "access": 0, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/util/List;"}, {"method": "__w_40", "owner": "av", "name": "w", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)V"}, {"method": "__u_41", "owner": "av", "name": "u", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/util/HashSet;<PERSON>ja<PERSON>/util/List;B)V"}, {"method": "__e_42", "owner": "av", "name": "e", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "__x_43", "owner": "av", "name": "x", "access": 0, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/util/List;I)V"}, {"method": "__d_44", "owner": "av", "name": "d", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__a_45", "owner": "av", "name": "a", "access": 0, "parameters": ["arg0"], "descriptor": "(Lir;B)Z"}, {"method": "__j_46", "owner": "av", "name": "j", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIILjava/util/HashSet;I)V"}, {"method": "__s_47", "owner": "av", "name": "s", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(<PERSON><PERSON><PERSON>/util/HashSet;IIB)V"}, {"method": "__aq_48", "owner": "av", "name": "aq", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IILjava/util/HashSet;IB)V"}, {"method": "__aj_49", "owner": "av", "name": "aj", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(<PERSON><PERSON><PERSON>/util/HashSet;III)V"}, {"method": "__av_50", "owner": "av", "name": "av", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(Lic;IIIIB)V"}, {"method": "__ar_51", "owner": "av", "name": "ar", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(Lak;IIFI)V"}, {"method": "__ac_52", "owner": "av", "name": "ac", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(Lic;III)V"}, {"method": "__ay_53", "owner": "av", "name": "ay", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(Lak;Lic;IIFI)V"}, {"method": "__ah_54", "owner": "av", "name": "ah", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IILjava/util/HashSet;IB)V"}, {"method": "__al_55", "owner": "av", "name": "al", "access": 0, "parameters": ["arg0"], "descriptor": "(II)Laj;"}, {"method": "__ab_56", "owner": "av", "name": "ab", "access": 0, "parameters": ["arg0"], "descriptor": "(Lic;B)Laj;"}, {"method": "__ae_57", "owner": "av", "name": "ae", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIIII)Ljava/util/List;"}, {"method": "__ad_58", "owner": "av", "name": "ad", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIIII)V"}], "constructors": [{"access": 0, "descriptor": "(III<PERSON><PERSON><PERSON>/util/HashMap;)V"}]}, {"class": "WorldMapSection2", "name": "ah", "super": "java.lang.Object", "access": 33, "interfaces": ["ab"], "fields": [{"field": "__f", "owner": "ah", "name": "f", "access": 0, "descriptor": "I", "decoder": -245382089}, {"field": "__m", "owner": "ah", "name": "m", "access": 0, "descriptor": "I", "decoder": -1561552223}, {"field": "__o", "owner": "ah", "name": "o", "access": 0, "descriptor": "I", "decoder": 982234927}, {"field": "__q", "owner": "ah", "name": "q", "access": 0, "descriptor": "I", "decoder": -1024859679}, {"field": "__u", "owner": "ah", "name": "u", "access": 0, "descriptor": "I", "decoder": -1272330125}, {"field": "__w", "owner": "ah", "name": "w", "access": 0, "descriptor": "I", "decoder": 40495485}], "methods": [{"method": "__m_59", "owner": "ah", "name": "m", "access": 1, "parameters": ["arg0"], "descriptor": "(Lag;I)V"}, {"method": "__f_60", "owner": "ah", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)Z"}, {"method": "__q_61", "owner": "ah", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)Z"}, {"method": "__w_62", "owner": "ah", "name": "w", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)[I"}, {"method": "__o_63", "owner": "ah", "name": "o", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)<PERSON>hu;"}, {"method": "__u_64", "owner": "ah", "name": "u", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgr;B)V"}, {"method": "__g_65", "owner": "ah", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "AbstractWorldMapIcon", "name": "ak", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [{"field": "coord1", "owner": "ak", "name": "l", "access": 17, "descriptor": "<PERSON><PERSON>;"}, {"field": "coord2", "owner": "ak", "name": "g", "access": 17, "descriptor": "<PERSON><PERSON>;"}, {"field": "__e", "owner": "ak", "name": "e", "access": 0, "descriptor": "I", "decoder": -521086143}, {"field": "__x", "owner": "ak", "name": "x", "access": 0, "descriptor": "I", "decoder": -1065362217}], "methods": [{"method": "element", "owner": "ak", "name": "m", "access": 1025, "parameters": [], "descriptor": "(I)I"}, {"method": "label", "owner": "ak", "name": "f", "access": 1024, "parameters": [], "descriptor": "(I)<PERSON>j;"}, {"method": "__y_66", "owner": "ak", "name": "y", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)Z"}, {"method": "__h_67", "owner": "ak", "name": "h", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "__b_68", "owner": "ak", "name": "b", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)Z"}, {"method": "__c_69", "owner": "ak", "name": "c", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)Z"}, {"method": "__w_70", "owner": "ak", "name": "w", "access": 1024, "parameters": [], "descriptor": "(I)I"}, {"method": "__q_71", "owner": "ak", "name": "q", "access": 1024, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(<PERSON>hu;<PERSON>hu;)V"}]}, {"class": "WorldMapManager", "name": "al", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "fonts", "owner": "al", "name": "d", "access": 16, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "icons", "owner": "al", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "isLoaded0", "owner": "al", "name": "m", "access": 0, "descriptor": "Z"}, {"field": "loadStarted", "owner": "al", "name": "f", "access": 0, "descriptor": "Z"}, {"field": "mapAreaData", "owner": "al", "name": "q", "access": 0, "descriptor": "Lae;"}, {"field": "mapSceneSprites", "owner": "al", "name": "l", "access": 0, "descriptor": "[Llq;"}, {"field": "overviewSprite", "owner": "al", "name": "w", "access": 0, "descriptor": "Lln;"}, {"field": "regions", "owner": "al", "name": "u", "access": 0, "descriptor": "[[Lav;"}, {"field": "__a", "owner": "al", "name": "a", "access": 0, "descriptor": "I", "decoder": -395718741}, {"field": "__i", "owner": "al", "name": "i", "access": 0, "descriptor": "I", "decoder": 1729323633}, {"field": "__k", "owner": "al", "name": "k", "access": 0, "descriptor": "I", "decoder": -1459928419}, {"field": "__n", "owner": "al", "name": "n", "access": 0, "descriptor": "I", "decoder": 1364441271}, {"field": "__g", "owner": "al", "name": "g", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "__e", "owner": "al", "name": "e", "access": 16, "descriptor": "Lir;"}, {"field": "__x", "owner": "al", "name": "x", "access": 16, "descriptor": "Lir;"}, {"field": "__z", "owner": "al", "name": "z", "access": 1, "descriptor": "I", "decoder": 1085359673}], "methods": [{"method": "buildIcons", "owner": "al", "name": "e", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/util/HashMap;"}, {"method": "buildIcons0", "owner": "al", "name": "x", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "clearIcons", "owner": "al", "name": "f", "access": 17, "parameters": [], "descriptor": "(B)V"}, {"method": "drawOverview", "owner": "al", "name": "o", "access": 1, "descriptor": "(IIIILjava/util/HashSet;III)V"}, {"method": "isLoaded", "owner": "al", "name": "l", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "load", "owner": "al", "name": "m", "access": 1, "parameters": ["indexCache", "cacheName", "isMembersWorld"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON><PERSON>/lang/String;ZB)V"}, {"method": "__q_72", "owner": "al", "name": "q", "access": 17, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7"], "descriptor": "(IIIIIIIII)V"}, {"method": "__w_73", "owner": "al", "name": "w", "access": 17, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9", "arg10", "arg11", "arg12"], "descriptor": "(IIIIIIIILjava/util/HashSet;Ljava/util/HashSet;IIZB)V"}, {"method": "__u_74", "owner": "al", "name": "u", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9"], "descriptor": "(IIIIIIIIIII)Ljava/util/List;"}, {"method": "__d_75", "owner": "al", "name": "d", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)F"}], "constructors": [{"access": 1, "descriptor": "([Llq;<PERSON>ja<PERSON>/util/<PERSON>h<PERSON>;Lir;Lir;)V"}]}, {"class": "WorldMapAreaData", "name": "ae", "super": "ag", "access": 33, "interfaces": [], "fields": [{"field": "__i", "owner": "ae", "name": "i", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "__n", "owner": "ae", "name": "n", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "__a", "owner": "ae", "name": "a", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/List;"}], "methods": [{"method": "__ce_76", "owner": "ae", "name": "ce", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(Lgr;Lgr;IZI)V"}, {"method": "__cy_77", "owner": "ae", "name": "cy", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lgr;ZB)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "WorldMapSection1", "name": "as", "super": "java.lang.Object", "access": 33, "interfaces": ["ab"], "fields": [{"field": "__e", "owner": "as", "name": "e", "access": 0, "descriptor": "I", "decoder": -1797724181}, {"field": "__f", "owner": "as", "name": "f", "access": 0, "descriptor": "I", "decoder": 1483447179}, {"field": "__g", "owner": "as", "name": "g", "access": 0, "descriptor": "I", "decoder": 1526007343}, {"field": "__l", "owner": "as", "name": "l", "access": 0, "descriptor": "I", "decoder": -890169045}, {"field": "__m", "owner": "as", "name": "m", "access": 0, "descriptor": "I", "decoder": -1971304641}, {"field": "__o", "owner": "as", "name": "o", "access": 0, "descriptor": "I", "decoder": -1513010233}, {"field": "__q", "owner": "as", "name": "q", "access": 0, "descriptor": "I", "decoder": -1875803617}, {"field": "__u", "owner": "as", "name": "u", "access": 0, "descriptor": "I", "decoder": -1383826035}, {"field": "__w", "owner": "as", "name": "w", "access": 0, "descriptor": "I", "decoder": 284566685}, {"field": "__x", "owner": "as", "name": "x", "access": 0, "descriptor": "I", "decoder": 325777555}], "methods": [{"method": "__m_78", "owner": "as", "name": "m", "access": 1, "parameters": ["arg0"], "descriptor": "(Lag;I)V"}, {"method": "__f_79", "owner": "as", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)Z"}, {"method": "__q_80", "owner": "as", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)Z"}, {"method": "__w_81", "owner": "as", "name": "w", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)[I"}, {"method": "__o_82", "owner": "as", "name": "o", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)<PERSON>hu;"}, {"method": "__u_83", "owner": "as", "name": "u", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgr;B)V"}, {"method": "__g_84", "owner": "as", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "am", "super": "java.lang.Object", "access": 49, "interfaces": ["fv", "java.awt.event.MouseWheelListener"], "fields": [{"field": "rotation", "owner": "am", "name": "m", "access": 0, "descriptor": "I", "decoder": -182252389}], "methods": [{"method": "addTo", "owner": "am", "name": "m", "access": 0, "parameters": ["component"], "descriptor": "(<PERSON><PERSON><PERSON>/awt/Component;B)V"}, {"method": "removeFrom", "owner": "am", "name": "f", "access": 0, "parameters": ["component"], "descriptor": "(<PERSON><PERSON><PERSON>/awt/Component;I)V"}, {"method": "useRotation", "owner": "am", "name": "q", "access": 33, "parameters": [], "descriptor": "(I)I"}, {"method": "__mouseWheelMoved_85", "owner": "am", "name": "mouseWheelMoved", "access": 33, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseWheelEvent;)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON>", "name": "an", "super": "java.lang.Object", "access": 49, "interfaces": ["java.awt.event.KeyListener", "java.awt.event.FocusListener"], "fields": [], "methods": [{"method": "keyPressed", "owner": "an", "name": "keyPressed", "access": 49, "parameters": ["ke"], "descriptor": "(Ljava/awt/event/KeyEvent;)V"}, {"method": "__keyReleased_86", "owner": "an", "name": "keyReleased", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/KeyEvent;)V"}, {"method": "__keyTyped_87", "owner": "an", "name": "keyTyped", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/KeyEvent;)V"}, {"method": "__focusGained_88", "owner": "an", "name": "focusGained", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/FocusEvent;)V"}, {"method": "__focusLost_89", "owner": "an", "name": "focusLost", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/FocusEvent;)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GameShell", "name": "ba", "super": "java.applet.Applet", "access": 1057, "interfaces": ["java.lang.Runnable", "java.awt.event.FocusListener", "java.awt.event.WindowListener"], "fields": [{"field": "canvas", "owner": "ba", "name": "ay", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/awt/<PERSON>;"}, {"field": "canvasSetTimeMs", "owner": "ba", "name": "ae", "access": 64, "descriptor": "J", "decoder": 956221087975405011}, {"field": "canvasX", "owner": "ba", "name": "b", "access": 0, "descriptor": "I", "decoder": 781373219}, {"field": "canvasY", "owner": "ba", "name": "c", "access": 0, "descriptor": "I", "decoder": 198681537}, {"field": "clipboard", "owner": "ba", "name": "as", "access": 0, "descriptor": "Ljava/awt/datatransfer/Clipboard;"}, {"field": "contentHeight", "owner": "ba", "name": "h", "access": 4, "descriptor": "I", "decoder": -628781469}, {"field": "contentHeight0", "owner": "ba", "name": "ag", "access": 0, "descriptor": "I", "decoder": -2057041525}, {"field": "contentWidth", "owner": "ba", "name": "t", "access": 4, "descriptor": "I", "decoder": -1854329683}, {"field": "contentWidth0", "owner": "ba", "name": "v", "access": 0, "descriptor": "I", "decoder": 740831949}, {"field": "eventQueue", "owner": "ba", "name": "am", "access": 16, "descriptor": "L<PERSON>va/awt/EventQueue;"}, {"field": "frame", "owner": "ba", "name": "ac", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/awt/Frame;"}, {"field": "hasErrored", "owner": "ba", "name": "g", "access": 0, "descriptor": "Z"}, {"field": "isCanvasInvalid", "owner": "ba", "name": "ab", "access": 64, "descriptor": "Z"}, {"field": "mouseWheelHandler", "owner": "ba", "name": "ap", "access": 0, "descriptor": "Lam;"}, {"field": "stopTimeMs", "owner": "ba", "name": "o", "access": 8, "descriptor": "J", "decoder": 5666519919949401915}, {"field": "__al", "owner": "ba", "name": "al", "access": 0, "descriptor": "Z"}, {"field": "__aj", "owner": "ba", "name": "aj", "access": 0, "descriptor": "I", "decoder": 1911308233}, {"field": "__aq", "owner": "ba", "name": "aq", "access": 0, "descriptor": "I", "decoder": -2071955075}, {"field": "__ah", "owner": "ba", "name": "ah", "access": 64, "descriptor": "Z"}], "methods": [{"method": "addCanvas", "owner": "ba", "name": "aq", "access": 48, "parameters": [], "descriptor": "(B)V"}, {"method": "checkHost", "owner": "ba", "name": "aj", "access": 20, "parameters": [], "descriptor": "(B)Z"}, {"method": "clearBackground", "owner": "ba", "name": "p", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "clipboardSetString", "owner": "ba", "name": "x", "access": 4, "parameters": ["s"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "container", "owner": "ba", "name": "as", "access": 0, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/awt/Container;"}, {"method": "drawInitial", "owner": "ba", "name": "at", "access": 20, "descriptor": "(ILjava/lang/String;ZB)V"}, {"method": "error", "owner": "ba", "name": "ap", "access": 4, "parameters": ["type"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;B)V"}, {"method": "focusGained", "owner": "ba", "name": "focusGained", "access": 17, "descriptor": "(Ljava/awt/event/FocusEvent;)V"}, {"method": "getFrameContentBounds", "owner": "ba", "name": "am", "access": 0, "parameters": [], "descriptor": "(I)Lla;"}, {"method": "<PERSON><PERSON><PERSON><PERSON>", "owner": "ba", "name": "an", "access": 20, "parameters": [], "descriptor": "(B)Z"}, {"method": "kill", "owner": "ba", "name": "ah", "access": 48, "parameters": [], "descriptor": "(I)V"}, {"method": "kill0", "owner": "ba", "name": "ae", "access": 1028, "parameters": [], "descriptor": "(I)V"}, {"method": "mouseWheel", "owner": "ba", "name": "l", "access": 4, "parameters": [], "descriptor": "(B)Lfv;"}, {"method": "paint", "owner": "ba", "name": "paint", "access": 49, "parameters": ["g"], "descriptor": "(Ljava/awt/Graphics;)V"}, {"method": "replace<PERSON><PERSON><PERSON>", "owner": "ba", "name": "v", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "run", "owner": "ba", "name": "run", "access": 1, "descriptor": "()V"}, {"method": "setUp", "owner": "ba", "name": "aw", "access": 1028, "parameters": [], "descriptor": "(I)V"}, {"method": "setUpClipboard", "owner": "ba", "name": "e", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "setUpKeyboard", "owner": "ba", "name": "d", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "setUpMouse", "owner": "ba", "name": "a", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "startThread", "owner": "ba", "name": "ag", "access": 20, "parameters": ["width", "height", "revision"], "descriptor": "(IIII)V"}, {"method": "update", "owner": "ba", "name": "update", "access": 17, "parameters": ["g"], "descriptor": "(Ljava/awt/Graphics;)V"}, {"method": "__u_90", "owner": "ba", "name": "u", "access": 20, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)V"}, {"method": "__g_91", "owner": "ba", "name": "g", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"method": "__z_92", "owner": "ba", "name": "z", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__av_93", "owner": "ba", "name": "av", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "__ar_94", "owner": "ba", "name": "ar", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "__ac_95", "owner": "ba", "name": "ac", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__ay_96", "owner": "ba", "name": "ay", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__start_97", "owner": "ba", "name": "start", "access": 17, "parameters": [], "descriptor": "()V"}, {"method": "__stop_98", "owner": "ba", "name": "stop", "access": 17, "parameters": [], "descriptor": "()V"}, {"method": "__destroy_99", "owner": "ba", "name": "destroy", "access": 17, "parameters": [], "descriptor": "()V"}, {"method": "__focusLost_100", "owner": "ba", "name": "focusLost", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/FocusEvent;)V"}, {"method": "__windowActivated_101", "owner": "ba", "name": "windowActivated", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowClosed_102", "owner": "ba", "name": "windowClosed", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowClosing_103", "owner": "ba", "name": "windowClosing", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowDeactivated_104", "owner": "ba", "name": "windowDeactivated", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowDeiconified_105", "owner": "ba", "name": "windowDeiconified", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowIconified_106", "owner": "ba", "name": "windowIconified", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__windowOpened_107", "owner": "ba", "name": "windowOpened", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/WindowEvent;)V"}, {"method": "__ad_108", "owner": "ba", "name": "ad", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "__init_109", "owner": "ba", "name": "init", "access": 1025, "parameters": [], "descriptor": "()V"}, {"method": "__au_110", "owner": "ba", "name": "au", "access": 1028, "parameters": [], "descriptor": "(I)V"}, {"method": "__al_111", "owner": "ba", "name": "al", "access": 1028, "parameters": [], "descriptor": "(I)V"}, {"method": "__ab_112", "owner": "ba", "name": "ab", "access": 1028, "parameters": ["arg0"], "descriptor": "(ZI)V"}, {"method": "__j_113", "owner": "ba", "name": "j", "access": 1028, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON>", "name": "bb", "super": "java.lang.Object", "access": 33, "interfaces": ["java.awt.event.MouseListener", "java.awt.event.MouseMotionListener", "java.awt.event.FocusListener"], "fields": [], "methods": [{"method": "getButton", "owner": "bb", "name": "f", "access": 16, "parameters": ["mouseEvent"], "descriptor": "(Ljava/awt/event/MouseEvent;I)I"}, {"method": "mouseMoved", "owner": "bb", "name": "mouseMoved", "access": 49, "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "mousePressed", "owner": "bb", "name": "mousePressed", "access": 49, "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__mouseReleased_114", "owner": "bb", "name": "mouseReleased", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__mouseClicked_115", "owner": "bb", "name": "mouseClicked", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__mouseEntered_116", "owner": "bb", "name": "mouseEntered", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__mouseExited_117", "owner": "bb", "name": "mouseExited", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__mouseDragged_118", "owner": "bb", "name": "mouseDragged", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/MouseEvent;)V"}, {"method": "__focusGained_119", "owner": "bb", "name": "focusGained", "access": 17, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/FocusEvent;)V"}, {"method": "__focusLost_120", "owner": "bb", "name": "focusLost", "access": 49, "parameters": ["arg0"], "descriptor": "(Ljava/awt/event/FocusEvent;)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "SecureRandomCallable", "name": "bk", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.concurrent.Callable"], "fields": [], "methods": [{"method": "__call_121", "owner": "bk", "name": "call", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ArchiveLoader", "name": "bd", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "archive", "owner": "bd", "name": "f", "access": 16, "descriptor": "Lit;"}, {"field": "__w", "owner": "bd", "name": "w", "access": 0, "descriptor": "I", "decoder": 782003089}, {"field": "__q", "owner": "bd", "name": "q", "access": 16, "descriptor": "I", "decoder": 1459455501}], "methods": [{"method": "__m_122", "owner": "bd", "name": "m", "access": 0, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "(Lit;<PERSON><PERSON><PERSON>/lang/String;)V"}]}, {"class": "ScriptEvent", "name": "by", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "args0", "owner": "by", "name": "m", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "boolean1", "owner": "by", "name": "f", "access": 0, "descriptor": "Z"}, {"field": "component", "owner": "by", "name": "q", "access": 0, "descriptor": "Lho;"}, {"field": "dragTarget", "owner": "by", "name": "g", "access": 0, "descriptor": "Lho;"}, {"field": "keyPressed", "owner": "by", "name": "l", "access": 0, "descriptor": "I", "decoder": -1330602393}, {"field": "keyTyped", "owner": "by", "name": "e", "access": 0, "descriptor": "I", "decoder": 327085889}, {"field": "mouseX", "owner": "by", "name": "w", "access": 0, "descriptor": "I", "decoder": 488198377}, {"field": "mouseY", "owner": "by", "name": "o", "access": 0, "descriptor": "I", "decoder": 95719815}, {"field": "opIndex", "owner": "by", "name": "u", "access": 0, "descriptor": "I", "decoder": -1089993721}, {"field": "targetName", "owner": "by", "name": "x", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "type0", "owner": "by", "name": "k", "access": 0, "descriptor": "I", "decoder": -161292529}, {"field": "__d", "owner": "by", "name": "d", "access": 0, "descriptor": "I", "decoder": -2138736593}], "methods": [{"method": "set<PERSON>rgs", "owner": "by", "name": "m", "access": 1, "parameters": ["args"], "descriptor": "([<PERSON><PERSON><PERSON>/lang/Object;I)V"}, {"method": "setType", "owner": "by", "name": "f", "access": 1, "parameters": ["type"], "descriptor": "(IB)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "MouseRecorder", "name": "bu", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "index", "owner": "bu", "name": "q", "access": 0, "descriptor": "I", "decoder": 973045825}, {"field": "isRunning", "owner": "bu", "name": "m", "access": 0, "descriptor": "Z"}, {"field": "lock", "owner": "bu", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "millis", "owner": "bu", "name": "u", "access": 0, "descriptor": "[J"}, {"field": "xs", "owner": "bu", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "ys", "owner": "bu", "name": "o", "access": 0, "descriptor": "[I"}], "methods": [{"method": "__run_123", "owner": "bu", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Player", "name": "br", "super": "bz", "access": 49, "interfaces": [], "fields": [{"field": "actions", "owner": "br", "name": "u", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "animationCycleEnd", "owner": "br", "name": "d", "access": 0, "descriptor": "I", "decoder": -359981047}, {"field": "animationCycleStart", "owner": "br", "name": "x", "access": 0, "descriptor": "I", "decoder": -351876137}, {"field": "appearance", "owner": "br", "name": "f", "access": 0, "descriptor": "Lhi;"}, {"field": "combatLevel", "owner": "br", "name": "g", "access": 0, "descriptor": "I", "decoder": 984869133}, {"field": "headIconPk", "owner": "br", "name": "q", "access": 0, "descriptor": "I", "decoder": 2021372599}, {"field": "head<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "br", "name": "w", "access": 0, "descriptor": "I", "decoder": -793415473}, {"field": "index", "owner": "br", "name": "r", "access": 0, "descriptor": "I", "decoder": 1486240677}, {"field": "isFriend", "owner": "br", "name": "p", "access": 0, "descriptor": "Lkd;"}, {"field": "isHidden", "owner": "br", "name": "b", "access": 0, "descriptor": "Z"}, {"field": "isInClanChat", "owner": "br", "name": "v", "access": 0, "descriptor": "Lkd;"}, {"field": "isUnanimated", "owner": "br", "name": "y", "access": 0, "descriptor": "Z"}, {"field": "model0", "owner": "br", "name": "a", "access": 0, "descriptor": "Ldu;"}, {"field": "plane", "owner": "br", "name": "c", "access": 0, "descriptor": "I", "decoder": 1332604413}, {"field": "skillLevel", "owner": "br", "name": "l", "access": 0, "descriptor": "I", "decoder": -1136542281}, {"field": "team", "owner": "br", "name": "h", "access": 0, "descriptor": "I", "decoder": 91041167}, {"field": "tileHeight", "owner": "br", "name": "e", "access": 0, "descriptor": "I", "decoder": 1989338999}, {"field": "tileHeight2", "owner": "br", "name": "n", "access": 0, "descriptor": "I", "decoder": -1624108131}, {"field": "tileX", "owner": "br", "name": "aq", "access": 0, "descriptor": "I", "decoder": -539678769}, {"field": "tileY", "owner": "br", "name": "aj", "access": 0, "descriptor": "I", "decoder": 702193221}, {"field": "username", "owner": "br", "name": "m", "access": 0, "descriptor": "Lkp;"}, {"field": "__ag", "owner": "br", "name": "ag", "access": 0, "descriptor": "Z"}, {"field": "__i", "owner": "br", "name": "i", "access": 0, "descriptor": "I", "decoder": -1771431457}, {"field": "__j", "owner": "br", "name": "j", "access": 0, "descriptor": "I", "decoder": 224552403}, {"field": "__k", "owner": "br", "name": "k", "access": 0, "descriptor": "I", "decoder": 665272587}, {"field": "__s", "owner": "br", "name": "s", "access": 0, "descriptor": "I", "decoder": 305969541}, {"field": "__t", "owner": "br", "name": "t", "access": 0, "descriptor": "I", "decoder": 892671055}, {"field": "__z", "owner": "br", "name": "z", "access": 0, "descriptor": "I", "decoder": 1610462047}], "methods": [{"method": "getModel", "owner": "br", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}, {"method": "isVisible", "owner": "br", "name": "z", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "read", "owner": "br", "name": "m", "access": 16, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "resetPath", "owner": "br", "name": "d", "access": 0, "parameters": ["x", "y"], "descriptor": "(III)V"}, {"method": "transformedSize", "owner": "br", "name": "l", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_124", "owner": "br", "name": "f", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "__q_125", "owner": "br", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__w_126", "owner": "br", "name": "w", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__o_127", "owner": "br", "name": "o", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "__u_128", "owner": "br", "name": "u", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__g_129", "owner": "br", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__x_130", "owner": "br", "name": "x", "access": 16, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIBI)V"}, {"method": "__a_131", "owner": "br", "name": "a", "access": 16, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIBI)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "OwnWorldComparator", "name": "bj", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Comparator"], "fields": [{"field": "__m", "owner": "bj", "name": "m", "access": 0, "descriptor": "Z"}], "methods": [{"method": "__m_132", "owner": "bj", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Ll;Ll;B)I"}, {"method": "__compare_133", "owner": "bj", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_134", "owner": "bj", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Actor", "name": "bz", "super": "ex", "access": 1057, "interfaces": [], "fields": [{"field": "defaultHeight", "owner": "bz", "name": "cy", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "false0", "owner": "bz", "name": "bd", "access": 0, "descriptor": "Z"}, {"field": "healthBars", "owner": "bz", "name": "bn", "access": 0, "descriptor": "Lja;"}, {"field": "heightOffset", "owner": "bz", "name": "bp", "access": 0, "descriptor": "I", "decoder": -779457309}, {"field": "hitmarkCount", "owner": "bz", "name": "af", "access": 0, "descriptor": "B"}, {"field": "hitmarkCycles", "owner": "bz", "name": "bb", "access": 0, "descriptor": "[I"}, {"field": "hitmarkTypes", "owner": "bz", "name": "ai", "access": 0, "descriptor": "[I"}, {"field": "hitmarkTypes2", "owner": "bz", "name": "bs", "access": 0, "descriptor": "[I"}, {"field": "hitmarkValues", "owner": "bz", "name": "ba", "access": 0, "descriptor": "[I"}, {"field": "hitmarkValues2", "owner": "bz", "name": "bq", "access": 0, "descriptor": "[I"}, {"field": "isAutoChatting", "owner": "bz", "name": "an", "access": 0, "descriptor": "Z"}, {"field": "movementFrame", "owner": "bz", "name": "bx", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "movementFrameCycle", "owner": "bz", "name": "by", "access": 0, "descriptor": "I", "decoder": -1646413227}, {"field": "movementSequence", "owner": "bz", "name": "bo", "access": 0, "descriptor": "I", "decoder": 1614854211}, {"field": "npcCycle", "owner": "bz", "name": "ce", "access": 0, "descriptor": "I", "decoder": 2051334073}, {"field": "orientation", "owner": "bz", "name": "cs", "access": 0, "descriptor": "I", "decoder": -1189634367}, {"field": "overheadText", "owner": "bz", "name": "am", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "overheadTextColor", "owner": "bz", "name": "ao", "access": 0, "descriptor": "I", "decoder": -1905191799}, {"field": "overheadTextCyclesRemaining", "owner": "bz", "name": "au", "access": 0, "descriptor": "I", "decoder": 137508815}, {"field": "overheadTextEffect", "owner": "bz", "name": "aa", "access": 0, "descriptor": "I", "decoder": 541000549}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "bz", "name": "cu", "access": 0, "descriptor": "I", "decoder": -2033082375}, {"field": "pathTraversed", "owner": "bz", "name": "cn", "access": 0, "descriptor": "[B"}, {"field": "pathX", "owner": "bz", "name": "cb", "access": 0, "descriptor": "[I"}, {"field": "pathY", "owner": "bz", "name": "ck", "access": 0, "descriptor": "[I"}, {"field": "playerCycle", "owner": "bz", "name": "ak", "access": 0, "descriptor": "I", "decoder": 141226463}, {"field": "readySequence", "owner": "bz", "name": "aw", "access": 0, "descriptor": "I", "decoder": -299122235}, {"field": "runSequence", "owner": "bz", "name": "as", "access": 0, "descriptor": "I", "decoder": 1708570917}, {"field": "sequence", "owner": "bz", "name": "bu", "access": 0, "descriptor": "I", "decoder": 323216015}, {"field": "sequence<PERSON><PERSON>y", "owner": "bz", "name": "br", "access": 0, "descriptor": "I", "decoder": -134491649}, {"field": "sequenceFrame", "owner": "bz", "name": "bm", "access": 0, "descriptor": "I", "decoder": 1425662251}, {"field": "sequenceFrameCycle", "owner": "bz", "name": "bl", "access": 0, "descriptor": "I", "decoder": -1753867715}, {"field": "size", "owner": "bz", "name": "ah", "access": 0, "descriptor": "I", "decoder": -1357622739}, {"field": "spotAnimation", "owner": "bz", "name": "bi", "access": 0, "descriptor": "I", "decoder": -220610999}, {"field": "spotAnimationFrame", "owner": "bz", "name": "bz", "access": 0, "descriptor": "I", "decoder": -2125404733}, {"field": "spotAnimationFrameCycle", "owner": "bz", "name": "bg", "access": 0, "descriptor": "I", "decoder": 330334599}, {"field": "targetIndex", "owner": "bz", "name": "bk", "access": 0, "descriptor": "I", "decoder": -1387109107}, {"field": "turnLeftSequence", "owner": "bz", "name": "al", "access": 0, "descriptor": "I", "decoder": -831787909}, {"field": "turnRightSequence", "owner": "bz", "name": "ab", "access": 0, "descriptor": "I", "decoder": 1788714977}, {"field": "walkBackSequence", "owner": "bz", "name": "at", "access": 0, "descriptor": "I", "decoder": -1598871111}, {"field": "walkLeftSequence", "owner": "bz", "name": "ad", "access": 0, "descriptor": "I", "decoder": -568830521}, {"field": "walkRightSequence", "owner": "bz", "name": "ap", "access": 0, "descriptor": "I", "decoder": -257240117}, {"field": "walkSequence", "owner": "bz", "name": "ae", "access": 0, "descriptor": "I", "decoder": -2075997699}, {"field": "x", "owner": "bz", "name": "av", "access": 0, "descriptor": "I", "decoder": -1573449469}, {"field": "y", "owner": "bz", "name": "ar", "access": 0, "descriptor": "I", "decoder": 676600595}, {"field": "__ay", "owner": "bz", "name": "ay", "access": 0, "descriptor": "Z"}, {"field": "__az", "owner": "bz", "name": "az", "access": 0, "descriptor": "Z"}, {"field": "__ac", "owner": "bz", "name": "ac", "access": 0, "descriptor": "I", "decoder": 1439711403}, {"field": "__bc", "owner": "bz", "name": "bc", "access": 0, "descriptor": "I", "decoder": 139384875}, {"field": "__be", "owner": "bz", "name": "be", "access": 0, "descriptor": "I", "decoder": 837989037}, {"field": "__bf", "owner": "bz", "name": "bf", "access": 0, "descriptor": "I", "decoder": 2121599561}, {"field": "__bh", "owner": "bz", "name": "bh", "access": 0, "descriptor": "I", "decoder": 280591281}, {"field": "__bj", "owner": "bz", "name": "bj", "access": 0, "descriptor": "I", "decoder": -1523249259}, {"field": "__bt", "owner": "bz", "name": "bt", "access": 0, "descriptor": "I", "decoder": 886371765}, {"field": "__bv", "owner": "bz", "name": "bv", "access": 0, "descriptor": "I", "decoder": -1857513783}, {"field": "__bw", "owner": "bz", "name": "bw", "access": 0, "descriptor": "I", "decoder": -1630403001}, {"field": "__ch", "owner": "bz", "name": "ch", "access": 0, "descriptor": "I", "decoder": 1584102533}, {"field": "__cj", "owner": "bz", "name": "cj", "access": 0, "descriptor": "I", "decoder": -336931157}, {"field": "__cm", "owner": "bz", "name": "cm", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "__cq", "owner": "bz", "name": "cq", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "__cr", "owner": "bz", "name": "cr", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "__cv", "owner": "bz", "name": "cv", "access": 0, "descriptor": "I", "decoder": -**********}], "methods": [{"method": "addHealthBar", "owner": "bz", "name": "bq", "access": 16, "parameters": ["healthBarDefinition", "cycle", "n0", "n1", "n2", "n3"], "descriptor": "(IIIIIIB)V"}, {"method": "addHitmark", "owner": "bz", "name": "bs", "access": 16, "parameters": ["type", "value", "type2", "value2", "cycle", "cycle2"], "descriptor": "(IIIIIII)V"}, {"method": "isVisible", "owner": "bz", "name": "z", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "removeHealthBar", "owner": "bz", "name": "bn", "access": 16, "parameters": ["healthBarDefinition"], "descriptor": "(IS)V"}, {"method": "__bb_143", "owner": "bz", "name": "bb", "access": 16, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "World", "name": "bt", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "activity", "owner": "bt", "name": "a", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "host", "owner": "bt", "name": "i", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "id", "owner": "bt", "name": "d", "access": 0, "descriptor": "I", "decoder": -144265493}, {"field": "index", "owner": "bt", "name": "j", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "location", "owner": "bt", "name": "z", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "population", "owner": "bt", "name": "n", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "properties", "owner": "bt", "name": "k", "access": 0, "descriptor": "I", "decoder": 956991319}], "methods": [{"method": "__e_144", "owner": "bt", "name": "e", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "__x_145", "owner": "bt", "name": "x", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "__d_146", "owner": "bt", "name": "d", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "__a_147", "owner": "bt", "name": "a", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "__z_148", "owner": "bt", "name": "z", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "__j_149", "owner": "bt", "name": "j", "access": 0, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ClientPreferences", "name": "bf", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "hideUsername", "owner": "bf", "name": "g", "access": 0, "descriptor": "Z"}, {"field": "parameters", "owner": "bf", "name": "l", "access": 0, "descriptor": "Ljava/util/LinkedHashMap;"}, {"field": "rememberedUsername", "owner": "bf", "name": "u", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "roofsHidden", "owner": "bf", "name": "q", "access": 0, "descriptor": "Z"}, {"field": "titleMusicDisabled", "owner": "bf", "name": "w", "access": 0, "descriptor": "Z"}, {"field": "windowMode", "owner": "bf", "name": "o", "access": 0, "descriptor": "I", "decoder": -359530975}], "methods": [{"method": "<PERSON><PERSON><PERSON><PERSON>", "owner": "bf", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)Lgr;"}, {"method": "__m_150", "owner": "bf", "name": "m", "access": 0, "parameters": ["arg0"], "descriptor": "(ZI)V"}], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 0, "descriptor": "(Lgr;)V"}]}, {"class": "FriendSystem", "name": "bh", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "friendsList", "owner": "bh", "name": "o", "access": 17, "descriptor": "Lkg;"}, {"field": "ignoreList", "owner": "bh", "name": "u", "access": 17, "descriptor": "Lkj;"}, {"field": "loginType", "owner": "bh", "name": "w", "access": 16, "descriptor": "Llx;"}, {"field": "__g", "owner": "bh", "name": "g", "access": 0, "descriptor": "I", "decoder": 1477282369}], "methods": [{"method": "clear", "owner": "bh", "name": "o", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "removeFriend", "owner": "bh", "name": "z", "access": 16, "parameters": ["name"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "removeIgnore", "owner": "bh", "name": "j", "access": 16, "parameters": ["name"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "__m_151", "owner": "bh", "name": "m", "access": 0, "parameters": [], "descriptor": "(B)Z"}, {"method": "__f_152", "owner": "bh", "name": "f", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__q_153", "owner": "bh", "name": "q", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(Lgr;II)V"}, {"method": "__w_154", "owner": "bh", "name": "w", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__u_155", "owner": "bh", "name": "u", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(Lkp;ZI)Z"}, {"method": "__g_156", "owner": "bh", "name": "g", "access": 16, "parameters": ["arg0"], "descriptor": "(Lkp;I)Z"}, {"method": "__l_157", "owner": "bh", "name": "l", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "__e_158", "owner": "bh", "name": "e", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "__x_159", "owner": "bh", "name": "x", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;B)V"}, {"method": "__a_160", "owner": "bh", "name": "a", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "__t_161", "owner": "bh", "name": "t", "access": 16, "parameters": ["arg0"], "descriptor": "(Lkp;S)Z"}], "constructors": [{"access": 0, "descriptor": "(Llx;)V"}]}, {"class": "ObjectSound", "name": "be", "super": "gw", "access": 49, "interfaces": [], "fields": [{"field": "obj", "owner": "be", "name": "a", "access": 0, "descriptor": "Ljr;"}, {"field": "soundEffectId", "owner": "be", "name": "l", "access": 0, "descriptor": "I", "decoder": -731474443}, {"field": "soundEffectIds", "owner": "be", "name": "k", "access": 0, "descriptor": "[I"}, {"field": "stream1", "owner": "be", "name": "e", "access": 0, "descriptor": "Lds;"}, {"field": "stream2", "owner": "be", "name": "i", "access": 0, "descriptor": "Lds;"}, {"field": "__d", "owner": "be", "name": "d", "access": 0, "descriptor": "I", "decoder": 1748890449}, {"field": "__f", "owner": "be", "name": "f", "access": 0, "descriptor": "I", "decoder": 647270745}, {"field": "__g", "owner": "be", "name": "g", "access": 0, "descriptor": "I", "decoder": 1344558633}, {"field": "__n", "owner": "be", "name": "n", "access": 0, "descriptor": "I", "decoder": -1041508847}, {"field": "__o", "owner": "be", "name": "o", "access": 0, "descriptor": "I", "decoder": -211591209}, {"field": "__q", "owner": "be", "name": "q", "access": 0, "descriptor": "I", "decoder": -800464493}, {"field": "__u", "owner": "be", "name": "u", "access": 0, "descriptor": "I", "decoder": 512258949}, {"field": "__w", "owner": "be", "name": "w", "access": 0, "descriptor": "I", "decoder": -85911525}, {"field": "__x", "owner": "be", "name": "x", "access": 0, "descriptor": "I", "decoder": -1608400407}], "methods": [{"method": "set", "owner": "be", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Npc", "name": "cv", "super": "bz", "access": 49, "interfaces": [], "fields": [{"field": "definition", "owner": "cv", "name": "m", "access": 0, "descriptor": "Ljz;"}], "methods": [{"method": "getModel", "owner": "cv", "name": "e", "access": 20, "parameters": [], "descriptor": "(I)Ldu;"}, {"method": "isVisible", "owner": "cv", "name": "z", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "__m_162", "owner": "cv", "name": "m", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(IBI)V"}, {"method": "__f_163", "owner": "cv", "name": "f", "access": 16, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIZI)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON>", "name": "cg", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "intArgumentCount", "owner": "cg", "name": "g", "access": 0, "descriptor": "I", "decoder": -1077473933}, {"field": "intOperands", "owner": "cg", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "localIntCount", "owner": "cg", "name": "o", "access": 0, "descriptor": "I", "decoder": 1952727759}, {"field": "localStringCount", "owner": "cg", "name": "u", "access": 0, "descriptor": "I", "decoder": -1124477681}, {"field": "opcodes", "owner": "cg", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "stringArgumentCount", "owner": "cg", "name": "l", "access": 0, "descriptor": "I", "decoder": -503680987}, {"field": "stringOperands", "owner": "cg", "name": "w", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "switches", "owner": "cg", "name": "e", "access": 0, "descriptor": "[Llh;"}], "methods": [{"method": "__o_164", "owner": "cg", "name": "o", "access": 0, "parameters": ["arg0"], "descriptor": "(II)[Llh;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "PacketWriter", "name": "cl", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "buffer", "owner": "cl", "name": "w", "access": 0, "descriptor": "Lgr;"}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "cl", "name": "o", "access": 1, "descriptor": "Lhn;"}, {"field": "packetBuffer", "owner": "cl", "name": "u", "access": 0, "descriptor": "Lhx;"}, {"field": "packetBufferNodes", "owner": "cl", "name": "f", "access": 0, "descriptor": "Lja;"}, {"field": "serverPacket0", "owner": "cl", "name": "g", "access": 0, "descriptor": "Lge;"}, {"field": "serverPacket0Length", "owner": "cl", "name": "l", "access": 0, "descriptor": "I", "decoder": -1986766063}, {"field": "socket0", "owner": "cl", "name": "m", "access": 0, "descriptor": "Lfu;"}, {"field": "__e", "owner": "cl", "name": "e", "access": 0, "descriptor": "Z"}, {"field": "__i", "owner": "cl", "name": "i", "access": 0, "descriptor": "Lge;"}, {"field": "__k", "owner": "cl", "name": "k", "access": 0, "descriptor": "Lge;"}, {"field": "__n", "owner": "cl", "name": "n", "access": 0, "descriptor": "Lge;"}, {"field": "__d", "owner": "cl", "name": "d", "access": 0, "descriptor": "I", "decoder": 1672229609}, {"field": "__q", "owner": "cl", "name": "q", "access": 0, "descriptor": "I", "decoder": -1028046199}, {"field": "__x", "owner": "cl", "name": "x", "access": 0, "descriptor": "I", "decoder": 120570975}], "methods": [{"method": "close", "owner": "cl", "name": "o", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "getSocket", "owner": "cl", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)Lfu;"}, {"method": "removeSocket", "owner": "cl", "name": "u", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "setSocket", "owner": "cl", "name": "w", "access": 0, "parameters": ["socket"], "descriptor": "(Lfu;B)V"}, {"method": "__m_165", "owner": "cl", "name": "m", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__f_166", "owner": "cl", "name": "f", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__q_167", "owner": "cl", "name": "q", "access": 17, "parameters": ["arg0"], "descriptor": "(Lgg;I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "PcmStreamMixer", "name": "ct", "super": "dc", "access": 33, "interfaces": [], "fields": [{"field": "subStreams", "owner": "ct", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "__o", "owner": "ct", "name": "o", "access": 0, "descriptor": "I"}, {"field": "__q", "owner": "ct", "name": "q", "access": 0, "descriptor": "I"}, {"field": "__f", "owner": "ct", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON>;"}], "methods": [{"method": "addSubStream", "owner": "ct", "name": "m", "access": 49, "parameters": ["subStream"], "descriptor": "(Ldc;)V"}, {"method": "firstSubStream", "owner": "ct", "name": "u", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "nextSubStream", "owner": "ct", "name": "g", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "removeSubStream", "owner": "ct", "name": "f", "access": 49, "parameters": ["subStream"], "descriptor": "(Ldc;)V"}, {"method": "skipSubStreams", "owner": "ct", "name": "a", "access": 0, "parameters": ["length"], "descriptor": "(I)V"}, {"method": "updateSubStreams", "owner": "ct", "name": "x", "access": 0, "parameters": ["buffer", "start", "end"], "descriptor": "([III)V"}, {"method": "__q_168", "owner": "ct", "name": "q", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__w_169", "owner": "ct", "name": "w", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lgw;Ldf;)V"}, {"method": "__o_170", "owner": "ct", "name": "o", "access": 0, "parameters": ["arg0"], "descriptor": "(Ldf;)V"}, {"method": "__l_171", "owner": "ct", "name": "l", "access": 4, "parameters": [], "descriptor": "()I"}, {"method": "__e_172", "owner": "ct", "name": "e", "access": 49, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([III)V"}, {"method": "__d_173", "owner": "ct", "name": "d", "access": 49, "parameters": ["arg0"], "descriptor": "(I)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "SoundEffect", "name": "ci", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "end", "owner": "ci", "name": "w", "access": 0, "descriptor": "I"}, {"field": "instruments", "owner": "ci", "name": "f", "access": 0, "descriptor": "[Ldb;"}, {"field": "start", "owner": "ci", "name": "q", "access": 0, "descriptor": "I"}], "methods": [{"method": "mix", "owner": "ci", "name": "w", "access": 16, "parameters": [], "descriptor": "()[B"}, {"method": "toRawSound", "owner": "ci", "name": "f", "access": 1, "parameters": [], "descriptor": "()Lcx;"}, {"method": "__q_174", "owner": "ci", "name": "q", "access": 17, "parameters": [], "descriptor": "()I"}], "constructors": [{"access": 0, "descriptor": "(Lgr;)V"}]}, {"class": "MusicSample", "name": "cd", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "end", "owner": "cd", "name": "o", "access": 0, "descriptor": "I"}, {"field": "sampleCount", "owner": "cd", "name": "q", "access": 0, "descriptor": "I"}, {"field": "sampleRate", "owner": "cd", "name": "f", "access": 0, "descriptor": "I"}, {"field": "samples", "owner": "cd", "name": "ac", "access": 0, "descriptor": "[B"}, {"field": "start", "owner": "cd", "name": "w", "access": 0, "descriptor": "I"}, {"field": "__b", "owner": "cd", "name": "b", "access": 0, "descriptor": "Z"}, {"field": "__u", "owner": "cd", "name": "u", "access": 0, "descriptor": "Z"}, {"field": "__m", "owner": "cd", "name": "m", "access": 0, "descriptor": "[[B"}, {"field": "__t", "owner": "cd", "name": "t", "access": 0, "descriptor": "[F"}, {"field": "__ah", "owner": "cd", "name": "ah", "access": 0, "descriptor": "I"}, {"field": "__ay", "owner": "cd", "name": "ay", "access": 0, "descriptor": "I"}, {"field": "__h", "owner": "cd", "name": "h", "access": 0, "descriptor": "I"}, {"field": "__y", "owner": "cd", "name": "y", "access": 0, "descriptor": "I"}], "methods": [{"method": "read", "owner": "cd", "name": "o", "access": 0, "descriptor": "([B)V"}, {"method": "toRawSound", "owner": "cd", "name": "x", "access": 0, "descriptor": "([I)Lcx;"}, {"method": "__g_175", "owner": "cd", "name": "g", "access": 0, "parameters": ["arg0"], "descriptor": "(I)[F"}], "constructors": [{"access": 0, "descriptor": "([B)V"}]}, {"class": "RawSound", "name": "cx", "super": "dd", "access": 33, "interfaces": [], "fields": [{"field": "end", "owner": "cx", "name": "w", "access": 0, "descriptor": "I"}, {"field": "sampleRate", "owner": "cx", "name": "m", "access": 1, "descriptor": "I"}, {"field": "samples", "owner": "cx", "name": "f", "access": 1, "descriptor": "[B"}, {"field": "start", "owner": "cx", "name": "q", "access": 1, "descriptor": "I"}, {"field": "__o", "owner": "cx", "name": "o", "access": 1, "descriptor": "Z"}], "methods": [{"method": "resample", "owner": "cx", "name": "m", "access": 1, "parameters": ["decimator"], "descriptor": "(Ldp;)Lcx;"}], "constructors": [{"access": 0, "descriptor": "(I[BII)V"}, {"access": 0, "descriptor": "(I[BIIZ)V"}]}, {"class": "PcmPlayer", "name": "co", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "capacity", "owner": "co", "name": "s", "access": 0, "descriptor": "I", "decoder": -1671644011}, {"field": "frequency", "owner": "co", "name": "t", "access": 0, "descriptor": "I", "decoder": 951871565}, {"field": "nextPosition", "owner": "co", "name": "r", "access": 0, "descriptor": "I", "decoder": -1586398263}, {"field": "retryTimeMs", "owner": "co", "name": "h", "access": 0, "descriptor": "J", "decoder": -200656835998283411}, {"field": "samples", "owner": "co", "name": "i", "access": 4, "descriptor": "[I"}, {"field": "stream0", "owner": "co", "name": "a", "access": 0, "descriptor": "Ldc;"}, {"field": "timeMs", "owner": "co", "name": "j", "access": 0, "descriptor": "J", "decoder": -8124323644430480811}, {"field": "__v", "owner": "co", "name": "v", "access": 0, "descriptor": "Z"}, {"field": "__ah", "owner": "co", "name": "ah", "access": 0, "descriptor": "[Ldc;"}, {"field": "__ay", "owner": "co", "name": "ay", "access": 0, "descriptor": "[Ldc;"}, {"field": "__ac", "owner": "co", "name": "ac", "access": 0, "descriptor": "I", "decoder": -645495803}, {"field": "__b", "owner": "co", "name": "b", "access": 0, "descriptor": "I", "decoder": 48658021}, {"field": "__c", "owner": "co", "name": "c", "access": 0, "descriptor": "I", "decoder": 1624619697}, {"field": "__y", "owner": "co", "name": "y", "access": 0, "descriptor": "I", "decoder": 1907203055}, {"field": "__z", "owner": "co", "name": "z", "access": 0, "descriptor": "I", "decoder": 1125788887}, {"field": "__p", "owner": "co", "name": "p", "access": 0, "descriptor": "J", "decoder": -8188447292076890879}], "methods": [{"method": "close", "owner": "co", "name": "o", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "discard", "owner": "co", "name": "u", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "fill", "owner": "co", "name": "aw", "access": 16, "parameters": ["buffer", "length"], "descriptor": "([II)V"}, {"method": "init", "owner": "co", "name": "m", "access": 4, "parameters": [], "descriptor": "(I)V"}, {"method": "open", "owner": "co", "name": "f", "access": 4, "parameters": ["bufferSize"], "descriptor": "(II)V"}, {"method": "position", "owner": "co", "name": "q", "access": 4, "parameters": [], "descriptor": "(B)I"}, {"method": "run", "owner": "co", "name": "ar", "access": 49, "parameters": [], "descriptor": "(I)V"}, {"method": "setStream", "owner": "co", "name": "av", "access": 49, "parameters": ["stream"], "descriptor": "(Ldc;I)V"}, {"method": "shutdown", "owner": "co", "name": "ah", "access": 49, "parameters": [], "descriptor": "(I)V"}, {"method": "skip", "owner": "co", "name": "ak", "access": 16, "parameters": ["length"], "descriptor": "(II)V"}, {"method": "tryDiscard", "owner": "co", "name": "ay", "access": 49, "parameters": [], "descriptor": "(B)V"}, {"method": "write", "owner": "co", "name": "w", "access": 4, "parameters": [], "descriptor": "()V"}, {"method": "__ac_176", "owner": "co", "name": "ac", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "__ab_177", "owner": "co", "name": "ab", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(Ldc;II)V"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "SoundSystem", "name": "dx", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "players", "owner": "dx", "name": "m", "access": 64, "descriptor": "[Lco;"}], "methods": [{"method": "__run_178", "owner": "dx", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "PcmStreamMixerListener", "name": "df", "super": "gw", "access": 1057, "interfaces": [], "fields": [{"field": "__m", "owner": "df", "name": "m", "access": 0, "descriptor": "I"}], "methods": [{"method": "remove2", "owner": "df", "name": "m", "access": 1024, "parameters": [], "descriptor": "()V"}, {"method": "update", "owner": "df", "name": "f", "access": 1024, "parameters": ["mixer"], "descriptor": "(Lct;)I"}], "constructors": []}, {"class": "RawPcmStream", "name": "ds", "super": "dc", "access": 33, "interfaces": [], "fields": [{"field": "end", "owner": "ds", "name": "d", "access": 0, "descriptor": "I"}, {"field": "start", "owner": "ds", "name": "x", "access": 0, "descriptor": "I"}, {"field": "__k", "owner": "ds", "name": "k", "access": 0, "descriptor": "Z"}, {"field": "__a", "owner": "ds", "name": "a", "access": 0, "descriptor": "I"}, {"field": "__e", "owner": "ds", "name": "e", "access": 0, "descriptor": "I"}, {"field": "__f", "owner": "ds", "name": "f", "access": 0, "descriptor": "I"}, {"field": "__g", "owner": "ds", "name": "g", "access": 0, "descriptor": "I"}, {"field": "__i", "owner": "ds", "name": "i", "access": 0, "descriptor": "I"}, {"field": "__l", "owner": "ds", "name": "l", "access": 0, "descriptor": "I"}, {"field": "__m", "owner": "ds", "name": "m", "access": 0, "descriptor": "I"}, {"field": "__n", "owner": "ds", "name": "n", "access": 0, "descriptor": "I"}, {"field": "__o", "owner": "ds", "name": "o", "access": 0, "descriptor": "I"}, {"field": "__q", "owner": "ds", "name": "q", "access": 0, "descriptor": "I"}, {"field": "__u", "owner": "ds", "name": "u", "access": 0, "descriptor": "I"}, {"field": "__z", "owner": "ds", "name": "z", "access": 0, "descriptor": "I"}], "methods": [{"method": "__az_179", "owner": "ds", "name": "az", "access": 0, "parameters": [], "descriptor": "()I"}, {"method": "__o_180", "owner": "ds", "name": "o", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__x_181", "owner": "ds", "name": "x", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__a_182", "owner": "ds", "name": "a", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__z_183", "owner": "ds", "name": "z", "access": 32, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__j_184", "owner": "ds", "name": "j", "access": 32, "parameters": ["arg0", "arg1"], "descriptor": "(II)V"}, {"method": "__s_185", "owner": "ds", "name": "s", "access": 33, "parameters": [], "descriptor": "()I"}, {"method": "__t_186", "owner": "ds", "name": "t", "access": 33, "parameters": [], "descriptor": "()I"}, {"method": "__y_187", "owner": "ds", "name": "y", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__h_188", "owner": "ds", "name": "h", "access": 33, "parameters": ["arg0"], "descriptor": "(Z)V"}, {"method": "__b_189", "owner": "ds", "name": "b", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__c_190", "owner": "ds", "name": "c", "access": 33, "parameters": ["arg0", "arg1"], "descriptor": "(II)V"}, {"method": "__p_191", "owner": "ds", "name": "p", "access": 33, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__v_192", "owner": "ds", "name": "v", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__ah_193", "owner": "ds", "name": "ah", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__ab_194", "owner": "ds", "name": "ab", "access": 33, "parameters": [], "descriptor": "()I"}, {"method": "__ae_195", "owner": "ds", "name": "ae", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "__at_196", "owner": "ds", "name": "at", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "__u_197", "owner": "ds", "name": "u", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__g_198", "owner": "ds", "name": "g", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__l_199", "owner": "ds", "name": "l", "access": 4, "parameters": [], "descriptor": "()I"}, {"method": "__e_200", "owner": "ds", "name": "e", "access": 33, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([III)V"}, {"method": "__d_201", "owner": "ds", "name": "d", "access": 33, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__ad_202", "owner": "ds", "name": "ad", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "([IIIII)I"}, {"method": "__ap_203", "owner": "ds", "name": "ap", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "([IIIII)I"}, {"method": "__au_204", "owner": "ds", "name": "au", "access": 0, "parameters": [], "descriptor": "()Z"}], "constructors": [{"access": 0, "descriptor": "(Lcx;II)V"}, {"access": 0, "descriptor": "(Lcx;III)V"}]}, {"class": "AudioFilter", "name": "di", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "di", "name": "m", "access": 0, "descriptor": "[I"}, {"field": "__o", "owner": "di", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "__q", "owner": "di", "name": "q", "access": 0, "descriptor": "[[[I"}, {"field": "__w", "owner": "di", "name": "w", "access": 0, "descriptor": "[[[I"}], "methods": [{"method": "__m_205", "owner": "di", "name": "m", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIF)F"}, {"method": "__q_206", "owner": "di", "name": "q", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIF)F"}, {"method": "__w_207", "owner": "di", "name": "w", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(IF)I"}, {"method": "__o_208", "owner": "di", "name": "o", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(Lgr;Lde;)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "PcmStream", "name": "dc", "super": "gw", "access": 1057, "interfaces": [], "fields": [{"field": "active", "owner": "dc", "name": "w", "access": 64, "descriptor": "Z"}, {"field": "after", "owner": "dc", "name": "j", "access": 0, "descriptor": "Ldc;"}, {"field": "sound", "owner": "dc", "name": "t", "access": 0, "descriptor": "Ldd;"}, {"field": "__s", "owner": "dc", "name": "s", "access": 0, "descriptor": "I"}], "methods": [{"method": "fill", "owner": "dc", "name": "e", "access": 1028, "parameters": ["buffer", "start", "end"], "descriptor": "([III)V"}, {"method": "firstSubStream", "owner": "dc", "name": "u", "access": 1028, "parameters": [], "descriptor": "()Ldc;"}, {"method": "nextSubStream", "owner": "dc", "name": "g", "access": 1028, "parameters": [], "descriptor": "()Ldc;"}, {"method": "skip", "owner": "dc", "name": "d", "access": 1028, "parameters": ["length"], "descriptor": "(I)V"}, {"method": "update", "owner": "dc", "name": "ef", "access": 16, "parameters": ["buffer", "start", "end"], "descriptor": "([III)V"}, {"method": "__az_209", "owner": "dc", "name": "az", "access": 0, "parameters": [], "descriptor": "()I"}, {"method": "__l_210", "owner": "dc", "name": "l", "access": 1028, "parameters": [], "descriptor": "()I"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "Instrument", "name": "db", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "delayDecay", "owner": "db", "name": "n", "access": 0, "descriptor": "I"}, {"field": "delayTime", "owner": "db", "name": "k", "access": 0, "descriptor": "I"}, {"field": "duration", "owner": "db", "name": "z", "access": 0, "descriptor": "I"}, {"field": "filter", "owner": "db", "name": "i", "access": 0, "descriptor": "Ldi;"}, {"field": "offset", "owner": "db", "name": "j", "access": 0, "descriptor": "I"}, {"field": "oscillatorDelays", "owner": "db", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "oscillatorPitch", "owner": "db", "name": "x", "access": 0, "descriptor": "[I"}, {"field": "oscillatorVolume", "owner": "db", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "__a", "owner": "db", "name": "a", "access": 0, "descriptor": "Lde;"}, {"field": "__f", "owner": "db", "name": "f", "access": 0, "descriptor": "Lde;"}, {"field": "__g", "owner": "db", "name": "g", "access": 0, "descriptor": "Lde;"}, {"field": "__l", "owner": "db", "name": "l", "access": 0, "descriptor": "Lde;"}, {"field": "__m", "owner": "db", "name": "m", "access": 0, "descriptor": "Lde;"}, {"field": "__o", "owner": "db", "name": "o", "access": 0, "descriptor": "Lde;"}, {"field": "__q", "owner": "db", "name": "q", "access": 0, "descriptor": "Lde;"}, {"field": "__u", "owner": "db", "name": "u", "access": 0, "descriptor": "Lde;"}, {"field": "__w", "owner": "db", "name": "w", "access": 0, "descriptor": "Lde;"}], "methods": [{"method": "decode", "owner": "db", "name": "q", "access": 16, "parameters": ["buffer"], "descriptor": "(Lgr;)V"}, {"method": "evaluateWave", "owner": "db", "name": "f", "access": 16, "descriptor": "(III)I"}, {"method": "synthesize", "owner": "db", "name": "m", "access": 16, "descriptor": "(II)[I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "BufferedFile", "name": "dy", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "accessFile", "owner": "dy", "name": "m", "access": 0, "descriptor": "Ldk;"}, {"field": "capacity", "owner": "dy", "name": "x", "access": 0, "descriptor": "J", "decoder": 2663146692618913943}, {"field": "readBuffer", "owner": "dy", "name": "f", "access": 0, "descriptor": "[B"}, {"field": "writeBuffer", "owner": "dy", "name": "o", "access": 0, "descriptor": "[B"}, {"field": "__g", "owner": "dy", "name": "g", "access": 0, "descriptor": "I", "decoder": -1558233611}, {"field": "__w", "owner": "dy", "name": "w", "access": 0, "descriptor": "I", "decoder": -846810907}, {"field": "__d", "owner": "dy", "name": "d", "access": 0, "descriptor": "J", "decoder": 5720856138805191881}, {"field": "__e", "owner": "dy", "name": "e", "access": 0, "descriptor": "J", "decoder": -3728247331531750871}, {"field": "__l", "owner": "dy", "name": "l", "access": 0, "descriptor": "J", "decoder": -6819476051574242871}, {"field": "__q", "owner": "dy", "name": "q", "access": 0, "descriptor": "J", "decoder": -7773729680030815835}, {"field": "__u", "owner": "dy", "name": "u", "access": 0, "descriptor": "J", "decoder": 5249231081498323007}], "methods": [{"method": "close", "owner": "dy", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "flush", "owner": "dy", "name": "l", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "length", "owner": "dy", "name": "q", "access": 1, "parameters": [], "descriptor": "(B)J"}, {"method": "load", "owner": "dy", "name": "u", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "read", "owner": "dy", "name": "o", "access": 1, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "readFill", "owner": "dy", "name": "w", "access": 1, "parameters": ["dst"], "descriptor": "([BI)V"}, {"method": "seek", "owner": "dy", "name": "f", "access": 1, "parameters": ["index"], "descriptor": "(J)V"}, {"method": "write", "owner": "dy", "name": "g", "access": 1, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIII)V"}], "constructors": [{"access": 1, "descriptor": "(Ldk;II)V"}]}, {"class": "AccessFile", "name": "dk", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "capacity", "owner": "dk", "name": "f", "access": 0, "descriptor": "J", "decoder": -5849841282281477349}, {"field": "file", "owner": "dk", "name": "m", "access": 0, "descriptor": "Ljava/io/RandomAccessFile;"}, {"field": "index", "owner": "dk", "name": "q", "access": 0, "descriptor": "J", "decoder": 7465414039279036117}], "methods": [{"method": "close", "owner": "dk", "name": "q", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "closeSync", "owner": "dk", "name": "w", "access": 17, "parameters": ["sync"], "descriptor": "(ZI)V"}, {"method": "length", "owner": "dk", "name": "o", "access": 17, "parameters": [], "descriptor": "(I)J"}, {"method": "read", "owner": "dk", "name": "u", "access": 17, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIII)I"}, {"method": "seek", "owner": "dk", "name": "m", "access": 16, "parameters": ["index"], "descriptor": "(J)V"}, {"method": "write", "owner": "dk", "name": "f", "access": 17, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "__finalize_211", "owner": "dk", "name": "finalize", "access": 4, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/io/File;Lja<PERSON>/lang/String;J)V"}]}, {"class": "TextureProvider", "name": "dl", "super": "java.lang.Object", "access": 33, "interfaces": ["ei"], "fields": [{"field": "archive", "owner": "dl", "name": "g", "access": 0, "descriptor": "Lir;"}, {"field": "brightness0", "owner": "dl", "name": "o", "access": 0, "descriptor": "D"}, {"field": "capacity", "owner": "dl", "name": "q", "access": 0, "descriptor": "I", "decoder": 762273989}, {"field": "deque", "owner": "dl", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "remaining", "owner": "dl", "name": "w", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "textureSize", "owner": "dl", "name": "u", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "textures", "owner": "dl", "name": "m", "access": 0, "descriptor": "[Ldh;"}], "methods": [{"method": "animate", "owner": "dl", "name": "l", "access": 1, "parameters": ["n"], "descriptor": "(II)V"}, {"method": "clear", "owner": "dl", "name": "g", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "isLowDetail", "owner": "dl", "name": "u", "access": 1, "descriptor": "(II)Z"}, {"method": "load", "owner": "dl", "name": "q", "access": 1, "descriptor": "(II)[I"}, {"method": "setBrightness", "owner": "dl", "name": "f", "access": 1, "parameters": ["brightness"], "descriptor": "(D)V"}, {"method": "__m_212", "owner": "dl", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__w_213", "owner": "dl", "name": "w", "access": 1, "parameters": ["arg0"], "descriptor": "(II)I"}, {"method": "__o_214", "owner": "dl", "name": "o", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)Z"}], "constructors": [{"access": 1, "descriptor": "(Lir;Lir;IDI)V"}]}, {"class": "ModelData", "name": "dw", "super": "ex", "access": 33, "interfaces": [], "fields": [{"field": "face<PERSON><PERSON><PERSON>s", "owner": "dw", "name": "d", "access": 0, "descriptor": "[B"}, {"field": "faceColors", "owner": "dw", "name": "n", "access": 0, "descriptor": "[S"}, {"field": "faceCount", "owner": "dw", "name": "o", "access": 0, "descriptor": "I"}, {"field": "faceLabelsAlpha", "owner": "dw", "name": "r", "access": 0, "descriptor": "[[I"}, {"field": "faceNormals", "owner": "dw", "name": "p", "access": 0, "descriptor": "[Lel;"}, {"field": "faceTextures", "owner": "dw", "name": "i", "access": 0, "descriptor": "[S"}, {"field": "indices1", "owner": "dw", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "indices2", "owner": "dw", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "indices3", "owner": "dw", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "isBoundsCalculated", "owner": "dw", "name": "av", "access": 0, "descriptor": "Z"}, {"field": "vertexLabels", "owner": "dw", "name": "c", "access": 0, "descriptor": "[[I"}, {"field": "verticesCount", "owner": "dw", "name": "m", "access": 0, "descriptor": "I"}, {"field": "verticesX", "owner": "dw", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "verticesY", "owner": "dw", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "verticesZ", "owner": "dw", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "__a", "owner": "dw", "name": "a", "access": 0, "descriptor": "B"}, {"field": "__e", "owner": "dw", "name": "e", "access": 0, "descriptor": "[B"}, {"field": "__j", "owner": "dw", "name": "j", "access": 0, "descriptor": "[B"}, {"field": "__k", "owner": "dw", "name": "k", "access": 0, "descriptor": "[B"}, {"field": "__x", "owner": "dw", "name": "x", "access": 0, "descriptor": "[B"}, {"field": "__ag", "owner": "dw", "name": "ag", "access": 0, "descriptor": "[Ldq;"}, {"field": "__v", "owner": "dw", "name": "v", "access": 0, "descriptor": "[Ldq;"}, {"field": "__ac", "owner": "dw", "name": "ac", "access": 0, "descriptor": "I"}, {"field": "__ah", "owner": "dw", "name": "ah", "access": 0, "descriptor": "I"}, {"field": "__ak", "owner": "dw", "name": "ak", "access": 0, "descriptor": "I"}, {"field": "__ar", "owner": "dw", "name": "ar", "access": 0, "descriptor": "I"}, {"field": "__ay", "owner": "dw", "name": "ay", "access": 0, "descriptor": "I"}, {"field": "__z", "owner": "dw", "name": "z", "access": 0, "descriptor": "I"}, {"field": "__b", "owner": "dw", "name": "b", "access": 0, "descriptor": "[I"}, {"field": "__h", "owner": "dw", "name": "h", "access": 0, "descriptor": "[I"}, {"field": "__s", "owner": "dw", "name": "s", "access": 0, "descriptor": "[S"}, {"field": "__t", "owner": "dw", "name": "t", "access": 0, "descriptor": "[S"}, {"field": "__y", "owner": "dw", "name": "y", "access": 0, "descriptor": "[S"}, {"field": "__aj", "owner": "dw", "name": "aj", "access": 1, "descriptor": "S"}, {"field": "__aq", "owner": "dw", "name": "aq", "access": 1, "descriptor": "S"}], "methods": [{"method": "invalidate", "owner": "dw", "name": "b", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "recolor", "owner": "dw", "name": "j", "access": 1, "parameters": ["from", "to"], "descriptor": "(SS)V"}, {"method": "retexture", "owner": "dw", "name": "s", "access": 1, "parameters": ["from", "to"], "descriptor": "(SS)V"}, {"method": "toModel", "owner": "dw", "name": "v", "access": 17, "parameters": ["a", "b", "c", "d", "e"], "descriptor": "(IIIII)Ldu;"}, {"method": "__f_215", "owner": "dw", "name": "f", "access": 0, "parameters": ["arg0"], "descriptor": "([B)V"}, {"method": "__q_216", "owner": "dw", "name": "q", "access": 0, "parameters": ["arg0"], "descriptor": "([B)V"}, {"method": "__w_217", "owner": "dw", "name": "w", "access": 16, "parameters": ["arg0", "arg1"], "descriptor": "(Ldw;I)I"}, {"method": "__o_218", "owner": "dw", "name": "o", "access": 1, "parameters": [], "descriptor": "()Ldw;"}, {"method": "__u_219", "owner": "dw", "name": "u", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "([[IIIIZI)Ldw;"}, {"method": "__g_220", "owner": "dw", "name": "g", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__l_221", "owner": "dw", "name": "l", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__x_222", "owner": "dw", "name": "x", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__d_223", "owner": "dw", "name": "d", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__a_224", "owner": "dw", "name": "a", "access": 1, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__z_225", "owner": "dw", "name": "z", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__t_226", "owner": "dw", "name": "t", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__y_227", "owner": "dw", "name": "y", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__h_228", "owner": "dw", "name": "h", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__c_229", "owner": "dw", "name": "c", "access": 0, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 0, "descriptor": "([B)V"}, {"access": 1, "descriptor": "([Ldw;I)V"}, {"access": 1, "descriptor": "(Ldw;ZZZZ)V"}]}, {"class": "TileModel", "name": "dv", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "is<PERSON><PERSON>", "owner": "dv", "name": "d", "access": 0, "descriptor": "Z"}, {"field": "overlayRgb", "owner": "dv", "name": "a", "access": 0, "descriptor": "I"}, {"field": "rotation", "owner": "dv", "name": "n", "access": 0, "descriptor": "I"}, {"field": "shape", "owner": "dv", "name": "k", "access": 0, "descriptor": "I"}, {"field": "underlayRgb", "owner": "dv", "name": "i", "access": 0, "descriptor": "I"}, {"field": "__e", "owner": "dv", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "__f", "owner": "dv", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "__g", "owner": "dv", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "__l", "owner": "dv", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "__m", "owner": "dv", "name": "m", "access": 0, "descriptor": "[I"}, {"field": "__o", "owner": "dv", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "__q", "owner": "dv", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "__u", "owner": "dv", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "__w", "owner": "dv", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "__x", "owner": "dv", "name": "x", "access": 0, "descriptor": "[I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(IIIIIIIIIIIIIIIIIII)V"}]}, {"class": "Texture", "name": "dh", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "animationDirection", "owner": "dh", "name": "d", "access": 0, "descriptor": "I"}, {"field": "animationSpeed", "owner": "dh", "name": "k", "access": 0, "descriptor": "I"}, {"field": "files", "owner": "dh", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "int1", "owner": "dh", "name": "o", "access": 0, "descriptor": "I"}, {"field": "isLoaded", "owner": "dh", "name": "i", "access": 0, "descriptor": "Z"}, {"field": "pixels", "owner": "dh", "name": "n", "access": 0, "descriptor": "[I"}, {"field": "__u", "owner": "dh", "name": "u", "access": 0, "descriptor": "Z"}, {"field": "__e", "owner": "dh", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "__l", "owner": "dh", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "__x", "owner": "dh", "name": "x", "access": 0, "descriptor": "[I"}], "methods": [{"method": "animate", "owner": "dh", "name": "q", "access": 0, "parameters": ["n"], "descriptor": "(I)V"}, {"method": "reset", "owner": "dh", "name": "f", "access": 0, "descriptor": "()V"}, {"method": "__m_230", "owner": "dh", "name": "m", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(DILir;)Z"}], "constructors": [{"access": 0, "descriptor": "(Lgr;)V"}]}, {"class": "Tile", "name": "dj", "super": "gw", "access": 49, "interfaces": [], "fields": [{"field": "boundaryObject", "owner": "dj", "name": "g", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "drawGameObjectEdges", "owner": "dj", "name": "t", "access": 0, "descriptor": "I", "decoder": 769252193}, {"field": "drawGameObjects", "owner": "dj", "name": "s", "access": 0, "descriptor": "Z"}, {"field": "drawPrimary", "owner": "dj", "name": "z", "access": 0, "descriptor": "Z"}, {"field": "drawSecondary", "owner": "dj", "name": "j", "access": 0, "descriptor": "Z"}, {"field": "floorDecoration", "owner": "dj", "name": "e", "access": 0, "descriptor": "Ldo;"}, {"field": "gameObjectEdgeMasks", "owner": "dj", "name": "n", "access": 0, "descriptor": "[I"}, {"field": "gameObjects", "owner": "dj", "name": "k", "access": 0, "descriptor": "[Leh;"}, {"field": "gameObjectsCount", "owner": "dj", "name": "d", "access": 0, "descriptor": "I", "decoder": -792635627}, {"field": "gameObjectsEdgeMask", "owner": "dj", "name": "i", "access": 0, "descriptor": "I", "decoder": 1192947815}, {"field": "tileItemPile", "owner": "dj", "name": "x", "access": 0, "descriptor": "Ldr;"}, {"field": "linkedBelowTile", "owner": "dj", "name": "c", "access": 0, "descriptor": "Ldj;"}, {"field": "minPlane", "owner": "dj", "name": "a", "access": 0, "descriptor": "I", "decoder": -1078135311}, {"field": "model", "owner": "dj", "name": "u", "access": 0, "descriptor": "Ldv;"}, {"field": "originalPlane", "owner": "dj", "name": "w", "access": 0, "descriptor": "I", "decoder": 507854577}, {"field": "paint", "owner": "dj", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "plane", "owner": "dj", "name": "m", "access": 0, "descriptor": "I", "decoder": 1065165981}, {"field": "wallDecoration", "owner": "dj", "name": "l", "access": 0, "descriptor": "Leq;"}, {"field": "x", "owner": "dj", "name": "f", "access": 0, "descriptor": "I", "decoder": 103024387}, {"field": "y", "owner": "dj", "name": "q", "access": 0, "descriptor": "I", "decoder": -561562755}, {"field": "__b", "owner": "dj", "name": "b", "access": 0, "descriptor": "I", "decoder": 2030010491}, {"field": "__h", "owner": "dj", "name": "h", "access": 0, "descriptor": "I", "decoder": -172066661}, {"field": "__y", "owner": "dj", "name": "y", "access": 0, "descriptor": "I", "decoder": 1724493981}], "methods": [], "constructors": [{"access": 0, "descriptor": "(III)V"}]}, {"class": "Model", "name": "du", "super": "ex", "access": 33, "interfaces": [], "fields": [{"field": "bottomY", "owner": "du", "name": "aj", "access": 0, "descriptor": "I"}, {"field": "boundsType", "owner": "du", "name": "aq", "access": 0, "descriptor": "I"}, {"field": "diameter", "owner": "du", "name": "ar", "access": 0, "descriptor": "I"}, {"field": "face<PERSON><PERSON><PERSON>s", "owner": "du", "name": "j", "access": 0, "descriptor": "[B"}, {"field": "faceColors1", "owner": "du", "name": "n", "access": 0, "descriptor": "[I"}, {"field": "faceColors2", "owner": "du", "name": "i", "access": 0, "descriptor": "[I"}, {"field": "faceColors3", "owner": "du", "name": "a", "access": 0, "descriptor": "[I"}, {"field": "faceLabelsAlpha", "owner": "du", "name": "v", "access": 0, "descriptor": "[[I"}, {"field": "faceTextures", "owner": "du", "name": "t", "access": 0, "descriptor": "[S"}, {"field": "indices1", "owner": "du", "name": "x", "access": 0, "descriptor": "[I"}, {"field": "indices2", "owner": "du", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "indices3", "owner": "du", "name": "k", "access": 0, "descriptor": "[I"}, {"field": "indicesCount", "owner": "du", "name": "e", "access": 0, "descriptor": "I"}, {"field": "isSingleTile", "owner": "du", "name": "ag", "access": 1, "descriptor": "Z"}, {"field": "radius", "owner": "du", "name": "ac", "access": 0, "descriptor": "I"}, {"field": "vertexLabels", "owner": "du", "name": "p", "access": 0, "descriptor": "[[I"}, {"field": "verticesCount", "owner": "du", "name": "o", "access": 0, "descriptor": "I"}, {"field": "verticesX", "owner": "du", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "verticesY", "owner": "du", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "verticesZ", "owner": "du", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "xMid", "owner": "du", "name": "ay", "access": 0, "descriptor": "I"}, {"field": "xMidOffset", "owner": "du", "name": "aw", "access": 0, "descriptor": "I"}, {"field": "xzRadius", "owner": "du", "name": "av", "access": 0, "descriptor": "I"}, {"field": "yMid", "owner": "du", "name": "ah", "access": 0, "descriptor": "I"}, {"field": "yMidOffset", "owner": "du", "name": "al", "access": 0, "descriptor": "I"}, {"field": "zMid", "owner": "du", "name": "ak", "access": 0, "descriptor": "I"}, {"field": "zMidOffset", "owner": "du", "name": "ab", "access": 0, "descriptor": "I"}, {"field": "__y", "owner": "du", "name": "y", "access": 0, "descriptor": "B"}, {"field": "__s", "owner": "du", "name": "s", "access": 0, "descriptor": "[B"}, {"field": "__z", "owner": "du", "name": "z", "access": 0, "descriptor": "[B"}, {"field": "__h", "owner": "du", "name": "h", "access": 0, "descriptor": "I"}, {"field": "__b", "owner": "du", "name": "b", "access": 0, "descriptor": "[I"}, {"field": "__c", "owner": "du", "name": "c", "access": 0, "descriptor": "[I"}, {"field": "__r", "owner": "du", "name": "r", "access": 0, "descriptor": "[I"}], "methods": [{"method": "animate", "owner": "du", "name": "d", "access": 1, "parameters": ["frames", "frame"], "descriptor": "(Len;I)V", "finalArgument": -2}, {"method": "animate2", "owner": "du", "name": "a", "access": 1, "descriptor": "(Len;ILen;I[I)V"}, {"method": "calculateBoundingBox", "owner": "du", "name": "o", "access": 0, "parameters": ["yaw"], "descriptor": "(I)V"}, {"method": "calculateBoundsCylinder", "owner": "du", "name": "u", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "copy0", "owner": "du", "name": "w", "access": 0, "descriptor": "(ZLdu;[B)Ldu;"}, {"method": "draw", "owner": "du", "name": "cy", "access": 0, "parameters": ["yaw", "cameraPitchSine", "cameraPitchCosine", "cameraYawSine", "cameraYawCosine", "x", "y", "z", "tag"], "descriptor": "(IIIIIIIIJ)V"}, {"method": "draw0", "owner": "du", "name": "v", "access": 16, "descriptor": "(ZZZJ)V"}, {"method": "offsetBy", "owner": "du", "name": "h", "access": 1, "parameters": ["x", "y", "z"], "descriptor": "(III)V"}, {"method": "resetBounds", "owner": "du", "name": "x", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "rotateY180", "owner": "du", "name": "s", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "rotateY270Ccw", "owner": "du", "name": "t", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "rotateY90Ccw", "owner": "du", "name": "j", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "rotateZ", "owner": "du", "name": "y", "access": 1, "parameters": ["pitch"], "descriptor": "(I)V"}, {"method": "scale", "owner": "du", "name": "b", "access": 1, "parameters": ["x", "y", "z"], "descriptor": "(III)V"}, {"method": "toSharedSequenceModel", "owner": "du", "name": "f", "access": 1, "parameters": ["b"], "descriptor": "(Z)Ldu;"}, {"method": "toSharedSpotAnimationModel", "owner": "du", "name": "q", "access": 1, "parameters": ["b"], "descriptor": "(Z)Ldu;"}, {"method": "transform", "owner": "du", "name": "z", "access": 0, "parameters": ["type", "labels", "tx", "ty", "tz"], "descriptor": "(I[IIII)V"}, {"method": "__m_231", "owner": "du", "name": "m", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "([[IIIIZI)Ldu;"}, {"method": "__g_232", "owner": "du", "name": "g", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__l_233", "owner": "du", "name": "l", "access": 1, "parameters": [], "descriptor": "()I"}, {"method": "__c_234", "owner": "du", "name": "c", "access": 17, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6"], "descriptor": "(IIIIIII)V"}, {"method": "__p_235", "owner": "du", "name": "p", "access": 17, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7"], "descriptor": "(IIIIIIII)V"}, {"method": "__ag_236", "owner": "du", "name": "ag", "access": 16, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__aq_237", "owner": "du", "name": "aq", "access": 16, "parameters": ["arg0"], "descriptor": "(I)V"}], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 1, "descriptor": "([Ldu;I)V"}]}, {"class": "VertexNormal", "name": "dq", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "dq", "name": "f", "access": 0, "descriptor": "I", "decoder": -1493845293}, {"field": "__m", "owner": "dq", "name": "m", "access": 0, "descriptor": "I", "decoder": 1414310131}, {"field": "__q", "owner": "dq", "name": "q", "access": 0, "descriptor": "I", "decoder": 1575149515}, {"field": "__w", "owner": "dq", "name": "w", "access": 0, "descriptor": "I", "decoder": -487323631}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 0, "descriptor": "(Ldq;)V"}]}, {"class": "Scene", "name": "em", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "minPlane", "owner": "em", "name": "g", "access": 0, "descriptor": "I"}, {"field": "planes", "owner": "em", "name": "f", "access": 0, "descriptor": "I"}, {"field": "tempGameObjects", "owner": "em", "name": "e", "access": 0, "descriptor": "[Leh;"}, {"field": "tempGameObjectsCount", "owner": "em", "name": "l", "access": 0, "descriptor": "I"}, {"field": "tileHeights", "owner": "em", "name": "o", "access": 0, "descriptor": "[[[I"}, {"field": "tiles", "owner": "em", "name": "u", "access": 0, "descriptor": "[[[Ldj;"}, {"field": "xSize", "owner": "em", "name": "q", "access": 0, "descriptor": "I"}, {"field": "ySize", "owner": "em", "name": "w", "access": 0, "descriptor": "I"}, {"field": "__bd", "owner": "em", "name": "bd", "access": 0, "descriptor": "[[I"}, {"field": "__bk", "owner": "em", "name": "bk", "access": 0, "descriptor": "[[I"}, {"field": "__x", "owner": "em", "name": "x", "access": 0, "descriptor": "[[[I"}], "methods": [{"method": "addTile", "owner": "em", "name": "u", "access": 1, "descriptor": "(IIIIIIIIIIIIIIIIIIII)V"}, {"method": "clear", "owner": "em", "name": "m", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "clearTempGameObjects", "owner": "em", "name": "s", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "draw", "owner": "em", "name": "az", "access": 1, "parameters": ["x", "y", "z", "pitch", "yaw", "plane"], "descriptor": "(IIIIII)V"}, {"method": "drawTile", "owner": "em", "name": "au", "access": 0, "parameters": ["tile", "b"], "descriptor": "(Ldj;Z)V"}, {"method": "drawTileMinimap", "owner": "em", "name": "ae", "access": 1, "parameters": ["pixels", "index", "width", "plane", "x", "y"], "descriptor": "([IIIIII)V"}, {"method": "getBoundaryObject", "owner": "em", "name": "ag", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)Lej;"}, {"method": "getFloorDecoration", "owner": "em", "name": "av", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)Ldo;"}, {"method": "getFloorDecorationTag", "owner": "em", "name": "ah", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)J"}, {"method": "getObjectFlags", "owner": "em", "name": "ak", "access": 1, "parameters": ["plane", "x", "y", "id"], "descriptor": "(IIIJ)I"}, {"method": "getWallDecoration", "owner": "em", "name": "aq", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)Leq;"}, {"method": "init", "owner": "em", "name": "f", "access": 1, "parameters": ["minPlane"], "descriptor": "(I)V"}, {"method": "menuOpen", "owner": "em", "name": "ap", "access": 1, "parameters": ["plane", "screenX", "screenY", "b"], "descriptor": "(IIIZ)V"}, {"method": "newBoundaryObject", "owner": "em", "name": "e", "access": 1, "parameters": ["plane", "x", "y", "tileHeight", "entity1", "entity2", "orientationA", "orientationB", "tag", "flags"], "descriptor": "(IIIILex;Lex;IIJI)V"}, {"method": "newFloorDecoration", "owner": "em", "name": "g", "access": 1, "parameters": ["plane", "x", "y", "tileHeight", "entity", "tag", "flags"], "descriptor": "(IIIILex;JI)V"}, {"method": "newGameObject", "owner": "em", "name": "j", "access": 0, "parameters": ["plane", "startX", "startY", "sizeX", "sizeY", "centerX", "centerY", "height", "entity", "orientation", "isTemp", "tag", "flags"], "descriptor": "(IIIIIIIILex;IZJI)Z"}, {"method": "newGroundItemPile", "owner": "em", "name": "l", "access": 1, "parameters": ["plane", "x", "y", "tileHeight", "first", "tag", "second", "third"], "descriptor": "(IIIILex;JLex;Lex;)V"}, {"method": "newWallDecoration", "owner": "em", "name": "x", "access": 1, "parameters": ["plane", "x", "y", "tileHeight", "entity1", "entity2", "orientation", "int7", "xOffset", "yOffset", "tag", "flags"], "descriptor": "(IIIILex;Lex;IIIIJI)V"}, {"method": "occlude", "owner": "em", "name": "ai", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "removeBoundaryObject", "owner": "em", "name": "h", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)V"}, {"method": "removeFloorDecoration", "owner": "em", "name": "p", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)V"}, {"method": "removeGameObject", "owner": "em", "name": "t", "access": 0, "parameters": ["gameObject"], "descriptor": "(Leh;)V"}, {"method": "removeGroundItemPile", "owner": "em", "name": "v", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)V"}, {"method": "removeWallDecoration", "owner": "em", "name": "b", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(III)V"}, {"method": "setLinkBelow", "owner": "em", "name": "q", "access": 1, "parameters": ["x", "y"], "descriptor": "(II)V"}, {"method": "setTileMinPlane", "owner": "em", "name": "o", "access": 1, "parameters": ["plane", "x", "y", "minPlane"], "descriptor": "(IIII)V"}, {"method": "__d_238", "owner": "em", "name": "d", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9"], "descriptor": "(IIIIIILex;IJI)Z"}, {"method": "__a_239", "owner": "em", "name": "a", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8"], "descriptor": "(IIIIILex;IJZ)Z"}, {"method": "__z_240", "owner": "em", "name": "z", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9", "arg10", "arg11"], "descriptor": "(IIIIILex;IJIIII)Z"}, {"method": "__y_241", "owner": "em", "name": "y", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}, {"method": "__c_242", "owner": "em", "name": "c", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__aj_243", "owner": "em", "name": "aj", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)Leh;"}, {"method": "__ar_244", "owner": "em", "name": "ar", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)J"}, {"method": "__ac_245", "owner": "em", "name": "ac", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)J"}, {"method": "__ay_246", "owner": "em", "name": "ay", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)J"}, {"method": "__aw_247", "owner": "em", "name": "aw", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__al_248", "owner": "em", "name": "al", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(Ldw;III)V"}, {"method": "__ab_249", "owner": "em", "name": "ab", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(Ldw;IIIII)V"}, {"method": "__as_250", "owner": "em", "name": "as", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__ao_251", "owner": "em", "name": "ao", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7"], "descriptor": "(Leb;IIIIIII)V"}, {"method": "__aa_252", "owner": "em", "name": "aa", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6"], "descriptor": "(Ldv;IIIIII)V"}, {"method": "__ba_253", "owner": "em", "name": "ba", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)Z"}, {"method": "__bb_254", "owner": "em", "name": "bb", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)Z"}, {"method": "__bs_255", "owner": "em", "name": "bs", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)Z"}, {"method": "__bq_256", "owner": "em", "name": "bq", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIII)Z"}, {"method": "__bn_257", "owner": "em", "name": "bn", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)Z"}], "constructors": [{"access": 1, "descriptor": "(III[[[I)V"}]}, {"class": "FaceNormal", "name": "el", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "el", "name": "f", "access": 0, "descriptor": "I", "decoder": -1376860893}, {"field": "__m", "owner": "el", "name": "m", "access": 0, "descriptor": "I", "decoder": -2074333261}, {"field": "__q", "owner": "el", "name": "q", "access": 0, "descriptor": "I", "decoder": 2619977}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "TextureLoader", "name": "ei", "super": "java.lang.Object", "access": 1537, "interfaces": [], "fields": [], "methods": [{"method": "isLowDetail", "owner": "ei", "name": "u", "access": 1025, "descriptor": "(II)Z"}, {"method": "load", "owner": "ei", "name": "q", "access": 1025, "descriptor": "(II)[I"}, {"method": "__w_258", "owner": "ei", "name": "w", "access": 1025, "parameters": ["arg0"], "descriptor": "(II)I"}, {"method": "__o_259", "owner": "ei", "name": "o", "access": 1025, "parameters": ["arg0"], "descriptor": "(IB)Z"}], "constructors": []}, {"class": "Occluder", "name": "ee", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "maxTileX", "owner": "ee", "name": "f", "access": 0, "descriptor": "I", "decoder": -13700523}, {"field": "maxTileY", "owner": "ee", "name": "w", "access": 0, "descriptor": "I", "decoder": 1501977891}, {"field": "maxX", "owner": "ee", "name": "g", "access": 0, "descriptor": "I", "decoder": -2141305127}, {"field": "maxY", "owner": "ee", "name": "d", "access": 0, "descriptor": "I", "decoder": -1114046315}, {"field": "maxZ", "owner": "ee", "name": "e", "access": 0, "descriptor": "I", "decoder": -1805075909}, {"field": "minTileX", "owner": "ee", "name": "m", "access": 0, "descriptor": "I", "decoder": -691483983}, {"field": "minTileY", "owner": "ee", "name": "q", "access": 0, "descriptor": "I", "decoder": 768402657}, {"field": "minX", "owner": "ee", "name": "u", "access": 0, "descriptor": "I", "decoder": -1824663821}, {"field": "minY", "owner": "ee", "name": "x", "access": 0, "descriptor": "I", "decoder": 842961497}, {"field": "minZ", "owner": "ee", "name": "l", "access": 0, "descriptor": "I", "decoder": -41239751}, {"field": "type", "owner": "ee", "name": "o", "access": 0, "descriptor": "I", "decoder": -806776509}, {"field": "__a", "owner": "ee", "name": "a", "access": 0, "descriptor": "I", "decoder": -1527021367}, {"field": "__i", "owner": "ee", "name": "i", "access": 0, "descriptor": "I", "decoder": 1324959981}, {"field": "__j", "owner": "ee", "name": "j", "access": 0, "descriptor": "I", "decoder": -373839103}, {"field": "__k", "owner": "ee", "name": "k", "access": 0, "descriptor": "I", "decoder": 826041747}, {"field": "__n", "owner": "ee", "name": "n", "access": 0, "descriptor": "I", "decoder": -407777817}, {"field": "__s", "owner": "ee", "name": "s", "access": 0, "descriptor": "I", "decoder": -1334433969}, {"field": "__z", "owner": "ee", "name": "z", "access": 0, "descriptor": "I", "decoder": -570391007}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "GameObject", "name": "eh", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "centerX", "owner": "eh", "name": "w", "access": 0, "descriptor": "I", "decoder": 109300811}, {"field": "centerY", "owner": "eh", "name": "o", "access": 0, "descriptor": "I", "decoder": 714046631}, {"field": "endX", "owner": "eh", "name": "l", "access": 0, "descriptor": "I", "decoder": -379698951}, {"field": "endY", "owner": "eh", "name": "x", "access": 0, "descriptor": "I", "decoder": -607228629}, {"field": "entity", "owner": "eh", "name": "u", "access": 1, "descriptor": "Lex;"}, {"field": "flags", "owner": "eh", "name": "i", "access": 0, "descriptor": "I", "decoder": -161115901}, {"field": "height", "owner": "eh", "name": "q", "access": 0, "descriptor": "I", "decoder": -1791908691}, {"field": "lastDrawn", "owner": "eh", "name": "k", "access": 0, "descriptor": "I", "decoder": -1043687633}, {"field": "orientation", "owner": "eh", "name": "m", "access": 0, "descriptor": "I", "decoder": 1393658667}, {"field": "plane", "owner": "eh", "name": "f", "access": 0, "descriptor": "I", "decoder": 2095667225}, {"field": "startX", "owner": "eh", "name": "g", "access": 0, "descriptor": "I", "decoder": -1017836867}, {"field": "startY", "owner": "eh", "name": "e", "access": 0, "descriptor": "I", "decoder": -1519796265}, {"field": "tag", "owner": "eh", "name": "n", "access": 1, "descriptor": "J", "decoder": -114546124748632877}, {"field": "__d", "owner": "eh", "name": "d", "access": 0, "descriptor": "I", "decoder": 1941106389}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "UrlRe<PERSON><PERSON>", "name": "eo", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "isClosed", "owner": "eo", "name": "f", "access": 64, "descriptor": "Z"}, {"field": "requests", "owner": "eo", "name": "q", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/Queue;"}, {"field": "thread", "owner": "eo", "name": "m", "access": 16, "descriptor": "<PERSON><PERSON><PERSON>/lang/Thread;"}], "methods": [{"method": "close", "owner": "eo", "name": "f", "access": 1, "parameters": [], "descriptor": "(S)V"}, {"method": "request", "owner": "eo", "name": "m", "access": 1, "parameters": ["url"], "descriptor": "(Ljava/net/URL;I)Ley;"}, {"method": "__run_260", "owner": "eo", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "EvictingDualNodeHashTable", "name": "er", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "capacity", "owner": "er", "name": "f", "access": 0, "descriptor": "I"}, {"field": "deque", "owner": "er", "name": "o", "access": 0, "descriptor": "Ljp;"}, {"field": "hashTable", "owner": "er", "name": "w", "access": 0, "descriptor": "Lld;"}, {"field": "remainingCapacity", "owner": "er", "name": "q", "access": 0, "descriptor": "I"}, {"field": "__m", "owner": "er", "name": "m", "access": 0, "descriptor": "Lfn;"}], "methods": [{"method": "clear", "owner": "er", "name": "w", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "get", "owner": "er", "name": "m", "access": 1, "parameters": ["key"], "descriptor": "(J)Lfn;"}, {"method": "put", "owner": "er", "name": "q", "access": 1, "parameters": ["value", "key"], "descriptor": "(Lfn;J)V"}, {"method": "remove", "owner": "er", "name": "f", "access": 1, "parameters": ["key"], "descriptor": "(J)V"}], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "UserComparator4", "name": "ev", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Comparator"], "fields": [{"field": "__m", "owner": "ev", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_261", "owner": "ev", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>)I"}, {"method": "__compare_262", "owner": "ev", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_263", "owner": "ev", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator9", "name": "ea", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "ea", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_264", "owner": "ea", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>)I"}, {"method": "__compare_265", "owner": "ea", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator8", "name": "es", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "es", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_266", "owner": "es", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON>ke;B)I"}, {"method": "__compare_267", "owner": "es", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator10", "name": "eg", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "eg", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_268", "owner": "eg", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON>ke;B)I"}, {"method": "__compare_269", "owner": "eg", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator7", "name": "ez", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "ez", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_270", "owner": "ez", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>)I"}, {"method": "__compare_271", "owner": "ez", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator5", "name": "ep", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "ep", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_272", "owner": "ep", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON>ke;B)I"}, {"method": "__compare_273", "owner": "ep", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator3", "name": "fq", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "fq", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_274", "owner": "fq", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>)I"}, {"method": "__compare_275", "owner": "fq", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator6", "name": "fg", "super": "kf", "access": 33, "interfaces": [], "fields": [{"field": "__m", "owner": "fg", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_276", "owner": "fg", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON>;<PERSON><PERSON>;<PERSON>)I"}, {"method": "__compare_277", "owner": "fg", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON>", "name": "fd", "super": "fa", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "fd", "name": "f", "access": 0, "descriptor": "I", "decoder": 1940981129}, {"field": "__o", "owner": "fd", "name": "o", "access": 0, "descriptor": "I", "decoder": 250272357}, {"field": "__q", "owner": "fd", "name": "q", "access": 0, "descriptor": "I", "decoder": 1217137493}, {"field": "__u", "owner": "fd", "name": "u", "access": 0, "descriptor": "I", "decoder": 576555527}, {"field": "__w", "owner": "fd", "name": "w", "access": 0, "descriptor": "J", "decoder": -8372744267470318611}, {"field": "__m", "owner": "fd", "name": "m", "access": 0, "descriptor": "[J"}], "methods": [{"method": "mark", "owner": "fd", "name": "m", "access": 1, "parameters": [], "descriptor": "(B)V"}, {"method": "wait", "owner": "fd", "name": "f", "access": 1, "parameters": ["cycleMs", "minSleepMs"], "descriptor": "(III)I"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "BufferedSource", "name": "fh", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "buffer", "owner": "fh", "name": "w", "access": 0, "descriptor": "[B"}, {"field": "capacity", "owner": "fh", "name": "q", "access": 0, "descriptor": "I", "decoder": -1970062431}, {"field": "exception", "owner": "fh", "name": "g", "access": 0, "descriptor": "Ljava/io/IOException;"}, {"field": "inputStream", "owner": "fh", "name": "f", "access": 0, "descriptor": "Ljava/io/InputStream;"}, {"field": "limit", "owner": "fh", "name": "u", "access": 0, "descriptor": "I", "decoder": 1507905677}, {"field": "position", "owner": "fh", "name": "o", "access": 0, "descriptor": "I", "decoder": 267006153}, {"field": "thread", "owner": "fh", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Thread;"}], "methods": [{"method": "available", "owner": "fh", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "close", "owner": "fh", "name": "o", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "isAvailable", "owner": "fh", "name": "m", "access": 0, "parameters": ["length"], "descriptor": "(II)Z"}, {"method": "read", "owner": "fh", "name": "w", "access": 0, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIIB)I"}, {"method": "readUnsignedByte", "owner": "fh", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "__run_278", "owner": "fh", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/io/InputStream;I)V"}]}, {"class": "TaskHandler", "name": "fz", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "current", "owner": "fz", "name": "q", "access": 0, "descriptor": "Lfm;"}, {"field": "isClosed", "owner": "fz", "name": "u", "access": 0, "descriptor": "Z"}, {"field": "task0", "owner": "fz", "name": "w", "access": 0, "descriptor": "Lfm;"}, {"field": "thread", "owner": "fz", "name": "o", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Thread;"}], "methods": [{"method": "close", "owner": "fz", "name": "m", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "newSocketTask", "owner": "fz", "name": "q", "access": 17, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;II)Lfm;"}, {"method": "newTask", "owner": "fz", "name": "f", "access": 16, "descriptor": "(III<PERSON><PERSON><PERSON>/lang/Object;I)Lfm;"}, {"method": "newThreadTask", "owner": "fz", "name": "w", "access": 17, "descriptor": "(<PERSON><PERSON><PERSON>/lang/Runnable;II)Lfm;"}, {"method": "__run_279", "owner": "fz", "name": "run", "access": 17, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "BufferedNetSocket", "name": "ft", "super": "fu", "access": 33, "interfaces": [], "fields": [{"field": "sink", "owner": "ft", "name": "q", "access": 0, "descriptor": "Lfr;"}, {"field": "socket", "owner": "ft", "name": "m", "access": 0, "descriptor": "Ljava/net/Socket;"}, {"field": "source", "owner": "ft", "name": "f", "access": 0, "descriptor": "Lfh;"}], "methods": [{"method": "available", "owner": "ft", "name": "q", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "close", "owner": "ft", "name": "g", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "isAvailable", "owner": "ft", "name": "f", "access": 1, "parameters": ["length"], "descriptor": "(II)Z"}, {"method": "read", "owner": "ft", "name": "o", "access": 1, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIII)I"}, {"method": "readUnsignedByte", "owner": "ft", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "write", "owner": "ft", "name": "u", "access": 1, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "__finalize_280", "owner": "ft", "name": "finalize", "access": 4, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Ljava/net/Socket;II)V"}]}, {"class": "NetSocket", "name": "fb", "super": "fu", "access": 49, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "array", "owner": "fb", "name": "g", "access": 0, "descriptor": "[B"}, {"field": "exceptionWriting", "owner": "fb", "name": "x", "access": 0, "descriptor": "Z"}, {"field": "inputStream", "owner": "fb", "name": "m", "access": 0, "descriptor": "Ljava/io/InputStream;"}, {"field": "isClosed", "owner": "fb", "name": "w", "access": 0, "descriptor": "Z"}, {"field": "outputStream", "owner": "fb", "name": "f", "access": 0, "descriptor": "Ljava/io/OutputStream;"}, {"field": "socket", "owner": "fb", "name": "q", "access": 0, "descriptor": "Ljava/net/Socket;"}, {"field": "task", "owner": "fb", "name": "u", "access": 0, "descriptor": "Lfm;"}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "fb", "name": "o", "access": 0, "descriptor": "Lfz;"}, {"field": "__e", "owner": "fb", "name": "e", "access": 0, "descriptor": "I", "decoder": 2057244853}, {"field": "__l", "owner": "fb", "name": "l", "access": 0, "descriptor": "I", "decoder": -738185459}, {"field": "__d", "owner": "fb", "name": "d", "access": 16, "descriptor": "I", "decoder": -1644762957}, {"field": "__k", "owner": "fb", "name": "k", "access": 16, "descriptor": "I", "decoder": -782964015}], "methods": [{"method": "available", "owner": "fb", "name": "q", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "close", "owner": "fb", "name": "g", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "finalize", "owner": "fb", "name": "finalize", "access": 4, "descriptor": "()V"}, {"method": "isAvailable", "owner": "fb", "name": "f", "access": 1, "parameters": ["length"], "descriptor": "(II)Z"}, {"method": "read", "owner": "fb", "name": "o", "access": 1, "parameters": ["dst", "dstIndex", "length"], "descriptor": "([BIII)I"}, {"method": "readUnsignedByte", "owner": "fb", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "write", "owner": "fb", "name": "u", "access": 1, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "write0", "owner": "fb", "name": "ay", "access": 0, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "__run_281", "owner": "fb", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "(Ljava/net/Socket;Lfz;I)V"}]}, {"class": "BufferedSink", "name": "fr", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [{"field": "buffer", "owner": "fr", "name": "w", "access": 0, "descriptor": "[B"}, {"field": "capacity", "owner": "fr", "name": "q", "access": 0, "descriptor": "I", "decoder": 1805563727}, {"field": "exception", "owner": "fr", "name": "g", "access": 0, "descriptor": "Ljava/io/IOException;"}, {"field": "isClosed0", "owner": "fr", "name": "l", "access": 0, "descriptor": "Z"}, {"field": "limit", "owner": "fr", "name": "u", "access": 0, "descriptor": "I", "decoder": 1028856279}, {"field": "outputStream", "owner": "fr", "name": "f", "access": 0, "descriptor": "Ljava/io/OutputStream;"}, {"field": "position", "owner": "fr", "name": "o", "access": 0, "descriptor": "I", "decoder": 315461309}, {"field": "thread", "owner": "fr", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/Thread;"}], "methods": [{"method": "close", "owner": "fr", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "isClosed", "owner": "fr", "name": "m", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "write", "owner": "fr", "name": "f", "access": 0, "parameters": ["src", "srcIndex", "length"], "descriptor": "([BIIB)V"}, {"method": "__run_282", "owner": "fr", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Ljava/io/OutputStream;I)V"}]}, {"class": "CollisionMap", "name": "fy", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "flags", "owner": "fy", "name": "ab", "access": 1, "descriptor": "[[I"}, {"field": "xInset", "owner": "fy", "name": "ah", "access": 0, "descriptor": "I", "decoder": 264774249}, {"field": "xSize", "owner": "fy", "name": "aw", "access": 0, "descriptor": "I", "decoder": 674483785}, {"field": "yInset", "owner": "fy", "name": "ak", "access": 0, "descriptor": "I", "decoder": -1037712787}, {"field": "ySize", "owner": "fy", "name": "al", "access": 0, "descriptor": "I", "decoder": -871458249}], "methods": [{"method": "clear", "owner": "fy", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__f_283", "owner": "fy", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIIZB)V"}, {"method": "__q_284", "owner": "fy", "name": "q", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIIZI)V"}, {"method": "__w_285", "owner": "fy", "name": "w", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__o_286", "owner": "fy", "name": "o", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__u_287", "owner": "fy", "name": "u", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)V"}, {"method": "__g_288", "owner": "fy", "name": "g", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIIZB)V"}, {"method": "__l_289", "owner": "fy", "name": "l", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIIZB)V"}, {"method": "__e_290", "owner": "fy", "name": "e", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)V"}, {"method": "__x_291", "owner": "fy", "name": "x", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)V"}], "constructors": [{"access": 1, "descriptor": "(II)V"}]}, {"class": "Link", "name": "gh", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "gh", "name": "f", "access": 1, "descriptor": "Lgh;"}, {"field": "__m", "owner": "gh", "name": "m", "access": 1, "descriptor": "Lgh;"}], "methods": [{"method": "remove", "owner": "gh", "name": "m", "access": 1, "descriptor": "()V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "PacketBufferNode", "name": "gg", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "packetBuffer", "owner": "gg", "name": "q", "access": 1, "descriptor": "Lhx;"}, {"field": "__m", "owner": "gg", "name": "m", "access": 1, "descriptor": "Lgs;"}, {"field": "__f", "owner": "gg", "name": "f", "access": 1, "descriptor": "I", "decoder": 1433542775}, {"field": "__w", "owner": "gg", "name": "w", "access": 1, "descriptor": "I", "decoder": -2051819203}], "methods": [{"method": "__f_292", "owner": "gg", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON>", "name": "gd", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "gd", "name": "f", "access": 0, "descriptor": "[B"}, {"field": "__m", "owner": "gd", "name": "m", "access": 0, "descriptor": "[I"}, {"field": "__q", "owner": "gd", "name": "q", "access": 0, "descriptor": "[I"}], "methods": [{"method": "__m_293", "owner": "gd", "name": "m", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "([BII[BIB)I"}, {"method": "__f_294", "owner": "gd", "name": "f", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "([BI[BIII)I"}], "constructors": [{"access": 1, "descriptor": "([B)V"}]}, {"class": "Bzip2State", "name": "gt", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "__ag", "owner": "gt", "name": "ag", "access": 0, "descriptor": "[Z"}, {"field": "__aq", "owner": "gt", "name": "aq", "access": 0, "descriptor": "[Z"}, {"field": "__i", "owner": "gt", "name": "i", "access": 0, "descriptor": "B"}, {"field": "__ac", "owner": "gt", "name": "ac", "access": 0, "descriptor": "[B"}, {"field": "__aj", "owner": "gt", "name": "aj", "access": 0, "descriptor": "[B"}, {"field": "__av", "owner": "gt", "name": "av", "access": 0, "descriptor": "[B"}, {"field": "__ay", "owner": "gt", "name": "ay", "access": 0, "descriptor": "[B"}, {"field": "__g", "owner": "gt", "name": "g", "access": 0, "descriptor": "[B"}, {"field": "__x", "owner": "gt", "name": "x", "access": 0, "descriptor": "[B"}, {"field": "__ah", "owner": "gt", "name": "ah", "access": 0, "descriptor": "[[B"}, {"field": "__a", "owner": "gt", "name": "a", "access": 0, "descriptor": "I", "decoder": -1994129645}, {"field": "__ae", "owner": "gt", "name": "ae", "access": 0, "descriptor": "I", "decoder": 444639767}, {"field": "__c", "owner": "gt", "name": "c", "access": 0, "descriptor": "I", "decoder": -557334365}, {"field": "__d", "owner": "gt", "name": "d", "access": 0, "descriptor": "I", "decoder": -861462251}, {"field": "__e", "owner": "gt", "name": "e", "access": 0, "descriptor": "I", "decoder": -473971741}, {"field": "__h", "owner": "gt", "name": "h", "access": 0, "descriptor": "I", "decoder": -1981923611}, {"field": "__j", "owner": "gt", "name": "j", "access": 0, "descriptor": "I", "decoder": -1987934943}, {"field": "__k", "owner": "gt", "name": "k", "access": 0, "descriptor": "I", "decoder": 594179173}, {"field": "__l", "owner": "gt", "name": "l", "access": 0, "descriptor": "I", "decoder": -1510817847}, {"field": "__n", "owner": "gt", "name": "n", "access": 0, "descriptor": "I", "decoder": 1860254567}, {"field": "__s", "owner": "gt", "name": "s", "access": 0, "descriptor": "I"}, {"field": "__t", "owner": "gt", "name": "t", "access": 0, "descriptor": "I", "decoder": 1502359789}, {"field": "__v", "owner": "gt", "name": "v", "access": 0, "descriptor": "I", "decoder": -886333045}, {"field": "__y", "owner": "gt", "name": "y", "access": 0, "descriptor": "I", "decoder": -953353963}, {"field": "__z", "owner": "gt", "name": "z", "access": 0, "descriptor": "I", "decoder": -1793367959}, {"field": "__ab", "owner": "gt", "name": "ab", "access": 0, "descriptor": "[I"}, {"field": "__ar", "owner": "gt", "name": "ar", "access": 0, "descriptor": "[I"}, {"field": "__b", "owner": "gt", "name": "b", "access": 0, "descriptor": "[I"}, {"field": "__r", "owner": "gt", "name": "r", "access": 0, "descriptor": "[I"}, {"field": "__ak", "owner": "gt", "name": "ak", "access": 0, "descriptor": "[[I"}, {"field": "__al", "owner": "gt", "name": "al", "access": 0, "descriptor": "[[I"}, {"field": "__aw", "owner": "gt", "name": "aw", "access": 0, "descriptor": "[[I"}, {"field": "__f", "owner": "gt", "name": "f", "access": 16, "descriptor": "I"}, {"field": "__m", "owner": "gt", "name": "m", "access": 16, "descriptor": "I"}, {"field": "__o", "owner": "gt", "name": "o", "access": 16, "descriptor": "I"}, {"field": "__q", "owner": "gt", "name": "q", "access": 16, "descriptor": "I"}, {"field": "__u", "owner": "gt", "name": "u", "access": 16, "descriptor": "I"}, {"field": "__w", "owner": "gt", "name": "w", "access": 16, "descriptor": "I"}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "<PERSON><PERSON><PERSON>", "name": "gr", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "array", "owner": "gr", "name": "m", "access": 1, "descriptor": "[B"}, {"field": "index", "owner": "gr", "name": "f", "access": 1, "descriptor": "I", "decoder": -271438207}], "methods": [{"method": "encryptRsa", "owner": "gr", "name": "ao", "access": 1, "descriptor": "(<PERSON>ja<PERSON>/math/BigInteger;Ljava/math/BigInteger;I)V"}, {"method": "readBoolean", "owner": "gr", "name": "ac", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "readByte", "owner": "gr", "name": "v", "access": 1, "parameters": [], "descriptor": "(I)B"}, {"method": "readInt", "owner": "gr", "name": "av", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "readLong", "owner": "gr", "name": "ar", "access": 1, "parameters": [], "descriptor": "(I)J"}, {"method": "readMedium", "owner": "gr", "name": "aj", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "readStringCp1252NullCircumfixed", "owner": "gr", "name": "ak", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "readStringCp1252NullTerminated", "owner": "gr", "name": "ah", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "readStringCp1252NullTerminatedOrNull", "owner": "gr", "name": "ay", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "readUnsignedByte", "owner": "gr", "name": "p", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "readUnsignedByteNegate", "owner": "gr", "name": "bs", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "writeBoolean", "owner": "gr", "name": "e", "access": 1, "parameters": ["value"], "descriptor": "(ZB)V"}, {"method": "writeByte", "owner": "gr", "name": "q", "access": 1, "parameters": ["value"], "descriptor": "(II)V"}, {"method": "writeInt", "owner": "gr", "name": "u", "access": 1, "parameters": ["value"], "descriptor": "(II)V"}, {"method": "writeIntLE", "owner": "gr", "name": "bi", "access": 1, "parameters": ["value"], "descriptor": "(IB)V"}, {"method": "writeIntLE16", "owner": "gr", "name": "bg", "access": 1, "parameters": ["value"], "descriptor": "(II)V"}, {"method": "writeShortLE", "owner": "gr", "name": "bz", "access": 1, "parameters": ["value"], "descriptor": "(IB)V"}, {"method": "writeLong", "owner": "gr", "name": "l", "access": 1, "parameters": ["n"], "descriptor": "(J)V"}, {"method": "writeLongMedium", "owner": "gr", "name": "g", "access": 1, "parameters": ["n"], "descriptor": "(J)V"}, {"method": "writeMedium", "owner": "gr", "name": "o", "access": 1, "parameters": ["value"], "descriptor": "(IB)V"}, {"method": "writeShort", "owner": "gr", "name": "w", "access": 1, "parameters": ["value"], "descriptor": "(IB)V"}, {"method": "writeShortLE", "owner": "gr", "name": "bc", "access": 1, "parameters": ["value"], "descriptor": "(IB)V"}, {"method": "writeSmartByteShort", "owner": "gr", "name": "b", "access": 1, "parameters": ["value"], "descriptor": "(II)V"}, {"method": "writeStringCp1252NullCircumfixed", "owner": "gr", "name": "z", "access": 1, "parameters": ["string"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;S)V"}, {"method": "writeStringCp1252NullTerminated", "owner": "gr", "name": "d", "access": 1, "parameters": ["string"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;B)V"}, {"method": "xteaDecrypt", "owner": "gr", "name": "au", "access": 1, "parameters": ["xteaKey", "start", "end"], "descriptor": "([IIII)V"}, {"method": "xteaDecryptAll", "owner": "gr", "name": "an", "access": 1, "parameters": ["xteaKey"], "descriptor": "([II)V"}, {"method": "xteaEncrypt", "owner": "gr", "name": "az", "access": 1, "parameters": ["xteaKey", "start", "end"], "descriptor": "([IIII)V"}, {"method": "xteaEncryptAll", "owner": "gr", "name": "am", "access": 1, "parameters": ["xteaKey"], "descriptor": "([IB)V"}, {"method": "__f_295", "owner": "gr", "name": "f", "access": 1, "parameters": [], "descriptor": "(S)V"}, {"method": "__j_296", "owner": "gr", "name": "j", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/CharSequence;B)V"}, {"method": "__s_297", "owner": "gr", "name": "s", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([BIII)V"}, {"method": "__t_298", "owner": "gr", "name": "t", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__y_299", "owner": "gr", "name": "y", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__h_300", "owner": "gr", "name": "h", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__c_301", "owner": "gr", "name": "c", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ag_302", "owner": "gr", "name": "ag", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__aq_303", "owner": "gr", "name": "aq", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__aw_304", "owner": "gr", "name": "aw", "access": 1, "parameters": [], "descriptor": "(B)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "__al_305", "owner": "gr", "name": "al", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([BIIB)V"}, {"method": "__ab_306", "owner": "gr", "name": "ab", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__ae_307", "owner": "gr", "name": "ae", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__at_308", "owner": "gr", "name": "at", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__ad_309", "owner": "gr", "name": "ad", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__ap_310", "owner": "gr", "name": "ap", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__as_311", "owner": "gr", "name": "as", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__aa_312", "owner": "gr", "name": "aa", "access": 1, "parameters": ["arg0"], "descriptor": "(II)I"}, {"method": "__ax_313", "owner": "gr", "name": "ax", "access": 1, "parameters": [], "descriptor": "(B)Z"}, {"method": "__af_314", "owner": "gr", "name": "af", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)V"}, {"method": "__ai_315", "owner": "gr", "name": "ai", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ba_316", "owner": "gr", "name": "ba", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)V"}, {"method": "__bb_317", "owner": "gr", "name": "bb", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bq_318", "owner": "gr", "name": "bq", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bn_319", "owner": "gr", "name": "bn", "access": 1, "parameters": [], "descriptor": "(I)B"}, {"method": "__bk_320", "owner": "gr", "name": "bk", "access": 1, "parameters": [], "descriptor": "(I)B"}, {"method": "__bd_321", "owner": "gr", "name": "bd", "access": 1, "parameters": [], "descriptor": "(I)B"}, {"method": "__bo_322", "owner": "gr", "name": "bo", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__bx_323", "owner": "gr", "name": "bx", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)V"}, {"method": "__by_324", "owner": "gr", "name": "by", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bu_325", "owner": "gr", "name": "bu", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bm_326", "owner": "gr", "name": "bm", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__bl_327", "owner": "gr", "name": "bl", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__br_328", "owner": "gr", "name": "br", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bj_329", "owner": "gr", "name": "bj", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bt_330", "owner": "gr", "name": "bt", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bp_331", "owner": "gr", "name": "bp", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__bf_332", "owner": "gr", "name": "bf", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "__bh_333", "owner": "gr", "name": "bh", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([BIII)V"}], "constructors": [{"access": 1, "descriptor": "(I)V"}, {"access": 1, "descriptor": "([B)V"}]}, {"class": "PacketBuffer", "name": "hx", "super": "gr", "access": 49, "interfaces": [], "fields": [{"field": "bitIndex", "owner": "hx", "name": "d", "access": 0, "descriptor": "I", "decoder": -1048718919}, {"field": "isaacCipher0", "owner": "hx", "name": "e", "access": 0, "descriptor": "Lhn;"}], "methods": [{"method": "bitsRemaining", "owner": "hx", "name": "jr", "access": 1, "parameters": ["index"], "descriptor": "(II)I"}, {"method": "exportIndex", "owner": "hx", "name": "jm", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "importIndex", "owner": "hx", "name": "jq", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "newIsaacCipher", "owner": "hx", "name": "io", "access": 1, "parameters": ["array"], "descriptor": "([II)V"}, {"method": "readBits", "owner": "hx", "name": "jw", "access": 1, "parameters": ["bits"], "descriptor": "(II)I"}, {"method": "readByteIsaac", "owner": "hx", "name": "ii", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "readSmartByteShortIsaac", "owner": "hx", "name": "ia", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "setIsaacCipher", "owner": "hx", "name": "ih", "access": 1, "parameters": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "descriptor": "(<PERSON>hn;B)V"}, {"method": "writeByteIsaac", "owner": "hx", "name": "im", "access": 1, "parameters": ["b"], "descriptor": "(IS)V"}, {"method": "__ig_334", "owner": "hx", "name": "ig", "access": 1, "parameters": [], "descriptor": "(B)Z"}, {"method": "__jb_335", "owner": "hx", "name": "jb", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([BIII)V"}], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "<PERSON><PERSON><PERSON><PERSON>", "name": "hn", "super": "java.lang.Object", "access": 49, "interfaces": [], "fields": [{"field": "__e", "owner": "hn", "name": "e", "access": 0, "descriptor": "I", "decoder": -689202647}, {"field": "__g", "owner": "hn", "name": "g", "access": 0, "descriptor": "I", "decoder": -1670027699}, {"field": "__l", "owner": "hn", "name": "l", "access": 0, "descriptor": "I", "decoder": -325762649}, {"field": "__w", "owner": "hn", "name": "w", "access": 0, "descriptor": "I", "decoder": 1514845493}, {"field": "__o", "owner": "hn", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "__u", "owner": "hn", "name": "u", "access": 0, "descriptor": "[I"}], "methods": [{"method": "__m_336", "owner": "hn", "name": "m", "access": 16, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_337", "owner": "hn", "name": "f", "access": 16, "parameters": [], "descriptor": "(I)I"}, {"method": "__q_338", "owner": "hn", "name": "q", "access": 16, "parameters": [], "descriptor": "(B)V"}, {"method": "__w_339", "owner": "hn", "name": "w", "access": 16, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 1, "descriptor": "([I)V"}]}, {"class": "MusicPatchNode2", "name": "hd", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "hd", "name": "f", "access": 0, "descriptor": "[B"}, {"field": "__m", "owner": "hd", "name": "m", "access": 0, "descriptor": "[B"}, {"field": "__e", "owner": "hd", "name": "e", "access": 0, "descriptor": "I", "decoder": 1984131473}, {"field": "__g", "owner": "hd", "name": "g", "access": 0, "descriptor": "I", "decoder": 391670135}, {"field": "__l", "owner": "hd", "name": "l", "access": 0, "descriptor": "I", "decoder": 320761691}, {"field": "__o", "owner": "hd", "name": "o", "access": 0, "descriptor": "I", "decoder": -1455145595}, {"field": "__q", "owner": "hd", "name": "q", "access": 0, "descriptor": "I", "decoder": 140046797}, {"field": "__u", "owner": "hd", "name": "u", "access": 0, "descriptor": "I", "decoder": 1577527995}, {"field": "__w", "owner": "hd", "name": "w", "access": 0, "descriptor": "I", "decoder": -249046051}], "methods": [], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "MidiPcmStream", "name": "hm", "super": "dc", "access": 33, "interfaces": [], "fields": [{"field": "midiFile", "owner": "hm", "name": "aq", "access": 0, "descriptor": "Lhs;"}, {"field": "musicPatches", "owner": "hm", "name": "m", "access": 0, "descriptor": "Lld;"}, {"field": "patchStream", "owner": "hm", "name": "ah", "access": 0, "descriptor": "Lhq;"}, {"field": "track", "owner": "hm", "name": "av", "access": 0, "descriptor": "I", "decoder": 1450922835}, {"field": "trackLength", "owner": "hm", "name": "ar", "access": 0, "descriptor": "I", "decoder": 713752539}, {"field": "__aj", "owner": "hm", "name": "aj", "access": 0, "descriptor": "Z"}, {"field": "__ag", "owner": "hm", "name": "ag", "access": 0, "descriptor": "[[Lha;"}, {"field": "__v", "owner": "hm", "name": "v", "access": 0, "descriptor": "[[Lha;"}, {"field": "__f", "owner": "hm", "name": "f", "access": 0, "descriptor": "I", "decoder": 335918727}, {"field": "__q", "owner": "hm", "name": "q", "access": 0, "descriptor": "I", "decoder": 2095480747}, {"field": "__b", "owner": "hm", "name": "b", "access": 0, "descriptor": "[I"}, {"field": "__c", "owner": "hm", "name": "c", "access": 0, "descriptor": "[I"}, {"field": "__d", "owner": "hm", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "__e", "owner": "hm", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "__g", "owner": "hm", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "__h", "owner": "hm", "name": "h", "access": 0, "descriptor": "[I"}, {"field": "__k", "owner": "hm", "name": "k", "access": 0, "descriptor": "[I"}, {"field": "__l", "owner": "hm", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "__n", "owner": "hm", "name": "n", "access": 0, "descriptor": "[I"}, {"field": "__o", "owner": "hm", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "__p", "owner": "hm", "name": "p", "access": 0, "descriptor": "[I"}, {"field": "__r", "owner": "hm", "name": "r", "access": 0, "descriptor": "[I"}, {"field": "__u", "owner": "hm", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "__x", "owner": "hm", "name": "x", "access": 0, "descriptor": "[I"}, {"field": "__y", "owner": "hm", "name": "y", "access": 0, "descriptor": "[I"}, {"field": "__ac", "owner": "hm", "name": "ac", "access": 0, "descriptor": "J", "decoder": -7547625335559871937}, {"field": "__ay", "owner": "hm", "name": "ay", "access": 0, "descriptor": "J", "decoder": 3736036110499628937}], "methods": [{"method": "clear", "owner": "hm", "name": "a", "access": 33, "parameters": [], "descriptor": "(I)V"}, {"method": "clearAll", "owner": "hm", "name": "w", "access": 33, "parameters": [], "descriptor": "(B)V"}, {"method": "isReady", "owner": "hm", "name": "z", "access": 33, "parameters": [], "descriptor": "(I)Z"}, {"method": "loadMusicTrack", "owner": "hm", "name": "q", "access": 33, "parameters": ["musicTrack", "i", "s", "frequency"], "descriptor": "(<PERSON>he;Lir;Ldt;II)Z"}, {"method": "removeAll", "owner": "hm", "name": "o", "access": 33, "parameters": [], "descriptor": "(B)V"}, {"method": "setMusicTrack", "owner": "hm", "name": "x", "access": 33, "parameters": ["musicTrack", "b"], "descriptor": "(Lhe;ZI)V"}, {"method": "__m_340", "owner": "hm", "name": "m", "access": 33, "parameters": ["arg0"], "descriptor": "(IB)V"}, {"method": "__f_341", "owner": "hm", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__j_342", "owner": "hm", "name": "j", "access": 33, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__s_343", "owner": "hm", "name": "s", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__t_344", "owner": "hm", "name": "t", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__y_345", "owner": "hm", "name": "y", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)V"}, {"method": "__h_346", "owner": "hm", "name": "h", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lha;ZI)V"}, {"method": "__b_347", "owner": "hm", "name": "b", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)V"}, {"method": "__c_348", "owner": "hm", "name": "c", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)V"}, {"method": "__p_349", "owner": "hm", "name": "p", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)V"}, {"method": "__v_350", "owner": "hm", "name": "v", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)V"}, {"method": "__ah_351", "owner": "hm", "name": "ah", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ab_352", "owner": "hm", "name": "ab", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ae_353", "owner": "hm", "name": "ae", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__at_354", "owner": "hm", "name": "at", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__ad_355", "owner": "hm", "name": "ad", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ap_356", "owner": "hm", "name": "ap", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__au_357", "owner": "hm", "name": "au", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__ao_358", "owner": "hm", "name": "ao", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__aa_359", "owner": "hm", "name": "aa", "access": 0, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON>;I)I"}, {"method": "__ax_360", "owner": "hm", "name": "ax", "access": 0, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON>;I)I"}, {"method": "__af_361", "owner": "hm", "name": "af", "access": 0, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON>;I)I"}, {"method": "__u_362", "owner": "hm", "name": "u", "access": 36, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__g_363", "owner": "hm", "name": "g", "access": 36, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__l_364", "owner": "hm", "name": "l", "access": 36, "parameters": [], "descriptor": "()I"}, {"method": "__e_365", "owner": "hm", "name": "e", "access": 36, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([III)V"}, {"method": "__d_366", "owner": "hm", "name": "d", "access": 36, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__ai_367", "owner": "hm", "name": "ai", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "__ba_368", "owner": "hm", "name": "ba", "access": 0, "parameters": ["arg0"], "descriptor": "(Lha;I)Z"}, {"method": "__bb_369", "owner": "hm", "name": "bb", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(Lha;[IIII)Z"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "MusicPatchNode", "name": "ha", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "patch", "owner": "ha", "name": "f", "access": 0, "descriptor": "Lhl;"}, {"field": "rawSound", "owner": "ha", "name": "q", "access": 0, "descriptor": "Lcx;"}, {"field": "stream", "owner": "ha", "name": "t", "access": 0, "descriptor": "Lds;"}, {"field": "__w", "owner": "ha", "name": "w", "access": 0, "descriptor": "Lhd;"}, {"field": "__a", "owner": "ha", "name": "a", "access": 0, "descriptor": "I", "decoder": -2083252379}, {"field": "__b", "owner": "ha", "name": "b", "access": 0, "descriptor": "I", "decoder": 1359854411}, {"field": "__d", "owner": "ha", "name": "d", "access": 0, "descriptor": "I", "decoder": -852988281}, {"field": "__e", "owner": "ha", "name": "e", "access": 0, "descriptor": "I", "decoder": -471899285}, {"field": "__g", "owner": "ha", "name": "g", "access": 0, "descriptor": "I", "decoder": -836502917}, {"field": "__i", "owner": "ha", "name": "i", "access": 0, "descriptor": "I", "decoder": -2136855685}, {"field": "__j", "owner": "ha", "name": "j", "access": 0, "descriptor": "I", "decoder": 173672931}, {"field": "__k", "owner": "ha", "name": "k", "access": 0, "descriptor": "I", "decoder": -439388365}, {"field": "__l", "owner": "ha", "name": "l", "access": 0, "descriptor": "I", "decoder": -308599455}, {"field": "__m", "owner": "ha", "name": "m", "access": 0, "descriptor": "I", "decoder": 801157533}, {"field": "__n", "owner": "ha", "name": "n", "access": 0, "descriptor": "I", "decoder": 476852577}, {"field": "__o", "owner": "ha", "name": "o", "access": 0, "descriptor": "I", "decoder": -2058553171}, {"field": "__s", "owner": "ha", "name": "s", "access": 0, "descriptor": "I", "decoder": 219840779}, {"field": "__u", "owner": "ha", "name": "u", "access": 0, "descriptor": "I", "decoder": 62958303}, {"field": "__x", "owner": "ha", "name": "x", "access": 0, "descriptor": "I", "decoder": -1520706641}, {"field": "__y", "owner": "ha", "name": "y", "access": 0, "descriptor": "I", "decoder": -666877449}, {"field": "__z", "owner": "ha", "name": "z", "access": 0, "descriptor": "I", "decoder": 1605236771}], "methods": [{"method": "__m_370", "owner": "ha", "name": "m", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "MidiFileReader", "name": "hs", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "buffer", "owner": "hs", "name": "m", "access": 0, "descriptor": "Lgr;"}, {"field": "division", "owner": "hs", "name": "f", "access": 0, "descriptor": "I"}, {"field": "trackLengths", "owner": "hs", "name": "o", "access": 0, "descriptor": "[I"}, {"field": "trackPositions", "owner": "hs", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "trackStarts", "owner": "hs", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "__g", "owner": "hs", "name": "g", "access": 0, "descriptor": "I"}, {"field": "__u", "owner": "hs", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "__e", "owner": "hs", "name": "e", "access": 0, "descriptor": "J"}], "methods": [{"method": "clear", "owner": "hs", "name": "f", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "getPrioritizedTrack", "owner": "hs", "name": "z", "access": 0, "parameters": [], "descriptor": "()I"}, {"method": "gotoTrack", "owner": "hs", "name": "o", "access": 0, "parameters": ["trackId"], "descriptor": "(I)V"}, {"method": "isDone", "owner": "hs", "name": "j", "access": 0, "parameters": [], "descriptor": "()Z"}, {"method": "isReady", "owner": "hs", "name": "q", "access": 0, "parameters": [], "descriptor": "()Z"}, {"method": "markTrackPosition", "owner": "hs", "name": "u", "access": 0, "parameters": ["trackId"], "descriptor": "(I)V"}, {"method": "parse", "owner": "hs", "name": "m", "access": 0, "parameters": ["midi"], "descriptor": "([B)V"}, {"method": "readMessage", "owner": "hs", "name": "e", "access": 0, "parameters": ["trackId"], "descriptor": "(I)I"}, {"method": "readMessage0", "owner": "hs", "name": "x", "access": 0, "parameters": ["trackId"], "descriptor": "(I)I"}, {"method": "readTrackLength", "owner": "hs", "name": "l", "access": 0, "parameters": ["trackId"], "descriptor": "(I)V"}, {"method": "reset", "owner": "hs", "name": "s", "access": 0, "descriptor": "(J)V"}, {"method": "setTrackDone", "owner": "hs", "name": "g", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "trackCount", "owner": "hs", "name": "w", "access": 0, "parameters": [], "descriptor": "()I"}, {"method": "__d_371", "owner": "hs", "name": "d", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(II)I"}, {"method": "__a_372", "owner": "hs", "name": "a", "access": 0, "parameters": ["arg0"], "descriptor": "(I)J"}], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 0, "descriptor": "([B)V"}]}, {"class": "MusicPatch", "name": "hl", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "rawSounds", "owner": "hl", "name": "f", "access": 0, "descriptor": "[Lcx;"}, {"field": "__g", "owner": "hl", "name": "g", "access": 0, "descriptor": "[B"}, {"field": "__o", "owner": "hl", "name": "o", "access": 0, "descriptor": "[B"}, {"field": "__w", "owner": "hl", "name": "w", "access": 0, "descriptor": "[B"}, {"field": "__u", "owner": "hl", "name": "u", "access": 0, "descriptor": "[Lhd;"}, {"field": "__m", "owner": "hl", "name": "m", "access": 0, "descriptor": "I", "decoder": 574200865}, {"field": "__l", "owner": "hl", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "__q", "owner": "hl", "name": "q", "access": 0, "descriptor": "[S"}], "methods": [{"method": "clear", "owner": "hl", "name": "q", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "__f_373", "owner": "hl", "name": "f", "access": 0, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(Ldt;[B[II)Z"}], "constructors": [{"access": 0, "descriptor": "([B)V"}]}, {"class": "MusicPatchPcmStream", "name": "hq", "super": "dc", "access": 33, "interfaces": [], "fields": [{"field": "mixer", "owner": "hq", "name": "q", "access": 0, "descriptor": "Lct;"}, {"field": "queue", "owner": "hq", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "superStream", "owner": "hq", "name": "m", "access": 0, "descriptor": "Lhm;"}], "methods": [{"method": "__u_374", "owner": "hq", "name": "u", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__g_375", "owner": "hq", "name": "g", "access": 4, "parameters": [], "descriptor": "()Ldc;"}, {"method": "__l_376", "owner": "hq", "name": "l", "access": 4, "parameters": [], "descriptor": "()I"}, {"method": "__e_377", "owner": "hq", "name": "e", "access": 4, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "([III)V"}, {"method": "__d_378", "owner": "hq", "name": "d", "access": 4, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__m_379", "owner": "hq", "name": "m", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(Lha;[IIIII)V"}, {"method": "__f_380", "owner": "hq", "name": "f", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lha;II)V"}], "constructors": [{"access": 0, "descriptor": "(Lhm;)V"}]}, {"class": "MusicTrack", "name": "he", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "midi", "owner": "he", "name": "f", "access": 0, "descriptor": "[B"}, {"field": "table", "owner": "he", "name": "m", "access": 0, "descriptor": "Lld;"}], "methods": [{"method": "clear", "owner": "he", "name": "q", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__f_381", "owner": "he", "name": "f", "access": 0, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Lgr;)V"}]}, {"class": "<PERSON><PERSON>", "name": "hu", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "x", "owner": "hu", "name": "f", "access": 1, "descriptor": "I", "decoder": 1225620013}, {"field": "y", "owner": "hu", "name": "m", "access": 1, "descriptor": "I", "decoder": 503621925}, {"field": "z", "owner": "hu", "name": "q", "access": 1, "descriptor": "I", "decoder": 154740833}], "methods": [{"method": "equals0", "owner": "hu", "name": "f", "access": 0, "parameters": ["other"], "descriptor": "(<PERSON>hu;B)Z"}, {"method": "packed", "owner": "hu", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "toString0", "owner": "hu", "name": "q", "access": 0, "parameters": ["separator"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"method": "__equals_382", "owner": "hu", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"method": "__hashCode_383", "owner": "hu", "name": "hashCode", "access": 1, "parameters": [], "descriptor": "()I"}, {"method": "__toString_384", "owner": "hu", "name": "toString", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/String;"}], "constructors": [{"access": 1, "descriptor": "(III)V"}, {"access": 1, "descriptor": "(<PERSON>hu;)V"}, {"access": 1, "descriptor": "(I)V"}]}, {"class": "Player<PERSON><PERSON><PERSON><PERSON>", "name": "hi", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "bodyColors", "owner": "hi", "name": "f", "access": 0, "descriptor": "[I"}, {"field": "equipment", "owner": "hi", "name": "m", "access": 0, "descriptor": "[I"}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "hi", "name": "q", "access": 1, "descriptor": "Z"}, {"field": "npcTransformId", "owner": "hi", "name": "w", "access": 1, "descriptor": "I", "decoder": 1676934923}, {"field": "__o", "owner": "hi", "name": "o", "access": 0, "descriptor": "J", "decoder": -3506189412688081513}, {"field": "__u", "owner": "hi", "name": "u", "access": 0, "descriptor": "J", "decoder": 8217880921734028425}], "methods": [{"method": "getChatHeadId", "owner": "hi", "name": "e", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "getModel", "owner": "hi", "name": "g", "access": 1, "descriptor": "(Ljh;ILjh;II)Ldu;"}, {"method": "getModelData", "owner": "hi", "name": "l", "access": 0, "parameters": [], "descriptor": "(I)Ldw;"}, {"method": "__m_385", "owner": "hi", "name": "m", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "([I[IZII)V"}, {"method": "__f_386", "owner": "hi", "name": "f", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IZI)V"}, {"method": "__q_387", "owner": "hi", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IZI)V"}, {"method": "__w_388", "owner": "hi", "name": "w", "access": 1, "parameters": ["arg0"], "descriptor": "(ZB)V"}, {"method": "__o_389", "owner": "hi", "name": "o", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgr;I)V"}, {"method": "__u_390", "owner": "hi", "name": "u", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "Component", "name": "ho", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "actions", "owner": "ho", "name": "dx", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "buttonText", "owner": "ho", "name": "ec", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "buttonType", "owner": "ho", "name": "b", "access": 1, "descriptor": "I", "decoder": 1412102151}, {"field": "childIndex", "owner": "ho", "name": "y", "access": 1, "descriptor": "I", "decoder": -648033351}, {"field": "children", "owner": "ho", "name": "ep", "access": 1, "descriptor": "[Lho;"}, {"field": "clickMask", "owner": "ho", "name": "cw", "access": 1, "descriptor": "I", "decoder": 1856618959}, {"field": "clientCode", "owner": "ho", "name": "c", "access": 1, "descriptor": "I", "decoder": -581762425}, {"field": "color", "owner": "ho", "name": "am", "access": 1, "descriptor": "I", "decoder": -2022638615}, {"field": "color2", "owner": "ho", "name": "an", "access": 1, "descriptor": "I", "decoder": -365781561}, {"field": "cs1Comparison<PERSON><PERSON>ues", "owner": "ho", "name": "er", "access": 1, "descriptor": "[I"}, {"field": "cs1Comparisons", "owner": "ho", "name": "ey", "access": 1, "descriptor": "[I"}, {"field": "cs1Instructions", "owner": "ho", "name": "eo", "access": 1, "descriptor": "[[I"}, {"field": "cycle", "owner": "ho", "name": "fd", "access": 1, "descriptor": "I", "decoder": -1995006949}, {"field": "dataText", "owner": "ho", "name": "de", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "drag<PERSON><PERSON><PERSON><PERSON>", "owner": "ho", "name": "df", "access": 1, "descriptor": "I", "decoder": -1021798347}, {"field": "dragZoneSize", "owner": "ho", "name": "dn", "access": 1, "descriptor": "I", "decoder": 2092800189}, {"field": "fill", "owner": "ho", "name": "ao", "access": 1, "descriptor": "Z"}, {"field": "fontId", "owner": "ho", "name": "cr", "access": 1, "descriptor": "I", "decoder": 1435746863}, {"field": "hasListener", "owner": "ho", "name": "ds", "access": 1, "descriptor": "Z"}, {"field": "height", "owner": "ho", "name": "ak", "access": 1, "descriptor": "I", "decoder": 1656646737}, {"field": "heightAlignment", "owner": "ho", "name": "ag", "access": 1, "descriptor": "I", "decoder": -709256449}, {"field": "id", "owner": "ho", "name": "t", "access": 1, "descriptor": "I", "decoder": -941148335}, {"field": "invTransmitTriggers", "owner": "ho", "name": "du", "access": 1, "descriptor": "[I"}, {"field": "inventorySprites", "owner": "ho", "name": "cq", "access": 1, "descriptor": "[I"}, {"field": "inventoryXOffsets", "owner": "ho", "name": "ck", "access": 1, "descriptor": "[I"}, {"field": "inventoryYOffsets", "owner": "ho", "name": "cn", "access": 1, "descriptor": "[I"}, {"field": "isHidden", "owner": "ho", "name": "ae", "access": 1, "descriptor": "Z"}, {"field": "isIf3", "owner": "ho", "name": "s", "access": 1, "descriptor": "Z"}, {"field": "isScrollBar", "owner": "ho", "name": "da", "access": 1, "descriptor": "Z"}, {"field": "itemActions", "owner": "ho", "name": "ch", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "itemId", "owner": "ho", "name": "ea", "access": 1, "descriptor": "I", "decoder": 1248629915}, {"field": "itemIds", "owner": "ho", "name": "ef", "access": 1, "descriptor": "[I"}, {"field": "itemQuantities", "owner": "ho", "name": "ev", "access": 1, "descriptor": "[I"}, {"field": "itemQuantity", "owner": "ho", "name": "es", "access": 1, "descriptor": "I", "decoder": 340244859}, {"field": "lineWid", "owner": "ho", "name": "ai", "access": 1, "descriptor": "I", "decoder": -110526699}, {"field": "modelAngleX", "owner": "ho", "name": "bz", "access": 1, "descriptor": "I", "decoder": 2044722063}, {"field": "modelAngleY", "owner": "ho", "name": "bg", "access": 1, "descriptor": "I", "decoder": -1253859481}, {"field": "modelAngleZ", "owner": "ho", "name": "bt", "access": 1, "descriptor": "I", "decoder": -692411223}, {"field": "modelFrame", "owner": "ho", "name": "eg", "access": 1, "descriptor": "I", "decoder": -431656963}, {"field": "modelFrameCycle", "owner": "ho", "name": "ez", "access": 1, "descriptor": "I", "decoder": -95117407}, {"field": "modelId", "owner": "ho", "name": "by", "access": 1, "descriptor": "I", "decoder": -2013625215}, {"field": "modelId2", "owner": "ho", "name": "bm", "access": 0, "descriptor": "I", "decoder": -201557389}, {"field": "modelOffsetX", "owner": "ho", "name": "bj", "access": 1, "descriptor": "I", "decoder": -459670781}, {"field": "modelOffsetY", "owner": "ho", "name": "bi", "access": 1, "descriptor": "I", "decoder": -2018053063}, {"field": "model<PERSON><PERSON><PERSON>", "owner": "ho", "name": "be", "access": 1, "descriptor": "Z"}, {"field": "modelTransparency", "owner": "ho", "name": "bv", "access": 1, "descriptor": "Z"}, {"field": "modelType", "owner": "ho", "name": "bx", "access": 1, "descriptor": "I", "decoder": 630750753}, {"field": "modelType2", "owner": "ho", "name": "bu", "access": 0, "descriptor": "I", "decoder": 138222099}, {"field": "modelZoom", "owner": "ho", "name": "bp", "access": 1, "descriptor": "I", "decoder": -2133338643}, {"field": "mouseOverColor", "owner": "ho", "name": "az", "access": 1, "descriptor": "I", "decoder": -1831692129}, {"field": "mouseOverColor2", "owner": "ho", "name": "au", "access": 1, "descriptor": "I", "decoder": 1236161275}, {"field": "mouseOverRedirect", "owner": "ho", "name": "ew", "access": 1, "descriptor": "I", "decoder": -1390352887}, {"field": "noClickThrough", "owner": "ho", "name": "fx", "access": 1, "descriptor": "Z"}, {"field": "onChatTransmit", "owner": "ho", "name": "em", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onClanTransmit", "owner": "ho", "name": "ej", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onClick", "owner": "ho", "name": "dc", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onClickRepeat", "owner": "ho", "name": "dg", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onDialogAbort", "owner": "ho", "name": "en", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onDrag", "owner": "ho", "name": "dr", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onDragComplete", "owner": "ho", "name": "dw", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onFriendTransmit", "owner": "ho", "name": "ex", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onHold", "owner": "ho", "name": "dd", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onInvTransmit", "owner": "ho", "name": "dj", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onKey", "owner": "ho", "name": "ed", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onLoad", "owner": "ho", "name": "di", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onMiscTransmit", "owner": "ho", "name": "el", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onMouseLeave", "owner": "ho", "name": "dl", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onMouseOver", "owner": "ho", "name": "dy", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onMouseRepeat", "owner": "ho", "name": "dk", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onOp", "owner": "ho", "name": "eb", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onRelease", "owner": "ho", "name": "db", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onScrollWheel", "owner": "ho", "name": "ek", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onStatTransmit", "owner": "ho", "name": "dm", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onStockTransmit", "owner": "ho", "name": "eq", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onSubChange", "owner": "ho", "name": "ei", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onTargetEnter", "owner": "ho", "name": "dv", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onTargetLeave", "owner": "ho", "name": "do", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onTimer", "owner": "ho", "name": "et", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "onVarTransmit", "owner": "ho", "name": "dh", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "outline", "owner": "ho", "name": "bk", "access": 1, "descriptor": "I", "decoder": -594884327}, {"field": "paddingX", "owner": "ho", "name": "cu", "access": 1, "descriptor": "I", "decoder": -23173063}, {"field": "paddingY", "owner": "ho", "name": "cb", "access": 1, "descriptor": "I", "decoder": 720760323}, {"field": "parent", "owner": "ho", "name": "dt", "access": 1, "descriptor": "Lho;"}, {"field": "parentId", "owner": "ho", "name": "ab", "access": 1, "descriptor": "I", "decoder": -617209253}, {"field": "rawHeight", "owner": "ho", "name": "ar", "access": 1, "descriptor": "I", "decoder": 1202342839}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "ho", "name": "av", "access": 1, "descriptor": "I", "decoder": -2001402777}, {"field": "rawX", "owner": "ho", "name": "aq", "access": 1, "descriptor": "I", "decoder": -872904863}, {"field": "rawY", "owner": "ho", "name": "aj", "access": 1, "descriptor": "I", "decoder": -1814696937}, {"field": "rectangleMode", "owner": "ho", "name": "aa", "access": 1, "descriptor": "Llj;"}, {"field": "rootIndex", "owner": "ho", "name": "fp", "access": 1, "descriptor": "I", "decoder": 365867737}, {"field": "scrollHeight", "owner": "ho", "name": "as", "access": 1, "descriptor": "I", "decoder": -867175629}, {"field": "scrollWidth", "owner": "ho", "name": "ap", "access": 1, "descriptor": "I", "decoder": -623790443}, {"field": "scrollX", "owner": "ho", "name": "at", "access": 1, "descriptor": "I", "decoder": 2090399935}, {"field": "scrollY", "owner": "ho", "name": "ad", "access": 1, "descriptor": "I", "decoder": -537618643}, {"field": "sequenceId", "owner": "ho", "name": "bl", "access": 1, "descriptor": "I", "decoder": -1423753745}, {"field": "sequenceId2", "owner": "ho", "name": "br", "access": 1, "descriptor": "I", "decoder": -856053301}, {"field": "spellActionName", "owner": "ho", "name": "dp", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "spellName", "owner": "ho", "name": "eu", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "spriteAngle", "owner": "ho", "name": "bq", "access": 1, "descriptor": "I", "decoder": -1361791123}, {"field": "spriteFlipH", "owner": "ho", "name": "bo", "access": 1, "descriptor": "Z"}, {"field": "spriteFlipV", "owner": "ho", "name": "bc", "access": 1, "descriptor": "Z"}, {"field": "spriteId", "owner": "ho", "name": "bs", "access": 1, "descriptor": "I", "decoder": 1573420153}, {"field": "spriteId2", "owner": "ho", "name": "bb", "access": 1, "descriptor": "I", "decoder": -1074168553}, {"field": "spriteShadow", "owner": "ho", "name": "bd", "access": 1, "descriptor": "I", "decoder": 215797483}, {"field": "spriteTiling", "owner": "ho", "name": "bn", "access": 1, "descriptor": "Z"}, {"field": "statTransmitTriggers", "owner": "ho", "name": "dq", "access": 1, "descriptor": "[I"}, {"field": "text", "owner": "ho", "name": "cv", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "text2", "owner": "ho", "name": "ce", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "textLineHeight", "owner": "ho", "name": "cy", "access": 1, "descriptor": "I", "decoder": 1401272343}, {"field": "textShadowed", "owner": "ho", "name": "cj", "access": 1, "descriptor": "Z"}, {"field": "textXAlignment", "owner": "ho", "name": "cs", "access": 1, "descriptor": "I", "decoder": 1527989635}, {"field": "textYAlignment", "owner": "ho", "name": "cm", "access": 1, "descriptor": "I", "decoder": 1271997981}, {"field": "transparency", "owner": "ho", "name": "ax", "access": 1, "descriptor": "I", "decoder": -638557371}, {"field": "type", "owner": "ho", "name": "h", "access": 1, "descriptor": "I", "decoder": 1521310729}, {"field": "varTransmitTriggers", "owner": "ho", "name": "dz", "access": 1, "descriptor": "[I"}, {"field": "width", "owner": "ho", "name": "ah", "access": 1, "descriptor": "I", "decoder": 1716081883}, {"field": "widthAlignment", "owner": "ho", "name": "v", "access": 1, "descriptor": "I", "decoder": 1628640897}, {"field": "x", "owner": "ho", "name": "ac", "access": 1, "descriptor": "I", "decoder": 1271321381}, {"field": "xAlignment", "owner": "ho", "name": "r", "access": 1, "descriptor": "I", "decoder": 523318277}, {"field": "y", "owner": "ho", "name": "ay", "access": 1, "descriptor": "I", "decoder": 1492291497}, {"field": "yAlignment", "owner": "ho", "name": "p", "access": 1, "descriptor": "I", "decoder": 1300531185}, {"field": "__ba", "owner": "ho", "name": "ba", "access": 1, "descriptor": "Z"}, {"field": "__cg", "owner": "ho", "name": "cg", "access": 1, "descriptor": "Z"}, {"field": "__fm", "owner": "ho", "name": "fm", "access": 1, "descriptor": "Z"}, {"field": "__fq", "owner": "ho", "name": "fq", "access": 1, "descriptor": "Z"}, {"field": "__fw", "owner": "ho", "name": "fw", "access": 1, "descriptor": "Z"}, {"field": "__fz", "owner": "ho", "name": "fz", "access": 1, "descriptor": "Z"}, {"field": "__cf", "owner": "ho", "name": "cf", "access": 1, "descriptor": "[[B"}, {"field": "__cp", "owner": "ho", "name": "cp", "access": 1, "descriptor": "[[B"}, {"field": "__af", "owner": "ho", "name": "af", "access": 1, "descriptor": "I", "decoder": -1853255291}, {"field": "__al", "owner": "ho", "name": "al", "access": 1, "descriptor": "I", "decoder": -1047989223}, {"field": "__aw", "owner": "ho", "name": "aw", "access": 1, "descriptor": "I", "decoder": 206556853}, {"field": "__bf", "owner": "ho", "name": "bf", "access": 1, "descriptor": "I", "decoder": -1692331135}, {"field": "__bh", "owner": "ho", "name": "bh", "access": 1, "descriptor": "I", "decoder": 727421593}, {"field": "__bw", "owner": "ho", "name": "bw", "access": 1, "descriptor": "I", "decoder": -1658511475}, {"field": "__fc", "owner": "ho", "name": "fc", "access": 1, "descriptor": "I", "decoder": 209458049}, {"field": "__fg", "owner": "ho", "name": "fg", "access": 1, "descriptor": "I", "decoder": -1520351537}, {"field": "__fj", "owner": "ho", "name": "fj", "access": 1, "descriptor": "I", "decoder": 876695207}, {"field": "__fl", "owner": "ho", "name": "fl", "access": 1, "descriptor": "I", "decoder": -672198209}, {"field": "__cc", "owner": "ho", "name": "cc", "access": 1, "descriptor": "[I"}, {"field": "__ci", "owner": "ho", "name": "ci", "access": 1, "descriptor": "[I"}, {"field": "__fh", "owner": "ho", "name": "fh", "access": 1, "descriptor": "[I"}, {"field": "__ee", "owner": "ho", "name": "ee", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "__eh", "owner": "ho", "name": "eh", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}], "methods": [{"method": "decode", "owner": "ho", "name": "u", "access": 0, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "decodeLegacy", "owner": "ho", "name": "o", "access": 0, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "getFont", "owner": "ho", "name": "d", "access": 1, "parameters": [], "descriptor": "(B)Lkk;"}, {"method": "getInventorySprite", "owner": "ho", "name": "a", "access": 1, "descriptor": "(II)Lln;"}, {"method": "getModel", "owner": "ho", "name": "z", "access": 1, "parameters": ["sequence", "frame", "b", "appearance"], "descriptor": "(Ljh;IZLhi;I)Ldu;"}, {"method": "getSprite", "owner": "ho", "name": "x", "access": 1, "parameters": ["b"], "descriptor": "(ZI)Lln;"}, {"method": "getSpriteMask", "owner": "ho", "name": "j", "access": 1, "parameters": ["b"], "descriptor": "(ZB)Lhp;"}, {"method": "readListener", "owner": "ho", "name": "g", "access": 0, "parameters": ["buffer"], "descriptor": "(Lgr;I)[<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "readListenerTriggers", "owner": "ho", "name": "l", "access": 0, "parameters": ["buffer"], "descriptor": "(Lgr;B)[I"}, {"method": "setAction", "owner": "ho", "name": "t", "access": 1, "parameters": ["index", "s"], "descriptor": "(<PERSON>java/lang/String;B)V"}, {"method": "swapItems", "owner": "ho", "name": "e", "access": 1, "parameters": ["indexA", "indexB"], "descriptor": "(III)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "PlayerType", "name": "ij", "super": "java.lang.Object", "access": 33, "interfaces": ["gc"], "fields": [{"field": "id", "owner": "ij", "name": "g", "access": 16, "descriptor": "I", "decoder": 1999564657}, {"field": "isPrivileged", "owner": "ij", "name": "e", "access": 17, "descriptor": "Z"}, {"field": "isUser", "owner": "ij", "name": "x", "access": 17, "descriptor": "Z"}, {"field": "modIcon", "owner": "ij", "name": "l", "access": 17, "descriptor": "I", "decoder": 613822977}], "methods": [{"method": "__f_391", "owner": "ij", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(IIZZZ)V"}]}, {"class": "AbstractArchive", "name": "ir", "super": "java.lang.Object", "access": 1057, "interfaces": [], "fields": [{"field": "fileCounts", "owner": "ir", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "fileIds", "owner": "ir", "name": "e", "access": 0, "descriptor": "[[I"}, {"field": "fileNameHashTables", "owner": "ir", "name": "d", "access": 0, "descriptor": "[Llv;"}, {"field": "fileNameHashes", "owner": "ir", "name": "x", "access": 0, "descriptor": "[[I"}, {"field": "files", "owner": "ir", "name": "n", "access": 0, "descriptor": "[[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "groupCount", "owner": "ir", "name": "f", "access": 0, "descriptor": "I", "decoder": 1694798923}, {"field": "groupCrcs", "owner": "ir", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "groupIds", "owner": "ir", "name": "q", "access": 0, "descriptor": "[I"}, {"field": "groupNameHashTable", "owner": "ir", "name": "o", "access": 0, "descriptor": "Llv;"}, {"field": "groupNameHashes", "owner": "ir", "name": "w", "access": 0, "descriptor": "[I"}, {"field": "groupVersions", "owner": "ir", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "groups", "owner": "ir", "name": "k", "access": 0, "descriptor": "[<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "hash", "owner": "ir", "name": "a", "access": 1, "descriptor": "I", "decoder": -530370811}, {"field": "releaseGroups", "owner": "ir", "name": "z", "access": 0, "descriptor": "Z"}, {"field": "shallowFiles", "owner": "ir", "name": "j", "access": 0, "descriptor": "Z"}], "methods": [{"method": "buildFiles", "owner": "ir", "name": "c", "access": 0, "parameters": ["group", "xteaKey"], "descriptor": "(I[II)Z"}, {"method": "decodeIndex", "owner": "ir", "name": "m", "access": 0, "descriptor": "([BB)V"}, {"method": "getFile", "owner": "ir", "name": "d", "access": 1, "parameters": ["group", "file"], "descriptor": "(III)[B"}, {"method": "getFileFlat", "owner": "ir", "name": "a", "access": 1, "parameters": ["groupOrFile"], "descriptor": "(IB)[B"}, {"method": "getFileId", "owner": "ir", "name": "v", "access": 1, "parameters": ["group", "fileName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"method": "getGroupId", "owner": "ir", "name": "p", "access": 1, "parameters": ["groupName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"method": "groupLoadPercent", "owner": "ir", "name": "e", "access": 0, "descriptor": "(II)I"}, {"method": "groupLoadPercentByName", "owner": "ir", "name": "ac", "access": 1, "parameters": ["groupName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)I"}, {"method": "loadGroup", "owner": "ir", "name": "z", "access": 0, "descriptor": "(II)V"}, {"method": "takeFile", "owner": "ir", "name": "q", "access": 1, "parameters": ["group", "file"], "descriptor": "(III)[B"}, {"method": "takeFileByNames", "owner": "ir", "name": "aq", "access": 1, "parameters": ["groupName", "fileName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;S)[B"}, {"method": "takeFileEncrypted", "owner": "ir", "name": "w", "access": 1, "parameters": ["group", "file", "xteaKey"], "descriptor": "(II[II)[B"}, {"method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "ir", "name": "x", "access": 1, "parameters": ["groupOrFile"], "descriptor": "(IS)[B"}, {"method": "tryLoadFile", "owner": "ir", "name": "o", "access": 1, "parameters": ["group", "file"], "descriptor": "(III)Z"}, {"method": "tryLoadFileByNames", "owner": "ir", "name": "aj", "access": 1, "parameters": ["groupName", "fileName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)Z"}, {"method": "tryLoadGroup", "owner": "ir", "name": "g", "access": 1, "parameters": ["group"], "descriptor": "(II)Z"}, {"method": "tryLoadGroupByName", "owner": "ir", "name": "av", "access": 1, "parameters": ["groupName"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Z"}, {"method": "__f_392", "owner": "ir", "name": "f", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__u_393", "owner": "ir", "name": "u", "access": 1, "parameters": ["arg0"], "descriptor": "(II)Z"}, {"method": "__l_394", "owner": "ir", "name": "l", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "__j_395", "owner": "ir", "name": "j", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)[I"}, {"method": "__s_396", "owner": "ir", "name": "s", "access": 1, "parameters": ["arg0"], "descriptor": "(II)I"}, {"method": "__t_397", "owner": "ir", "name": "t", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__y_398", "owner": "ir", "name": "y", "access": 1, "parameters": [], "descriptor": "(B)V"}, {"method": "__h_399", "owner": "ir", "name": "h", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__b_400", "owner": "ir", "name": "b", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__ag_401", "owner": "ir", "name": "ag", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;B)Z"}, {"method": "__ar_402", "owner": "ir", "name": "ar", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;B)V"}], "constructors": [{"access": 0, "descriptor": "(ZZ)V"}]}, {"class": "ArchiveDiskActionHandler", "name": "iv", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Runnable"], "fields": [], "methods": [{"method": "__run_403", "owner": "iv", "name": "run", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Archive", "name": "it", "super": "ir", "access": 33, "interfaces": [], "fields": [{"field": "archiveDisk", "owner": "it", "name": "c", "access": 0, "descriptor": "Lff;"}, {"field": "index", "owner": "it", "name": "p", "access": 0, "descriptor": "I", "decoder": -1579549759}, {"field": "indexCrc", "owner": "it", "name": "av", "access": 0, "descriptor": "I", "decoder": 1472870917}, {"field": "indexVersion", "owner": "it", "name": "ar", "access": 0, "descriptor": "I", "decoder": -785528219}, {"field": "masterDisk", "owner": "it", "name": "r", "access": 0, "descriptor": "Lff;"}, {"field": "validGroups", "owner": "it", "name": "aq", "access": 64, "descriptor": "[Z"}, {"field": "__ag", "owner": "it", "name": "ag", "access": 0, "descriptor": "Z"}, {"field": "__ac", "owner": "it", "name": "ac", "access": 0, "descriptor": "I", "decoder": -1884661133}, {"field": "__v", "owner": "it", "name": "v", "access": 64, "descriptor": "Z"}], "methods": [{"method": "groupLoadPercent", "owner": "it", "name": "e", "access": 0, "descriptor": "(II)I"}, {"method": "load", "owner": "it", "name": "dx", "access": 0, "descriptor": "(Lff;I[BZI)V"}, {"method": "loadAllLocal", "owner": "it", "name": "dt", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "loadGroup", "owner": "it", "name": "z", "access": 0, "descriptor": "(II)V"}, {"method": "loadIndex", "owner": "it", "name": "co", "access": 1, "descriptor": "(III)V"}, {"method": "loadPercent", "owner": "it", "name": "da", "access": 1, "descriptor": "(I)I"}, {"method": "write", "owner": "it", "name": "de", "access": 1, "descriptor": "(I[BZZB)V"}, {"method": "__cx_404", "owner": "it", "name": "cx", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "__ca_405", "owner": "it", "name": "ca", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__f_406", "owner": "it", "name": "f", "access": 0, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__dn_407", "owner": "it", "name": "dn", "access": 1, "parameters": ["arg0"], "descriptor": "(IB)Z"}, {"method": "__df_408", "owner": "it", "name": "df", "access": 1, "parameters": ["arg0"], "descriptor": "(II)Z"}], "constructors": [{"access": 1, "descriptor": "(Lff;Lff;IZZZ)V"}]}, {"class": "WorldMapElement", "name": "ic", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "category", "owner": "ic", "name": "c", "access": 1, "descriptor": "I", "decoder": -653112729}, {"field": "sprite1", "owner": "ic", "name": "u", "access": 1, "descriptor": "I", "decoder": 2012857993}, {"field": "sprite2", "owner": "ic", "name": "g", "access": 0, "descriptor": "I", "decoder": -322924875}, {"field": "string1", "owner": "ic", "name": "n", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "strings", "owner": "ic", "name": "k", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "textSize", "owner": "ic", "name": "x", "access": 1, "descriptor": "I", "decoder": 1184517129}, {"field": "__b", "owner": "ic", "name": "b", "access": 0, "descriptor": "[B"}, {"field": "__a", "owner": "ic", "name": "a", "access": 0, "descriptor": "I", "decoder": 1336803515}, {"field": "__j", "owner": "ic", "name": "j", "access": 0, "descriptor": "I", "decoder": -1007121521}, {"field": "__s", "owner": "ic", "name": "s", "access": 0, "descriptor": "I", "decoder": -1209054101}, {"field": "__z", "owner": "ic", "name": "z", "access": 0, "descriptor": "I", "decoder": -1825795359}, {"field": "__h", "owner": "ic", "name": "h", "access": 0, "descriptor": "[I"}, {"field": "__i", "owner": "ic", "name": "i", "access": 0, "descriptor": "[I"}, {"field": "__e", "owner": "ic", "name": "e", "access": 1, "descriptor": "I", "decoder": -231067}, {"field": "__l", "owner": "ic", "name": "l", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__o", "owner": "ic", "name": "o", "access": 17, "descriptor": "I", "decoder": -1579051565}], "methods": [{"method": "decode", "owner": "ic", "name": "f", "access": 1, "parameters": ["buffer"], "descriptor": "(Lgr;B)V"}, {"method": "decode0", "owner": "ic", "name": "q", "access": 0, "parameters": ["buffer", "opcode"], "descriptor": "(Lgr;II)V"}, {"method": "getSprite", "owner": "ic", "name": "o", "access": 1, "parameters": ["b"], "descriptor": "(ZI)Lln;"}, {"method": "getSprite0", "owner": "ic", "name": "u", "access": 0, "parameters": ["id"], "descriptor": "(IB)Lln;"}, {"method": "__w_409", "owner": "ic", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__g_410", "owner": "ic", "name": "g", "access": 1, "parameters": [], "descriptor": "(I)I"}], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "VarcInt", "name": "iw", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "persist", "owner": "iw", "name": "q", "access": 1, "descriptor": "Z"}], "methods": [{"method": "__q_411", "owner": "iw", "name": "q", "access": 0, "parameters": ["arg0"], "descriptor": "(Lgr;I)V"}, {"method": "__w_412", "owner": "iw", "name": "w", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lgr;II)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "SpotAnimationDefinition", "name": "io", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "id", "owner": "io", "name": "o", "access": 0, "descriptor": "I", "decoder": -713866165}, {"field": "model", "owner": "io", "name": "u", "access": 0, "descriptor": "I", "decoder": -2065492247}, {"field": "orientation", "owner": "io", "name": "i", "access": 0, "descriptor": "I", "decoder": 1650590363}, {"field": "recol_d", "owner": "io", "name": "e", "access": 0, "descriptor": "[S"}, {"field": "recol_s", "owner": "io", "name": "l", "access": 0, "descriptor": "[S"}, {"field": "resizeh", "owner": "io", "name": "k", "access": 0, "descriptor": "I", "decoder": 680796997}, {"field": "resizev", "owner": "io", "name": "n", "access": 0, "descriptor": "I", "decoder": 1199960995}, {"field": "retex_d", "owner": "io", "name": "d", "access": 0, "descriptor": "[S"}, {"field": "retex_s", "owner": "io", "name": "x", "access": 0, "descriptor": "[S"}, {"field": "sequence", "owner": "io", "name": "g", "access": 1, "descriptor": "I", "decoder": 1580078789}, {"field": "__a", "owner": "io", "name": "a", "access": 0, "descriptor": "I", "decoder": -20619847}, {"field": "__z", "owner": "io", "name": "z", "access": 0, "descriptor": "I", "decoder": 1866148625}], "methods": [{"method": "decode", "owner": "io", "name": "f", "access": 0, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "io", "name": "q", "access": 0, "parameters": ["buffer", "n"], "descriptor": "(Lgr;II)V"}, {"method": "getModel", "owner": "io", "name": "w", "access": 17, "descriptor": "(II)Ldu;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "KitDefinition", "name": "im", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "bodyPart", "owner": "im", "name": "o", "access": 1, "descriptor": "I", "decoder": 1619779051}, {"field": "models", "owner": "im", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "models2", "owner": "im", "name": "u", "access": 0, "descriptor": "[I"}, {"field": "recol_d", "owner": "im", "name": "l", "access": 0, "descriptor": "[S"}, {"field": "recol_s", "owner": "im", "name": "g", "access": 0, "descriptor": "[S"}, {"field": "retex_d", "owner": "im", "name": "x", "access": 0, "descriptor": "[S"}, {"field": "retex_s", "owner": "im", "name": "e", "access": 0, "descriptor": "[S"}, {"field": "__k", "owner": "im", "name": "k", "access": 1, "descriptor": "Z"}], "methods": [{"method": "decode", "owner": "im", "name": "f", "access": 0, "descriptor": "(Lgr;S)V"}, {"method": "decode0", "owner": "im", "name": "q", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "__w_413", "owner": "im", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "__o_414", "owner": "im", "name": "o", "access": 1, "parameters": [], "descriptor": "(B)Ldw;"}, {"method": "__u_415", "owner": "im", "name": "u", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "__g_416", "owner": "im", "name": "g", "access": 1, "parameters": [], "descriptor": "(B)Ldw;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "HealthBarDefinition", "name": "ii", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "int1", "owner": "ii", "name": "l", "access": 1, "descriptor": "I", "decoder": 390656615}, {"field": "int2", "owner": "ii", "name": "e", "access": 1, "descriptor": "I", "decoder": **********}, {"field": "int3", "owner": "ii", "name": "x", "access": 1, "descriptor": "I", "decoder": -**********}, {"field": "int4", "owner": "ii", "name": "d", "access": 1, "descriptor": "I", "decoder": -**********}, {"field": "int5", "owner": "ii", "name": "k", "access": 1, "descriptor": "I", "decoder": -642961053}, {"field": "spriteId1", "owner": "ii", "name": "n", "access": 0, "descriptor": "I", "decoder": **********}, {"field": "spriteId2", "owner": "ii", "name": "i", "access": 0, "descriptor": "I", "decoder": -**********}, {"field": "width", "owner": "ii", "name": "a", "access": 1, "descriptor": "I", "decoder": -354704697}, {"field": "widthPadding", "owner": "ii", "name": "z", "access": 1, "descriptor": "I", "decoder": 166567703}, {"field": "__o", "owner": "ii", "name": "o", "access": 1, "descriptor": "I", "decoder": 1515955705}], "methods": [{"method": "decode", "owner": "ii", "name": "m", "access": 1, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "ii", "name": "f", "access": 0, "descriptor": "(Lgr;IB)V"}, {"method": "getFrontSprite", "owner": "ii", "name": "q", "access": 1, "parameters": [], "descriptor": "(B)Lln;"}, {"method": "getBackSprite", "owner": "ii", "name": "w", "access": 1, "parameters": [], "descriptor": "(B)Lln;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "HitmarkDefinition", "name": "jm", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "fontId", "owner": "jm", "name": "k", "access": 0, "descriptor": "I", "decoder": 431076793}, {"field": "transformVarbit", "owner": "jm", "name": "v", "access": 0, "descriptor": "I", "decoder": 516423393}, {"field": "transformVarp", "owner": "jm", "name": "ag", "access": 0, "descriptor": "I", "decoder": -1841054487}, {"field": "transforms", "owner": "jm", "name": "p", "access": 1, "descriptor": "[I"}, {"field": "__a", "owner": "jm", "name": "a", "access": 0, "descriptor": "I", "decoder": -1533306389}, {"field": "__j", "owner": "jm", "name": "j", "access": 0, "descriptor": "I", "decoder": 1683792491}, {"field": "__s", "owner": "jm", "name": "s", "access": 0, "descriptor": "I", "decoder": 1845590719}, {"field": "__z", "owner": "jm", "name": "z", "access": 0, "descriptor": "I", "decoder": -1677628889}, {"field": "__b", "owner": "jm", "name": "b", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__c", "owner": "jm", "name": "c", "access": 1, "descriptor": "I", "decoder": 1003116765}, {"field": "__h", "owner": "jm", "name": "h", "access": 1, "descriptor": "I", "decoder": 199815415}, {"field": "__i", "owner": "jm", "name": "i", "access": 1, "descriptor": "I", "decoder": -102270561}, {"field": "__n", "owner": "jm", "name": "n", "access": 1, "descriptor": "I", "decoder": -1151968735}, {"field": "__r", "owner": "jm", "name": "r", "access": 1, "descriptor": "I", "decoder": 1498880475}, {"field": "__t", "owner": "jm", "name": "t", "access": 1, "descriptor": "I", "decoder": -389283589}, {"field": "__y", "owner": "jm", "name": "y", "access": 1, "descriptor": "I", "decoder": 1751286975}], "methods": [{"method": "decode", "owner": "jm", "name": "q", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jm", "name": "w", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "getFont", "owner": "jm", "name": "d", "access": 1, "parameters": [], "descriptor": "(I)Lkk;"}, {"method": "getString", "owner": "jm", "name": "u", "access": 1, "parameters": ["n"], "descriptor": "(IS)Ljava/lang/String;"}, {"method": "transform", "owner": "jm", "name": "o", "access": 17, "parameters": [], "descriptor": "(B)Ljm;"}, {"method": "__g_417", "owner": "jm", "name": "g", "access": 1, "parameters": [], "descriptor": "(I)Lln;"}, {"method": "__l_418", "owner": "jm", "name": "l", "access": 1, "parameters": [], "descriptor": "(B)Lln;"}, {"method": "__e_419", "owner": "jm", "name": "e", "access": 1, "parameters": [], "descriptor": "(B)Lln;"}, {"method": "__x_420", "owner": "jm", "name": "x", "access": 1, "parameters": [], "descriptor": "(I)Lln;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ObjectDefinition", "name": "jr", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "ambient", "owner": "jr", "name": "ag", "access": 0, "descriptor": "I", "decoder": 533219139}, {"field": "ambientSoundId", "owner": "jr", "name": "az", "access": 1, "descriptor": "I", "decoder": 374616127}, {"field": "animationId", "owner": "jr", "name": "p", "access": 1, "descriptor": "I", "decoder": -1018236769}, {"field": "boolean1", "owner": "jr", "name": "y", "access": 1, "descriptor": "Z"}, {"field": "boolean2", "owner": "jr", "name": "at", "access": 1, "descriptor": "Z"}, {"field": "clipType", "owner": "jr", "name": "b", "access": 0, "descriptor": "I", "decoder": -268374709}, {"field": "clipped", "owner": "jr", "name": "ay", "access": 1, "descriptor": "Z"}, {"field": "contrast", "owner": "jr", "name": "aq", "access": 0, "descriptor": "I", "decoder": -216646581}, {"field": "id", "owner": "jr", "name": "e", "access": 1, "descriptor": "I", "decoder": -2034835389}, {"field": "int1", "owner": "jr", "name": "h", "access": 1, "descriptor": "I", "decoder": 1419333853}, {"field": "int2", "owner": "jr", "name": "v", "access": 1, "descriptor": "I", "decoder": 713665805}, {"field": "int3", "owner": "jr", "name": "ap", "access": 1, "descriptor": "I", "decoder": 1455451843}, {"field": "int4", "owner": "jr", "name": "au", "access": 1, "descriptor": "I", "decoder": -1990510197}, {"field": "int5", "owner": "jr", "name": "ao", "access": 1, "descriptor": "I", "decoder": -1519592967}, {"field": "int6", "owner": "jr", "name": "aa", "access": 1, "descriptor": "I", "decoder": 1201773735}, {"field": "interactType", "owner": "jr", "name": "t", "access": 1, "descriptor": "I", "decoder": -553719901}, {"field": "isRotated", "owner": "jr", "name": "ac", "access": 0, "descriptor": "Z"}, {"field": "isSolid", "owner": "jr", "name": "ad", "access": 0, "descriptor": "Z"}, {"field": "mapIconId", "owner": "jr", "name": "av", "access": 1, "descriptor": "I", "decoder": -1737602209}, {"field": "mapSceneId", "owner": "jr", "name": "ar", "access": 1, "descriptor": "I", "decoder": -324183669}, {"field": "modelClipped", "owner": "jr", "name": "r", "access": 1, "descriptor": "Z"}, {"field": "modelHeight", "owner": "jr", "name": "ak", "access": 0, "descriptor": "I", "decoder": 511434049}, {"field": "modelSizeX", "owner": "jr", "name": "ah", "access": 0, "descriptor": "I", "decoder": -1509832827}, {"field": "modelSizeY", "owner": "jr", "name": "aw", "access": 0, "descriptor": "I", "decoder": 1746573233}, {"field": "name", "owner": "jr", "name": "k", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "nonFlatShading", "owner": "jr", "name": "c", "access": 0, "descriptor": "Z"}, {"field": "offsetHeight", "owner": "jr", "name": "ab", "access": 0, "descriptor": "I", "decoder": -1720702573}, {"field": "offsetX", "owner": "jr", "name": "al", "access": 0, "descriptor": "I", "decoder": 967376007}, {"field": "offsetY", "owner": "jr", "name": "ae", "access": 0, "descriptor": "I", "decoder": 818611483}, {"field": "op", "owner": "jr", "name": "aj", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "params", "owner": "jr", "name": "af", "access": 0, "descriptor": "Llh;"}, {"field": "recol_d", "owner": "jr", "name": "i", "access": 0, "descriptor": "[S"}, {"field": "recol_s", "owner": "jr", "name": "n", "access": 0, "descriptor": "[S"}, {"field": "retex_d", "owner": "jr", "name": "z", "access": 0, "descriptor": "[S"}, {"field": "retex_s", "owner": "jr", "name": "a", "access": 0, "descriptor": "[S"}, {"field": "sizeX", "owner": "jr", "name": "j", "access": 1, "descriptor": "I", "decoder": 998026479}, {"field": "sizeY", "owner": "jr", "name": "s", "access": 1, "descriptor": "I", "decoder": -301273619}, {"field": "transformVarbit", "owner": "jr", "name": "am", "access": 0, "descriptor": "I", "decoder": 1136644531}, {"field": "transformVarp", "owner": "jr", "name": "an", "access": 0, "descriptor": "I", "decoder": -2119965579}, {"field": "transforms", "owner": "jr", "name": "as", "access": 1, "descriptor": "[I"}, {"field": "__d", "owner": "jr", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "__x", "owner": "jr", "name": "x", "access": 0, "descriptor": "[I"}, {"field": "__ax", "owner": "jr", "name": "ax", "access": 1, "descriptor": "[I"}], "methods": [{"method": "decode", "owner": "jr", "name": "w", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jr", "name": "o", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "getIntParam", "owner": "jr", "name": "z", "access": 1, "descriptor": "(IIB)I"}, {"method": "getModel", "owner": "jr", "name": "e", "access": 17, "descriptor": "(II[[IIIIB)Ldu;"}, {"method": "getModelData", "owner": "jr", "name": "d", "access": 16, "parameters": ["n", "m"], "descriptor": "(IIB)Ldw;"}, {"method": "getModelDynamic", "owner": "jr", "name": "x", "access": 17, "descriptor": "(II[[IIIILjh;II)Ldu;"}, {"method": "getStringParam", "owner": "jr", "name": "j", "access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"method": "postDecode", "owner": "jr", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "transform", "owner": "jr", "name": "a", "access": 17, "parameters": [], "descriptor": "(B)Ljr;"}, {"method": "__u_421", "owner": "jr", "name": "u", "access": 17, "parameters": ["arg0"], "descriptor": "(II)Z"}, {"method": "__g_422", "owner": "jr", "name": "g", "access": 17, "parameters": [], "descriptor": "(I)Z"}, {"method": "__l_423", "owner": "jr", "name": "l", "access": 17, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(II[[IIIIS)Lex;"}, {"method": "__t_424", "owner": "jr", "name": "t", "access": 1, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ItemDefinition", "name": "jv", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "ambient", "owner": "jv", "name": "ba", "access": 1, "descriptor": "I", "decoder": 738319589}, {"field": "boughtlink", "owner": "jv", "name": "bk", "access": 0, "descriptor": "I", "decoder": 700576005}, {"field": "boughttemplate", "owner": "jv", "name": "bd", "access": 0, "descriptor": "I", "decoder": -1969719185}, {"field": "certlink", "owner": "jv", "name": "ao", "access": 1, "descriptor": "I", "decoder": 619422509}, {"field": "certtemplate", "owner": "jv", "name": "aa", "access": 1, "descriptor": "I", "decoder": -2121346423}, {"field": "contrast", "owner": "jv", "name": "bb", "access": 1, "descriptor": "I", "decoder": -1984887305}, {"field": "cost", "owner": "jv", "name": "aq", "access": 1, "descriptor": "I", "decoder": -343501105}, {"field": "countco", "owner": "jv", "name": "au", "access": 0, "descriptor": "[I"}, {"field": "count<PERSON><PERSON>", "owner": "jv", "name": "az", "access": 0, "descriptor": "[I"}, {"field": "id", "owner": "jv", "name": "i", "access": 1, "descriptor": "I", "decoder": -590103687}, {"field": "iop", "owner": "jv", "name": "ar", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "manhead", "owner": "jv", "name": "ap", "access": 0, "descriptor": "I", "decoder": -650184697}, {"field": "manhead2", "owner": "jv", "name": "as", "access": 0, "descriptor": "I", "decoder": 548044049}, {"field": "manwear", "owner": "jv", "name": "ah", "access": 0, "descriptor": "I", "decoder": 939367983}, {"field": "manwear2", "owner": "jv", "name": "ak", "access": 0, "descriptor": "I", "decoder": 140643039}, {"field": "manwear3", "owner": "jv", "name": "at", "access": 0, "descriptor": "I", "decoder": -691559863}, {"field": "man<PERSON><PERSON><PERSON>", "owner": "jv", "name": "aw", "access": 0, "descriptor": "I", "decoder": -1378899027}, {"field": "members", "owner": "jv", "name": "aj", "access": 1, "descriptor": "Z"}, {"field": "model", "owner": "jv", "name": "a", "access": 0, "descriptor": "I", "decoder": 1070080857}, {"field": "name", "owner": "jv", "name": "z", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "op", "owner": "jv", "name": "av", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "params", "owner": "jv", "name": "bq", "access": 0, "descriptor": "Llh;"}, {"field": "placeholderlink", "owner": "jv", "name": "bc", "access": 1, "descriptor": "I", "decoder": 807815565}, {"field": "placeholdertemplate", "owner": "jv", "name": "bo", "access": 1, "descriptor": "I", "decoder": -1854342455}, {"field": "recol_d", "owner": "jv", "name": "s", "access": 0, "descriptor": "[S"}, {"field": "recol_s", "owner": "jv", "name": "j", "access": 0, "descriptor": "[S"}, {"field": "resizex", "owner": "jv", "name": "ax", "access": 0, "descriptor": "I", "decoder": 69034859}, {"field": "resizey", "owner": "jv", "name": "af", "access": 0, "descriptor": "I", "decoder": -17174497}, {"field": "resizez", "owner": "jv", "name": "ai", "access": 0, "descriptor": "I", "decoder": 1395132907}, {"field": "retex_d", "owner": "jv", "name": "y", "access": 0, "descriptor": "[S"}, {"field": "retex_s", "owner": "jv", "name": "t", "access": 0, "descriptor": "[S"}, {"field": "shiftclickindex", "owner": "jv", "name": "ay", "access": 0, "descriptor": "I", "decoder": 881309855}, {"field": "stackable", "owner": "jv", "name": "ag", "access": 1, "descriptor": "I", "decoder": -237597001}, {"field": "stockmarket", "owner": "jv", "name": "bn", "access": 1, "descriptor": "Z"}, {"field": "team", "owner": "jv", "name": "bs", "access": 1, "descriptor": "I", "decoder": -1057008657}, {"field": "womanhead", "owner": "jv", "name": "am", "access": 0, "descriptor": "I", "decoder": 1709659237}, {"field": "womanhead2", "owner": "jv", "name": "an", "access": 0, "descriptor": "I", "decoder": 106668727}, {"field": "womanwear", "owner": "jv", "name": "al", "access": 0, "descriptor": "I", "decoder": 886682223}, {"field": "womanwear2", "owner": "jv", "name": "ab", "access": 0, "descriptor": "I", "decoder": 154223105}, {"field": "womanwear3", "owner": "jv", "name": "ad", "access": 0, "descriptor": "I", "decoder": -771136925}, {"field": "woman<PERSON>yoff", "owner": "jv", "name": "ae", "access": 0, "descriptor": "I", "decoder": -1619361485}, {"field": "xan2d", "owner": "jv", "name": "b", "access": 1, "descriptor": "I", "decoder": 880150839}, {"field": "xof2d", "owner": "jv", "name": "p", "access": 1, "descriptor": "I", "decoder": -533708435}, {"field": "yan2d", "owner": "jv", "name": "c", "access": 1, "descriptor": "I", "decoder": 507808107}, {"field": "yof2d", "owner": "jv", "name": "v", "access": 1, "descriptor": "I", "decoder": -2130350563}, {"field": "zan2d", "owner": "jv", "name": "r", "access": 1, "descriptor": "I", "decoder": 551313257}, {"field": "zoom2d", "owner": "jv", "name": "h", "access": 1, "descriptor": "I", "decoder": 278037959}], "methods": [{"method": "decode", "owner": "jv", "name": "q", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jv", "name": "w", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "genBought", "owner": "jv", "name": "u", "access": 0, "parameters": ["template", "link"], "descriptor": "(Ljv;Ljv;I)V"}, {"method": "gen<PERSON><PERSON>", "owner": "jv", "name": "o", "access": 0, "parameters": ["template", "link"], "descriptor": "(Ljv;Ljv;I)V"}, {"method": "genPlaceholder", "owner": "jv", "name": "g", "access": 0, "parameters": ["template", "link"], "descriptor": "(Ljv;Ljv;I)V"}, {"method": "getCountObj", "owner": "jv", "name": "x", "access": 1, "parameters": ["quantity"], "descriptor": "(IB)Ljv;"}, {"method": "getIntParam", "owner": "jv", "name": "y", "access": 1, "descriptor": "(IIS)I"}, {"method": "getModel", "owner": "jv", "name": "e", "access": 17, "parameters": ["quantity"], "descriptor": "(II)Ldu;"}, {"method": "getShiftClickIndex", "owner": "jv", "name": "b", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "getStringParam", "owner": "jv", "name": "h", "access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"method": "__f_425", "owner": "jv", "name": "f", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__l_426", "owner": "jv", "name": "l", "access": 17, "parameters": ["arg0"], "descriptor": "(II)Ldw;"}, {"method": "__z_427", "owner": "jv", "name": "z", "access": 17, "parameters": ["arg0"], "descriptor": "(ZI)Z"}, {"method": "__j_428", "owner": "jv", "name": "j", "access": 17, "parameters": ["arg0"], "descriptor": "(ZB)Ldw;"}, {"method": "__s_429", "owner": "jv", "name": "s", "access": 17, "parameters": ["arg0"], "descriptor": "(ZI)Z"}, {"method": "__t_430", "owner": "jv", "name": "t", "access": 17, "parameters": ["arg0"], "descriptor": "(ZI)Ldw;"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "NpcDefinition", "name": "jz", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "combatLevel", "owner": "jz", "name": "c", "access": 1, "descriptor": "I", "decoder": 1192179847}, {"field": "drawMapDot", "owner": "jz", "name": "b", "access": 1, "descriptor": "Z"}, {"field": "head<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "jz", "name": "aj", "access": 1, "descriptor": "I", "decoder": 235824283}, {"field": "id", "owner": "jz", "name": "o", "access": 1, "descriptor": "I", "decoder": -1937346627}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "jz", "name": "aw", "access": 1, "descriptor": "Z"}, {"field": "isInteractable", "owner": "jz", "name": "ah", "access": 1, "descriptor": "Z"}, {"field": "models", "owner": "jz", "name": "l", "access": 0, "descriptor": "[I"}, {"field": "name", "owner": "jz", "name": "u", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "op", "owner": "jz", "name": "h", "access": 1, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "params", "owner": "jz", "name": "al", "access": 0, "descriptor": "Llh;"}, {"field": "<PERSON><PERSON><PERSON>", "owner": "jz", "name": "x", "access": 1, "descriptor": "I", "decoder": 275200787}, {"field": "recol_d", "owner": "jz", "name": "s", "access": 0, "descriptor": "[S"}, {"field": "recol_s", "owner": "jz", "name": "j", "access": 0, "descriptor": "[S"}, {"field": "resizeh", "owner": "jz", "name": "r", "access": 0, "descriptor": "I", "decoder": -1469219067}, {"field": "resizev", "owner": "jz", "name": "p", "access": 0, "descriptor": "I", "decoder": 1390683537}, {"field": "retex_d", "owner": "jz", "name": "y", "access": 0, "descriptor": "[S"}, {"field": "retex_s", "owner": "jz", "name": "t", "access": 0, "descriptor": "[S"}, {"field": "size", "owner": "jz", "name": "g", "access": 1, "descriptor": "I", "decoder": -1541308911}, {"field": "transformVarbit", "owner": "jz", "name": "ac", "access": 0, "descriptor": "I", "decoder": 1011388197}, {"field": "transformVarp", "owner": "jz", "name": "ay", "access": 0, "descriptor": "I", "decoder": 1631362233}, {"field": "transforms", "owner": "jz", "name": "ar", "access": 1, "descriptor": "[I"}, {"field": "turnleftanim", "owner": "jz", "name": "d", "access": 1, "descriptor": "I", "decoder": 819690597}, {"field": "turnright<PERSON>m", "owner": "jz", "name": "k", "access": 1, "descriptor": "I", "decoder": 1311553413}, {"field": "walkanim", "owner": "jz", "name": "n", "access": 1, "descriptor": "I", "decoder": -284110917}, {"field": "walkback<PERSON><PERSON>", "owner": "jz", "name": "i", "access": 1, "descriptor": "I", "decoder": 1952230339}, {"field": "walkleftanim", "owner": "jz", "name": "a", "access": 1, "descriptor": "I", "decoder": -1213774321}, {"field": "walk<PERSON><PERSON>m", "owner": "jz", "name": "z", "access": 1, "descriptor": "I", "decoder": -96930503}, {"field": "__ag", "owner": "jz", "name": "ag", "access": 0, "descriptor": "I", "decoder": -1235927151}, {"field": "__aq", "owner": "jz", "name": "aq", "access": 0, "descriptor": "I", "decoder": 1962875903}, {"field": "__e", "owner": "jz", "name": "e", "access": 0, "descriptor": "[I"}, {"field": "__ak", "owner": "jz", "name": "ak", "access": 1, "descriptor": "Z"}, {"field": "__v", "owner": "jz", "name": "v", "access": 1, "descriptor": "Z"}, {"field": "__av", "owner": "jz", "name": "av", "access": 1, "descriptor": "I", "decoder": -506638747}], "methods": [{"method": "decode", "owner": "jz", "name": "w", "access": 0, "descriptor": "(Lgr;I)V"}, {"method": "decode0", "owner": "jz", "name": "o", "access": 0, "descriptor": "(Lgr;IB)V"}, {"method": "getIntParam", "owner": "jz", "name": "x", "access": 1, "descriptor": "(III)I"}, {"method": "getModel", "owner": "jz", "name": "u", "access": 17, "parameters": ["s1", "n1", "s2", "n2"], "descriptor": "(Ljh;ILjh;II)Ldu;"}, {"method": "getModelData", "owner": "jz", "name": "g", "access": 17, "parameters": [], "descriptor": "(I)Ldw;"}, {"method": "getStringParam", "owner": "jz", "name": "d", "access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/String;"}, {"method": "postDecode", "owner": "jz", "name": "q", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "transform", "owner": "jz", "name": "l", "access": 17, "parameters": [], "descriptor": "(B)Ljz;"}, {"method": "__e_431", "owner": "jz", "name": "e", "access": 1, "parameters": [], "descriptor": "(I)Z"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "FloorOverlayDefinition", "name": "jf", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "hue", "owner": "jf", "name": "g", "access": 1, "descriptor": "I", "decoder": 2013422835}, {"field": "hue2", "owner": "jf", "name": "x", "access": 1, "descriptor": "I", "decoder": -122393061}, {"field": "lightness", "owner": "jf", "name": "e", "access": 1, "descriptor": "I", "decoder": -624223733}, {"field": "lightness2", "owner": "jf", "name": "k", "access": 1, "descriptor": "I", "decoder": 127639041}, {"field": "rgb", "owner": "jf", "name": "q", "access": 1, "descriptor": "I", "decoder": -111851793}, {"field": "rgb2", "owner": "jf", "name": "u", "access": 1, "descriptor": "I", "decoder": 528865753}, {"field": "saturation", "owner": "jf", "name": "l", "access": 1, "descriptor": "I", "decoder": -1719896767}, {"field": "saturation2", "owner": "jf", "name": "d", "access": 1, "descriptor": "I", "decoder": 1514187401}, {"field": "texture", "owner": "jf", "name": "w", "access": 1, "descriptor": "I", "decoder": 733377225}, {"field": "__o", "owner": "jf", "name": "o", "access": 1, "descriptor": "Z"}], "methods": [{"method": "decode", "owner": "jf", "name": "q", "access": 1, "descriptor": "(Lgr;II)V"}, {"method": "decode0", "owner": "jf", "name": "w", "access": 0, "descriptor": "(Lgr;III)V"}, {"method": "postDecode", "owner": "jf", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "setHsl", "owner": "jf", "name": "o", "access": 0, "parameters": ["rgb"], "descriptor": "(II)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "SequenceDefinition", "name": "jh", "super": "fn", "access": 33, "interfaces": [], "fields": [{"field": "frameCount", "owner": "jh", "name": "x", "access": 1, "descriptor": "I", "decoder": -736885973}, {"field": "frameIds", "owner": "jh", "name": "u", "access": 1, "descriptor": "[I"}, {"field": "frameIds2", "owner": "jh", "name": "g", "access": 0, "descriptor": "[I"}, {"field": "frameLengths", "owner": "jh", "name": "l", "access": 1, "descriptor": "[I"}, {"field": "shield", "owner": "jh", "name": "i", "access": 1, "descriptor": "I", "decoder": -1308226131}, {"field": "weapon", "owner": "jh", "name": "a", "access": 1, "descriptor": "I", "decoder": -1629538741}, {"field": "__d", "owner": "jh", "name": "d", "access": 0, "descriptor": "[I"}, {"field": "__k", "owner": "jh", "name": "k", "access": 1, "descriptor": "Z"}, {"field": "__j", "owner": "jh", "name": "j", "access": 1, "descriptor": "I", "decoder": 1339014215}, {"field": "__n", "owner": "jh", "name": "n", "access": 1, "descriptor": "I", "decoder": 1309695045}, {"field": "__s", "owner": "jh", "name": "s", "access": 1, "descriptor": "I", "decoder": 2012242069}, {"field": "__t", "owner": "jh", "name": "t", "access": 1, "descriptor": "I", "decoder": 1147391369}, {"field": "__z", "owner": "jh", "name": "z", "access": 1, "descriptor": "I", "decoder": -28345361}, {"field": "__e", "owner": "jh", "name": "e", "access": 1, "descriptor": "[I"}], "methods": [{"method": "animateComponent", "owner": "jh", "name": "e", "access": 1, "parameters": ["model", "frame"], "descriptor": "(Ldu;II)Ldu;"}, {"method": "animateObject", "owner": "jh", "name": "u", "access": 0, "parameters": ["model", "frame", "orientation"], "descriptor": "(Ldu;IIB)Ldu;"}, {"method": "animateSequence", "owner": "jh", "name": "o", "access": 1, "parameters": ["model", "frame"], "descriptor": "(Ldu;II)Ldu;"}, {"method": "animateSequence2", "owner": "jh", "name": "l", "access": 1, "parameters": ["model", "frame", "sequence", "sequenceFrame"], "descriptor": "(Ldu;ILjh;II)Ldu;"}, {"method": "animateSpotAnimation", "owner": "jh", "name": "g", "access": 0, "parameters": ["model", "frame"], "descriptor": "(Ldu;II)Ldu;"}, {"method": "decode", "owner": "jh", "name": "f", "access": 0, "descriptor": "(Lgr;S)V"}, {"method": "decode0", "owner": "jh", "name": "q", "access": 0, "descriptor": "(Lgr;II)V"}, {"method": "postDecode", "owner": "jh", "name": "w", "access": 0, "parameters": [], "descriptor": "(B)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "IterableNodeDeque", "name": "ja", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Iterable"], "fields": [{"field": "current", "owner": "ja", "name": "f", "access": 0, "descriptor": "Lgw;"}, {"field": "sentinel", "owner": "ja", "name": "m", "access": 0, "descriptor": "Lgw;"}], "methods": [{"method": "add<PERSON><PERSON><PERSON>", "owner": "ja", "name": "f", "access": 1, "parameters": ["node"], "descriptor": "(Lgw;)V"}, {"method": "addLast", "owner": "ja", "name": "q", "access": 1, "parameters": ["node"], "descriptor": "(Lgw;)V"}, {"method": "clear", "owner": "ja", "name": "m", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "isEmpty", "owner": "ja", "name": "l", "access": 1, "descriptor": "()Z"}, {"method": "last", "owner": "ja", "name": "o", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "previous", "owner": "ja", "name": "g", "access": 1, "parameters": [], "descriptor": "()Lgw;"}, {"method": "previousOrLast", "owner": "ja", "name": "u", "access": 0, "parameters": ["node"], "descriptor": "(Lgw;)Lgw;"}, {"method": "__iterator_432", "owner": "ja", "name": "iterator", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "LinkDeque", "name": "jy", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "jy", "name": "f", "access": 0, "descriptor": "Lgh;"}, {"field": "__m", "owner": "jy", "name": "m", "access": 0, "descriptor": "Lgh;"}], "methods": [{"method": "__m_433", "owner": "jy", "name": "m", "access": 1, "parameters": ["arg0"], "descriptor": "(Lgh;)V"}, {"method": "__f_434", "owner": "jy", "name": "f", "access": 1, "parameters": [], "descriptor": "()Lgh;"}, {"method": "__q_435", "owner": "jy", "name": "q", "access": 1, "parameters": [], "descriptor": "()Lgh;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "IterableDualNodeQueueIterator", "name": "jl", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Iterator"], "fields": [{"field": "queue", "owner": "jl", "name": "m", "access": 0, "descriptor": "Ljc;"}, {"field": "__f", "owner": "jl", "name": "f", "access": 0, "descriptor": "Lfn;"}, {"field": "__q", "owner": "jl", "name": "q", "access": 0, "descriptor": "Lfn;"}], "methods": [{"method": "__next_436", "owner": "jl", "name": "next", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "__hasNext_437", "owner": "jl", "name": "hasNext", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "__remove_438", "owner": "jl", "name": "remove", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Ljc;)V"}]}, {"class": "IterableNodeDequeDescendingIterator", "name": "jt", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Iterator"], "fields": [{"field": "deque", "owner": "jt", "name": "m", "access": 0, "descriptor": "Lja;"}, {"field": "__f", "owner": "jt", "name": "f", "access": 0, "descriptor": "Lgw;"}, {"field": "__q", "owner": "jt", "name": "q", "access": 0, "descriptor": "Lgw;"}], "methods": [{"method": "__u_439", "owner": "jt", "name": "u", "access": 0, "parameters": ["arg0"], "descriptor": "(Lja;)V"}, {"method": "__g_440", "owner": "jt", "name": "g", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__next_441", "owner": "jt", "name": "next", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "__hasNext_442", "owner": "jt", "name": "hasNext", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "__remove_443", "owner": "jt", "name": "remove", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Lja;)V"}]}, {"class": "IterableDualNodeQueue", "name": "jc", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Iterable"], "fields": [{"field": "head", "owner": "jc", "name": "f", "access": 0, "descriptor": "Lfn;"}, {"field": "sentinel", "owner": "jc", "name": "m", "access": 1, "descriptor": "Lfn;"}], "methods": [{"method": "add", "owner": "jc", "name": "f", "access": 1, "descriptor": "(Lfn;)V"}, {"method": "clear", "owner": "jc", "name": "m", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__q_444", "owner": "jc", "name": "q", "access": 1, "parameters": [], "descriptor": "()Lfn;"}, {"method": "__w_445", "owner": "jc", "name": "w", "access": 1, "parameters": [], "descriptor": "()Lfn;"}, {"method": "__o_446", "owner": "jc", "name": "o", "access": 0, "parameters": ["arg0"], "descriptor": "(Lfn;)Lfn;"}, {"method": "__u_447", "owner": "jc", "name": "u", "access": 1, "parameters": [], "descriptor": "()Lfn;"}, {"method": "__iterator_448", "owner": "jc", "name": "iterator", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "Timer", "name": "jo", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__e", "owner": "jo", "name": "e", "access": 0, "descriptor": "I", "decoder": 978183419}, {"field": "__g", "owner": "jo", "name": "g", "access": 0, "descriptor": "I", "decoder": -500639955}, {"field": "__l", "owner": "jo", "name": "l", "access": 0, "descriptor": "I", "decoder": 2043109577}, {"field": "__x", "owner": "jo", "name": "x", "access": 0, "descriptor": "I", "decoder": 1684916659}, {"field": "__f", "owner": "jo", "name": "f", "access": 0, "descriptor": "J", "decoder": -8166664055136527097}, {"field": "__m", "owner": "jo", "name": "m", "access": 0, "descriptor": "J", "decoder": 6181704275746246665}, {"field": "__o", "owner": "jo", "name": "o", "access": 0, "descriptor": "J", "decoder": -5820022560321695785}, {"field": "__u", "owner": "jo", "name": "u", "access": 0, "descriptor": "J", "decoder": -7044840680232740733}, {"field": "__w", "owner": "jo", "name": "w", "access": 0, "descriptor": "J", "decoder": -6206176920376957899}, {"field": "__q", "owner": "jo", "name": "q", "access": 1, "descriptor": "Z"}], "methods": [{"method": "write", "owner": "jo", "name": "g", "access": 1, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "__m_449", "owner": "jo", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__f_450", "owner": "jo", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)V"}, {"method": "__q_451", "owner": "jo", "name": "q", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__w_452", "owner": "jo", "name": "w", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__o_453", "owner": "jo", "name": "o", "access": 1, "parameters": [], "descriptor": "(S)V"}, {"method": "__u_454", "owner": "jo", "name": "u", "access": 1, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "User", "name": "js", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Comparable"], "fields": [{"field": "previousUsername", "owner": "js", "name": "w", "access": 0, "descriptor": "Lkp;"}, {"field": "username0", "owner": "js", "name": "q", "access": 0, "descriptor": "Lkp;"}], "methods": [{"method": "compareTo0", "owner": "js", "name": "aj", "access": 1, "parameters": ["other"], "descriptor": "(<PERSON><PERSON><PERSON>;<PERSON>)I"}, {"method": "name", "owner": "js", "name": "v", "access": 1, "parameters": [], "descriptor": "(B)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "previousName", "owner": "js", "name": "ag", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "set", "owner": "js", "name": "aq", "access": 0, "parameters": ["username", "previousUsername"], "descriptor": "(Lkp;Lkp;I)V"}, {"method": "username", "owner": "js", "name": "p", "access": 1, "parameters": [], "descriptor": "(I)Lkp;"}, {"method": "__compareTo_455", "owner": "js", "name": "compareTo", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "AbstractUserComparator", "name": "kf", "super": "java.lang.Object", "access": 1057, "interfaces": ["java.util.Comparator"], "fields": [{"field": "__f", "owner": "kf", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}], "methods": [{"method": "__e_456", "owner": "kf", "name": "e", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/util/Comparator;B)V"}, {"method": "__x_457", "owner": "kf", "name": "x", "access": 20, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON>js;<PERSON>js;I)I"}, {"method": "__equals_458", "owner": "kf", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 4, "descriptor": "()V"}]}, {"class": "Ignored", "name": "ks", "super": "js", "access": 33, "interfaces": [], "fields": [{"field": "id", "owner": "ks", "name": "m", "access": 0, "descriptor": "I", "decoder": 344024525}], "methods": [{"method": "compareTo00", "owner": "ks", "name": "m", "access": 0, "parameters": ["other"], "descriptor": "(Lks;I)I"}, {"method": "__aj_459", "owner": "ks", "name": "aj", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>;<PERSON>)I"}, {"method": "__compareTo_460", "owner": "ks", "name": "compareTo", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "FriendsList", "name": "kg", "super": "km", "access": 33, "interfaces": [], "fields": [{"field": "friendLoginUpdates", "owner": "kg", "name": "d", "access": 1, "descriptor": "Lj<PERSON>;"}, {"field": "loginType", "owner": "kg", "name": "q", "access": 16, "descriptor": "Llx;"}, {"field": "__x", "owner": "kg", "name": "x", "access": 0, "descriptor": "I", "decoder": -217526077}], "methods": [{"method": "newInstance", "owner": "kg", "name": "m", "access": 0, "descriptor": "(I)Ljs;"}, {"method": "newTypedArray", "owner": "kg", "name": "f", "access": 0, "descriptor": "(II)[Ljs;"}, {"method": "read", "owner": "kg", "name": "x", "access": 1, "parameters": ["buffer", "n"], "descriptor": "(Lgr;IB)V"}, {"method": "__q_461", "owner": "kg", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(Lkp;ZI)Z"}], "constructors": [{"access": 1, "descriptor": "(Llx;)V"}]}, {"class": "Username", "name": "kp", "super": "java.lang.Object", "access": 33, "interfaces": ["java.lang.Comparable"], "fields": [{"field": "cleanName", "owner": "kp", "name": "f", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "name0", "owner": "kp", "name": "m", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [{"method": "compareTo0", "owner": "kp", "name": "q", "access": 1, "parameters": ["other"], "descriptor": "(Lkp;B)I"}, {"method": "hasCleanName", "owner": "kp", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)Z"}, {"method": "name", "owner": "kp", "name": "m", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "__equals_462", "owner": "kp", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}, {"method": "__hashCode_463", "owner": "kp", "name": "hashCode", "access": 1, "parameters": [], "descriptor": "()I"}, {"method": "__toString_464", "owner": "kp", "name": "toString", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "__compareTo_465", "owner": "kp", "name": "compareTo", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 1, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;Llx;)V"}]}, {"class": "Friend", "name": "kq", "super": "ke", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "kq", "name": "f", "access": 0, "descriptor": "Z"}, {"field": "__m", "owner": "kq", "name": "m", "access": 0, "descriptor": "Z"}], "methods": [{"method": "compareTo00", "owner": "kq", "name": "m", "access": 0, "parameters": ["other"], "descriptor": "(Lkq;I)I"}, {"method": "__aj_466", "owner": "kq", "name": "aj", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>;<PERSON>)I"}, {"method": "__compareTo_467", "owner": "kq", "name": "compareTo", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)I"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "ClanChat", "name": "kv", "super": "km", "access": 33, "interfaces": [], "fields": [{"field": "localUser", "owner": "kv", "name": "q", "access": 16, "descriptor": "Lky;"}, {"field": "loginType", "owner": "kv", "name": "f", "access": 16, "descriptor": "Llx;"}, {"field": "min<PERSON><PERSON>", "owner": "kv", "name": "k", "access": 1, "descriptor": "B"}, {"field": "name", "owner": "kv", "name": "x", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "owner", "owner": "kv", "name": "d", "access": 1, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "rank", "owner": "kv", "name": "n", "access": 1, "descriptor": "I", "decoder": -1534670751}, {"field": "__i", "owner": "kv", "name": "i", "access": 0, "descriptor": "I", "decoder": 386706253}], "methods": [{"method": "clearFriends", "owner": "kv", "name": "cg", "access": 17, "parameters": [], "descriptor": "(I)V"}, {"method": "clearIgnoreds", "owner": "kv", "name": "cf", "access": 17, "parameters": [], "descriptor": "(B)V"}, {"method": "newInstance", "owner": "kv", "name": "m", "access": 0, "descriptor": "(I)Ljs;"}, {"method": "newTypedArray", "owner": "kv", "name": "f", "access": 0, "descriptor": "(II)[Ljs;"}, {"method": "readUpdate", "owner": "kv", "name": "d", "access": 17, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}, {"method": "__q_468", "owner": "kv", "name": "q", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "__x_469", "owner": "kv", "name": "x", "access": 16, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;B)V"}, {"method": "__a_470", "owner": "kv", "name": "a", "access": 17, "parameters": ["arg0"], "descriptor": "(Lgr;B)V"}, {"method": "__cp_471", "owner": "kv", "name": "cp", "access": 16, "parameters": ["arg0"], "descriptor": "(Ljk;I)V"}], "constructors": [{"access": 1, "descriptor": "(Llx;Lky;)V"}]}, {"class": "FontName", "name": "kx", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__g", "owner": "kx", "name": "g", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [], "constructors": [{"access": 0, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;)V"}]}, {"class": "Fonts", "name": "kz", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "map", "owner": "kz", "name": "q", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "__f", "owner": "kz", "name": "f", "access": 0, "descriptor": "Lir;"}, {"field": "__m", "owner": "kz", "name": "m", "access": 0, "descriptor": "Lir;"}], "methods": [{"method": "createMap", "owner": "kz", "name": "m", "access": 1, "descriptor": "([Lkx;I)Ljava/util/HashMap;"}], "constructors": [{"access": 1, "descriptor": "(Lir;Lir;)V"}]}, {"class": "GraphicsDefaults", "name": "lg", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "crosses", "owner": "lg", "name": "l", "access": 1, "descriptor": "I", "decoder": 536911453}, {"field": "headIconsHint", "owner": "lg", "name": "u", "access": 1, "descriptor": "I", "decoder": 185111653}, {"field": "headIconsPk", "owner": "lg", "name": "w", "access": 1, "descriptor": "I", "decoder": -1052769237}, {"field": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "lg", "name": "o", "access": 1, "descriptor": "I", "decoder": -1744250043}, {"field": "mapDots", "owner": "lg", "name": "e", "access": 1, "descriptor": "I", "decoder": -1114417319}, {"field": "mapMarkers", "owner": "lg", "name": "g", "access": 1, "descriptor": "I", "decoder": -1217061337}, {"field": "mapScenes", "owner": "lg", "name": "q", "access": 1, "descriptor": "I", "decoder": -104203005}, {"field": "modIcons", "owner": "lg", "name": "d", "access": 1, "descriptor": "I", "decoder": 2043130323}, {"field": "scrollBars", "owner": "lg", "name": "x", "access": 1, "descriptor": "I", "decoder": -919662109}, {"field": "__f", "owner": "lg", "name": "f", "access": 1, "descriptor": "I", "decoder": 159591367}, {"field": "__m", "owner": "lg", "name": "m", "access": 1, "descriptor": "I", "decoder": 1673796151}], "methods": [{"method": "decode", "owner": "lg", "name": "m", "access": 1, "parameters": ["index"], "descriptor": "(<PERSON>r;I)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "UserComparator1", "name": "ly", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Comparator"], "fields": [{"field": "__m", "owner": "ly", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_472", "owner": "ly", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Ljs;Ljs;B)I"}, {"method": "__compare_473", "owner": "ly", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_474", "owner": "ly", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "UserComparator2", "name": "li", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Comparator"], "fields": [{"field": "__m", "owner": "li", "name": "m", "access": 16, "descriptor": "Z"}], "methods": [{"method": "__m_475", "owner": "li", "name": "m", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON>js;<PERSON>js;I)I"}, {"method": "__compare_476", "owner": "li", "name": "compare", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;<PERSON><PERSON><PERSON>/lang/Object;)I"}, {"method": "__equals_477", "owner": "li", "name": "equals", "access": 1, "parameters": ["arg0"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;)Z"}], "constructors": [{"access": 1, "descriptor": "(Z)V"}]}, {"class": "IterableNodeHashTable", "name": "lh", "super": "java.lang.Object", "access": 49, "interfaces": ["java.lang.Iterable"], "fields": [{"field": "buckets", "owner": "lh", "name": "f", "access": 0, "descriptor": "[Lgw;"}, {"field": "current", "owner": "lh", "name": "w", "access": 0, "descriptor": "Lgw;"}, {"field": "currentGet", "owner": "lh", "name": "q", "access": 0, "descriptor": "Lgw;"}, {"field": "index", "owner": "lh", "name": "o", "access": 0, "descriptor": "I"}, {"field": "size", "owner": "lh", "name": "m", "access": 0, "descriptor": "I"}], "methods": [{"method": "clear", "owner": "lh", "name": "q", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "first", "owner": "lh", "name": "w", "access": 1, "descriptor": "()Lgw;"}, {"method": "get", "owner": "lh", "name": "m", "access": 1, "descriptor": "(J)Lgw;"}, {"method": "next", "owner": "lh", "name": "o", "access": 1, "descriptor": "()Lgw;"}, {"method": "put", "owner": "lh", "name": "f", "access": 1, "descriptor": "(Lgw;J)V"}, {"method": "__iterator_478", "owner": "lh", "name": "iterator", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/util/Iterator;"}], "constructors": [{"access": 1, "descriptor": "(I)V"}]}, {"class": "IterableNodeHashTableIterator", "name": "lk", "super": "java.lang.Object", "access": 33, "interfaces": ["java.util.Iterator"], "fields": [{"field": "hashTable", "owner": "lk", "name": "m", "access": 0, "descriptor": "Llh;"}, {"field": "__f", "owner": "lk", "name": "f", "access": 0, "descriptor": "Lgw;"}, {"field": "__w", "owner": "lk", "name": "w", "access": 0, "descriptor": "Lgw;"}, {"field": "__q", "owner": "lk", "name": "q", "access": 0, "descriptor": "I"}], "methods": [{"method": "__u_479", "owner": "lk", "name": "u", "access": 0, "parameters": [], "descriptor": "()V"}, {"method": "__next_480", "owner": "lk", "name": "next", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/Object;"}, {"method": "__hasNext_481", "owner": "lk", "name": "hasNext", "access": 1, "parameters": [], "descriptor": "()Z"}, {"method": "__remove_482", "owner": "lk", "name": "remove", "access": 1, "parameters": [], "descriptor": "()V"}], "constructors": [{"access": 0, "descriptor": "(Llh;)V"}]}, {"class": "Bounds", "name": "la", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__f", "owner": "la", "name": "f", "access": 1, "descriptor": "I", "decoder": -1887298731}, {"field": "__m", "owner": "la", "name": "m", "access": 1, "descriptor": "I", "decoder": -2004088829}, {"field": "__q", "owner": "la", "name": "q", "access": 1, "descriptor": "I", "decoder": 2001187823}, {"field": "__w", "owner": "la", "name": "w", "access": 1, "descriptor": "I", "decoder": 1377856837}], "methods": [{"method": "__m_483", "owner": "la", "name": "m", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(IIB)V"}, {"method": "__f_484", "owner": "la", "name": "f", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__toString_485", "owner": "la", "name": "toString", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "__q_486", "owner": "la", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(Lla;Lla;I)V"}, {"method": "__w_487", "owner": "la", "name": "w", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lla;Lla;I)V"}, {"method": "__o_488", "owner": "la", "name": "o", "access": 0, "parameters": ["arg0", "arg1"], "descriptor": "(Lla;Lla;S)V"}, {"method": "__u_489", "owner": "la", "name": "u", "access": 0, "parameters": [], "descriptor": "(I)I"}, {"method": "__g_490", "owner": "la", "name": "g", "access": 0, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 1, "descriptor": "(II)V"}, {"access": 1, "descriptor": "(IIII)V"}]}, {"class": "RectangleMode", "name": "lj", "super": "java.lang.Object", "access": 33, "interfaces": ["gc"], "fields": [{"field": "id", "owner": "lj", "name": "w", "access": 17, "descriptor": "I", "decoder": -1391612747}, {"field": "id2", "owner": "lj", "name": "o", "access": 16, "descriptor": "I", "decoder": -1204297807}], "methods": [{"method": "__f_491", "owner": "lj", "name": "f", "access": 1, "parameters": [], "descriptor": "(B)I"}], "constructors": [{"access": 0, "descriptor": "(II)V"}]}, {"class": "IndexedSprite", "name": "lq", "super": "lf", "access": 49, "interfaces": [], "fields": [{"field": "height", "owner": "lq", "name": "l", "access": 1, "descriptor": "I"}, {"field": "palette", "owner": "lq", "name": "f", "access": 1, "descriptor": "[I"}, {"field": "pixels", "owner": "lq", "name": "m", "access": 1, "descriptor": "[B"}, {"field": "subHeight", "owner": "lq", "name": "w", "access": 1, "descriptor": "I"}, {"field": "subWidth", "owner": "lq", "name": "q", "access": 1, "descriptor": "I"}, {"field": "width", "owner": "lq", "name": "g", "access": 1, "descriptor": "I"}, {"field": "xOffset", "owner": "lq", "name": "o", "access": 1, "descriptor": "I"}, {"field": "yOffset", "owner": "lq", "name": "u", "access": 1, "descriptor": "I"}], "methods": [{"method": "normalize", "owner": "lq", "name": "m", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "shiftColors", "owner": "lq", "name": "f", "access": 1, "parameters": ["r", "g", "b"], "descriptor": "(III)V"}, {"method": "__q_492", "owner": "lq", "name": "q", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(II)V"}, {"method": "__o_493", "owner": "lq", "name": "o", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}], "constructors": [{"access": 0, "descriptor": "()V"}]}, {"class": "Sprite", "name": "ln", "super": "lf", "access": 49, "interfaces": [], "fields": [{"field": "height", "owner": "ln", "name": "g", "access": 1, "descriptor": "I"}, {"field": "pixels", "owner": "ln", "name": "m", "access": 1, "descriptor": "[I"}, {"field": "subHeight", "owner": "ln", "name": "q", "access": 1, "descriptor": "I"}, {"field": "subWidth", "owner": "ln", "name": "f", "access": 1, "descriptor": "I"}, {"field": "width", "owner": "ln", "name": "u", "access": 1, "descriptor": "I"}, {"field": "xOffset", "owner": "ln", "name": "o", "access": 0, "descriptor": "I"}, {"field": "yOffset", "owner": "ln", "name": "w", "access": 1, "descriptor": "I"}], "methods": [{"method": "copy", "owner": "ln", "name": "m", "access": 1, "parameters": [], "descriptor": "()Lln;"}, {"method": "copyNormalized", "owner": "ln", "name": "f", "access": 1, "parameters": [], "descriptor": "()Lln;"}, {"method": "normalize", "owner": "ln", "name": "w", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__q_494", "owner": "ln", "name": "q", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__o_495", "owner": "ln", "name": "o", "access": 1, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__u_496", "owner": "ln", "name": "u", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__g_497", "owner": "ln", "name": "g", "access": 1, "parameters": [], "descriptor": "()V"}, {"method": "__l_498", "owner": "ln", "name": "l", "access": 1, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__e_499", "owner": "ln", "name": "e", "access": 1, "parameters": ["arg0"], "descriptor": "(I)V"}, {"method": "__x_500", "owner": "ln", "name": "x", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(II)V"}, {"method": "__a_501", "owner": "ln", "name": "a", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(II)V"}, {"method": "__j_502", "owner": "ln", "name": "j", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}, {"method": "__t_503", "owner": "ln", "name": "t", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}, {"method": "__h_504", "owner": "ln", "name": "h", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__c_505", "owner": "ln", "name": "c", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIII)V"}, {"method": "__v_506", "owner": "ln", "name": "v", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(III)V"}, {"method": "__aj_507", "owner": "ln", "name": "aj", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4"], "descriptor": "(IIIII)V"}, {"method": "__ac_508", "owner": "ln", "name": "ac", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7"], "descriptor": "(IIIIII[I[I)V"}, {"method": "__ay_509", "owner": "ln", "name": "ay", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "arg8", "arg9"], "descriptor": "(IIIIIIII[I[I)V"}, {"method": "__ah_510", "owner": "ln", "name": "ah", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7"], "descriptor": "(IIIIIIDI)V"}, {"method": "__ak_511", "owner": "ln", "name": "ak", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}, {"method": "__aw_512", "owner": "ln", "name": "aw", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIII)V"}, {"method": "__al_513", "owner": "ln", "name": "al", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIII)V"}], "constructors": [{"access": 0, "descriptor": "()V"}, {"access": 1, "descriptor": "(II)V"}, {"access": 1, "descriptor": "([III)V"}]}, {"class": "PlatformInfo", "name": "lb", "super": "gw", "access": 33, "interfaces": [], "fields": [{"field": "__ah", "owner": "lb", "name": "ah", "access": 0, "descriptor": "Z"}, {"field": "__at", "owner": "lb", "name": "at", "access": 0, "descriptor": "Z"}, {"field": "__ab", "owner": "lb", "name": "ab", "access": 0, "descriptor": "I", "decoder": -1667177301}, {"field": "__ad", "owner": "lb", "name": "ad", "access": 0, "descriptor": "I", "decoder": -1765017}, {"field": "__ae", "owner": "lb", "name": "ae", "access": 0, "descriptor": "I", "decoder": 385109109}, {"field": "__af", "owner": "lb", "name": "af", "access": 0, "descriptor": "I", "decoder": 1955966365}, {"field": "__ai", "owner": "lb", "name": "ai", "access": 0, "descriptor": "I", "decoder": 1196598207}, {"field": "__ak", "owner": "lb", "name": "ak", "access": 0, "descriptor": "I", "decoder": 703904161}, {"field": "__al", "owner": "lb", "name": "al", "access": 0, "descriptor": "I", "decoder": 2097018521}, {"field": "__am", "owner": "lb", "name": "am", "access": 0, "descriptor": "I", "decoder": 30207059}, {"field": "__an", "owner": "lb", "name": "an", "access": 0, "descriptor": "I", "decoder": 890109053}, {"field": "__ap", "owner": "lb", "name": "ap", "access": 0, "descriptor": "I", "decoder": -817941105}, {"field": "__aw", "owner": "lb", "name": "aw", "access": 0, "descriptor": "I", "decoder": 467004305}, {"field": "__ax", "owner": "lb", "name": "ax", "access": 0, "descriptor": "I", "decoder": 871094255}, {"field": "__ay", "owner": "lb", "name": "ay", "access": 0, "descriptor": "I", "decoder": 1318366785}, {"field": "__ba", "owner": "lb", "name": "ba", "access": 0, "descriptor": "I", "decoder": -170839105}, {"field": "__bn", "owner": "lb", "name": "bn", "access": 0, "descriptor": "I", "decoder": 1466207635}, {"field": "__bq", "owner": "lb", "name": "bq", "access": 0, "descriptor": "[I"}, {"field": "__aa", "owner": "lb", "name": "aa", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__ao", "owner": "lb", "name": "ao", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__au", "owner": "lb", "name": "au", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__az", "owner": "lb", "name": "az", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__bb", "owner": "lb", "name": "bb", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__bk", "owner": "lb", "name": "bk", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__bs", "owner": "lb", "name": "bs", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}], "methods": [{"method": "size", "owner": "lb", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "write", "owner": "lb", "name": "m", "access": 1, "parameters": ["buffer"], "descriptor": "(Lgr;I)V"}], "constructors": [{"access": 0, "descriptor": "(IZIIIIIZIIIILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIILjava/lang/String;Ljava/lang/String;[IILjava/lang/String;)V"}]}, {"class": "WorldMap", "name": "lz", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "lz", "name": "h", "access": 0, "descriptor": "Lls;"}, {"field": "currentMapArea0", "owner": "lz", "name": "s", "access": 0, "descriptor": "Lag;"}, {"field": "elementsDisabled", "owner": "lz", "name": "ai", "access": 0, "descriptor": "Z"}, {"field": "enabledCategories", "owner": "lz", "name": "aa", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "enabledElements", "owner": "lz", "name": "ao", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "flashingElements", "owner": "lz", "name": "ak", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "font", "owner": "lz", "name": "n", "access": 0, "descriptor": "Lkk;"}, {"field": "fonts", "owner": "lz", "name": "i", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "iconIterator", "owner": "lz", "name": "bn", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/Iterator;"}, {"field": "mainMapArea", "owner": "lz", "name": "j", "access": 0, "descriptor": "Lag;"}, {"field": "mapAreas", "owner": "lz", "name": "z", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "mapSceneSprites", "owner": "lz", "name": "a", "access": 0, "descriptor": "[Llq;"}, {"field": "mouseCoord", "owner": "lz", "name": "bd", "access": 0, "descriptor": "<PERSON><PERSON>;"}, {"field": "perpetualFlash0", "owner": "lz", "name": "ah", "access": 0, "descriptor": "Z"}, {"field": "sprite", "owner": "lz", "name": "bo", "access": 0, "descriptor": "Lln;"}, {"field": "worldMapManager", "owner": "lz", "name": "y", "access": 0, "descriptor": "<PERSON>;"}, {"field": "zoom", "owner": "lz", "name": "v", "access": 0, "descriptor": "F"}, {"field": "zoomTarget", "owner": "lz", "name": "ag", "access": 0, "descriptor": "F"}, {"field": "__t", "owner": "lz", "name": "t", "access": 0, "descriptor": "Lag;"}, {"field": "__an", "owner": "lz", "name": "an", "access": 0, "descriptor": "Z"}, {"field": "__ab", "owner": "lz", "name": "ab", "access": 0, "descriptor": "I", "decoder": -1979665983}, {"field": "__ac", "owner": "lz", "name": "ac", "access": 0, "descriptor": "I", "decoder": -1653084915}, {"field": "__ad", "owner": "lz", "name": "ad", "access": 0, "descriptor": "I", "decoder": -988933407}, {"field": "__ae", "owner": "lz", "name": "ae", "access": 0, "descriptor": "I", "decoder": 547126547}, {"field": "__aj", "owner": "lz", "name": "aj", "access": 0, "descriptor": "I", "decoder": -1140942239}, {"field": "__al", "owner": "lz", "name": "al", "access": 0, "descriptor": "I", "decoder": 2064651113}, {"field": "__am", "owner": "lz", "name": "am", "access": 0, "descriptor": "I", "decoder": -869889187}, {"field": "__aq", "owner": "lz", "name": "aq", "access": 0, "descriptor": "I", "decoder": -1469654441}, {"field": "__ar", "owner": "lz", "name": "ar", "access": 0, "descriptor": "I", "decoder": -298919797}, {"field": "__as", "owner": "lz", "name": "as", "access": 0, "descriptor": "I", "decoder": -969094439}, {"field": "__at", "owner": "lz", "name": "at", "access": 0, "descriptor": "I", "decoder": -815748997}, {"field": "__av", "owner": "lz", "name": "av", "access": 0, "descriptor": "I", "decoder": -1301477687}, {"field": "__aw", "owner": "lz", "name": "aw", "access": 0, "descriptor": "I", "decoder": -1683910031}, {"field": "__ay", "owner": "lz", "name": "ay", "access": 0, "descriptor": "I", "decoder": 1664740569}, {"field": "__b", "owner": "lz", "name": "b", "access": 0, "descriptor": "I", "decoder": 1711576969}, {"field": "__ba", "owner": "lz", "name": "ba", "access": 0, "descriptor": "I", "decoder": -237734529}, {"field": "__bm", "owner": "lz", "name": "bm", "access": 0, "descriptor": "I", "decoder": -1774424249}, {"field": "__bu", "owner": "lz", "name": "bu", "access": 0, "descriptor": "I", "decoder": 2064728623}, {"field": "__bx", "owner": "lz", "name": "bx", "access": 0, "descriptor": "I", "decoder": 2031417343}, {"field": "__by", "owner": "lz", "name": "by", "access": 0, "descriptor": "I", "decoder": 754899959}, {"field": "__c", "owner": "lz", "name": "c", "access": 0, "descriptor": "I", "decoder": 1318118645}, {"field": "__p", "owner": "lz", "name": "p", "access": 0, "descriptor": "I", "decoder": -56022357}, {"field": "__r", "owner": "lz", "name": "r", "access": 0, "descriptor": "I", "decoder": -89657613}, {"field": "__e", "owner": "lz", "name": "e", "access": 0, "descriptor": "Lir;"}, {"field": "__g", "owner": "lz", "name": "g", "access": 0, "descriptor": "Lir;"}, {"field": "__l", "owner": "lz", "name": "l", "access": 0, "descriptor": "Lir;"}, {"field": "__af", "owner": "lz", "name": "af", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "__ax", "owner": "lz", "name": "ax", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "__bk", "owner": "lz", "name": "bk", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/HashSet;"}, {"field": "__bq", "owner": "lz", "name": "bq", "access": 0, "descriptor": "<PERSON><PERSON><PERSON>/util/List;"}, {"field": "__ap", "owner": "lz", "name": "ap", "access": 0, "descriptor": "J", "decoder": -4747643242047040283}, {"field": "__bs", "owner": "lz", "name": "bs", "access": 16, "descriptor": "[I"}, {"field": "__bc", "owner": "lz", "name": "bc", "access": 1, "descriptor": "Z"}], "methods": [{"method": "currentMapAreaId", "owner": "lz", "name": "j", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "disableCategory", "owner": "lz", "name": "bb", "access": 1, "descriptor": "(IZI)V"}, {"method": "disableElement", "owner": "lz", "name": "ba", "access": 1, "descriptor": "(IZI)V"}, {"method": "draw", "owner": "lz", "name": "c", "access": 1, "parameters": ["x", "y", "width", "height", "cycle"], "descriptor": "(IIIIII)V"}, {"method": "drawLoading", "owner": "lz", "name": "aj", "access": 0, "descriptor": "(IIIIIB)V"}, {"method": "drawOverview", "owner": "lz", "name": "ag", "access": 1, "parameters": ["x", "y", "width", "height"], "descriptor": "(IIIIB)V"}, {"method": "flashCategory", "owner": "lz", "name": "ax", "access": 1, "descriptor": "(IB)V"}, {"method": "flashElement", "owner": "lz", "name": "aa", "access": 1, "descriptor": "(II)V"}, {"method": "getCurrentMapArea", "owner": "lz", "name": "s", "access": 1, "parameters": [], "descriptor": "(I)Lag;"}, {"method": "getElementsEnabled", "owner": "lz", "name": "bs", "access": 1, "parameters": [], "descriptor": "(B)Z"}, {"method": "getMapArea", "owner": "lz", "name": "ah", "access": 1, "parameters": ["id"], "descriptor": "(II)Lag;"}, {"method": "getZoomLevel", "owner": "lz", "name": "ar", "access": 1, "parameters": [], "descriptor": "(B)I"}, {"method": "iconNext", "owner": "lz", "name": "by", "access": 1, "descriptor": "(B)Lak;"}, {"method": "iconStart", "owner": "lz", "name": "bx", "access": 1, "descriptor": "(I)Lak;"}, {"method": "init", "owner": "lz", "name": "m", "access": 1, "descriptor": "(Lir;Lir;Lir;Lkk;<PERSON><PERSON><PERSON>/util/<PERSON>h<PERSON>ap;[Llq;I)V"}, {"method": "initializeWorldMapManager", "owner": "lz", "name": "y", "access": 0, "parameters": ["mapArea"], "descriptor": "(Lag;I)V"}, {"method": "isCacheLoaded", "owner": "lz", "name": "ay", "access": 1, "parameters": [], "descriptor": "(S)Z"}, {"method": "isCategoryDisabled", "owner": "lz", "name": "bn", "access": 1, "descriptor": "(II)Z"}, {"method": "isElementDisabled", "owner": "lz", "name": "bq", "access": 1, "descriptor": "(IB)Z"}, {"method": "jump", "owner": "lz", "name": "b", "access": 0, "parameters": ["plane", "x", "y"], "descriptor": "(IIII)V"}, {"method": "loadCache", "owner": "lz", "name": "ac", "access": 1, "parameters": [], "descriptor": "(B)V"}, {"method": "mapAreaAtCoord", "owner": "lz", "name": "d", "access": 1, "parameters": ["plane", "x", "y"], "descriptor": "(IIII)Lag;"}, {"method": "menuAction", "owner": "lz", "name": "bo", "access": 1, "parameters": ["a", "b", "c", "d"], "descriptor": "(<PERSON><PERSON><PERSON>;<PERSON>hu;B)V"}, {"method": "onCycle", "owner": "lz", "name": "q", "access": 1, "parameters": ["a", "b", "c", "d", "e", "f", "g"], "descriptor": "(IIZIIIIB)V"}, {"method": "perpetualFlash", "owner": "lz", "name": "ao", "access": 1, "descriptor": "(ZI)V"}, {"method": "setCurrentMapArea", "owner": "lz", "name": "t", "access": 0, "parameters": ["mapArea"], "descriptor": "(Lag;B)V"}, {"method": "setCurrentMapAreaId", "owner": "lz", "name": "z", "access": 1, "parameters": ["id"], "descriptor": "(II)V"}, {"method": "setElementsEnabled", "owner": "lz", "name": "ai", "access": 1, "descriptor": "(ZI)V"}, {"method": "setZoomLevel", "owner": "lz", "name": "aq", "access": 1, "parameters": ["zoom"], "descriptor": "(II)V"}, {"method": "smoothZoom", "owner": "lz", "name": "u", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "stopCurrentFlashes", "owner": "lz", "name": "af", "access": 1, "descriptor": "(I)V"}, {"method": "__f_514", "owner": "lz", "name": "f", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__w_515", "owner": "lz", "name": "w", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIZZS)V"}, {"method": "__o_516", "owner": "lz", "name": "o", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIZJ)V"}, {"method": "__g_517", "owner": "lz", "name": "g", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__l_518", "owner": "lz", "name": "l", "access": 16, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIZI)V"}, {"method": "__e_519", "owner": "lz", "name": "e", "access": 16, "parameters": [], "descriptor": "(B)V"}, {"method": "__x_520", "owner": "lz", "name": "x", "access": 0, "parameters": [], "descriptor": "(I)Z"}, {"method": "__a_521", "owner": "lz", "name": "a", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(IIIZI)V"}, {"method": "__h_522", "owner": "lz", "name": "h", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3"], "descriptor": "(<PERSON>g;<PERSON>hu;<PERSON>hu;ZI)V"}, {"method": "__p_523", "owner": "lz", "name": "p", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIIII)Z"}, {"method": "__v_524", "owner": "lz", "name": "v", "access": 0, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIIII)V"}, {"method": "__av_525", "owner": "lz", "name": "av", "access": 0, "parameters": ["arg0"], "descriptor": "(IB)F"}, {"method": "__ak_526", "owner": "lz", "name": "ak", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__aw_527", "owner": "lz", "name": "aw", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(III)V"}, {"method": "__al_528", "owner": "lz", "name": "al", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIIB)V"}, {"method": "__ab_529", "owner": "lz", "name": "ab", "access": 1, "parameters": ["arg0", "arg1", "arg2"], "descriptor": "(IIII)V"}, {"method": "__ae_530", "owner": "lz", "name": "ae", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__at_531", "owner": "lz", "name": "at", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__ad_532", "owner": "lz", "name": "ad", "access": 1, "parameters": [], "descriptor": "(I)<PERSON><PERSON>;"}, {"method": "__ap_533", "owner": "lz", "name": "ap", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__as_534", "owner": "lz", "name": "as", "access": 1, "parameters": [], "descriptor": "(I)I"}, {"method": "__am_535", "owner": "lz", "name": "am", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__an_536", "owner": "lz", "name": "an", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__az_537", "owner": "lz", "name": "az", "access": 1, "parameters": ["arg0"], "descriptor": "(II)V"}, {"method": "__au_538", "owner": "lz", "name": "au", "access": 1, "parameters": [], "descriptor": "(I)V"}, {"method": "__bk_539", "owner": "lz", "name": "bk", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__bd_540", "owner": "lz", "name": "bd", "access": 1, "parameters": ["arg0", "arg1", "arg2", "arg3", "arg4", "arg5"], "descriptor": "(IIIIIIB)V"}, {"method": "__bc_541", "owner": "lz", "name": "bc", "access": 1, "parameters": ["arg0", "arg1"], "descriptor": "(ILhu;I)Lhu;"}], "constructors": [{"access": 1, "descriptor": "()V"}]}, {"class": "LoginType", "name": "lx", "super": "java.lang.Object", "access": 33, "interfaces": [], "fields": [{"field": "__d", "owner": "lx", "name": "d", "access": 16, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__x", "owner": "lx", "name": "x", "access": 17, "descriptor": "I", "decoder": -1916729379}], "methods": [{"method": "__toString_542", "owner": "lx", "name": "toString", "access": 1, "parameters": [], "descriptor": "()<PERSON><PERSON><PERSON>/lang/String;"}], "constructors": [{"access": 0, "descriptor": "(II<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;)V"}, {"access": 0, "descriptor": "(<PERSON><PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;Z[Llx;)V"}]}, {"class": "Client", "name": "client", "super": "ba", "access": 49, "interfaces": ["ky"], "fields": [{"field": "AbstractFont_alpha", "owner": "kt", "name": "s", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_color", "owner": "kt", "name": "j", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_justificationCurrent", "owner": "kt", "name": "y", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_justificationTotal", "owner": "kt", "name": "t", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_lines", "owner": "kt", "name": "b", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "AbstractFont_modIconSprites", "owner": "kt", "name": "x", "access": 9, "descriptor": "[Llq;"}, {"field": "AbstractFont_previousColor", "owner": "kt", "name": "z", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_previousShadow", "owner": "kt", "name": "i", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_random", "owner": "kt", "name": "h", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/util/Random;"}, {"field": "AbstractFont_shadow", "owner": "kt", "name": "a", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_strike", "owner": "kt", "name": "k", "access": 8, "descriptor": "I"}, {"field": "AbstractFont_underline", "owner": "kt", "name": "n", "access": 8, "descriptor": "I"}, {"field": "ArchiveDiskActionHandler_lock", "owner": "iv", "name": "w", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/Object;"}, {"field": "ArchiveDiskActionHandler_requestQueue", "owner": "iv", "name": "m", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "ArchiveDiskActionHandler_responseQueue", "owner": "iv", "name": "f", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "ArchiveDiskActionHandler_thread", "owner": "en", "name": "o", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/Thread;"}, {"field": "ArchiveDisk_buffer", "owner": "ff", "name": "m", "access": 8, "descriptor": "[B"}, {"field": "Archive_crc", "owner": "it", "name": "aj", "access": 8, "descriptor": "Ljava/util/zip/CRC32;"}, {"field": "AttackOption_alwaysRightClick", "owner": "cj", "name": "f", "access": 24, "descriptor": "Lcj;"}, {"field": "AttackOption_dependsOnCombatLevels", "owner": "cj", "name": "m", "access": 24, "descriptor": "Lcj;"}, {"field": "AttackOption_hidden", "owner": "cj", "name": "w", "access": 24, "descriptor": "Lcj;"}, {"field": "AttackOption_leftClickWhereAvailable", "owner": "cj", "name": "q", "access": 24, "descriptor": "Lcj;"}, {"field": "ByteArrayPool_large", "owner": "gi", "name": "u", "access": 8, "descriptor": "[[B"}, {"field": "ByteArrayPool_largeCount", "owner": "gi", "name": "q", "access": 8, "descriptor": "I", "decoder": -971800905}, {"field": "ByteArrayPool_medium", "owner": "gi", "name": "o", "access": 8, "descriptor": "[[B"}, {"field": "ByteArrayPool_mediumCount", "owner": "gi", "name": "f", "access": 8, "descriptor": "I", "decoder": 2121641763}, {"field": "ByteArrayPool_small", "owner": "gi", "name": "w", "access": 8, "descriptor": "[[B"}, {"field": "ByteArrayPool_smallCount", "owner": "gi", "name": "m", "access": 8, "descriptor": "I", "decoder": -791125441}, {"field": "Bzip2Decompressor_state", "owner": "gk", "name": "l", "access": 8, "descriptor": "Lgt;"}, {"field": "Component_archive", "owner": "ho", "name": "e", "access": 9, "descriptor": "Lir;"}, {"field": "Component_cachedFonts", "owner": "ho", "name": "a", "access": 8, "descriptor": "Ler;"}, {"field": "Component_cachedModels", "owner": "ho", "name": "i", "access": 8, "descriptor": "Ler;"}, {"field": "DefaultsGroup_graphics", "owner": "ka", "name": "m", "access": 24, "descriptor": "Lka;"}, {"field": "EnumDefinition_archive", "owner": "jw", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "EnumDefinition_cached", "owner": "jw", "name": "f", "access": 8, "descriptor": "Ler;"}, {"field": "FloorUnderlayDefinition_archive", "owner": "ih", "name": "m", "access": 8, "descriptor": "Lir;"}, {"field": "FloorUnderlayDefinition_cached", "owner": "ih", "name": "f", "access": 8, "descriptor": "Ler;"}, {"field": "FontName_bold12", "owner": "kx", "name": "q", "access": 25, "descriptor": "Lkx;"}, {"field": "FontName_plain11", "owner": "kx", "name": "m", "access": 25, "descriptor": "Lkx;"}, {"field": "FontName_plain12", "owner": "kx", "name": "f", "access": 25, "descriptor": "Lkx;"}, {"field": "FontName_verdana11", "owner": "kx", "name": "w", "access": 25, "descriptor": "Lkx;"}, {"field": "FontName_verdana13", "owner": "kx", "name": "o", "access": 25, "descriptor": "Lkx;"}, {"field": "FontName_verdana15", "owner": "kx", "name": "u", "access": 25, "descriptor": "Lkx;"}, {"field": "Formatting_colorEndTag", "owner": "cy", "name": "l", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_comma", "owner": "cy", "name": "f", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_lineBreakTag", "owner": "cy", "name": "g", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_pipe", "owner": "cy", "name": "q", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_rightArrow", "owner": "cy", "name": "u", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_rightParenthesis", "owner": "cy", "name": "o", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_spaceLeftParenthesis", "owner": "cy", "name": "w", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Formatting_true", "owner": "cy", "name": "m", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "HealthBarDefinition_cached", "owner": "ii", "name": "q", "access": 9, "descriptor": "Ler;"}, {"field": "HealthBarDefinition_cachedSprites", "owner": "ii", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "HitmarkDefinition_cached", "owner": "jm", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "HitmarkDefinition_cachedFonts", "owner": "jm", "name": "u", "access": 8, "descriptor": "Ler;"}, {"field": "Instrument_delays", "owner": "db", "name": "c", "access": 8, "descriptor": "[I"}, {"field": "Instrument_noise", "owner": "db", "name": "t", "access": 8, "descriptor": "[I"}, {"field": "Instrument_phases", "owner": "db", "name": "b", "access": 8, "descriptor": "[I"}, {"field": "Instrument_pitchBaseSteps", "owner": "db", "name": "v", "access": 8, "descriptor": "[I"}, {"field": "Instrument_pitchSteps", "owner": "db", "name": "p", "access": 8, "descriptor": "[I"}, {"field": "Instrument_samples", "owner": "db", "name": "s", "access": 8, "descriptor": "[I"}, {"field": "Instrument_sine", "owner": "db", "name": "y", "access": 8, "descriptor": "[I"}, {"field": "Instrument_volumeSteps", "owner": "db", "name": "r", "access": 8, "descriptor": "[I"}, {"field": "Interpreter_arrayLengths", "owner": "bv", "name": "o", "access": 8, "descriptor": "[I"}, {"field": "Interpreter_arrays", "owner": "bv", "name": "u", "access": 8, "descriptor": "[[I"}, {"field": "Interpreter_calendar", "owner": "bv", "name": "a", "access": 8, "descriptor": "Ljava/util/Calendar;"}, {"field": "Interpreter_frameDepth", "owner": "bv", "name": "d", "access": 8, "descriptor": "I", "decoder": 545127349}, {"field": "Interpreter_frames", "owner": "bv", "name": "k", "access": 8, "descriptor": "[Lbn;"}, {"field": "Interpreter_intLocals", "owner": "kp", "name": "q", "access": 8, "descriptor": "[I"}, {"field": "Interpreter_intStack", "owner": "bv", "name": "g", "access": 8, "descriptor": "[I"}, {"field": "Interpreter_intStackSize", "owner": "fe", "name": "l", "access": 8, "descriptor": "I", "decoder": -1237576843}, {"field": "Interpreter_stringLocals", "owner": "bv", "name": "w", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Interpreter_stringStack", "owner": "bv", "name": "e", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Interpreter_stringStackSize", "owner": "bv", "name": "x", "access": 8, "descriptor": "I", "decoder": -114766123}, {"field": "ItemDefinition_archive", "owner": "jv", "name": "u", "access": 9, "descriptor": "Lir;"}, {"field": "ItemDefinition_cached", "owner": "jv", "name": "x", "access": 8, "descriptor": "Ler;"}, {"field": "ItemDefinition_cachedModels", "owner": "jv", "name": "d", "access": 8, "descriptor": "Ler;"}, {"field": "ItemDefinition_modelArchive", "owner": "jv", "name": "g", "access": 9, "descriptor": "Lir;"}, {"field": "KeyHandler_idleCycles", "owner": "an", "name": "ci", "access": 73, "descriptor": "I", "decoder": -474146073}, {"field": "KeyHandler_instance", "owner": "an", "name": "m", "access": 9, "descriptor": "Lan;"}, {"field": "KeyHandler_keyCodes", "owner": "an", "name": "cd", "access": 8, "descriptor": "[I"}, {"field": "Key<PERSON><PERSON><PERSON>_pressed<PERSON>eys", "owner": "an", "name": "cb", "access": 9, "descriptor": "[Z"}, {"field": "KitDefinition_archive", "owner": "im", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "KitDefinition_cached", "owner": "im", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "Login_isUsernameRemembered", "owner": "client", "name": "ew", "access": 8, "descriptor": "Z"}, {"field": "Login_loadingPercent", "owner": "cu", "name": "b", "access": 8, "descriptor": "I", "decoder": -1514610183}, {"field": "Login_loadingText", "owner": "cu", "name": "c", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_password", "owner": "cu", "name": "ap", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_response0", "owner": "cu", "name": "al", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_response1", "owner": "cu", "name": "ab", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_response2", "owner": "cu", "name": "ae", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_response3", "owner": "cu", "name": "at", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Login_username", "owner": "cu", "name": "ad", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Messages_channels", "owner": "cw", "name": "m", "access": 24, "descriptor": "Ljava/util/Map;"}, {"field": "Messages_count", "owner": "cw", "name": "w", "access": 8, "descriptor": "I", "decoder": -1168497831}, {"field": "Messages_hashTable", "owner": "cw", "name": "f", "access": 24, "descriptor": "Llh;"}, {"field": "Messages_queue", "owner": "cw", "name": "q", "access": 24, "descriptor": "Ljc;"}, {"field": "ModelData_cosine", "owner": "dw", "name": "at", "access": 8, "descriptor": "[I"}, {"field": "ModelData_sine", "owner": "dw", "name": "ae", "access": 8, "descriptor": "[I"}, {"field": "Model_cosine", "owner": "du", "name": "bl", "access": 8, "descriptor": "[I"}, {"field": "Model_sharedSequenceModel", "owner": "du", "name": "m", "access": 8, "descriptor": "Ldu;"}, {"field": "Model_sharedSequenceModelFaceAlphas", "owner": "du", "name": "f", "access": 8, "descriptor": "[B"}, {"field": "Model_sharedSpotAnimationModel", "owner": "du", "name": "q", "access": 8, "descriptor": "Ldu;"}, {"field": "Model_sharedSpotAnimationModelFaceAlphas", "owner": "du", "name": "w", "access": 8, "descriptor": "[B"}, {"field": "Model_sine", "owner": "du", "name": "bm", "access": 8, "descriptor": "[I"}, {"field": "Model_transformTempX", "owner": "du", "name": "bd", "access": 8, "descriptor": "I"}, {"field": "Model_transformTempY", "owner": "du", "name": "bc", "access": 8, "descriptor": "I"}, {"field": "Model_transformTempZ", "owner": "du", "name": "bo", "access": 8, "descriptor": "I"}, {"field": "MouseHandler_currentButton", "owner": "bb", "name": "d", "access": 9, "descriptor": "I", "decoder": -2044247305}, {"field": "MouseHandler_currentButton0", "owner": "bb", "name": "u", "access": 73, "descriptor": "I", "decoder": 642308075}, {"field": "MouseHandler_idleCycles", "owner": "bb", "name": "o", "access": 73, "descriptor": "I", "decoder": -1080652669}, {"field": "MouseHandler_instance", "owner": "bb", "name": "w", "access": 9, "descriptor": "Lbb;"}, {"field": "<PERSON><PERSON><PERSON><PERSON>_lastB<PERSON>on", "owner": "bb", "name": "s", "access": 9, "descriptor": "I", "decoder": 2126310249}, {"field": "MouseHandler_lastButton0", "owner": "bb", "name": "i", "access": 73, "descriptor": "I", "decoder": 590718769}, {"field": "MouseHandler_lastPressedTimeMillis", "owner": "bb", "name": "h", "access": 9, "descriptor": "J", "decoder": 3806612340723844439}, {"field": "MouseHandler_lastPressedTimeMillis0", "owner": "bb", "name": "j", "access": 73, "descriptor": "J", "decoder": 3555003757894628243}, {"field": "MouseHandler_lastPressedX", "owner": "bb", "name": "t", "access": 9, "descriptor": "I", "decoder": -1853704165}, {"field": "MouseHandler_lastPressedX0", "owner": "bb", "name": "a", "access": 73, "descriptor": "I", "decoder": -813801905}, {"field": "Mouse<PERSON><PERSON>ler_lastPressedY", "owner": "bb", "name": "y", "access": 9, "descriptor": "I", "decoder": 1876967757}, {"field": "MouseHandler_lastPressedY0", "owner": "bb", "name": "z", "access": 73, "descriptor": "I", "decoder": 14138669}, {"field": "MouseH<PERSON>ler_millis", "owner": "bb", "name": "n", "access": 9, "descriptor": "J", "decoder": -7587574528523107151}, {"field": "MouseHandler_millis0", "owner": "bb", "name": "e", "access": 73, "descriptor": "J", "decoder": -5429651987476437823}, {"field": "MouseHandler_x", "owner": "bb", "name": "k", "access": 9, "descriptor": "I", "decoder": -649043937}, {"field": "MouseHandler_x0", "owner": "bb", "name": "g", "access": 73, "descriptor": "I", "decoder": 1048471439}, {"field": "Mouse<PERSON><PERSON><PERSON>_y", "owner": "bb", "name": "x", "access": 9, "descriptor": "I", "decoder": 1291011547}, {"field": "MouseHandler_y0", "owner": "bb", "name": "l", "access": 73, "descriptor": "I", "decoder": 2000222973}, {"field": "NetCache_archives", "owner": "iq", "name": "h", "access": 9, "descriptor": "[Lit;"}, {"field": "NetCache_crc", "owner": "iq", "name": "t", "access": 9, "descriptor": "Ljava/util/zip/CRC32;"}, {"field": "NetCache_crcMismatches", "owner": "iq", "name": "c", "access": 9, "descriptor": "I", "decoder": -1404585009}, {"field": "NetCache_currentResponse", "owner": "ed", "name": "i", "access": 9, "descriptor": "<PERSON>;"}, {"field": "NetCache_ioExceptions", "owner": "iq", "name": "r", "access": 9, "descriptor": "I", "decoder": -1141619871}, {"field": "NetCache_pendingPriorityResponses", "owner": "iq", "name": "u", "access": 9, "descriptor": "Lld;"}, {"field": "NetCache_pendingPriorityResponsesCount", "owner": "iq", "name": "g", "access": 9, "descriptor": "I", "decoder": 516430597}, {"field": "NetCache_pendingPriorityWrites", "owner": "iq", "name": "w", "access": 9, "descriptor": "Lld;"}, {"field": "NetCache_pendingPriorityWritesCount", "owner": "iq", "name": "o", "access": 9, "descriptor": "I", "decoder": 1495729287}, {"field": "NetCache_pendingResponses", "owner": "iq", "name": "d", "access": 9, "descriptor": "Lld;"}, {"field": "NetCache_pendingResponsesCount", "owner": "iq", "name": "k", "access": 9, "descriptor": "I", "decoder": -1627751389}, {"field": "NetCache_pendingWrites", "owner": "iq", "name": "e", "access": 9, "descriptor": "Lld;"}, {"field": "NetCache_pendingWritesCount", "owner": "iq", "name": "x", "access": 9, "descriptor": "I", "decoder": -1592359229}, {"field": "NetCache_pendingWritesQueue", "owner": "iq", "name": "l", "access": 9, "descriptor": "Ljp;"}, {"field": "NetCache_reference", "owner": "cq", "name": "y", "access": 9, "descriptor": "Lgr;"}, {"field": "NetCache_responseArchiveBuffer", "owner": "c", "name": "z", "access": 9, "descriptor": "Lgr;"}, {"field": "NetCache_responseHeaderBuffer", "owner": "iq", "name": "a", "access": 9, "descriptor": "Lgr;"}, {"field": "NetCache_socket", "owner": "iq", "name": "m", "access": 9, "descriptor": "Lfu;"}, {"field": "NpcDefinition_archive", "owner": "jz", "name": "m", "access": 8, "descriptor": "Lir;"}, {"field": "NpcDefinition_cached", "owner": "jz", "name": "q", "access": 8, "descriptor": "Ler;"}, {"field": "NpcDefinition_cachedModels", "owner": "jz", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "NpcDefinition_modelArchive", "owner": "jz", "name": "f", "access": 8, "descriptor": "Lir;"}, {"field": "ObjectDefinition_archive", "owner": "jr", "name": "f", "access": 8, "descriptor": "Lir;"}, {"field": "ObjectDefinition_cached", "owner": "jr", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "ObjectDefinition_cachedModels", "owner": "jr", "name": "g", "access": 8, "descriptor": "Ler;"}, {"field": "ObjectDefinition_isLowDetail", "owner": "jr", "name": "m", "access": 8, "descriptor": "Z"}, {"field": "PacketBuffer_masks", "owner": "hx", "name": "x", "access": 24, "descriptor": "[I"}, {"field": "ParamDefinition_cached", "owner": "jb", "name": "f", "access": 8, "descriptor": "Ler;"}, {"field": "PcmPlayer_sampleRate", "owner": "kl", "name": "o", "access": 9, "descriptor": "I", "decoder": 147524751}, {"field": "PlayerAppearance_cachedModels", "owner": "hi", "name": "k", "access": 9, "descriptor": "Ler;"}, {"field": "PlayerType_hardcoreIronman", "owner": "ij", "name": "u", "access": 24, "descriptor": "Lij;"}, {"field": "PlayerType_ironman", "owner": "ij", "name": "w", "access": 24, "descriptor": "Lij;"}, {"field": "PlayerType_jagexModerator", "owner": "ij", "name": "q", "access": 24, "descriptor": "Lij;"}, {"field": "PlayerType_normal", "owner": "ij", "name": "m", "access": 24, "descriptor": "Lij;"}, {"field": "PlayerType_playerModerator", "owner": "ij", "name": "f", "access": 24, "descriptor": "Lij;"}, {"field": "PlayerType_ultimateIronman", "owner": "ij", "name": "o", "access": 24, "descriptor": "Lij;"}, {"field": "Players_count", "owner": "cq", "name": "u", "access": 8, "descriptor": "I", "decoder": -766468303}, {"field": "Players_indices", "owner": "cq", "name": "g", "access": 8, "descriptor": "[I"}, {"field": "Players_orientations", "owner": "cq", "name": "d", "access": 8, "descriptor": "[I"}, {"field": "Players_regions", "owner": "cq", "name": "x", "access": 8, "descriptor": "[I"}, {"field": "Players_targetIndices", "owner": "cq", "name": "k", "access": 8, "descriptor": "[I"}, {"field": "Rasterizer2D_height", "owner": "lf", "name": "av", "access": 9, "descriptor": "I"}, {"field": "Rasterizer2D_pixels", "owner": "lf", "name": "aq", "access": 9, "descriptor": "[I"}, {"field": "Rasterizer2D_width", "owner": "lf", "name": "aj", "access": 9, "descriptor": "I"}, {"field": "Rasterizer2D_xClipEnd", "owner": "lf", "name": "ah", "access": 9, "descriptor": "I"}, {"field": "Rasterizer2D_xClipStart", "owner": "lf", "name": "ay", "access": 9, "descriptor": "I"}, {"field": "Rasterizer2D_yClipEnd", "owner": "lf", "name": "ac", "access": 9, "descriptor": "I"}, {"field": "Rasterizer2D_yClipStart", "owner": "lf", "name": "ar", "access": 9, "descriptor": "I"}, {"field": "Rasterizer3D_alpha", "owner": "et", "name": "o", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipHeight", "owner": "et", "name": "z", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipMidX", "owner": "et", "name": "n", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipMidX2", "owner": "et", "name": "s", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipMidY", "owner": "et", "name": "i", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipMidY2", "owner": "et", "name": "y", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipNegativeMidX", "owner": "et", "name": "j", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipNegativeMidY", "owner": "et", "name": "t", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_clipWidth", "owner": "et", "name": "a", "access": 8, "descriptor": "I"}, {"field": "Rasterizer3D_colorPalette", "owner": "et", "name": "b", "access": 9, "descriptor": "[I"}, {"field": "Rasterizer3D_cosine", "owner": "et", "name": "ag", "access": 9, "descriptor": "[I"}, {"field": "Rasterizer3D_isLowDetailTexture", "owner": "et", "name": "q", "access": 8, "descriptor": "Z"}, {"field": "Rasterizer3D_rowOffsets", "owner": "et", "name": "h", "access": 8, "descriptor": "[I"}, {"field": "Rasterizer3D_sine", "owner": "et", "name": "v", "access": 9, "descriptor": "[I"}, {"field": "Rasterizer3D_textureLoader", "owner": "et", "name": "c", "access": 9, "descriptor": "Lei;"}, {"field": "Rasterizer3D_zoom", "owner": "et", "name": "k", "access": 9, "descriptor": "I"}, {"field": "Scene_camera<PERSON><PERSON><PERSON><PERSON><PERSON>", "owner": "em", "name": "aq", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraPitchSine", "owner": "em", "name": "ag", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraX", "owner": "em", "name": "r", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraXTile", "owner": "em", "name": "b", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraXTileMax", "owner": "em", "name": "t", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraXTileMin", "owner": "em", "name": "s", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraY", "owner": "em", "name": "p", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraYTile", "owner": "em", "name": "c", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraYTileMax", "owner": "em", "name": "h", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraYTileMin", "owner": "em", "name": "y", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraYawCosine", "owner": "em", "name": "av", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraYawSine", "owner": "em", "name": "aj", "access": 8, "descriptor": "I"}, {"field": "Scene_cameraZ", "owner": "em", "name": "v", "access": 8, "descriptor": "I"}, {"field": "Scene_currentOccluders", "owner": "em", "name": "an", "access": 8, "descriptor": "[<PERSON>;"}, {"field": "Scene_currentOccludersCount", "owner": "em", "name": "am", "access": 8, "descriptor": "I"}, {"field": "Scene_drawnCount", "owner": "em", "name": "j", "access": 8, "descriptor": "I"}, {"field": "Scene_isLowDetail", "owner": "em", "name": "m", "access": 9, "descriptor": "Z"}, {"field": "Scene_plane", "owner": "em", "name": "z", "access": 8, "descriptor": "I"}, {"field": "Scene_planeOccluderCounts", "owner": "em", "name": "ap", "access": 8, "descriptor": "[I"}, {"field": "Scene_planeOccluders", "owner": "em", "name": "as", "access": 8, "descriptor": "[[<PERSON>;"}, {"field": "Scene_selected<PERSON>lane", "owner": "em", "name": "ay", "access": 8, "descriptor": "I"}, {"field": "Scene_selectedScreenX", "owner": "em", "name": "ah", "access": 8, "descriptor": "I"}, {"field": "Scene_selectedScreenY", "owner": "em", "name": "ak", "access": 8, "descriptor": "I"}, {"field": "Scene_selectedX", "owner": "em", "name": "aw", "access": 9, "descriptor": "I"}, {"field": "Scene_selectedY", "owner": "em", "name": "al", "access": 9, "descriptor": "I"}, {"field": "Scene_tilesDeque", "owner": "em", "name": "az", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "Scene_viewportXCenter", "owner": "em", "name": "bx", "access": 8, "descriptor": "I"}, {"field": "Scene_viewportXMax", "owner": "em", "name": "bl", "access": 8, "descriptor": "I"}, {"field": "Scene_viewportXMin", "owner": "em", "name": "bu", "access": 8, "descriptor": "I"}, {"field": "Scene_viewportYCenter", "owner": "em", "name": "by", "access": 8, "descriptor": "I"}, {"field": "Scene_viewportYMax", "owner": "em", "name": "br", "access": 8, "descriptor": "I"}, {"field": "Scene_viewportYMin", "owner": "em", "name": "bm", "access": 8, "descriptor": "I"}, {"field": "Script_cached", "owner": "cg", "name": "m", "access": 8, "descriptor": "Ler;"}, {"field": "SequenceDefinition_archive", "owner": "jh", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "SequenceDefinition_cached", "owner": "jh", "name": "w", "access": 9, "descriptor": "Ler;"}, {"field": "Skills_enabled", "owner": "hc", "name": "f", "access": 25, "descriptor": "[Z"}, {"field": "Skills_experienceTable", "owner": "hc", "name": "q", "access": 9, "descriptor": "[I"}, {"field": "SpotAnimationDefinition_archive", "owner": "io", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "SpotAnimationDefinition_cached", "owner": "io", "name": "q", "access": 8, "descriptor": "Ler;"}, {"field": "SpotAnimationDefinition_cachedModels", "owner": "io", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "SpotAnimationDefinition_modelArchive", "owner": "io", "name": "f", "access": 9, "descriptor": "Lir;"}, {"field": "Sprite_cached", "owner": "jv", "name": "k", "access": 8, "descriptor": "Ler;"}, {"field": "Strings_almostEverywhere", "owner": "id", "name": "hw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_andTheProtectItemPrayerWontWork", "owner": "id", "name": "hj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_attack", "owner": "id", "name": "fq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_cancel", "owner": "id", "name": "iz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_checkingForUpdates", "owner": "id", "name": "s", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_chooseOption", "owner": "id", "name": "fw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_close", "owner": "id", "name": "fd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_connectingToServer", "owner": "id", "name": "if", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_connectingToUpdateServer", "owner": "id", "name": "j", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_connectionLost", "owner": "id", "name": "n", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_continue", "owner": "id", "name": "g", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_drop", "owner": "id", "name": "q", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_examine", "owner": "id", "name": "ep", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_existingUser", "owner": "id", "name": "is", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_fromYourFriendListFirst", "owner": "id", "name": "fe", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_fromYourIgnoreListFirst", "owner": "id", "name": "fy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_hasLoggedIn", "owner": "id", "name": "ea", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_hasLoggedOut", "owner": "id", "name": "es", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_hidden", "owner": "id", "name": "w", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_isAlreadyOnYourFriendList", "owner": "id", "name": "fo", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_level", "owner": "id", "name": "fl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedConfig", "owner": "id", "name": "v", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedFonts", "owner": "id", "name": "b", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedInputHandler", "owner": "id", "name": "ar", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedInterfaces", "owner": "id", "name": "ak", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedSprites", "owner": "id", "name": "aq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedTextures", "owner": "id", "name": "av", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedTitleScreen", "owner": "id", "name": "r", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedUpdateList", "owner": "id", "name": "t", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedWordpack", "owner": "id", "name": "ay", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadedWorldMap", "owner": "id", "name": "al", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loading", "owner": "id", "name": "kq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingConfig", "owner": "id", "name": "p", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingFonts", "owner": "id", "name": "h", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingInterfaces", "owner": "id", "name": "ah", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingPleaseWait", "owner": "id", "name": "k", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingSprites", "owner": "id", "name": "ag", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingTextures", "owner": "id", "name": "aj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingTitleScreen", "owner": "id", "name": "c", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingWordpack", "owner": "id", "name": "ac", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_loadingWorldMap", "owner": "id", "name": "aw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_login", "owner": "id", "name": "ie", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_membersObject", "owner": "id", "name": "m", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_moreOptions", "owner": "id", "name": "fg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_newUser", "owner": "id", "name": "iy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_notWorkOnThisWorld", "owner": "id", "name": "hu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_null", "owner": "id", "name": "e", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_ok", "owner": "id", "name": "o", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_password", "owner": "id", "name": "in", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pin", "owner": "id", "name": "il", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_playersCanAttackEachOther", "owner": "id", "name": "he", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_playersCanAttackEachOtherAlmostEverywhere", "owner": "id", "name": "ht", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseEnterYourPassword", "owner": "id", "name": "dh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseEnterYourUsername", "owner": "id", "name": "dw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseRemoveFriend", "owner": "id", "name": "fi", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseRemoveIgnore", "owner": "id", "name": "fr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseWait", "owner": "id", "name": "fp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_pleaseWaitAttemptingToReestablish", "owner": "id", "name": "i", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_preparedSoundEngine", "owner": "id", "name": "y", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_preparedVisibilityMap", "owner": "id", "name": "z", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_select", "owner": "id", "name": "u", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_skill", "owner": "id", "name": "fj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_space", "owner": "id", "name": "fh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_startingGameEngine", "owner": "id", "name": "a", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_take", "owner": "id", "name": "f", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_theProtectItemPrayerWill", "owner": "id", "name": "hv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_thisIsABetaWorld", "owner": "id", "name": "ho", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_thisIsAHighRiskPvpWorld", "owner": "id", "name": "hi", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_thisIsAHighRiskWorld", "owner": "id", "name": "hp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_thisIsAPvpWorld", "owner": "id", "name": "hq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_unableToFind", "owner": "id", "name": "eg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_use", "owner": "id", "name": "ez", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_walkHere", "owner": "id", "name": "fc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_warning", "owner": "id", "name": "hl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_welcomeToRuneScape", "owner": "id", "name": "ij", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_yourFriendListIsFull", "owner": "id", "name": "fu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_yourIgnoreListIsFull", "owner": "id", "name": "ft", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Strings_yourNormalAccountWillNotBeAffected", "owner": "id", "name": "hz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "Texture_animatedPixels", "owner": "dh", "name": "a", "access": 8, "descriptor": "[I"}, {"field": "Tiles_heights", "owner": "bq", "name": "m", "access": 8, "descriptor": "[[[I"}, {"field": "Tiles_minPlane", "owner": "bq", "name": "q", "access": 8, "descriptor": "I", "decoder": -**********}, {"field": "Tiles_renderFlags", "owner": "bq", "name": "f", "access": 8, "descriptor": "[[[B"}, {"field": "TriBool_false", "owner": "kd", "name": "q", "access": 25, "descriptor": "Lkd;"}, {"field": "TriBool_true", "owner": "kd", "name": "f", "access": 25, "descriptor": "Lkd;"}, {"field": "TriBool_unknown", "owner": "kd", "name": "m", "access": 25, "descriptor": "Lkd;"}, {"field": "VarbitDefinition_archive", "owner": "ia", "name": "m", "access": 8, "descriptor": "Lir;"}, {"field": "VarbitDefinition_cached", "owner": "ia", "name": "f", "access": 9, "descriptor": "Ler;"}, {"field": "VarpDefinition_cached", "owner": "ix", "name": "q", "access": 9, "descriptor": "Ler;"}, {"field": "Varps_main", "owner": "hv", "name": "q", "access": 9, "descriptor": "[I"}, {"field": "Varps_masks", "owner": "hv", "name": "m", "access": 8, "descriptor": "[I"}, {"field": "Varps_temp", "owner": "hv", "name": "f", "access": 9, "descriptor": "[I"}, {"field": "ViewportMouse_entityCount", "owner": "dm", "name": "i", "access": 9, "descriptor": "I", "decoder": -2140977119}, {"field": "ViewportMouse_entityTags", "owner": "dm", "name": "a", "access": 9, "descriptor": "[J"}, {"field": "ViewportMouse_false0", "owner": "dm", "name": "w", "access": 9, "descriptor": "Z"}, {"field": "ViewportMouse_isInViewport", "owner": "dm", "name": "m", "access": 9, "descriptor": "Z"}, {"field": "ViewportMouse_x", "owner": "dm", "name": "f", "access": 9, "descriptor": "I", "decoder": 1891197573}, {"field": "ViewportMouse_y", "owner": "dm", "name": "q", "access": 9, "descriptor": "I", "decoder": 1535565231}, {"field": "WorldMapCacheName_area", "owner": "aw", "name": "w", "access": 24, "descriptor": "Law;"}, {"field": "WorldMapCacheName_compositeMap", "owner": "aw", "name": "f", "access": 25, "descriptor": "Law;"}, {"field": "WorldMapCacheName_compositeTexture", "owner": "aw", "name": "q", "access": 25, "descriptor": "Law;"}, {"field": "WorldMapCacheName_details", "owner": "aw", "name": "m", "access": 25, "descriptor": "Law;"}, {"field": "WorldMapCacheName_labels", "owner": "aw", "name": "o", "access": 25, "descriptor": "Law;"}, {"field": "WorldMapElement_cached", "owner": "ho", "name": "f", "access": 9, "descriptor": "[Lic;"}, {"field": "WorldMapElement_cachedSprites", "owner": "ic", "name": "w", "access": 8, "descriptor": "Ler;"}, {"field": "WorldMapElement_count", "owner": "ev", "name": "q", "access": 9, "descriptor": "I", "decoder": -112939189}, {"field": "WorldMapLabelSize_large", "owner": "s", "name": "q", "access": 25, "descriptor": "Ls;"}, {"field": "WorldMapLabelSize_medium", "owner": "s", "name": "f", "access": 25, "descriptor": "Ls;"}, {"field": "WorldMapLabelSize_small", "owner": "s", "name": "m", "access": 25, "descriptor": "Ls;"}, {"field": "applet", "owner": "fx", "name": "m", "access": 9, "descriptor": "Ljava/applet/Applet;"}, {"field": "archive0", "owner": "ir", "name": "di", "access": 8, "descriptor": "Lit;"}, {"field": "archive1", "owner": "ek", "name": "dc", "access": 8, "descriptor": "Lit;"}, {"field": "archive10", "owner": "j", "name": "dv", "access": 8, "descriptor": "Lit;"}, {"field": "archive11", "owner": "cu", "name": "do", "access": 8, "descriptor": "Lit;"}, {"field": "archive12", "owner": "cy", "name": "dh", "access": 8, "descriptor": "Lit;"}, {"field": "archive13", "owner": "gi", "name": "dz", "access": 8, "descriptor": "Lit;"}, {"field": "archive14", "owner": "q", "name": "dj", "access": 8, "descriptor": "Lit;"}, {"field": "archive15", "owner": "as", "name": "du", "access": 8, "descriptor": "Lit;"}, {"field": "archive16", "owner": "c", "name": "eb", "access": 8, "descriptor": "Lit;"}, {"field": "archive2", "owner": "be", "name": "dg", "access": 8, "descriptor": "Lit;"}, {"field": "archive3", "owner": "t", "name": "db", "access": 8, "descriptor": "Lit;"}, {"field": "archive4", "owner": "v", "name": "dd", "access": 8, "descriptor": "Lit;"}, {"field": "archive5", "owner": "cn", "name": "dy", "access": 8, "descriptor": "Lit;"}, {"field": "archive6", "owner": "fq", "name": "dk", "access": 8, "descriptor": "Lit;"}, {"field": "archive7", "owner": "ad", "name": "dl", "access": 8, "descriptor": "Lit;"}, {"field": "archive8", "owner": "p", "name": "dr", "access": 8, "descriptor": "Lit;"}, {"field": "archive9", "owner": "il", "name": "dw", "access": 8, "descriptor": "Lit;"}, {"field": "archiveLoaderArchive", "owner": "client", "name": "sq", "access": 8, "descriptor": "I", "decoder": -1546210597}, {"field": "archiveLoaders", "owner": "client", "name": "sy", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/util/ArrayList;"}, {"field": "base37Table", "owner": "ku", "name": "m", "access": 25, "descriptor": "[C"}, {"field": "baseX", "owner": "af", "name": "ff", "access": 8, "descriptor": "I", "decoder": -790717597}, {"field": "baseY", "owner": "bw", "name": "fr", "access": 8, "descriptor": "I", "decoder": -438288575}, {"field": "cacheDirectoryLocations", "owner": "ah", "name": "j", "access": 9, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "camAngleX", "owner": "client", "name": "hw", "access": 8, "descriptor": "I", "decoder": 410614005}, {"field": "camAngleY", "owner": "client", "name": "hp", "access": 8, "descriptor": "I", "decoder": -1163038981}, {"field": "camFollowHeight", "owner": "client", "name": "ho", "access": 8, "descriptor": "I", "decoder": 1005832199}, {"field": "<PERSON><PERSON><PERSON>", "owner": "t", "name": "hq", "access": 8, "descriptor": "I", "decoder": 490189491}, {"field": "cameraX", "owner": "as", "name": "ha", "access": 8, "descriptor": "I", "decoder": 1052834531}, {"field": "cameraY", "owner": "g", "name": "hs", "access": 8, "descriptor": "I", "decoder": 1326137897}, {"field": "cameraYaw", "owner": "ha", "name": "he", "access": 8, "descriptor": "I", "decoder": 2069364655}, {"field": "cameraZ", "owner": "k", "name": "hl", "access": 8, "descriptor": "I", "decoder": -1945589829}, {"field": "canvasHeight", "owner": "gd", "name": "p", "access": 9, "descriptor": "I", "decoder": -842662451}, {"field": "canvasWidth", "owner": "dt", "name": "r", "access": 9, "descriptor": "I", "decoder": 1784515731}, {"field": "chatEffects", "owner": "client", "name": "mg", "access": 8, "descriptor": "I", "decoder": 247245677}, {"field": "clDat", "owner": "lc", "name": "w", "access": 9, "descriptor": "Ljava/io/File;"}, {"field": "clanChat", "owner": "cl", "name": "pg", "access": 8, "descriptor": "Lkv;"}, {"field": "clickedComponent", "owner": "client", "name": "mc", "access": 8, "descriptor": "Lho;"}, {"field": "clickedComponentParent", "owner": "client", "name": "mr", "access": 8, "descriptor": "Lho;"}, {"field": "client", "owner": "dl", "name": "af", "access": 8, "descriptor": "Lclient;"}, {"field": "clientPreferences", "owner": "lw", "name": "su", "access": 8, "descriptor": "Lbf;"}, {"field": "clientType", "owner": "client", "name": "bm", "access": 8, "descriptor": "I", "decoder": 1678472241}, {"field": "clock", "owner": "fg", "name": "n", "access": 8, "descriptor": "Lfa;"}, {"field": "collisionMaps", "owner": "client", "name": "w", "access": 8, "descriptor": "[Lfy;"}, {"field": "combatTargetPlayerIndex", "owner": "client", "name": "kx", "access": 8, "descriptor": "I", "decoder": -1309758309}, {"field": "componentClickMasks", "owner": "client", "name": "on", "access": 8, "descriptor": "Lld;"}, {"field": "componentClickX", "owner": "client", "name": "mt", "access": 8, "descriptor": "I", "decoder": -1972775883}, {"field": "componentClickY", "owner": "client", "name": "me", "access": 8, "descriptor": "I", "decoder": 1052276727}, {"field": "componentDragDuration", "owner": "bu", "name": "no", "access": 8, "descriptor": "I", "decoder": 2059180753}, {"field": "cp1252AsciiExtension", "owner": "kr", "name": "m", "access": 25, "descriptor": "[C"}, {"field": "crossSprites", "owner": "an", "name": "gx", "access": 8, "descriptor": "[Lln;"}, {"field": "currentLevels", "owner": "client", "name": "ki", "access": 8, "descriptor": "[I"}, {"field": "currentTimeMsLast", "owner": "ed", "name": "m", "access": 8, "descriptor": "J", "decoder": 3311945626287518857}, {"field": "currentTimeMsOffset", "owner": "ga", "name": "f", "access": 8, "descriptor": "J", "decoder": -6166028446082638015}, {"field": "cycle", "owner": "client", "name": "cu", "access": 8, "descriptor": "I", "decoder": 1909006131}, {"field": "dat2File", "owner": "fs", "name": "n", "access": 9, "descriptor": "Ldy;"}, {"field": "decimator", "owner": "fd", "name": "qi", "access": 8, "descriptor": "Ldp;"}, {"field": "destinationX", "owner": "client", "name": "pd", "access": 8, "descriptor": "I", "decoder": -362336323}, {"field": "destinationY", "owner": "client", "name": "pk", "access": 8, "descriptor": "I", "decoder": 1298845031}, {"field": "directBufferUnavailable", "owner": "gu", "name": "f", "access": 8, "descriptor": "Z"}, {"field": "displayFps", "owner": "client", "name": "cg", "access": 8, "descriptor": "Z"}, {"field": "dragInventoryComponent", "owner": "az", "name": "jf", "access": 8, "descriptor": "Lho;"}, {"field": "dragItemSlotDestination", "owner": "client", "name": "jy", "access": 8, "descriptor": "I", "decoder": -836653679}, {"field": "dragItemSlotSource", "owner": "client", "name": "jp", "access": 8, "descriptor": "I", "decoder": 485534011}, {"field": "experience", "owner": "client", "name": "kb", "access": 8, "descriptor": "[I"}, {"field": "followerIndex", "owner": "client", "name": "ml", "access": 8, "descriptor": "I", "decoder": 1602665191}, {"field": "followerOpsLowPriority", "owner": "client", "name": "lw", "access": 8, "descriptor": "Z"}, {"field": "fontBold12", "owner": "q", "name": "fo", "access": 8, "descriptor": "Lkk;"}, {"field": "fontNameVerdana11", "owner": "lz", "name": "x", "access": 24, "descriptor": "Lkx;"}, {"field": "fontNameVerdana13", "owner": "lz", "name": "d", "access": 24, "descriptor": "Lkx;"}, {"field": "fontNameVerdana15", "owner": "lz", "name": "k", "access": 24, "descriptor": "Lkx;"}, {"field": "fontPlain11", "owner": "by", "name": "fv", "access": 8, "descriptor": "Lkk;"}, {"field": "fontPlain12", "owner": "az", "name": "fu", "access": 8, "descriptor": "Lkk;"}, {"field": "fonts", "owner": "ic", "name": "fs", "access": 8, "descriptor": "Lkz;"}, {"field": "fontsMap", "owner": "client", "name": "ft", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/util/HashMap;"}, {"field": "fps", "owner": "ba", "name": "k", "access": 12, "descriptor": "I", "decoder": 1789653893}, {"field": "friendSystem", "owner": "ag", "name": "rc", "access": 9, "descriptor": "Lbh;"}, {"field": "gameBuild", "owner": "client", "name": "bq", "access": 8, "descriptor": "I", "decoder": 1186004739}, {"field": "gameDrawingMode", "owner": "client", "name": "oz", "access": 8, "descriptor": "I", "decoder": -12672775}, {"field": "gameObjects", "owner": "em", "name": "ar", "access": 8, "descriptor": "[Leh;"}, {"field": "gameShell", "owner": "ba", "name": "f", "access": 8, "descriptor": "Lba;"}, {"field": "gameState", "owner": "client", "name": "br", "access": 8, "descriptor": "I", "decoder": 244995961}, {"field": "garbageCollector", "owner": "ir", "name": "au", "access": 8, "descriptor": "Ljava/lang/management/GarbageCollectorMXBean;"}, {"field": "garbageCollectorLastCheckTimeMs", "owner": "ba", "name": "aa", "access": 8, "descriptor": "J", "decoder": -3607118089363666005}, {"field": "garbageCollectorLastCollectionTime", "owner": "ba", "name": "ao", "access": 8, "descriptor": "J", "decoder": 1013162399149344707}, {"field": "grandExchangeEvents", "owner": "do", "name": "sm", "access": 8, "descriptor": "Lg;"}, {"field": "grandExchangeOffers", "owner": "client", "name": "sb", "access": 8, "descriptor": "[Lx;"}, {"field": "graphicsObjects", "owner": "client", "name": "kt", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "groundItems", "owner": "client", "name": "kz", "access": 8, "descriptor": "[[[<PERSON><PERSON>;"}, {"field": "gzipDecompressor", "owner": "ir", "name": "i", "access": 8, "descriptor": "Lgo;"}, {"field": "hasFocus", "owner": "ba", "name": "an", "access": 72, "descriptor": "Z"}, {"field": "headIconHintSprites", "owner": "br", "name": "gi", "access": 8, "descriptor": "[Lln;"}, {"field": "headIconPkSprites", "owner": "z", "name": "gt", "access": 8, "descriptor": "[Lln;"}, {"field": "headIconPrayerSprites", "owner": "bf", "name": "gr", "access": 8, "descriptor": "[Lln;"}, {"field": "hintArrowHeight", "owner": "client", "name": "ci", "access": 8, "descriptor": "I", "decoder": -627650567}, {"field": "hintArrowNpcIndex", "owner": "client", "name": "cl", "access": 8, "descriptor": "I", "decoder": 1485594821}, {"field": "hintArrowPlayerIndex", "owner": "client", "name": "ct", "access": 8, "descriptor": "I", "decoder": -1036874153}, {"field": "hintArrowSubX", "owner": "client", "name": "cd", "access": 8, "descriptor": "I", "decoder": 749254081}, {"field": "hintArrowSubY", "owner": "client", "name": "cx", "access": 8, "descriptor": "I", "decoder": 1393270633}, {"field": "hintArrowType", "owner": "client", "name": "cp", "access": 8, "descriptor": "I", "decoder": -871534943}, {"field": "hintArrowX", "owner": "client", "name": "cz", "access": 8, "descriptor": "I", "decoder": -1757834373}, {"field": "hintArrowY", "owner": "client", "name": "cc", "access": 8, "descriptor": "I", "decoder": -123748133}, {"field": "huffman", "owner": "ki", "name": "m", "access": 9, "descriptor": "Lgd;"}, {"field": "idx255File", "owner": "fs", "name": "i", "access": 9, "descriptor": "Ldy;"}, {"field": "idxCount", "owner": "fs", "name": "u", "access": 9, "descriptor": "I", "decoder": -2051451755}, {"field": "idxFiles", "owner": "ik", "name": "a", "access": 9, "descriptor": "[Ldy;"}, {"field": "instanceChunkTemplates", "owner": "client", "name": "gq", "access": 8, "descriptor": "[[[I"}, {"field": "interfaceComponents", "owner": "ho", "name": "g", "access": 9, "descriptor": "[[Lho;"}, {"field": "interfaceParents", "owner": "client", "name": "mj", "access": 8, "descriptor": "Lld;"}, {"field": "isCameraLocked", "owner": "client", "name": "qz", "access": 8, "descriptor": "Z"}, {"field": "isDraggingComponent", "owner": "client", "name": "ny", "access": 8, "descriptor": "Z"}, {"field": "isInInstance", "owner": "client", "name": "gg", "access": 8, "descriptor": "Z"}, {"field": "isItemSelected", "owner": "client", "name": "lz", "access": 8, "descriptor": "I", "decoder": 1630772923}, {"field": "isKilled", "owner": "ba", "name": "u", "access": 8, "descriptor": "Z"}, {"field": "isLoading", "owner": "client", "name": "cj", "access": 8, "descriptor": "Z"}, {"field": "isLowDetail", "owner": "client", "name": "bc", "access": 8, "descriptor": "Z"}, {"field": "isMembersWorld", "owner": "client", "name": "bd", "access": 9, "descriptor": "Z"}, {"field": "isMenuOpen", "owner": "client", "name": "kc", "access": 8, "descriptor": "Z"}, {"field": "isResizable", "owner": "client", "name": "or", "access": 8, "descriptor": "Z"}, {"field": "isSpellSelected", "owner": "client", "name": "lv", "access": 8, "descriptor": "Z"}, {"field": "isStereo", "owner": "co", "name": "u", "access": 12, "descriptor": "Z"}, {"field": "itemContainers", "owner": "bc", "name": "m", "access": 8, "descriptor": "Lld;"}, {"field": "itemDragDuration", "owner": "client", "name": "jt", "access": 8, "descriptor": "I", "decoder": 1486366835}, {"field": "javaVendor", "owner": "fz", "name": "m", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "javaVersion", "owner": "fz", "name": "f", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "js5ConnectState", "owner": "client", "name": "da", "access": 8, "descriptor": "I", "decoder": -1431050759}, {"field": "js5Errors", "owner": "client", "name": "ek", "access": 8, "descriptor": "I", "decoder": 1791955261}, {"field": "js5Socket", "owner": "cf", "name": "df", "access": 8, "descriptor": "Lfu;"}, {"field": "js5SocketTask", "owner": "hr", "name": "dn", "access": 8, "descriptor": "Lfm;"}, {"field": "js5StartTimeMs", "owner": "eh", "name": "ds", "access": 8, "descriptor": "J", "decoder": -6104270600047501055}, {"field": "language", "owner": "client", "name": "bo", "access": 8, "descriptor": "I", "decoder": 2068355509}, {"field": "levels", "owner": "client", "name": "kr", "access": 8, "descriptor": "[I"}, {"field": "loadedInterfaces", "owner": "ho", "name": "l", "access": 9, "descriptor": "[Z"}, {"field": "localPlayer", "owner": "ao", "name": "jk", "access": 8, "descriptor": "Lbr;"}, {"field": "localPlayerIndex", "owner": "client", "name": "ji", "access": 8, "descriptor": "I", "decoder": -1869532553}, {"field": "localPlayerName", "owner": "fx", "name": "f", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "loginState", "owner": "client", "name": "ed", "access": 8, "descriptor": "I", "decoder": -163579343}, {"field": "loginType", "owner": "an", "name": "bk", "access": 8, "descriptor": "Llx;"}, {"field": "mapDotSprites", "owner": "at", "name": "gu", "access": 8, "descriptor": "[Lln;"}, {"field": "mapIconCount", "owner": "client", "name": "pi", "access": 8, "descriptor": "I", "decoder": 864673321}, {"field": "mapIconXs", "owner": "client", "name": "ps", "access": 8, "descriptor": "[I"}, {"field": "mapIconYs", "owner": "client", "name": "pw", "access": 8, "descriptor": "[I"}, {"field": "mapIcons", "owner": "client", "name": "po", "access": 8, "descriptor": "[Lln;"}, {"field": "mapMarkerSprites", "owner": "j", "name": "ga", "access": 8, "descriptor": "[Lln;"}, {"field": "mapSceneSprites", "owner": "gq", "name": "gv", "access": 8, "descriptor": "[Llq;"}, {"field": "masterDisk", "owner": "kz", "name": "sc", "access": 8, "descriptor": "Lff;"}, {"field": "menuActions", "owner": "client", "name": "ld", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "menuArguments0", "owner": "client", "name": "lk", "access": 8, "descriptor": "[I"}, {"field": "menuArguments1", "owner": "client", "name": "ly", "access": 8, "descriptor": "[I"}, {"field": "menuArguments2", "owner": "client", "name": "li", "access": 8, "descriptor": "[I"}, {"field": "menuHeight", "owner": "ak", "name": "ka", "access": 8, "descriptor": "I", "decoder": -1734004743}, {"field": "menuOpcodes", "owner": "client", "name": "lh", "access": 8, "descriptor": "[I"}, {"field": "menuOptionsCount", "owner": "client", "name": "ll", "access": 8, "descriptor": "I", "decoder": -1749538901}, {"field": "menuShiftClick", "owner": "client", "name": "lt", "access": 8, "descriptor": "[Z"}, {"field": "menuTargetNames", "owner": "client", "name": "la", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "menuWidth", "owner": "hf", "name": "ko", "access": 8, "descriptor": "I", "decoder": 2079315667}, {"field": "menuX", "owner": "v", "name": "kw", "access": 8, "descriptor": "I", "decoder": -12041645}, {"field": "menuY", "owner": "ba", "name": "kl", "access": 8, "descriptor": "I", "decoder": 1875936371}, {"field": "midiPcmStream", "owner": "hf", "name": "w", "access": 9, "descriptor": "Lhm;"}, {"field": "minimapState", "owner": "client", "name": "qm", "access": 8, "descriptor": "I", "decoder": -137788125}, {"field": "modIconSprites", "owner": "h", "name": "gc", "access": 8, "descriptor": "[Llq;"}, {"field": "modelViewportXs", "owner": "du", "name": "ap", "access": 8, "descriptor": "[I"}, {"field": "modelViewportYs", "owner": "du", "name": "as", "access": 8, "descriptor": "[I"}, {"field": "mouseCrossColor", "owner": "client", "name": "jm", "access": 8, "descriptor": "I", "decoder": 233318639}, {"field": "mouseCrossState", "owner": "client", "name": "jw", "access": 8, "descriptor": "I", "decoder": -1302827103}, {"field": "mouseCrossX", "owner": "client", "name": "jb", "access": 8, "descriptor": "I", "decoder": 202604997}, {"field": "mouseCrossY", "owner": "client", "name": "jq", "access": 8, "descriptor": "I", "decoder": -1984048653}, {"field": "mouseLastLastPressedTimeMillis", "owner": "client", "name": "cb", "access": 8, "descriptor": "J", "decoder": -8445874050676184263}, {"field": "mouseRecorder", "owner": "cb", "name": "ck", "access": 8, "descriptor": "Lbu;"}, {"field": "mouseWheel", "owner": "j", "name": "ol", "access": 8, "descriptor": "Lfv;"}, {"field": "mouseWheelRotation", "owner": "client", "name": "oe", "access": 8, "descriptor": "I", "decoder": -1628183831}, {"field": "mousedOverComponentIf1", "owner": "fa", "name": "lu", "access": 8, "descriptor": "Lho;"}, {"field": "musicTrack", "owner": "i", "name": "k", "access": 9, "descriptor": "Lhe;"}, {"field": "musicTrackBoolean", "owner": "lj", "name": "d", "access": 9, "descriptor": "Z"}, {"field": "musicTrackFileId", "owner": "x", "name": "l", "access": 9, "descriptor": "I", "decoder": -812708185}, {"field": "musicTrackGroupId", "owner": "ar", "name": "g", "access": 9, "descriptor": "I", "decoder": -18917723}, {"field": "npcAttackOption", "owner": "client", "name": "de", "access": 8, "descriptor": "Lcj;"}, {"field": "npcCount", "owner": "client", "name": "ep", "access": 8, "descriptor": "I", "decoder": -1569269731}, {"field": "npcIndices", "owner": "client", "name": "fq", "access": 8, "descriptor": "[I"}, {"field": "npcs", "owner": "client", "name": "ez", "access": 8, "descriptor": "[Lcv;"}, {"field": "objectSounds", "owner": "be", "name": "m", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "osName", "owner": "ju", "name": "b", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "osNameLowerCase", "owner": "p", "name": "h", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "overheadText", "owner": "client", "name": "ih", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "overheadTextAscents", "owner": "client", "name": "ip", "access": 8, "descriptor": "[I"}, {"field": "overheadTextColors", "owner": "client", "name": "ic", "access": 8, "descriptor": "[I"}, {"field": "overheadTextCount", "owner": "client", "name": "ir", "access": 8, "descriptor": "I", "decoder": 616180377}, {"field": "overheadTextCyclesRemaining", "owner": "client", "name": "io", "access": 8, "descriptor": "[I"}, {"field": "overheadTextEffects", "owner": "client", "name": "iw", "access": 8, "descriptor": "[I"}, {"field": "overheadTextLimit", "owner": "client", "name": "iv", "access": 8, "descriptor": "I", "decoder": 1889277277}, {"field": "overheadTextXOffsets", "owner": "client", "name": "ib", "access": 8, "descriptor": "[I"}, {"field": "overheadTextXs", "owner": "client", "name": "iq", "access": 8, "descriptor": "[I"}, {"field": "overheadTextYs", "owner": "client", "name": "iu", "access": 8, "descriptor": "[I"}, {"field": "packetBufferNodes", "owner": "gg", "name": "o", "access": 9, "descriptor": "[Lgg;"}, {"field": "packetWriter", "owner": "client", "name": "fj", "access": 25, "descriptor": "Lcl;"}, {"field": "pcmPlayer0", "owner": "aw", "name": "qu", "access": 8, "descriptor": "Lco;"}, {"field": "pcmPlayer1", "owner": "ir", "name": "qb", "access": 8, "descriptor": "Lco;"}, {"field": "pcmPlayerCount", "owner": "cu", "name": "e", "access": 8, "descriptor": "I", "decoder": -184301597}, {"field": "pcmPlayerProvider", "owner": "bl", "name": "n", "access": 9, "descriptor": "Lcz;"}, {"field": "pcmStreamMixer", "owner": "fz", "name": "qr", "access": 8, "descriptor": "Lct;"}, {"field": "plane", "owner": "dx", "name": "jg", "access": 8, "descriptor": "I", "decoder": -631434243}, {"field": "platformInfo", "owner": "s", "name": "st", "access": 8, "descriptor": "Llb;"}, {"field": "platformInfoProvider", "owner": "client", "name": "sk", "access": 8, "descriptor": "Llm;"}, {"field": "playerAttackOption", "owner": "client", "name": "co", "access": 8, "descriptor": "Lcj;"}, {"field": "playerMenuActions", "owner": "client", "name": "kq", "access": 8, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "playerMenuOpcodes", "owner": "client", "name": "kp", "access": 24, "descriptor": "[I"}, {"field": "playerMod", "owner": "client", "name": "mn", "access": 8, "descriptor": "Z"}, {"field": "players", "owner": "client", "name": "jd", "access": 8, "descriptor": "[Lbr;"}, {"field": "port1", "owner": "q", "name": "ee", "access": 8, "descriptor": "I", "decoder": -753026759}, {"field": "port2", "owner": "ag", "name": "eq", "access": 8, "descriptor": "I", "decoder": -256520591}, {"field": "port3", "owner": "ga", "name": "eh", "access": 8, "descriptor": "I", "decoder": 994298267}, {"field": "projectiles", "owner": "client", "name": "kk", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "publicChatMode", "owner": "client", "name": "pt", "access": 8, "descriptor": "I", "decoder": -**********}, {"field": "randomDat", "owner": "fs", "name": "k", "access": 9, "descriptor": "Ldy;"}, {"field": "rasterProvider", "owner": "gz", "name": "ad", "access": 9, "descriptor": "Llp;"}, {"field": "rebootTimer", "owner": "client", "name": "cf", "access": 8, "descriptor": "I", "decoder": -**********}, {"field": "reflectionChecks", "owner": "le", "name": "m", "access": 9, "descriptor": "Lja;"}, {"field": "regionLandArchiveIds", "owner": "bb", "name": "gb", "access": 8, "descriptor": "[I"}, {"field": "regionLandArchives", "owner": "ad", "name": "gs", "access": 8, "descriptor": "[[B"}, {"field": "regionMapArchiveIds", "owner": "bd", "name": "gw", "access": 8, "descriptor": "[I"}, {"field": "regionMapArchives", "owner": "bg", "name": "gl", "access": 8, "descriptor": "[[B"}, {"field": "regions", "owner": "kw", "name": "gn", "access": 8, "descriptor": "[I"}, {"field": "renderSelf", "owner": "client", "name": "js", "access": 8, "descriptor": "Z"}, {"field": "revision", "owner": "fx", "name": "q", "access": 9, "descriptor": "I", "decoder": -787495321}, {"field": "rootComponentCount", "owner": "client", "name": "os", "access": 8, "descriptor": "I", "decoder": -546798303}, {"field": "rootComponentHeights", "owner": "client", "name": "ox", "access": 8, "descriptor": "[I"}, {"field": "rootComponentWidths", "owner": "client", "name": "oj", "access": 8, "descriptor": "[I"}, {"field": "rootComponentXs", "owner": "client", "name": "oy", "access": 8, "descriptor": "[I"}, {"field": "rootComponentYs", "owner": "client", "name": "oi", "access": 8, "descriptor": "[I"}, {"field": "rootInterface", "owner": "client", "name": "mm", "access": 8, "descriptor": "I", "decoder": -783816509}, {"field": "runEnergy", "owner": "client", "name": "mi", "access": 8, "descriptor": "I", "decoder": 933910819}, {"field": "scene", "owner": "bl", "name": "gf", "access": 8, "descriptor": "Le<PERSON>;"}, {"field": "sceneMinimapSprite", "owner": "bx", "name": "px", "access": 8, "descriptor": "Lln;"}, {"field": "scriptEvents", "owner": "client", "name": "og", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "scrollBarSprites", "owner": "ay", "name": "gj", "access": 8, "descriptor": "[Llq;"}, {"field": "secureRandom", "owner": "t", "name": "ec", "access": 8, "descriptor": "Ljava/security/SecureRandom;"}, {"field": "secureRandomFuture", "owner": "client", "name": "eu", "access": 8, "descriptor": "Lcn;"}, {"field": "selectedItemComponent", "owner": "ge", "name": "ix", "access": 8, "descriptor": "I", "decoder": -1645409107}, {"field": "selectedItemId", "owner": "cs", "name": "iz", "access": 8, "descriptor": "I", "decoder": -2062450631}, {"field": "selectedItemName", "owner": "client", "name": "it", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "selectedItemSlot", "owner": "ii", "name": "lx", "access": 8, "descriptor": "I", "decoder": 1912123225}, {"field": "selectedSpellActionName", "owner": "client", "name": "mk", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "selectedSpellFlags", "owner": "do", "name": "my", "access": 8, "descriptor": "I", "decoder": -1344682613}, {"field": "selectedSpellName", "owner": "client", "name": "mu", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "serverBuild", "owner": "z", "name": "bs", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "shiftClickDrop", "owner": "client", "name": "lf", "access": 8, "descriptor": "Z"}, {"field": "showLoadingMessages", "owner": "client", "name": "jj", "access": 8, "descriptor": "Z"}, {"field": "showMouseCross", "owner": "client", "name": "jr", "access": 8, "descriptor": "Z"}, {"field": "showMouseOverText", "owner": "client", "name": "lj", "access": 8, "descriptor": "Z"}, {"field": "socketTask", "owner": "fq", "name": "fl", "access": 8, "descriptor": "Lfm;"}, {"field": "soundCache", "owner": "kc", "name": "n", "access": 9, "descriptor": "Ldt;"}, {"field": "soundEffectCount", "owner": "client", "name": "qa", "access": 8, "descriptor": "I", "decoder": -1481002083}, {"field": "soundEffectIds", "owner": "client", "name": "qv", "access": 8, "descriptor": "[I"}, {"field": "soundEffects", "owner": "client", "name": "qe", "access": 8, "descriptor": "[Lci;"}, {"field": "soundSystem", "owner": "cw", "name": "x", "access": 8, "descriptor": "Ldx;"}, {"field": "soundSystemExecutor", "owner": "kq", "name": "l", "access": 8, "descriptor": "Ljava/util/concurrent/ScheduledExecutorService;"}, {"field": "spriteIds", "owner": "hg", "name": "em", "access": 8, "descriptor": "Llg;"}, {"field": "staffModLevel", "owner": "client", "name": "mw", "access": 8, "descriptor": "I", "decoder": 821563709}, {"field": "studioGame", "owner": "c", "name": "bn", "access": 8, "descriptor": "Lis;"}, {"field": "tapToDrop", "owner": "client", "name": "lp", "access": 8, "descriptor": "Z"}, {"field": "<PERSON><PERSON><PERSON><PERSON>", "owner": "ba", "name": "m", "access": 12, "descriptor": "Lfz;"}, {"field": "tempMenuAction", "owner": "d", "name": "lr", "access": 8, "descriptor": "Lcs;"}, {"field": "textureProvider", "owner": "cn", "name": "jn", "access": 8, "descriptor": "Ldl;"}, {"field": "tileLastDrawnActor", "owner": "client", "name": "im", "access": 8, "descriptor": "[[I"}, {"field": "timer", "owner": "client", "name": "fm", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "titleLoadingStage", "owner": "client", "name": "dt", "access": 8, "descriptor": "I", "decoder": -**********}, {"field": "url<PERSON>e<PERSON><PERSON>", "owner": "kd", "name": "fz", "access": 8, "descriptor": "<PERSON>;"}, {"field": "useBufferedSocket", "owner": "client", "name": "fx", "access": 8, "descriptor": "Z"}, {"field": "userHomeDirectory", "owner": "as", "name": "c", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "varcs", "owner": "gy", "name": "of", "access": 8, "descriptor": "Lch;"}, {"field": "viewportComponent", "owner": "client", "name": "mx", "access": 8, "descriptor": "Lho;"}, {"field": "viewportDrawCount", "owner": "client", "name": "ii", "access": 8, "descriptor": "I", "decoder": 289477221}, {"field": "viewportHeight", "owner": "client", "name": "rx", "access": 8, "descriptor": "I", "decoder": -629885193}, {"field": "viewportOffsetX", "owner": "client", "name": "rr", "access": 8, "descriptor": "I", "decoder": 386331251}, {"field": "viewportOffsetY", "owner": "client", "name": "rn", "access": 8, "descriptor": "I", "decoder": 992721193}, {"field": "viewportTempX", "owner": "client", "name": "ig", "access": 8, "descriptor": "I", "decoder": 424442889}, {"field": "viewportTempY", "owner": "client", "name": "ia", "access": 8, "descriptor": "I", "decoder": -81902865}, {"field": "viewportWidth", "owner": "client", "name": "ry", "access": 8, "descriptor": "I", "decoder": 2035623995}, {"field": "viewportZoom", "owner": "client", "name": "rz", "access": 8, "descriptor": "I", "decoder": 541065633}, {"field": "visibilityMap", "owner": "em", "name": "bc", "access": 8, "descriptor": "[[[[Z"}, {"field": "visibleTiles", "owner": "em", "name": "bo", "access": 8, "descriptor": "[[Z"}, {"field": "weight", "owner": "client", "name": "mv", "access": 8, "descriptor": "I", "decoder": 36001423}, {"field": "worldHost", "owner": "af", "name": "ei", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "worldId", "owner": "client", "name": "ba", "access": 9, "descriptor": "I", "decoder": 1412639841}, {"field": "worldMap0", "owner": "bo", "name": "sz", "access": 8, "descriptor": "Llz;"}, {"field": "worldMapEvent", "owner": "z", "name": "j", "access": 8, "descriptor": "Lap;"}, {"field": "worldProperties", "owner": "client", "name": "bb", "access": 8, "descriptor": "I", "decoder": 352819683}, {"field": "worlds", "owner": "bc", "name": "o", "access": 8, "descriptor": "[Lbt;"}, {"field": "worldsCount", "owner": "bt", "name": "u", "access": 8, "descriptor": "I", "decoder": 233222155}, {"field": "worldsUrl", "owner": "iy", "name": "bx", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "xteaKeys", "owner": "gf", "name": "ge", "access": 8, "descriptor": "[[I"}, {"field": "__f_m", "owner": "f", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__w_qs", "owner": "w", "name": "qs", "access": 8, "descriptor": "I", "decoder": 1608439891}, {"field": "__o_m", "owner": "o", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__u_n", "owner": "u", "name": "n", "access": 9, "descriptor": "Z"}, {"field": "__g_f", "owner": "g", "name": "f", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}, {"field": "__g_o", "owner": "g", "name": "o", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}, {"field": "__g_q", "owner": "g", "name": "q", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}, {"field": "__g_w", "owner": "g", "name": "w", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/util/Comparator;"}, {"field": "__g_z", "owner": "g", "name": "z", "access": 8, "descriptor": "I", "decoder": -1605454211}, {"field": "__x_rg", "owner": "x", "name": "rg", "access": 8, "descriptor": "I", "decoder": -1286287981}, {"field": "__d_gz", "owner": "d", "name": "gz", "access": 8, "descriptor": "Lln;"}, {"field": "__n_n", "owner": "n", "name": "n", "access": 8, "descriptor": "Lho;"}, {"field": "__i_af", "owner": "i", "name": "af", "access": 8, "descriptor": "I", "decoder": -1810047523}, {"field": "__i_x", "owner": "i", "name": "x", "access": 8, "descriptor": "[I"}, {"field": "__z_si", "owner": "z", "name": "si", "access": 8, "descriptor": "J", "decoder": 3423502126894795079}, {"field": "__s_fi", "owner": "s", "name": "fi", "access": 8, "descriptor": "I", "decoder": 1577759565}, {"field": "__s_dq", "owner": "s", "name": "dq", "access": 8, "descriptor": "Lit;"}, {"field": "__t_i", "owner": "t", "name": "i", "access": 8, "descriptor": "Lho;"}, {"field": "__y_sn", "owner": "y", "name": "sn", "access": 8, "descriptor": "I", "decoder": -1946112139}, {"field": "__y_p", "owner": "y", "name": "p", "access": 8, "descriptor": "[I"}, {"field": "__h_n", "owner": "h", "name": "n", "access": 8, "descriptor": "[I"}, {"field": "__h_e", "owner": "h", "name": "e", "access": 8, "descriptor": "Llq;"}, {"field": "__h_f", "owner": "h", "name": "f", "access": 24, "descriptor": "Lh;"}, {"field": "__h_m", "owner": "h", "name": "m", "access": 24, "descriptor": "Lh;"}, {"field": "__h_q", "owner": "h", "name": "q", "access": 24, "descriptor": "Lh;"}, {"field": "__h_w", "owner": "h", "name": "w", "access": 24, "descriptor": "Lh;"}, {"field": "__b_by", "owner": "b", "name": "by", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__p_go", "owner": "p", "name": "go", "access": 8, "descriptor": "I", "decoder": -948290091}, {"field": "__aq_k", "owner": "aq", "name": "k", "access": 9, "descriptor": "Lir;"}, {"field": "__aj_e", "owner": "aj", "name": "e", "access": 9, "descriptor": "I", "decoder": -1536028937}, {"field": "__av_o", "owner": "av", "name": "o", "access": 9, "descriptor": "Lec;"}, {"field": "__ar_l", "owner": "ar", "name": "l", "access": 9, "descriptor": "Z"}, {"field": "__ar_qw", "owner": "ar", "name": "qw", "access": 8, "descriptor": "I", "decoder": -1555836449}, {"field": "__ar_bd", "owner": "ar", "name": "bd", "access": 8, "descriptor": "[Lln;"}, {"field": "__ay_u", "owner": "ay", "name": "u", "access": 8, "descriptor": "[[[B"}, {"field": "__ah_l", "owner": "ah", "name": "l", "access": 8, "descriptor": "[I"}, {"field": "__ak_qj", "owner": "ak", "name": "qj", "access": 8, "descriptor": "I", "decoder": -1942470229}, {"field": "__ap_w", "owner": "ap", "name": "w", "access": 8, "descriptor": "[Llq;"}, {"field": "__as_ey", "owner": "as", "name": "ey", "access": 8, "descriptor": "I", "decoder": -1516124929}, {"field": "__an_cl", "owner": "an", "name": "cl", "access": 9, "descriptor": "I", "decoder": -674534717}, {"field": "__an_cp", "owner": "an", "name": "cp", "access": 9, "descriptor": "[I"}, {"field": "__an_cg", "owner": "an", "name": "cg", "access": 8, "descriptor": "[C"}, {"field": "__an_cc", "owner": "an", "name": "cc", "access": 8, "descriptor": "I", "decoder": -1942898885}, {"field": "__an_ch", "owner": "an", "name": "ch", "access": 8, "descriptor": "I", "decoder": 1112094745}, {"field": "__an_ct", "owner": "an", "name": "ct", "access": 8, "descriptor": "I", "decoder": -93280591}, {"field": "__an_cw", "owner": "an", "name": "cw", "access": 8, "descriptor": "I", "decoder": -1208053825}, {"field": "__an_cz", "owner": "an", "name": "cz", "access": 8, "descriptor": "I", "decoder": 2066759739}, {"field": "__an_ra", "owner": "an", "name": "ra", "access": 8, "descriptor": "I", "decoder": -817957995}, {"field": "__an_cf", "owner": "an", "name": "cf", "access": 8, "descriptor": "[I"}, {"field": "__an_cq", "owner": "an", "name": "cq", "access": 8, "descriptor": "[I"}, {"field": "__an_i", "owner": "an", "name": "i", "access": 8, "descriptor": "Llq;"}, {"field": "__an_x", "owner": "an", "name": "x", "access": 8, "descriptor": "[Llq;"}, {"field": "__ao_s", "owner": "ao", "name": "s", "access": 9, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__ao_gk", "owner": "ao", "name": "gk", "access": 8, "descriptor": "I", "decoder": -1131782991}, {"field": "__af_m", "owner": "af", "name": "m", "access": 9, "descriptor": "Ljava/applet/Applet;"}, {"field": "__af_f", "owner": "af", "name": "f", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__ba_aw", "owner": "ba", "name": "aw", "access": 8, "descriptor": "I", "decoder": -1042005999}, {"field": "__ba_d", "owner": "ba", "name": "d", "access": 8, "descriptor": "I", "decoder": 425367827}, {"field": "__ba_e", "owner": "ba", "name": "e", "access": 8, "descriptor": "I", "decoder": -1698831905}, {"field": "__ba_l", "owner": "ba", "name": "l", "access": 8, "descriptor": "I", "decoder": -1179083285}, {"field": "__ba_q", "owner": "ba", "name": "q", "access": 8, "descriptor": "I", "decoder": -281664295}, {"field": "__ba_s", "owner": "ba", "name": "s", "access": 8, "descriptor": "I", "decoder": -2078849253}, {"field": "__ba_a", "owner": "ba", "name": "a", "access": 8, "descriptor": "[J"}, {"field": "__ba_j", "owner": "ba", "name": "j", "access": 8, "descriptor": "[J"}, {"field": "__bb_hz", "owner": "bb", "name": "hz", "access": 8, "descriptor": "I", "decoder": -215744973}, {"field": "__bs_nb", "owner": "bs", "name": "nb", "access": 8, "descriptor": "I", "decoder": -1247107945}, {"field": "__bq_l", "owner": "bq", "name": "l", "access": 8, "descriptor": "[[[B"}, {"field": "__bq_w", "owner": "bq", "name": "w", "access": 8, "descriptor": "[[[B"}, {"field": "__bq_p", "owner": "bq", "name": "p", "access": 8, "descriptor": "I", "decoder": -2142579405}, {"field": "__bq_v", "owner": "bq", "name": "v", "access": 8, "descriptor": "I", "decoder": 834543729}, {"field": "__bq_a", "owner": "bq", "name": "a", "access": 8, "descriptor": "[[[I"}, {"field": "__bq_b", "owner": "bq", "name": "b", "access": 24, "descriptor": "[I"}, {"field": "__bq_c", "owner": "bq", "name": "c", "access": 24, "descriptor": "[I"}, {"field": "__bq_h", "owner": "bq", "name": "h", "access": 24, "descriptor": "[I"}, {"field": "__bq_r", "owner": "bq", "name": "r", "access": 24, "descriptor": "[I"}, {"field": "__bq_t", "owner": "bq", "name": "t", "access": 24, "descriptor": "[I"}, {"field": "__bq_y", "owner": "bq", "name": "y", "access": 24, "descriptor": "[I"}, {"field": "__bk_g", "owner": "bk", "name": "g", "access": 8, "descriptor": "[I"}, {"field": "__bd_x", "owner": "bd", "name": "x", "access": 9, "descriptor": "[[S"}, {"field": "__bd_er", "owner": "bd", "name": "er", "access": 8, "descriptor": "I", "decoder": 636454135}, {"field": "__bd_u", "owner": "bd", "name": "u", "access": 8, "descriptor": "Llq;"}, {"field": "__bc_dm", "owner": "bc", "name": "dm", "access": 8, "descriptor": "Lit;"}, {"field": "__bo_ar", "owner": "bo", "name": "ar", "access": 8, "descriptor": "Ljava/awt/FontMetrics;"}, {"field": "__bx_o", "owner": "bx", "name": "o", "access": 8, "descriptor": "I", "decoder": -992379173}, {"field": "__by_hb", "owner": "by", "name": "hb", "access": 8, "descriptor": "I", "decoder": -1218466245}, {"field": "__bu_hy", "owner": "bu", "name": "hy", "access": 8, "descriptor": "I", "decoder": 1075569245}, {"field": "__bm_i", "owner": "bm", "name": "i", "access": 8, "descriptor": "[I"}, {"field": "__client_il", "owner": "client", "name": "il", "access": 9, "descriptor": "I", "decoder": -1063713523}, {"field": "__client_ss", "owner": "client", "name": "ss", "access": 9, "descriptor": "I", "decoder": -451622431}, {"field": "__client_sj", "owner": "client", "name": "sj", "access": 8, "descriptor": "Lbj;"}, {"field": "__client_ai", "owner": "client", "name": "ai", "access": 8, "descriptor": "Z"}, {"field": "__client_bl", "owner": "client", "name": "bl", "access": 8, "descriptor": "Z"}, {"field": "__client_cw", "owner": "client", "name": "cw", "access": 8, "descriptor": "Z"}, {"field": "__client_fh", "owner": "client", "name": "fh", "access": 8, "descriptor": "Z"}, {"field": "__client_hh", "owner": "client", "name": "hh", "access": 8, "descriptor": "Z"}, {"field": "__client_ij", "owner": "client", "name": "ij", "access": 8, "descriptor": "Z"}, {"field": "__client_is", "owner": "client", "name": "is", "access": 8, "descriptor": "Z"}, {"field": "__client_jl", "owner": "client", "name": "jl", "access": 8, "descriptor": "Z"}, {"field": "__client_nn", "owner": "client", "name": "nn", "access": 8, "descriptor": "Z"}, {"field": "__client_nt", "owner": "client", "name": "nt", "access": 8, "descriptor": "Z"}, {"field": "__client_qq", "owner": "client", "name": "qq", "access": 8, "descriptor": "Z"}, {"field": "__client_kv", "owner": "client", "name": "kv", "access": 8, "descriptor": "[Z"}, {"field": "__client_od", "owner": "client", "name": "od", "access": 8, "descriptor": "[Z"}, {"field": "__client_oq", "owner": "client", "name": "oq", "access": 8, "descriptor": "[Z"}, {"field": "__client_ot", "owner": "client", "name": "ot", "access": 8, "descriptor": "[Z"}, {"field": "__client_rh", "owner": "client", "name": "rh", "access": 8, "descriptor": "[Z"}, {"field": "__client_ev", "owner": "client", "name": "ev", "access": 8, "descriptor": "[B"}, {"field": "__client_ru", "owner": "client", "name": "ru", "access": 8, "descriptor": "Lhi;"}, {"field": "__client_md", "owner": "client", "name": "md", "access": 8, "descriptor": "Lho;"}, {"field": "__client_mo", "owner": "client", "name": "mo", "access": 8, "descriptor": "Lho;"}, {"field": "__client_cn", "owner": "client", "name": "cn", "access": 8, "descriptor": "I", "decoder": -1731573291}, {"field": "__client_cq", "owner": "client", "name": "cq", "access": 8, "descriptor": "I", "decoder": -1313705721}, {"field": "__client_dp", "owner": "client", "name": "dp", "access": 8, "descriptor": "I", "decoder": 1154760205}, {"field": "__client_ej", "owner": "client", "name": "ej", "access": 8, "descriptor": "I", "decoder": -964416527}, {"field": "__client_el", "owner": "client", "name": "el", "access": 8, "descriptor": "I", "decoder": 689094231}, {"field": "__client_eo", "owner": "client", "name": "eo", "access": 8, "descriptor": "I", "decoder": -447055355}, {"field": "__client_ex", "owner": "client", "name": "ex", "access": 8, "descriptor": "I", "decoder": 755300107}, {"field": "__client_fd", "owner": "client", "name": "fd", "access": 8, "descriptor": "I", "decoder": -1050480853}, {"field": "__client_fe", "owner": "client", "name": "fe", "access": 8, "descriptor": "I", "decoder": 829228151}, {"field": "__client_fk", "owner": "client", "name": "fk", "access": 8, "descriptor": "I", "decoder": 8470625}, {"field": "__client_fn", "owner": "client", "name": "fn", "access": 8, "descriptor": "I", "decoder": -597100741}, {"field": "__client_fw", "owner": "client", "name": "fw", "access": 8, "descriptor": "I", "decoder": 516059665}, {"field": "__client_gh", "owner": "client", "name": "gh", "access": 8, "descriptor": "I", "decoder": -1456975367}, {"field": "__client_gp", "owner": "client", "name": "gp", "access": 8, "descriptor": "I", "decoder": 685418907}, {"field": "__client_gy", "owner": "client", "name": "gy", "access": 8, "descriptor": "I", "decoder": -134014371}, {"field": "__client_hc", "owner": "client", "name": "hc", "access": 8, "descriptor": "I", "decoder": -441913785}, {"field": "__client_hd", "owner": "client", "name": "hd", "access": 8, "descriptor": "I", "decoder": 438998245}, {"field": "__client_hg", "owner": "client", "name": "hg", "access": 8, "descriptor": "I", "decoder": 1658018867}, {"field": "__client_hi", "owner": "client", "name": "hi", "access": 8, "descriptor": "I", "decoder": 402871301}, {"field": "__client_hj", "owner": "client", "name": "hj", "access": 8, "descriptor": "I", "decoder": -1420078475}, {"field": "__client_hk", "owner": "client", "name": "hk", "access": 8, "descriptor": "I", "decoder": -1149138989}, {"field": "__client_hn", "owner": "client", "name": "hn", "access": 8, "descriptor": "I", "decoder": -516933613}, {"field": "__client_hr", "owner": "client", "name": "hr", "access": 8, "descriptor": "I", "decoder": 1664350313}, {"field": "__client_ht", "owner": "client", "name": "ht", "access": 8, "descriptor": "I", "decoder": 1502058429}, {"field": "__client_hu", "owner": "client", "name": "hu", "access": 8, "descriptor": "I", "decoder": -480633777}, {"field": "__client_hv", "owner": "client", "name": "hv", "access": 8, "descriptor": "I", "decoder": 218086227}, {"field": "__client_hx", "owner": "client", "name": "hx", "access": 8, "descriptor": "I", "decoder": -1406125895}, {"field": "__client_ie", "owner": "client", "name": "ie", "access": 8, "descriptor": "I", "decoder": 236939601}, {"field": "__client_if", "owner": "client", "name": "if", "access": 8, "descriptor": "I", "decoder": -952172003}, {"field": "__client_ik", "owner": "client", "name": "ik", "access": 8, "descriptor": "I", "decoder": 910983131}, {"field": "__client_in", "owner": "client", "name": "in", "access": 8, "descriptor": "I", "decoder": 351193663}, {"field": "__client_iy", "owner": "client", "name": "iy", "access": 8, "descriptor": "I", "decoder": -1213062413}, {"field": "__client_ja", "owner": "client", "name": "ja", "access": 8, "descriptor": "I", "decoder": 813973829}, {"field": "__client_jc", "owner": "client", "name": "jc", "access": 8, "descriptor": "I", "decoder": 1084498299}, {"field": "__client_je", "owner": "client", "name": "je", "access": 8, "descriptor": "I", "decoder": 1959822241}, {"field": "__client_jo", "owner": "client", "name": "jo", "access": 8, "descriptor": "I", "decoder": -836962805}, {"field": "__client_ju", "owner": "client", "name": "ju", "access": 8, "descriptor": "I", "decoder": 1691923909}, {"field": "__client_jz", "owner": "client", "name": "jz", "access": 8, "descriptor": "I", "decoder": -1267243587}, {"field": "__client_ke", "owner": "client", "name": "ke", "access": 8, "descriptor": "I", "decoder": -1208823721}, {"field": "__client_ku", "owner": "client", "name": "ku", "access": 8, "descriptor": "I", "decoder": -898263359}, {"field": "__client_ky", "owner": "client", "name": "ky", "access": 8, "descriptor": "I", "decoder": -64320473}, {"field": "__client_lb", "owner": "client", "name": "lb", "access": 8, "descriptor": "I", "decoder": 699084605}, {"field": "__client_lc", "owner": "client", "name": "lc", "access": 8, "descriptor": "I", "decoder": 196309625}, {"field": "__client_ln", "owner": "client", "name": "ln", "access": 8, "descriptor": "I", "decoder": 885052255}, {"field": "__client_lq", "owner": "client", "name": "lq", "access": 8, "descriptor": "I", "decoder": 26099827}, {"field": "__client_ls", "owner": "client", "name": "ls", "access": 8, "descriptor": "I", "decoder": -274968037}, {"field": "__client_mb", "owner": "client", "name": "mb", "access": 8, "descriptor": "I", "decoder": 1694810089}, {"field": "__client_mf", "owner": "client", "name": "mf", "access": 8, "descriptor": "I", "decoder": -1715020411}, {"field": "__client_mh", "owner": "client", "name": "mh", "access": 8, "descriptor": "I", "decoder": 535219347}, {"field": "__client_ms", "owner": "client", "name": "ms", "access": 8, "descriptor": "I", "decoder": 2128395635}, {"field": "__client_na", "owner": "client", "name": "na", "access": 8, "descriptor": "I", "decoder": -934082817}, {"field": "__client_nc", "owner": "client", "name": "nc", "access": 8, "descriptor": "I", "decoder": 344308849}, {"field": "__client_nd", "owner": "client", "name": "nd", "access": 8, "descriptor": "I", "decoder": 760022015}, {"field": "__client_ng", "owner": "client", "name": "ng", "access": 8, "descriptor": "I", "decoder": -1379876433}, {"field": "__client_nh", "owner": "client", "name": "nh", "access": 8, "descriptor": "I", "decoder": -278874973}, {"field": "__client_ni", "owner": "client", "name": "ni", "access": 8, "descriptor": "I", "decoder": 1990512261}, {"field": "__client_nk", "owner": "client", "name": "nk", "access": 8, "descriptor": "I", "decoder": 1019025247}, {"field": "__client_nl", "owner": "client", "name": "nl", "access": 8, "descriptor": "I", "decoder": 1939745821}, {"field": "__client_np", "owner": "client", "name": "np", "access": 8, "descriptor": "I", "decoder": 1881890525}, {"field": "__client_nq", "owner": "client", "name": "nq", "access": 8, "descriptor": "I", "decoder": 1467519791}, {"field": "__client_ns", "owner": "client", "name": "ns", "access": 8, "descriptor": "I", "decoder": 743073293}, {"field": "__client_nv", "owner": "client", "name": "nv", "access": 8, "descriptor": "I", "decoder": -758575207}, {"field": "__client_nw", "owner": "client", "name": "nw", "access": 8, "descriptor": "I", "decoder": -843739713}, {"field": "__client_nx", "owner": "client", "name": "nx", "access": 8, "descriptor": "I", "decoder": 1132932607}, {"field": "__client_oo", "owner": "client", "name": "oo", "access": 8, "descriptor": "I", "decoder": 1348053843}, {"field": "__client_pb", "owner": "client", "name": "pb", "access": 8, "descriptor": "I", "decoder": 1230692157}, {"field": "__client_ph", "owner": "client", "name": "ph", "access": 8, "descriptor": "I", "decoder": 587773171}, {"field": "__client_pl", "owner": "client", "name": "pl", "access": 8, "descriptor": "I", "decoder": -1362734353}, {"field": "__client_pm", "owner": "client", "name": "pm", "access": 8, "descriptor": "I", "decoder": -460406997}, {"field": "__client_qc", "owner": "client", "name": "qc", "access": 8, "descriptor": "I", "decoder": -818676431}, {"field": "__client_qf", "owner": "client", "name": "qf", "access": 8, "descriptor": "I", "decoder": 1365951359}, {"field": "__client_qp", "owner": "client", "name": "qp", "access": 8, "descriptor": "I", "decoder": -1352723865}, {"field": "__client_qy", "owner": "client", "name": "qy", "access": 8, "descriptor": "I", "decoder": -34294211}, {"field": "__client_sd", "owner": "client", "name": "sd", "access": 8, "descriptor": "I", "decoder": 944933439}, {"field": "__client_sl", "owner": "client", "name": "sl", "access": 8, "descriptor": "I", "decoder": -1605855913}, {"field": "__client_sx", "owner": "client", "name": "sx", "access": 8, "descriptor": "I", "decoder": 206275573}, {"field": "__client_fg", "owner": "client", "name": "fg", "access": 8, "descriptor": "[I"}, {"field": "__client_kd", "owner": "client", "name": "kd", "access": 8, "descriptor": "[I"}, {"field": "__client_kn", "owner": "client", "name": "kn", "access": 8, "descriptor": "[I"}, {"field": "__client_nf", "owner": "client", "name": "nf", "access": 8, "descriptor": "[I"}, {"field": "__client_nr", "owner": "client", "name": "nr", "access": 8, "descriptor": "[I"}, {"field": "__client_nz", "owner": "client", "name": "nz", "access": 8, "descriptor": "[I"}, {"field": "__client_pn", "owner": "client", "name": "pn", "access": 8, "descriptor": "[I"}, {"field": "__client_pq", "owner": "client", "name": "pq", "access": 8, "descriptor": "[I"}, {"field": "__client_pv", "owner": "client", "name": "pv", "access": 8, "descriptor": "[I"}, {"field": "__client_qd", "owner": "client", "name": "qd", "access": 8, "descriptor": "[I"}, {"field": "__client_qk", "owner": "client", "name": "qk", "access": 8, "descriptor": "[I"}, {"field": "__client_qn", "owner": "client", "name": "qn", "access": 8, "descriptor": "[I"}, {"field": "__client_rd", "owner": "client", "name": "rd", "access": 8, "descriptor": "[I"}, {"field": "__client_rj", "owner": "client", "name": "rj", "access": 8, "descriptor": "[I"}, {"field": "__client_rk", "owner": "client", "name": "rk", "access": 8, "descriptor": "[I"}, {"field": "__client_rl", "owner": "client", "name": "rl", "access": 8, "descriptor": "[I"}, {"field": "__client_th", "owner": "client", "name": "th", "access": 8, "descriptor": "[I"}, {"field": "__client_tp", "owner": "client", "name": "tp", "access": 8, "descriptor": "[I"}, {"field": "__client_pz", "owner": "client", "name": "pz", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__client_kh", "owner": "client", "name": "kh", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "__client_oc", "owner": "client", "name": "oc", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "__client_oh", "owner": "client", "name": "oh", "access": 8, "descriptor": "<PERSON><PERSON>;"}, {"field": "__client_ch", "owner": "client", "name": "ch", "access": 8, "descriptor": "J", "decoder": -6574384275386813769}, {"field": "__client_oa", "owner": "client", "name": "oa", "access": 8, "descriptor": "J", "decoder": 8818983958751331899}, {"field": "__client_pp", "owner": "client", "name": "pp", "access": 8, "descriptor": "J", "decoder": -1704358328771670975}, {"field": "__client_pu", "owner": "client", "name": "pu", "access": 8, "descriptor": "[J"}, {"field": "__client_rb", "owner": "client", "name": "rb", "access": 8, "descriptor": "S"}, {"field": "__client_re", "owner": "client", "name": "re", "access": 8, "descriptor": "S"}, {"field": "__client_ri", "owner": "client", "name": "ri", "access": 8, "descriptor": "S"}, {"field": "__client_rm", "owner": "client", "name": "rm", "access": 8, "descriptor": "S"}, {"field": "__client_rp", "owner": "client", "name": "rp", "access": 8, "descriptor": "S"}, {"field": "__client_rs", "owner": "client", "name": "rs", "access": 8, "descriptor": "S"}, {"field": "__client_rv", "owner": "client", "name": "rv", "access": 8, "descriptor": "S"}, {"field": "__client_rw", "owner": "client", "name": "rw", "access": 8, "descriptor": "S"}, {"field": "__client_gd", "owner": "client", "name": "gd", "access": 24, "descriptor": "[I"}, {"field": "__bt_g", "owner": "bt", "name": "g", "access": 8, "descriptor": "I", "decoder": 1143875805}, {"field": "__bt_e", "owner": "bt", "name": "e", "access": 8, "descriptor": "[I"}, {"field": "__bt_l", "owner": "bt", "name": "l", "access": 8, "descriptor": "[I"}, {"field": "__bf_m", "owner": "bf", "name": "m", "access": 8, "descriptor": "I", "decoder": 1548448409}, {"field": "__bv_s", "owner": "bv", "name": "s", "access": 8, "descriptor": "Z"}, {"field": "__bv_t", "owner": "bv", "name": "t", "access": 8, "descriptor": "Z"}, {"field": "__bv_y", "owner": "bv", "name": "y", "access": 8, "descriptor": "I", "decoder": -1019558835}, {"field": "__bv_r", "owner": "bv", "name": "r", "access": 24, "descriptor": "D"}, {"field": "__bv_z", "owner": "bv", "name": "z", "access": 24, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__bw_d", "owner": "bw", "name": "d", "access": 8, "descriptor": "I", "decoder": -1807262579}, {"field": "__bw_k", "owner": "bw", "name": "k", "access": 8, "descriptor": "Llq;"}, {"field": "__ce_o", "owner": "ce", "name": "o", "access": 24, "descriptor": "Ljava/math/BigInteger;"}, {"field": "__ce_u", "owner": "ce", "name": "u", "access": 24, "descriptor": "Ljava/math/BigInteger;"}, {"field": "__cy_d", "owner": "cy", "name": "d", "access": 8, "descriptor": "[I"}, {"field": "__cm_e", "owner": "cm", "name": "e", "access": 9, "descriptor": "I", "decoder": 533653621}, {"field": "__cm_f", "owner": "cm", "name": "f", "access": 24, "descriptor": "Ljava/math/BigInteger;"}, {"field": "__cm_m", "owner": "cm", "name": "m", "access": 24, "descriptor": "Ljava/math/BigInteger;"}, {"field": "__cj_ay", "owner": "cj", "name": "ay", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__cj_bo", "owner": "cj", "name": "bo", "access": 8, "descriptor": "[Llq;"}, {"field": "__cj_sp", "owner": "cj", "name": "sp", "access": 8, "descriptor": "[S"}, {"field": "__cu_aa", "owner": "cu", "name": "aa", "access": 8, "descriptor": "Z"}, {"field": "__cu_ax", "owner": "cu", "name": "ax", "access": 8, "descriptor": "Z"}, {"field": "__cu_ba", "owner": "cu", "name": "ba", "access": 8, "descriptor": "Z"}, {"field": "__cu_bk", "owner": "cu", "name": "bk", "access": 8, "descriptor": "Z"}, {"field": "__cu_f", "owner": "cu", "name": "f", "access": 8, "descriptor": "Z"}, {"field": "__cu_ac", "owner": "cu", "name": "ac", "access": 8, "descriptor": "I", "decoder": 1677133239}, {"field": "__cu_aj", "owner": "cu", "name": "aj", "access": 8, "descriptor": "I", "decoder": 1077910071}, {"field": "__cu_aw", "owner": "cu", "name": "aw", "access": 8, "descriptor": "I", "decoder": 1861681835}, {"field": "__cu_bb", "owner": "cu", "name": "bb", "access": 8, "descriptor": "I", "decoder": 1769484271}, {"field": "__cu_bj", "owner": "cu", "name": "bj", "access": 8, "descriptor": "I", "decoder": 1548722671}, {"field": "__cu_bl", "owner": "cu", "name": "bl", "access": 8, "descriptor": "I", "decoder": 1431381579}, {"field": "__cu_br", "owner": "cu", "name": "br", "access": 8, "descriptor": "I", "decoder": -376233039}, {"field": "__cu_q", "owner": "cu", "name": "q", "access": 8, "descriptor": "I", "decoder": 1606541885}, {"field": "__cu_y", "owner": "cu", "name": "y", "access": 8, "descriptor": "I", "decoder": 932541923}, {"field": "__cu_ai", "owner": "cu", "name": "ai", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__cu_bq", "owner": "cu", "name": "bq", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__cu_bs", "owner": "cu", "name": "bs", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__cu_g", "owner": "cu", "name": "g", "access": 8, "descriptor": "Lln;"}, {"field": "__cu_bi", "owner": "cu", "name": "bi", "access": 8, "descriptor": "J", "decoder": 803647694904294325}, {"field": "__cu_bz", "owner": "cu", "name": "bz", "access": 8, "descriptor": "J", "decoder": -3234258972092460093}, {"field": "__cu_d", "owner": "cu", "name": "d", "access": 8, "descriptor": "Llq;"}, {"field": "__cu_o", "owner": "cu", "name": "o", "access": 8, "descriptor": "Llq;"}, {"field": "__cn_bu", "owner": "cn", "name": "bu", "access": 8, "descriptor": "Llq;"}, {"field": "__cq_q", "owner": "cq", "name": "q", "access": 8, "descriptor": "[B"}, {"field": "__cq_w", "owner": "cq", "name": "w", "access": 8, "descriptor": "[B"}, {"field": "__cq_a", "owner": "cq", "name": "a", "access": 8, "descriptor": "Lgr;"}, {"field": "__cq_o", "owner": "cq", "name": "o", "access": 8, "descriptor": "[Lgr;"}, {"field": "__cq_l", "owner": "cq", "name": "l", "access": 8, "descriptor": "I", "decoder": 945250525}, {"field": "__cq_n", "owner": "cq", "name": "n", "access": 8, "descriptor": "I", "decoder": -22568397}, {"field": "__cq_e", "owner": "cq", "name": "e", "access": 8, "descriptor": "[I"}, {"field": "__cq_i", "owner": "cq", "name": "i", "access": 8, "descriptor": "[I"}, {"field": "__cd_s", "owner": "cd", "name": "s", "access": 8, "descriptor": "Z"}, {"field": "__cd_z", "owner": "cd", "name": "z", "access": 8, "descriptor": "[Z"}, {"field": "__cd_g", "owner": "cd", "name": "g", "access": 8, "descriptor": "[B"}, {"field": "__cd_ag", "owner": "cd", "name": "ag", "access": 8, "descriptor": "[F"}, {"field": "__cd_aj", "owner": "cd", "name": "aj", "access": 8, "descriptor": "[F"}, {"field": "__cd_aq", "owner": "cd", "name": "aq", "access": 8, "descriptor": "[F"}, {"field": "__cd_c", "owner": "cd", "name": "c", "access": 8, "descriptor": "[F"}, {"field": "__cd_p", "owner": "cd", "name": "p", "access": 8, "descriptor": "[F"}, {"field": "__cd_r", "owner": "cd", "name": "r", "access": 8, "descriptor": "[F"}, {"field": "__cd_v", "owner": "cd", "name": "v", "access": 8, "descriptor": "[F"}, {"field": "__cd_d", "owner": "cd", "name": "d", "access": 8, "descriptor": "I"}, {"field": "__cd_e", "owner": "cd", "name": "e", "access": 8, "descriptor": "I"}, {"field": "__cd_l", "owner": "cd", "name": "l", "access": 8, "descriptor": "I"}, {"field": "__cd_x", "owner": "cd", "name": "x", "access": 8, "descriptor": "I"}, {"field": "__cd_ar", "owner": "cd", "name": "ar", "access": 8, "descriptor": "[I"}, {"field": "__cd_av", "owner": "cd", "name": "av", "access": 8, "descriptor": "[I"}, {"field": "__cd_j", "owner": "cd", "name": "j", "access": 8, "descriptor": "[I"}, {"field": "__ca_k", "owner": "ca", "name": "k", "access": 8, "descriptor": "[Z"}, {"field": "__ca_d", "owner": "ca", "name": "d", "access": 8, "descriptor": "[I"}, {"field": "__ca_x", "owner": "ca", "name": "x", "access": 8, "descriptor": "[I"}, {"field": "__ca_f", "owner": "ca", "name": "f", "access": 24, "descriptor": "[F"}, {"field": "__ca_m", "owner": "ca", "name": "m", "access": 24, "descriptor": "[I"}, {"field": "__di_l", "owner": "di", "name": "l", "access": 8, "descriptor": "F"}, {"field": "__di_u", "owner": "di", "name": "u", "access": 8, "descriptor": "[[F"}, {"field": "__di_e", "owner": "di", "name": "e", "access": 8, "descriptor": "I"}, {"field": "__di_g", "owner": "di", "name": "g", "access": 8, "descriptor": "[[I"}, {"field": "__dk_lm", "owner": "dk", "name": "lm", "access": 8, "descriptor": "Lho;"}, {"field": "__dk_o", "owner": "dk", "name": "o", "access": 8, "descriptor": "I", "decoder": -1762891465}, {"field": "__dr_ao", "owner": "dr", "name": "ao", "access": 8, "descriptor": "I", "decoder": -704675849}, {"field": "__dw_ab", "owner": "dw", "name": "ab", "access": 8, "descriptor": "I"}, {"field": "__dw_al", "owner": "dw", "name": "al", "access": 8, "descriptor": "[I"}, {"field": "__dw_aw", "owner": "dw", "name": "aw", "access": 8, "descriptor": "[I"}, {"field": "__dv_j", "owner": "dv", "name": "j", "access": 8, "descriptor": "[I"}, {"field": "__dv_s", "owner": "dv", "name": "s", "access": 8, "descriptor": "[I"}, {"field": "__dv_t", "owner": "dv", "name": "t", "access": 8, "descriptor": "[I"}, {"field": "__dv_y", "owner": "dv", "name": "y", "access": 8, "descriptor": "[I"}, {"field": "__dv_z", "owner": "dv", "name": "z", "access": 8, "descriptor": "[I"}, {"field": "__dv_b", "owner": "dv", "name": "b", "access": 24, "descriptor": "[[I"}, {"field": "__dv_h", "owner": "dv", "name": "h", "access": 24, "descriptor": "[[I"}, {"field": "__dz_f", "owner": "dz", "name": "f", "access": 8, "descriptor": "[I"}, {"field": "__dz_m", "owner": "dz", "name": "m", "access": 8, "descriptor": "[I"}, {"field": "__dz_q", "owner": "dz", "name": "q", "access": 8, "descriptor": "[I"}, {"field": "__dz_w", "owner": "dz", "name": "w", "access": 8, "descriptor": "[I"}, {"field": "__du_bx", "owner": "du", "name": "bx", "access": 8, "descriptor": "Z"}, {"field": "__du_ad", "owner": "du", "name": "ad", "access": 8, "descriptor": "[Z"}, {"field": "__du_at", "owner": "du", "name": "at", "access": 8, "descriptor": "[Z"}, {"field": "__du_aa", "owner": "du", "name": "aa", "access": 8, "descriptor": "[I"}, {"field": "__du_af", "owner": "du", "name": "af", "access": 8, "descriptor": "[I"}, {"field": "__du_am", "owner": "du", "name": "am", "access": 8, "descriptor": "[I"}, {"field": "__du_an", "owner": "du", "name": "an", "access": 8, "descriptor": "[I"}, {"field": "__du_au", "owner": "du", "name": "au", "access": 8, "descriptor": "[I"}, {"field": "__du_az", "owner": "du", "name": "az", "access": 8, "descriptor": "[I"}, {"field": "__du_ba", "owner": "du", "name": "ba", "access": 8, "descriptor": "[I"}, {"field": "__du_bb", "owner": "du", "name": "bb", "access": 8, "descriptor": "[I"}, {"field": "__du_bj", "owner": "du", "name": "bj", "access": 8, "descriptor": "[I"}, {"field": "__du_bk", "owner": "du", "name": "bk", "access": 8, "descriptor": "[I"}, {"field": "__du_bn", "owner": "du", "name": "bn", "access": 8, "descriptor": "[I"}, {"field": "__du_bq", "owner": "du", "name": "bq", "access": 8, "descriptor": "[I"}, {"field": "__du_br", "owner": "du", "name": "br", "access": 8, "descriptor": "[I"}, {"field": "__du_bs", "owner": "du", "name": "bs", "access": 8, "descriptor": "[I"}, {"field": "__du_ai", "owner": "du", "name": "ai", "access": 8, "descriptor": "[[I"}, {"field": "__du_ax", "owner": "du", "name": "ax", "access": 8, "descriptor": "[[I"}, {"field": "__dm_g", "owner": "dm", "name": "g", "access": 8, "descriptor": "I", "decoder": 262104267}, {"field": "__dm_k", "owner": "dm", "name": "k", "access": 8, "descriptor": "I", "decoder": -852671813}, {"field": "__dm_l", "owner": "dm", "name": "l", "access": 8, "descriptor": "I", "decoder": -2071972829}, {"field": "__dm_u", "owner": "dm", "name": "u", "access": 8, "descriptor": "I", "decoder": 1135175963}, {"field": "__dq_pe", "owner": "dq", "name": "pe", "access": 8, "descriptor": "Z"}, {"field": "__dq_rq", "owner": "dq", "name": "rq", "access": 8, "descriptor": "I", "decoder": 1351620693}, {"field": "__et_w", "owner": "et", "name": "w", "access": 9, "descriptor": "Z"}, {"field": "__et_f", "owner": "et", "name": "f", "access": 8, "descriptor": "Z"}, {"field": "__et_m", "owner": "et", "name": "m", "access": 8, "descriptor": "Z"}, {"field": "__et_p", "owner": "et", "name": "p", "access": 8, "descriptor": "[I"}, {"field": "__et_r", "owner": "et", "name": "r", "access": 8, "descriptor": "[I"}, {"field": "__eb_ah", "owner": "eb", "name": "ah", "access": 8, "descriptor": "Lla;"}, {"field": "__ek_x", "owner": "ek", "name": "x", "access": 8, "descriptor": "I", "decoder": 2096389021}, {"field": "__em_ab", "owner": "em", "name": "ab", "access": 8, "descriptor": "Z"}, {"field": "__em_ac", "owner": "em", "name": "ac", "access": 8, "descriptor": "Z"}, {"field": "__em_a", "owner": "em", "name": "a", "access": 8, "descriptor": "I"}, {"field": "__em_ad", "owner": "em", "name": "ad", "access": 8, "descriptor": "I"}, {"field": "__em_aa", "owner": "em", "name": "aa", "access": 24, "descriptor": "[I"}, {"field": "__em_af", "owner": "em", "name": "af", "access": 24, "descriptor": "[I"}, {"field": "__em_ai", "owner": "em", "name": "ai", "access": 24, "descriptor": "[I"}, {"field": "__em_ao", "owner": "em", "name": "ao", "access": 24, "descriptor": "[I"}, {"field": "__em_au", "owner": "em", "name": "au", "access": 24, "descriptor": "[I"}, {"field": "__em_ax", "owner": "em", "name": "ax", "access": 24, "descriptor": "[I"}, {"field": "__em_ba", "owner": "em", "name": "ba", "access": 24, "descriptor": "[I"}, {"field": "__ej_jv", "owner": "ej", "name": "jv", "access": 8, "descriptor": "Lho;"}, {"field": "__el_x", "owner": "el", "name": "x", "access": 9, "descriptor": "I", "decoder": 422473713}, {"field": "__el_gm", "owner": "el", "name": "gm", "access": 8, "descriptor": "Lln;"}, {"field": "__en_bc", "owner": "en", "name": "bc", "access": 8, "descriptor": "[Llq;"}, {"field": "__eh_fy", "owner": "eh", "name": "fy", "access": 8, "descriptor": "I", "decoder": -457229589}, {"field": "__eo_u", "owner": "eo", "name": "u", "access": 8, "descriptor": "I", "decoder": -883931753}, {"field": "__eo_ak", "owner": "eo", "name": "ak", "access": 8, "descriptor": "Lla;"}, {"field": "__ey_bx", "owner": "ey", "name": "bx", "access": 8, "descriptor": "[Llq;"}, {"field": "__fw_nm", "owner": "fw", "name": "nm", "access": 8, "descriptor": "I", "decoder": 1006519291}, {"field": "__fg_jh", "owner": "fg", "name": "jh", "access": 8, "descriptor": "Lho;"}, {"field": "__fh_x", "owner": "fh", "name": "x", "access": 9, "descriptor": "Lir;"}, {"field": "__fx_w", "owner": "fx", "name": "w", "access": 9, "descriptor": "I", "decoder": 121013503}, {"field": "__fs_o", "owner": "fs", "name": "o", "access": 9, "descriptor": "Ljava/io/File;"}, {"field": "__fs_au", "owner": "fs", "name": "au", "access": 8, "descriptor": "I", "decoder": 1181569415}, {"field": "__fo_m", "owner": "fo", "name": "m", "access": 9, "descriptor": "Z"}, {"field": "__fo_f", "owner": "fo", "name": "f", "access": 9, "descriptor": "Ljava/io/File;"}, {"field": "__fo_ro", "owner": "fo", "name": "ro", "access": 8, "descriptor": "I", "decoder": -852697585}, {"field": "__fo_q", "owner": "fo", "name": "q", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/util/Hashtable;"}, {"field": "__fb_bm", "owner": "fb", "name": "bm", "access": 8, "descriptor": "Llq;"}, {"field": "__fi_e", "owner": "fi", "name": "e", "access": 8, "descriptor": "[I"}, {"field": "__fi_l", "owner": "fi", "name": "l", "access": 8, "descriptor": "[I"}, {"field": "__fi_q", "owner": "fi", "name": "q", "access": 8, "descriptor": "[[I"}, {"field": "__fi_w", "owner": "fi", "name": "w", "access": 8, "descriptor": "[[I"}, {"field": "__fe_e", "owner": "fe", "name": "e", "access": 8, "descriptor": "[[[B"}, {"field": "__ge_a", "owner": "ge", "name": "a", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_aa", "owner": "ge", "name": "aa", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ab", "owner": "ge", "name": "ab", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ac", "owner": "ge", "name": "ac", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ad", "owner": "ge", "name": "ad", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ae", "owner": "ge", "name": "ae", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_af", "owner": "ge", "name": "af", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ag", "owner": "ge", "name": "ag", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ah", "owner": "ge", "name": "ah", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ai", "owner": "ge", "name": "ai", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_aj", "owner": "ge", "name": "aj", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ak", "owner": "ge", "name": "ak", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_al", "owner": "ge", "name": "al", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_am", "owner": "ge", "name": "am", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_an", "owner": "ge", "name": "an", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ao", "owner": "ge", "name": "ao", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ap", "owner": "ge", "name": "ap", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_aq", "owner": "ge", "name": "aq", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ar", "owner": "ge", "name": "ar", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_as", "owner": "ge", "name": "as", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_at", "owner": "ge", "name": "at", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_au", "owner": "ge", "name": "au", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_av", "owner": "ge", "name": "av", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_aw", "owner": "ge", "name": "aw", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ax", "owner": "ge", "name": "ax", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ay", "owner": "ge", "name": "ay", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_az", "owner": "ge", "name": "az", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_b", "owner": "ge", "name": "b", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ba", "owner": "ge", "name": "ba", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bb", "owner": "ge", "name": "bb", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bc", "owner": "ge", "name": "bc", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bd", "owner": "ge", "name": "bd", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_be", "owner": "ge", "name": "be", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bf", "owner": "ge", "name": "bf", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bg", "owner": "ge", "name": "bg", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bh", "owner": "ge", "name": "bh", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bi", "owner": "ge", "name": "bi", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bj", "owner": "ge", "name": "bj", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bk", "owner": "ge", "name": "bk", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bl", "owner": "ge", "name": "bl", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bm", "owner": "ge", "name": "bm", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bn", "owner": "ge", "name": "bn", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bo", "owner": "ge", "name": "bo", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bp", "owner": "ge", "name": "bp", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bq", "owner": "ge", "name": "bq", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_br", "owner": "ge", "name": "br", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bs", "owner": "ge", "name": "bs", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bt", "owner": "ge", "name": "bt", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bu", "owner": "ge", "name": "bu", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bv", "owner": "ge", "name": "bv", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bw", "owner": "ge", "name": "bw", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bx", "owner": "ge", "name": "bx", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_by", "owner": "ge", "name": "by", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_bz", "owner": "ge", "name": "bz", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_c", "owner": "ge", "name": "c", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_ce", "owner": "ge", "name": "ce", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cj", "owner": "ge", "name": "cj", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cm", "owner": "ge", "name": "cm", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cr", "owner": "ge", "name": "cr", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cs", "owner": "ge", "name": "cs", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cv", "owner": "ge", "name": "cv", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_cy", "owner": "ge", "name": "cy", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_d", "owner": "ge", "name": "d", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_e", "owner": "ge", "name": "e", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_f", "owner": "ge", "name": "f", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_g", "owner": "ge", "name": "g", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_h", "owner": "ge", "name": "h", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_i", "owner": "ge", "name": "i", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_j", "owner": "ge", "name": "j", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_k", "owner": "ge", "name": "k", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_l", "owner": "ge", "name": "l", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_m", "owner": "ge", "name": "m", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_n", "owner": "ge", "name": "n", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_o", "owner": "ge", "name": "o", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_p", "owner": "ge", "name": "p", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_q", "owner": "ge", "name": "q", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_r", "owner": "ge", "name": "r", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_s", "owner": "ge", "name": "s", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_t", "owner": "ge", "name": "t", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_u", "owner": "ge", "name": "u", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_v", "owner": "ge", "name": "v", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_w", "owner": "ge", "name": "w", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_x", "owner": "ge", "name": "x", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_y", "owner": "ge", "name": "y", "access": 25, "descriptor": "Lge;"}, {"field": "__ge_z", "owner": "ge", "name": "z", "access": 25, "descriptor": "Lge;"}, {"field": "__gs_a", "owner": "gs", "name": "a", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_aa", "owner": "gs", "name": "aa", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ab", "owner": "gs", "name": "ab", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ac", "owner": "gs", "name": "ac", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ad", "owner": "gs", "name": "ad", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ae", "owner": "gs", "name": "ae", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_af", "owner": "gs", "name": "af", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ag", "owner": "gs", "name": "ag", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ah", "owner": "gs", "name": "ah", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ai", "owner": "gs", "name": "ai", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_aj", "owner": "gs", "name": "aj", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ak", "owner": "gs", "name": "ak", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_al", "owner": "gs", "name": "al", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_an", "owner": "gs", "name": "an", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ao", "owner": "gs", "name": "ao", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ap", "owner": "gs", "name": "ap", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_aq", "owner": "gs", "name": "aq", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ar", "owner": "gs", "name": "ar", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_as", "owner": "gs", "name": "as", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_at", "owner": "gs", "name": "at", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_au", "owner": "gs", "name": "au", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_av", "owner": "gs", "name": "av", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_aw", "owner": "gs", "name": "aw", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ax", "owner": "gs", "name": "ax", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ay", "owner": "gs", "name": "ay", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_az", "owner": "gs", "name": "az", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_b", "owner": "gs", "name": "b", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ba", "owner": "gs", "name": "ba", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bb", "owner": "gs", "name": "bb", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bc", "owner": "gs", "name": "bc", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bd", "owner": "gs", "name": "bd", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_be", "owner": "gs", "name": "be", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bf", "owner": "gs", "name": "bf", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bg", "owner": "gs", "name": "bg", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bh", "owner": "gs", "name": "bh", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bi", "owner": "gs", "name": "bi", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bj", "owner": "gs", "name": "bj", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bk", "owner": "gs", "name": "bk", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bl", "owner": "gs", "name": "bl", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bm", "owner": "gs", "name": "bm", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bn", "owner": "gs", "name": "bn", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bo", "owner": "gs", "name": "bo", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bp", "owner": "gs", "name": "bp", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bq", "owner": "gs", "name": "bq", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_br", "owner": "gs", "name": "br", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bs", "owner": "gs", "name": "bs", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bt", "owner": "gs", "name": "bt", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bu", "owner": "gs", "name": "bu", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bv", "owner": "gs", "name": "bv", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bw", "owner": "gs", "name": "bw", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bx", "owner": "gs", "name": "bx", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_by", "owner": "gs", "name": "by", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_bz", "owner": "gs", "name": "bz", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_c", "owner": "gs", "name": "c", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cb", "owner": "gs", "name": "cb", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cc", "owner": "gs", "name": "cc", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ce", "owner": "gs", "name": "ce", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cf", "owner": "gs", "name": "cf", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cg", "owner": "gs", "name": "cg", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ch", "owner": "gs", "name": "ch", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cj", "owner": "gs", "name": "cj", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ck", "owner": "gs", "name": "ck", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cl", "owner": "gs", "name": "cl", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cm", "owner": "gs", "name": "cm", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cn", "owner": "gs", "name": "cn", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cp", "owner": "gs", "name": "cp", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cq", "owner": "gs", "name": "cq", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cr", "owner": "gs", "name": "cr", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cs", "owner": "gs", "name": "cs", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_ct", "owner": "gs", "name": "ct", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cu", "owner": "gs", "name": "cu", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cv", "owner": "gs", "name": "cv", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cw", "owner": "gs", "name": "cw", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cy", "owner": "gs", "name": "cy", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_cz", "owner": "gs", "name": "cz", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_d", "owner": "gs", "name": "d", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_e", "owner": "gs", "name": "e", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_f", "owner": "gs", "name": "f", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_g", "owner": "gs", "name": "g", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_h", "owner": "gs", "name": "h", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_i", "owner": "gs", "name": "i", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_j", "owner": "gs", "name": "j", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_k", "owner": "gs", "name": "k", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_l", "owner": "gs", "name": "l", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_m", "owner": "gs", "name": "m", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_n", "owner": "gs", "name": "n", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_o", "owner": "gs", "name": "o", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_p", "owner": "gs", "name": "p", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_q", "owner": "gs", "name": "q", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_r", "owner": "gs", "name": "r", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_s", "owner": "gs", "name": "s", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_t", "owner": "gs", "name": "t", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_u", "owner": "gs", "name": "u", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_v", "owner": "gs", "name": "v", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_w", "owner": "gs", "name": "w", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_x", "owner": "gs", "name": "x", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_y", "owner": "gs", "name": "y", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_z", "owner": "gs", "name": "z", "access": 25, "descriptor": "Lgs;"}, {"field": "__gs_qh", "owner": "gs", "name": "qh", "access": 8, "descriptor": "I", "decoder": 1076448827}, {"field": "__gs_am", "owner": "gs", "name": "am", "access": 24, "descriptor": "Lgs;"}, {"field": "__gs_ci", "owner": "gs", "name": "ci", "access": 24, "descriptor": "Lgs;"}, {"field": "__gl_m", "owner": "gl", "name": "m", "access": 25, "descriptor": "Lgl;"}, {"field": "__gl_q", "owner": "gl", "name": "q", "access": 25, "descriptor": "Lgl;"}, {"field": "__gl_w", "owner": "gl", "name": "w", "access": 25, "descriptor": "Lgl;"}, {"field": "__gl_f", "owner": "gl", "name": "f", "access": 24, "descriptor": "Lgl;"}, {"field": "__gl_o", "owner": "gl", "name": "o", "access": 24, "descriptor": "Lgl;"}, {"field": "__gl_g", "owner": "gl", "name": "g", "access": 24, "descriptor": "[Lgl;"}, {"field": "__gg_u", "owner": "gg", "name": "u", "access": 9, "descriptor": "I", "decoder": -1583654139}, {"field": "__gd_e", "owner": "gd", "name": "e", "access": 8, "descriptor": "[[I"}, {"field": "__go_ck", "owner": "go", "name": "ck", "access": 9, "descriptor": "C"}, {"field": "__gr_q", "owner": "gr", "name": "q", "access": 8, "descriptor": "[I"}, {"field": "__gr_o", "owner": "gr", "name": "o", "access": 8, "descriptor": "[J"}, {"field": "__gi_g", "owner": "gi", "name": "g", "access": 8, "descriptor": "[I"}, {"field": "__gx_n", "owner": "gx", "name": "n", "access": 9, "descriptor": "Lkk;"}, {"field": "__gx_d", "owner": "gx", "name": "d", "access": 24, "descriptor": "[I"}, {"field": "__gx_x", "owner": "gx", "name": "x", "access": 24, "descriptor": "[I"}, {"field": "__gu_et", "owner": "gu", "name": "et", "access": 8, "descriptor": "Lit;"}, {"field": "__hx_z", "owner": "hx", "name": "z", "access": 9, "descriptor": "I", "decoder": 701739159}, {"field": "__hg_q", "owner": "hg", "name": "q", "access": 8, "descriptor": "Ljava/util/Calendar;"}, {"field": "__hg_f", "owner": "hg", "name": "f", "access": 24, "descriptor": "[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__hg_m", "owner": "hg", "name": "m", "access": 24, "descriptor": "[[<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__hd_fc", "owner": "hd", "name": "fc", "access": 8, "descriptor": "J", "decoder": -6780258776884306463}, {"field": "__hf_o", "owner": "hf", "name": "o", "access": 9, "descriptor": "I", "decoder": -848833001}, {"field": "__hf_f", "owner": "hf", "name": "f", "access": 9, "descriptor": "Lir;"}, {"field": "__hf_q", "owner": "hf", "name": "q", "access": 9, "descriptor": "Lir;"}, {"field": "__hf_u", "owner": "hf", "name": "u", "access": 9, "descriptor": "Lir;"}, {"field": "__hf_k", "owner": "hf", "name": "k", "access": 8, "descriptor": "[I"}, {"field": "__hs_x", "owner": "hs", "name": "x", "access": 24, "descriptor": "[B"}, {"field": "__hq_qg", "owner": "hq", "name": "qg", "access": 8, "descriptor": "I", "decoder": -1367955455}, {"field": "__hv_h", "owner": "hv", "name": "h", "access": 8, "descriptor": "I", "decoder": 120772859}, {"field": "__hi_g", "owner": "hi", "name": "g", "access": 9, "descriptor": "[S"}, {"field": "__hi_d", "owner": "hi", "name": "d", "access": 24, "descriptor": "[I"}, {"field": "__ht_m", "owner": "ht", "name": "m", "access": 25, "descriptor": "[S"}, {"field": "__ht_q", "owner": "ht", "name": "q", "access": 25, "descriptor": "[S"}, {"field": "__ht_f", "owner": "ht", "name": "f", "access": 25, "descriptor": "[[S"}, {"field": "__ht_w", "owner": "ht", "name": "w", "access": 25, "descriptor": "[[S"}, {"field": "__hj_e", "owner": "hj", "name": "e", "access": 9, "descriptor": "[S"}, {"field": "__ho_j", "owner": "ho", "name": "j", "access": 9, "descriptor": "Z"}, {"field": "__ho_d", "owner": "ho", "name": "d", "access": 9, "descriptor": "Lir;"}, {"field": "__ho_n", "owner": "ho", "name": "n", "access": 8, "descriptor": "Ler;"}, {"field": "__ho_z", "owner": "ho", "name": "z", "access": 8, "descriptor": "Ler;"}, {"field": "__hz_l", "owner": "hz", "name": "l", "access": 9, "descriptor": "[[S"}, {"field": "__hy_az", "owner": "hy", "name": "az", "access": 12, "descriptor": "Z"}, {"field": "__hb_sg", "owner": "hb", "name": "sg", "access": 8, "descriptor": "I", "decoder": -745071109}, {"field": "__hb_av", "owner": "hb", "name": "av", "access": 8, "descriptor": "L<PERSON><PERSON>/awt/Font;"}, {"field": "__hc_ef", "owner": "hc", "name": "ef", "access": 8, "descriptor": "[I"}, {"field": "__id_aa", "owner": "id", "name": "aa", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ad", "owner": "id", "name": "ad", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ae", "owner": "id", "name": "ae", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_af", "owner": "id", "name": "af", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ai", "owner": "id", "name": "ai", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_am", "owner": "id", "name": "am", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_an", "owner": "id", "name": "an", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ao", "owner": "id", "name": "ao", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ap", "owner": "id", "name": "ap", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_as", "owner": "id", "name": "as", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_at", "owner": "id", "name": "at", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_au", "owner": "id", "name": "au", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ax", "owner": "id", "name": "ax", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_az", "owner": "id", "name": "az", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ba", "owner": "id", "name": "ba", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bb", "owner": "id", "name": "bb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bc", "owner": "id", "name": "bc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bd", "owner": "id", "name": "bd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_be", "owner": "id", "name": "be", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bf", "owner": "id", "name": "bf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bg", "owner": "id", "name": "bg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bh", "owner": "id", "name": "bh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bi", "owner": "id", "name": "bi", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bj", "owner": "id", "name": "bj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bk", "owner": "id", "name": "bk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bl", "owner": "id", "name": "bl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bm", "owner": "id", "name": "bm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bn", "owner": "id", "name": "bn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bo", "owner": "id", "name": "bo", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bp", "owner": "id", "name": "bp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bq", "owner": "id", "name": "bq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_br", "owner": "id", "name": "br", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bs", "owner": "id", "name": "bs", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bt", "owner": "id", "name": "bt", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bu", "owner": "id", "name": "bu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bv", "owner": "id", "name": "bv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bw", "owner": "id", "name": "bw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bx", "owner": "id", "name": "bx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_by", "owner": "id", "name": "by", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_bz", "owner": "id", "name": "bz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ca", "owner": "id", "name": "ca", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cb", "owner": "id", "name": "cb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cc", "owner": "id", "name": "cc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cd", "owner": "id", "name": "cd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ce", "owner": "id", "name": "ce", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cf", "owner": "id", "name": "cf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cg", "owner": "id", "name": "cg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ch", "owner": "id", "name": "ch", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ci", "owner": "id", "name": "ci", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cj", "owner": "id", "name": "cj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ck", "owner": "id", "name": "ck", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cl", "owner": "id", "name": "cl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cm", "owner": "id", "name": "cm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cn", "owner": "id", "name": "cn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_co", "owner": "id", "name": "co", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cp", "owner": "id", "name": "cp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cq", "owner": "id", "name": "cq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cr", "owner": "id", "name": "cr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cs", "owner": "id", "name": "cs", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ct", "owner": "id", "name": "ct", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cu", "owner": "id", "name": "cu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cv", "owner": "id", "name": "cv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cw", "owner": "id", "name": "cw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cx", "owner": "id", "name": "cx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cy", "owner": "id", "name": "cy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_cz", "owner": "id", "name": "cz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_d", "owner": "id", "name": "d", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_da", "owner": "id", "name": "da", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_db", "owner": "id", "name": "db", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dc", "owner": "id", "name": "dc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dd", "owner": "id", "name": "dd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_de", "owner": "id", "name": "de", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_df", "owner": "id", "name": "df", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dg", "owner": "id", "name": "dg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_di", "owner": "id", "name": "di", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dj", "owner": "id", "name": "dj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dk", "owner": "id", "name": "dk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dl", "owner": "id", "name": "dl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dm", "owner": "id", "name": "dm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dn", "owner": "id", "name": "dn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_do", "owner": "id", "name": "do", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dp", "owner": "id", "name": "dp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dq", "owner": "id", "name": "dq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dr", "owner": "id", "name": "dr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ds", "owner": "id", "name": "ds", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dt", "owner": "id", "name": "dt", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_du", "owner": "id", "name": "du", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dv", "owner": "id", "name": "dv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dx", "owner": "id", "name": "dx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dy", "owner": "id", "name": "dy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_dz", "owner": "id", "name": "dz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_eb", "owner": "id", "name": "eb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ec", "owner": "id", "name": "ec", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ed", "owner": "id", "name": "ed", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ee", "owner": "id", "name": "ee", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ef", "owner": "id", "name": "ef", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_eh", "owner": "id", "name": "eh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ei", "owner": "id", "name": "ei", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ej", "owner": "id", "name": "ej", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ek", "owner": "id", "name": "ek", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_el", "owner": "id", "name": "el", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_em", "owner": "id", "name": "em", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_en", "owner": "id", "name": "en", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_eo", "owner": "id", "name": "eo", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_eq", "owner": "id", "name": "eq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_er", "owner": "id", "name": "er", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_et", "owner": "id", "name": "et", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_eu", "owner": "id", "name": "eu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ev", "owner": "id", "name": "ev", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ew", "owner": "id", "name": "ew", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ex", "owner": "id", "name": "ex", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ey", "owner": "id", "name": "ey", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fa", "owner": "id", "name": "fa", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fb", "owner": "id", "name": "fb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ff", "owner": "id", "name": "ff", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fk", "owner": "id", "name": "fk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fm", "owner": "id", "name": "fm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fn", "owner": "id", "name": "fn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fs", "owner": "id", "name": "fs", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fv", "owner": "id", "name": "fv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fx", "owner": "id", "name": "fx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_fz", "owner": "id", "name": "fz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ga", "owner": "id", "name": "ga", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gb", "owner": "id", "name": "gb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gc", "owner": "id", "name": "gc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gd", "owner": "id", "name": "gd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ge", "owner": "id", "name": "ge", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gf", "owner": "id", "name": "gf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gg", "owner": "id", "name": "gg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gh", "owner": "id", "name": "gh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gi", "owner": "id", "name": "gi", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gj", "owner": "id", "name": "gj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gk", "owner": "id", "name": "gk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gl", "owner": "id", "name": "gl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gm", "owner": "id", "name": "gm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gn", "owner": "id", "name": "gn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_go", "owner": "id", "name": "go", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gp", "owner": "id", "name": "gp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gq", "owner": "id", "name": "gq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gr", "owner": "id", "name": "gr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gs", "owner": "id", "name": "gs", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gt", "owner": "id", "name": "gt", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gu", "owner": "id", "name": "gu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gv", "owner": "id", "name": "gv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gw", "owner": "id", "name": "gw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gx", "owner": "id", "name": "gx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gy", "owner": "id", "name": "gy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_gz", "owner": "id", "name": "gz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ha", "owner": "id", "name": "ha", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hb", "owner": "id", "name": "hb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hc", "owner": "id", "name": "hc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hd", "owner": "id", "name": "hd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hf", "owner": "id", "name": "hf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hg", "owner": "id", "name": "hg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hh", "owner": "id", "name": "hh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hk", "owner": "id", "name": "hk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hm", "owner": "id", "name": "hm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hn", "owner": "id", "name": "hn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hr", "owner": "id", "name": "hr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hs", "owner": "id", "name": "hs", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hx", "owner": "id", "name": "hx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_hy", "owner": "id", "name": "hy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ia", "owner": "id", "name": "ia", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ib", "owner": "id", "name": "ib", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ic", "owner": "id", "name": "ic", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_id", "owner": "id", "name": "id", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ig", "owner": "id", "name": "ig", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ih", "owner": "id", "name": "ih", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ii", "owner": "id", "name": "ii", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ik", "owner": "id", "name": "ik", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_im", "owner": "id", "name": "im", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_io", "owner": "id", "name": "io", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ip", "owner": "id", "name": "ip", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_iq", "owner": "id", "name": "iq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ir", "owner": "id", "name": "ir", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_it", "owner": "id", "name": "it", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_iu", "owner": "id", "name": "iu", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_iv", "owner": "id", "name": "iv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_iw", "owner": "id", "name": "iw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ix", "owner": "id", "name": "ix", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ja", "owner": "id", "name": "ja", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jb", "owner": "id", "name": "jb", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jc", "owner": "id", "name": "jc", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jd", "owner": "id", "name": "jd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_je", "owner": "id", "name": "je", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jf", "owner": "id", "name": "jf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jg", "owner": "id", "name": "jg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jh", "owner": "id", "name": "jh", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ji", "owner": "id", "name": "ji", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jj", "owner": "id", "name": "jj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jk", "owner": "id", "name": "jk", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jl", "owner": "id", "name": "jl", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jm", "owner": "id", "name": "jm", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jn", "owner": "id", "name": "jn", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jo", "owner": "id", "name": "jo", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jp", "owner": "id", "name": "jp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jq", "owner": "id", "name": "jq", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jr", "owner": "id", "name": "jr", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_js", "owner": "id", "name": "js", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jt", "owner": "id", "name": "jt", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ju", "owner": "id", "name": "ju", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jv", "owner": "id", "name": "jv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jw", "owner": "id", "name": "jw", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jx", "owner": "id", "name": "jx", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jy", "owner": "id", "name": "jy", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_jz", "owner": "id", "name": "jz", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kd", "owner": "id", "name": "kd", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ke", "owner": "id", "name": "ke", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kf", "owner": "id", "name": "kf", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kg", "owner": "id", "name": "kg", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kj", "owner": "id", "name": "kj", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_km", "owner": "id", "name": "km", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kp", "owner": "id", "name": "kp", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ks", "owner": "id", "name": "ks", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_kv", "owner": "id", "name": "kv", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ky", "owner": "id", "name": "ky", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_l", "owner": "id", "name": "l", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_x", "owner": "id", "name": "x", "access": 9, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__id_ab", "owner": "id", "name": "ab", "access": 8, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__ij_ak", "owner": "ij", "name": "ak", "access": 8, "descriptor": "Ljava/awt/Image;"}, {"field": "__iy_f", "owner": "iy", "name": "f", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__iy_m", "owner": "iy", "name": "m", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__iy_q", "owner": "iy", "name": "q", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__iy_w", "owner": "iy", "name": "w", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__iy_lo", "owner": "iy", "name": "lo", "access": 8, "descriptor": "I", "decoder": -365366303}, {"field": "__is_u", "owner": "is", "name": "u", "access": 25, "descriptor": "Lis;"}, {"field": "__is_f", "owner": "is", "name": "f", "access": 24, "descriptor": "Lis;"}, {"field": "__is_m", "owner": "is", "name": "m", "access": 24, "descriptor": "Lis;"}, {"field": "__is_o", "owner": "is", "name": "o", "access": 24, "descriptor": "Lis;"}, {"field": "__is_q", "owner": "is", "name": "q", "access": 24, "descriptor": "Lis;"}, {"field": "__is_w", "owner": "is", "name": "w", "access": 24, "descriptor": "Lis;"}, {"field": "__ik_cn", "owner": "ik", "name": "cn", "access": 9, "descriptor": "I", "decoder": -1753937079}, {"field": "__ik_rt", "owner": "ik", "name": "rt", "access": 8, "descriptor": "I", "decoder": -1086790653}, {"field": "__ir_bu", "owner": "ir", "name": "bu", "access": 8, "descriptor": "I", "decoder": 334735939}, {"field": "__ir_s", "owner": "ir", "name": "s", "access": 8, "descriptor": "I", "decoder": -244224193}, {"field": "__iv_q", "owner": "iv", "name": "q", "access": 8, "descriptor": "I", "decoder": -1282224505}, {"field": "__iq_b", "owner": "iq", "name": "b", "access": 9, "descriptor": "B"}, {"field": "__iq_f", "owner": "iq", "name": "f", "access": 9, "descriptor": "I", "decoder": 948359621}, {"field": "__iq_j", "owner": "iq", "name": "j", "access": 9, "descriptor": "I", "decoder": 840549915}, {"field": "__iq_q", "owner": "iq", "name": "q", "access": 9, "descriptor": "J", "decoder": -4580427166942649525}, {"field": "__iu_n", "owner": "iu", "name": "n", "access": 8, "descriptor": "I", "decoder": 662119345}, {"field": "__iu_e", "owner": "iu", "name": "e", "access": 8, "descriptor": "[I"}, {"field": "__ib_f", "owner": "ib", "name": "f", "access": 8, "descriptor": "Ler;"}, {"field": "__ix_f", "owner": "ix", "name": "f", "access": 9, "descriptor": "I", "decoder": 510774535}, {"field": "__ix_m", "owner": "ix", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__ic_m", "owner": "ic", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__iw_f", "owner": "iw", "name": "f", "access": 8, "descriptor": "Ler;"}, {"field": "__iw_m", "owner": "iw", "name": "m", "access": 8, "descriptor": "Lir;"}, {"field": "__im_q", "owner": "im", "name": "q", "access": 9, "descriptor": "I", "decoder": 235389995}, {"field": "__im_f", "owner": "im", "name": "f", "access": 9, "descriptor": "Lir;"}, {"field": "__ii_f", "owner": "ii", "name": "f", "access": 9, "descriptor": "Lir;"}, {"field": "__ii_m", "owner": "ii", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__ia_u", "owner": "ia", "name": "u", "access": 8, "descriptor": "[I"}, {"field": "__jb_m", "owner": "jb", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__jq_f", "owner": "jq", "name": "f", "access": 9, "descriptor": "Ler;"}, {"field": "__jq_m", "owner": "jq", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__jm_o", "owner": "jm", "name": "o", "access": 8, "descriptor": "Ler;"}, {"field": "__jm_f", "owner": "jm", "name": "f", "access": 8, "descriptor": "Lir;"}, {"field": "__jm_m", "owner": "jm", "name": "m", "access": 8, "descriptor": "Lir;"}, {"field": "__jm_q", "owner": "jm", "name": "q", "access": 8, "descriptor": "Lir;"}, {"field": "__jr_o", "owner": "jr", "name": "o", "access": 9, "descriptor": "Ler;"}, {"field": "__jr_l", "owner": "jr", "name": "l", "access": 8, "descriptor": "[Ldw;"}, {"field": "__jr_u", "owner": "jr", "name": "u", "access": 8, "descriptor": "Ler;"}, {"field": "__jr_q", "owner": "jr", "name": "q", "access": 8, "descriptor": "Lir;"}, {"field": "__jf_f", "owner": "jf", "name": "f", "access": 9, "descriptor": "Ler;"}, {"field": "__jf_m", "owner": "jf", "name": "m", "access": 9, "descriptor": "Lir;"}, {"field": "__jh_o", "owner": "jh", "name": "o", "access": 9, "descriptor": "Ler;"}, {"field": "__jh_f", "owner": "jh", "name": "f", "access": 9, "descriptor": "Lir;"}, {"field": "__jh_q", "owner": "jh", "name": "q", "access": 9, "descriptor": "Lir;"}, {"field": "__jn_by", "owner": "jn", "name": "by", "access": 8, "descriptor": "Llq;"}, {"field": "__jx_ne", "owner": "jx", "name": "ne", "access": 8, "descriptor": "[Lho;"}, {"field": "__ji_i", "owner": "ji", "name": "i", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_n", "owner": "ji", "name": "n", "access": 25, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_fp", "owner": "ji", "name": "fp", "access": 8, "descriptor": "Lfu;"}, {"field": "__ji_a", "owner": "ji", "name": "a", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_d", "owner": "ji", "name": "d", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_e", "owner": "ji", "name": "e", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_f", "owner": "ji", "name": "f", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_g", "owner": "ji", "name": "g", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_j", "owner": "ji", "name": "j", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_k", "owner": "ji", "name": "k", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_l", "owner": "ji", "name": "l", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_m", "owner": "ji", "name": "m", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_o", "owner": "ji", "name": "o", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_q", "owner": "ji", "name": "q", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_s", "owner": "ji", "name": "s", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_t", "owner": "ji", "name": "t", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_u", "owner": "ji", "name": "u", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_w", "owner": "ji", "name": "w", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_x", "owner": "ji", "name": "x", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ji_z", "owner": "ji", "name": "z", "access": 24, "descriptor": "<PERSON><PERSON>;"}, {"field": "__ks_at", "owner": "ks", "name": "at", "access": 12, "descriptor": "<PERSON><PERSON><PERSON>/lang/String;"}, {"field": "__kn_x", "owner": "kn", "name": "x", "access": 8, "descriptor": "Ley;"}, {"field": "__kz_o", "owner": "kz", "name": "o", "access": 8, "descriptor": "[[[B"}, {"field": "__kz_l", "owner": "kz", "name": "l", "access": 8, "descriptor": "Lln;"}, {"field": "__kb_q", "owner": "kb", "name": "q", "access": 24, "descriptor": "[C"}, {"field": "__kb_w", "owner": "kb", "name": "w", "access": 24, "descriptor": "[C"}, {"field": "__kc_m", "owner": "kc", "name": "m", "access": 9, "descriptor": "[C"}, {"field": "__kc_g", "owner": "kc", "name": "g", "access": 8, "descriptor": "[[[B"}, {"field": "__kc_f", "owner": "kc", "name": "f", "access": 8, "descriptor": "[C"}, {"field": "__kc_q", "owner": "kc", "name": "q", "access": 8, "descriptor": "[C"}, {"field": "__kc_w", "owner": "kc", "name": "w", "access": 8, "descriptor": "[I"}, {"field": "__kw_n", "owner": "kw", "name": "n", "access": 8, "descriptor": "Llq;"}, {"field": "__ka_e", "owner": "ka", "name": "e", "access": 8, "descriptor": "I", "decoder": -2077113503}, {"field": "__lp_ca", "owner": "lp", "name": "ca", "access": 8, "descriptor": "Z"}, {"field": "__lj_m", "owner": "lj", "name": "m", "access": 25, "descriptor": "Llj;"}, {"field": "__lj_f", "owner": "lj", "name": "f", "access": 24, "descriptor": "Llj;"}, {"field": "__lj_q", "owner": "lj", "name": "q", "access": 24, "descriptor": "Llj;"}, {"field": "__lr_e", "owner": "lr", "name": "e", "access": 8, "descriptor": "[[B"}, {"field": "__lr_f", "owner": "lr", "name": "f", "access": 8, "descriptor": "I", "decoder": 161627197}, {"field": "__lr_m", "owner": "lr", "name": "m", "access": 8, "descriptor": "I", "decoder": -1119867679}, {"field": "__lr_q", "owner": "lr", "name": "q", "access": 8, "descriptor": "I", "decoder": -76329055}, {"field": "__lr_l", "owner": "lr", "name": "l", "access": 8, "descriptor": "[I"}, {"field": "__lr_o", "owner": "lr", "name": "o", "access": 8, "descriptor": "[I"}, {"field": "__lr_w", "owner": "lr", "name": "w", "access": 8, "descriptor": "[I"}, {"field": "__lx_e", "owner": "lx", "name": "e", "access": 25, "descriptor": "Llx;"}, {"field": "__lx_m", "owner": "lx", "name": "m", "access": 25, "descriptor": "Llx;"}, {"field": "__lx_f", "owner": "lx", "name": "f", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_g", "owner": "lx", "name": "g", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_l", "owner": "lx", "name": "l", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_o", "owner": "lx", "name": "o", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_q", "owner": "lx", "name": "q", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_u", "owner": "lx", "name": "u", "access": 24, "descriptor": "Llx;"}, {"field": "__lx_w", "owner": "lx", "name": "w", "access": 24, "descriptor": "Llx;"}], "methods": [{"method": "AbstractFont_drawGlyph", "owner": "kt", "name": "ah", "access": 8, "parameters": ["pixels", "x", "y", "width", "height", "color"], "descriptor": "([BIIIII)V"}, {"method": "AbstractFont_drawGlyphAlpha", "owner": "kt", "name": "aw", "access": 8, "parameters": ["pixels", "x", "y", "width", "height", "color", "alpha"], "descriptor": "([BIIIIII)V"}, {"method": "AbstractFont_placeGlyph", "owner": "kt", "name": "ak", "access": 8, "descriptor": "([I[BIIIIIII)V"}, {"method": "AbstractFont_placeGlyphAlpha", "owner": "kt", "name": "al", "access": 8, "descriptor": "([I[BIIIIIIII)V"}, {"method": "ByteArrayPool_get", "owner": "gi", "name": "f", "access": 40, "descriptor": "(IZI)[B"}, {"method": "Bzip2Decompressor_decompress", "owner": "gk", "name": "m", "access": 9, "descriptor": "([BI[BII)I"}, {"method": "IterableNodeDeque_addBefore", "owner": "ja", "name": "w", "access": 9, "parameters": ["node", "old"], "descriptor": "(Lgw;Lgw;)V"}, {"method": "PcmStream_disable", "owner": "hm", "name": "al", "access": 24, "parameters": ["stream"], "descriptor": "(Ldc;B)V"}, {"method": "Rasterizer2D_clear", "owner": "lf", "name": "dd", "access": 9, "descriptor": "()V"}, {"method": "Rasterizer2D_drawHorizontalLine", "owner": "lf", "name": "dj", "access": 9, "parameters": ["x", "y", "length", "color"], "descriptor": "(IIII)V"}, {"method": "Rasterizer2D_drawHorizontalLineAlpha", "owner": "lf", "name": "du", "access": 8, "parameters": ["x", "y", "length", "rgb", "alpha"], "descriptor": "(IIIII)V"}, {"method": "Rasterizer2D_drawLine", "owner": "lf", "name": "et", "access": 9, "parameters": ["x0", "y0", "x1", "y1", "color"], "descriptor": "(IIIII)V"}, {"method": "Rasterizer2D_drawRectangle", "owner": "lf", "name": "dh", "access": 9, "parameters": ["x", "y", "width", "height", "color"], "descriptor": "(IIIII)V"}, {"method": "Rasterizer2D_drawRectangleAlpha", "owner": "lf", "name": "dz", "access": 9, "parameters": ["x", "y", "width", "height", "rgb", "alpha"], "descriptor": "(IIIIII)V"}, {"method": "Rasterizer2D_drawVerticalLine", "owner": "lf", "name": "dm", "access": 9, "parameters": ["x", "y", "length", "color"], "descriptor": "(IIII)V"}, {"method": "Rasterizer2D_drawVerticalLineAlpha", "owner": "lf", "name": "dq", "access": 8, "parameters": ["x", "y", "length", "rgb", "alpha"], "descriptor": "(IIIII)V"}, {"method": "Rasterizer2D_expandClip", "owner": "lf", "name": "dc", "access": 9, "parameters": ["xStart", "yStart", "xEnd", "yEnd"], "descriptor": "(IIII)V"}, {"method": "Rasterizer2D_fillRectangle", "owner": "lf", "name": "dr", "access": 9, "parameters": ["x", "y", "width", "height", "color"], "descriptor": "(IIIII)V"}, {"method": "Rasterizer2D_getClipArray", "owner": "lf", "name": "dg", "access": 9, "parameters": ["dst"], "descriptor": "([I)V"}, {"method": "Rasterizer2D_replace", "owner": "lf", "name": "dp", "access": 9, "parameters": ["pixels", "width", "height"], "descriptor": "([III)V"}, {"method": "Rasterizer2D_resetClip", "owner": "lf", "name": "ds", "access": 9, "descriptor": "()V"}, {"method": "Rasterizer2D_setClip", "owner": "lf", "name": "di", "access": 9, "parameters": ["xStart", "yStart", "xEnd", "yEnd"], "descriptor": "(IIII)V"}, {"method": "Rasterizer2D_setClipArray", "owner": "lf", "name": "db", "access": 9, "parameters": ["src"], "descriptor": "([I)V"}, {"method": "Rasterizer2D_setPixel", "owner": "lf", "name": "eb", "access": 8, "parameters": ["x", "y", "color"], "descriptor": "(III)V"}, {"method": "Rasterizer3D_brighten", "owner": "et", "name": "l", "access": 8, "parameters": ["rgb", "brightness"], "descriptor": "(ID)I"}, {"method": "Rasterizer3D_buildPalette", "owner": "et", "name": "g", "access": 24, "parameters": ["brightness", "hsMin", "hsMax"], "descriptor": "(DII)V"}, {"method": "Rasterizer3D_method1", "owner": "et", "name": "m", "access": 25, "descriptor": "()V"}, {"method": "Rasterizer3D_method3", "owner": "et", "name": "q", "access": 25, "descriptor": "()V"}, {"method": "Rasterizer3D_setBrightness", "owner": "et", "name": "u", "access": 25, "parameters": ["brightness"], "descriptor": "(D)V"}, {"method": "Rasterizer3D_setClip", "owner": "et", "name": "f", "access": 24, "parameters": ["xStart", "yStart", "xEnd", "yEnd"], "descriptor": "(IIII)V"}, {"method": "Rasterizer3D_setTextureLoader", "owner": "et", "name": "o", "access": 25, "parameters": ["textureLoader"], "descriptor": "(Lei;)V"}, {"method": "Scene_addOccluder", "owner": "em", "name": "w", "access": 9, "descriptor": "(IIIIIIII)V"}, {"method": "Scene_buildVisiblityMap", "owner": "em", "name": "at", "access": 9, "parameters": ["a", "b", "c", "viewportWidth", "viewportHeight"], "descriptor": "([IIIII)V"}, {"method": "addMessage", "owner": "g", "name": "f", "access": 8, "parameters": ["type", "sender", "text", "prefix"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;I)V"}, {"method": "addNpcToMenu", "owner": "dj", "name": "is", "access": 24, "parameters": ["npc", "menuArg0", "menuArg1", "menuArg2"], "descriptor": "(Ljz;IIIB)V"}, {"method": "addPlayerToMenu", "owner": "e", "name": "ik", "access": 24, "parameters": ["player", "menuArg0", "menuArg1", "menuArg2"], "descriptor": "(Lbr;IIII)V"}, {"method": "addPlayerToScene", "owner": "aw", "name": "gk", "access": 8, "parameters": ["player", "b"], "descriptor": "(Lbr;ZI)V"}, {"method": "alignComponent", "owner": "client", "name": "ip", "access": 0, "parameters": ["component"], "descriptor": "(Lho;B)V"}, {"method": "alignComponentPosition", "owner": "jf", "name": "iw", "access": 8, "parameters": ["component", "parentWidth", "parentHeight"], "descriptor": "(Lho;IIB)V"}, {"method": "alignComponentSize", "owner": "p", "name": "ic", "access": 8, "parameters": ["component", "parentWidth", "parentHeight", "b"], "descriptor": "(Lho;IIZB)V"}, {"method": "byteArrayFromObject", "owner": "dl", "name": "u", "access": 9, "parameters": ["o", "copyArray"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/Object;ZB)[B"}, {"method": "byteArrayToObject", "owner": "cb", "name": "o", "access": 9, "parameters": ["bytes", "copyArray"], "descriptor": "([BZB)Ljava/lang/Object;"}, {"method": "charToByteCp1252", "owner": "cr", "name": "m", "access": 9, "parameters": ["c"], "descriptor": "(CI)B"}, {"method": "clearIntArray", "owner": "hh", "name": "x", "access": 9, "descriptor": "([III)V"}, {"method": "clickComponent", "owner": "cq", "name": "jf", "access": 24, "parameters": ["component", "x", "y"], "descriptor": "(Lho;III)V"}, {"method": "closeInterface", "owner": "cs", "name": "jd", "access": 24, "parameters": ["w", "b"], "descriptor": "(Lbx;ZI)V"}, {"method": "colorStartTag", "owner": "dy", "name": "f", "access": 8, "parameters": ["color"], "descriptor": "(II)<PERSON><PERSON><PERSON>/lang/String;"}, {"method": "compareWorlds", "owner": "aj", "name": "u", "access": 8, "parameters": ["w0", "w1", "mode", "b"], "descriptor": "(Lbt;Lbt;IZI)I"}, {"method": "currentTimeMs", "owner": "ga", "name": "m", "access": 41, "parameters": [], "descriptor": "(I)J"}, {"method": "decodeStringCp1252", "owner": "bx", "name": "o", "access": 9, "parameters": ["src", "srcStart", "length"], "descriptor": "([BIII)Ljava/lang/String;"}, {"method": "decompressBytes", "owner": "id", "name": "ay", "access": 24, "descriptor": "([BI)[B"}, {"method": "doCycle", "owner": "client", "name": "al", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "doCycleJs5", "owner": "client", "name": "ef", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "doCycleJs5Connect", "owner": "client", "name": "ev", "access": 0, "parameters": [], "descriptor": "(B)V"}, {"method": "doCycleLoggedIn", "owner": "client", "name": "fx", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "doCycleLoggedOut", "owner": "client", "name": "fg", "access": 16, "parameters": [], "descriptor": "(B)V"}, {"method": "draw", "owner": "client", "name": "ab", "access": 20, "descriptor": "(ZI)V"}, {"method": "drawActor2d", "owner": "at", "name": "gr", "access": 24, "parameters": ["actor", "a", "b", "c", "d", "e"], "descriptor": "(Lbz;IIIIIB)V"}, {"method": "drawInterface", "owner": "az", "name": "ir", "access": 24, "descriptor": "([Lho;IIIIIIIII)V"}, {"method": "drawLoadingMessage", "owner": "bi", "name": "gl", "access": 24, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;ZI)V"}, {"method": "drawLoggedIn", "owner": "client", "name": "gs", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "drawTitle", "owner": "bd", "name": "x", "access": 8, "descriptor": "(Lkk;Lkk;Lkk;I)V"}, {"method": "encodeStringCp1252", "owner": "dt", "name": "w", "access": 9, "parameters": ["src", "srcStart", "srcEnd", "dst", "dstStart"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/CharSequence;II[BII)I"}, {"method": "escapeBrackets", "owner": "kt", "name": "s", "access": 9, "parameters": ["s"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;)Ljava/lang/String;"}, {"method": "findEnumerated", "owner": "bn", "name": "m", "access": 9, "parameters": ["values", "ordinal"], "descriptor": "([Lgc;II)Lgc;"}, {"method": "getComponent<PERSON>hild", "owner": "gx", "name": "f", "access": 9, "descriptor": "(III)Lho;"}, {"method": "getComponentClickMask", "owner": "f", "name": "kp", "access": 8, "parameters": ["component"], "descriptor": "(<PERSON><PERSON>;<PERSON>)I"}, {"method": "getFrames", "owner": "bc", "name": "x", "access": 8, "parameters": ["id"], "descriptor": "(II)Len;"}, {"method": "getHitmarkDefinition", "owner": "bp", "name": "f", "access": 9, "parameters": ["id"], "descriptor": "(II)Ljm;"}, {"method": "getInterfaceComponent", "owner": "gd", "name": "m", "access": 9, "parameters": ["id"], "descriptor": "(IB)Lho;"}, {"method": "getItemDefinition", "owner": "hc", "name": "m", "access": 9, "parameters": ["id"], "descriptor": "(II)Ljv;"}, {"method": "getItemSprite", "owner": "ht", "name": "d", "access": 25, "parameters": ["id", "quantity", "n0", "n1", "n2", "b0"], "descriptor": "(IIIIIZI)Lln;"}, {"method": "getKitDefinition", "owner": "as", "name": "m", "access": 9, "descriptor": "(II)Lim;"}, {"method": "getNpcDefinition", "owner": "jr", "name": "f", "access": 9, "parameters": ["id"], "descriptor": "(II)Ljz;"}, {"method": "getObjectDefinition", "owner": "af", "name": "f", "access": 9, "parameters": ["id"], "descriptor": "(II)Ljr;"}, {"method": "getParamDefinition", "owner": "hz", "name": "m", "access": 9, "parameters": ["id"], "descriptor": "(IB)Ljb;"}, {"method": "getPreferencesFile", "owner": "gl", "name": "q", "access": 9, "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;<PERSON>java/lang/String;ZS)Ldk;"}, {"method": "getSequenceDefinition", "owner": "ae", "name": "m", "access": 9, "descriptor": "(II)Ljh;"}, {"method": "getSpotAnimationDefinition", "owner": "af", "name": "m", "access": 9, "descriptor": "(IB)Lio;"}, {"method": "getTileHeight", "owner": "ay", "name": "gu", "access": 24, "descriptor": "(IIII)I"}, {"method": "getVarbit", "owner": "ah", "name": "m", "access": 9, "parameters": ["id"], "descriptor": "(II)I"}, {"method": "getWorldMapElement", "owner": "dm", "name": "m", "access": 9, "parameters": ["id"], "descriptor": "(IB)Lic;"}, {"method": "hashString", "owner": "client", "name": "l", "access": 9, "parameters": ["chars"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/CharSequence;I)I"}, {"method": "init", "owner": "client", "name": "init", "access": 17, "parameters": [], "descriptor": "()V"}, {"method": "insertMenuItem", "owner": "j", "name": "il", "access": 24, "parameters": ["action", "targetName", "opcode", "arg0", "arg1", "arg2", "b"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;Ljava/lang/String;IIIIZB)V"}, {"method": "itemContainerSetItem", "owner": "al", "name": "w", "access": 8, "parameters": ["itemContainerId", "index", "itemId", "itemQuantity"], "descriptor": "(IIIII)V"}, {"method": "js5Error", "owner": "client", "name": "ea", "access": 0, "parameters": ["code"], "descriptor": "(IS)V"}, {"method": "kill0", "owner": "client", "name": "ae", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "loadClassFromDescriptor", "owner": "iw", "name": "w", "access": 8, "parameters": ["descriptor"], "descriptor": "(<PERSON><PERSON><PERSON>/lang/String;I)Ljava/lang/Class;"}, {"method": "loadInterface", "owner": "dr", "name": "q", "access": 9, "parameters": ["itf"], "descriptor": "(IB)Z"}, {"method": "loadRegions", "owner": "af", "name": "gc", "access": 24, "parameters": ["isInInstance", "packetBuffer"], "descriptor": "(ZLhx;B)V"}, {"method": "loadTerrain", "owner": "e", "name": "o", "access": 24, "parameters": ["buffer", "plane", "x", "y", "x0", "y0", "n"], "descriptor": "(Lgr;IIIIIIB)V"}, {"method": "loadWorlds", "owner": "ji", "name": "m", "access": 8, "parameters": [], "descriptor": "(I)Z"}, {"method": "menuAction", "owner": "bz", "name": "hb", "access": 24, "parameters": ["argument1", "argument2", "opcode", "argument0", "action", "targetName", "mouseX", "mouseY"], "descriptor": "(III<PERSON><PERSON><PERSON>/lang/String;<PERSON><PERSON><PERSON>/lang/String;III)V"}, {"method": "newArchive", "owner": "c", "name": "fw", "access": 8, "descriptor": "(IZZZB)Lit;"}, {"method": "newPcmPlayer", "owner": "cq", "name": "aj", "access": 25, "descriptor": "(Lfz;III)Lco;"}, {"method": "newScript", "owner": "ee", "name": "w", "access": 8, "parameters": ["bytes"], "descriptor": "([BI)Lcg;"}, {"method": "openMenu", "owner": "client", "name": "hj", "access": 16, "parameters": ["x", "y"], "descriptor": "(IIB)V"}, {"method": "queueSoundEffect", "owner": "ks", "name": "fv", "access": 8, "descriptor": "(IIIB)V"}, {"method": "readMusicSample", "owner": "cd", "name": "e", "access": 8, "descriptor": "(Lir;II)Lcd;"}, {"method": "readSoundEffect", "owner": "ci", "name": "m", "access": 9, "descriptor": "(Lir;II)Lci;"}, {"method": "readSprite", "owner": "le", "name": "m", "access": 9, "parameters": ["archive", "group", "file"], "descriptor": "(Lir;III)Lln;"}, {"method": "readStringIntParameters", "owner": "ir", "name": "m", "access": 24, "parameters": ["buffer", "hashTable"], "descriptor": "(Lgr;Llh;I)Llh;"}, {"method": "readTrack", "owner": "he", "name": "m", "access": 9, "descriptor": "(Lir;II)Lhe;"}, {"method": "requestNetFile", "owner": "hx", "name": "f", "access": 8, "descriptor": "(Lit;IIIBZI)V"}, {"method": "runCs1", "owner": "il", "name": "im", "access": 24, "parameters": ["component"], "descriptor": "(<PERSON><PERSON>;I)Z"}, {"method": "runScript", "owner": "ir", "name": "m", "access": 9, "parameters": ["scriptEvent"], "descriptor": "(Lby;S)V"}, {"method": "runScript0", "owner": "l", "name": "f", "access": 8, "parameters": ["scriptEvent", "n"], "descriptor": "(Lby;II)V"}, {"method": "setUp", "owner": "client", "name": "aw", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "setViewportShape", "owner": "kx", "name": "gg", "access": 24, "parameters": ["x", "y", "width", "height", "clear"], "descriptor": "(IIIIZI)V"}, {"method": "updateExternalPlayer", "owner": "ie", "name": "o", "access": 8, "descriptor": "(Lhx;II)Z"}, {"method": "updateGameState", "owner": "ba", "name": "ec", "access": 8, "parameters": ["gameState"], "descriptor": "(IB)V"}, {"method": "updateInterface", "owner": "il", "name": "jr", "access": 24, "parameters": ["components", "parentId", "a", "b", "c", "d", "x", "y"], "descriptor": "([Lho;IIIIIIII)V"}, {"method": "updatePlayer", "owner": "k", "name": "m", "access": 24, "descriptor": "(Lhx;B)V"}, {"method": "username", "owner": "client", "name": "kr", "access": 1, "descriptor": "(B)Lkp;"}, {"method": "worldMap", "owner": "dy", "name": "er", "access": 8, "descriptor": "(I)Llz;"}, {"method": "worldToMinimap", "owner": "ep", "name": "jk", "access": 24, "descriptor": "(IIIILln;Lhp;I)V"}, {"method": "worldToScreen", "owner": "aj", "name": "gx", "access": 24, "descriptor": "(IIII)V"}, {"method": "__au_135", "owner": "client", "name": "au", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "__j_136", "owner": "client", "name": "j", "access": 20, "parameters": [], "descriptor": "(I)V"}, {"method": "__ge_137", "owner": "client", "name": "ge", "access": 0, "parameters": [], "descriptor": "(I)V"}, {"method": "__hg_138", "owner": "client", "name": "hg", "access": 16, "parameters": ["arg0"], "descriptor": "(Lcl;I)Z"}, {"method": "__hi_139", "owner": "client", "name": "hi", "access": 16, "parameters": [], "descriptor": "(I)V"}, {"method": "__ht_140", "owner": "client", "name": "ht", "access": 16, "parameters": [], "descriptor": "(B)Z"}, {"method": "__iu_141", "owner": "client", "name": "iu", "access": 16, "parameters": ["arg0"], "descriptor": "(ZI)V"}, {"method": "__jh_142", "owner": "client", "name": "jh", "access": 16, "parameters": [], "descriptor": "(I)V"}], "constructors": [{"access": 1, "descriptor": "()V"}]}]