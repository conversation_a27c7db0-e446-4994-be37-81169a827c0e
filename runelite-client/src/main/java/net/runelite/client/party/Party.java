// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: party.proto

package net.runelite.client.party;

/* public */ final class Party {
  private Party() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }
  public interface JoinOr<PERSON>uilder extends
      // @@protoc_insertion_point(interface_extends:party.Join)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    long getPartyId();

    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    long getMemberId();
  }
  /**
   * <pre>
   * c-&gt;s
   * </pre>
   *
   * Protobuf type {@code party.Join}
   */
  public  static final class Join extends
      com.google.protobuf.GeneratedMessageLite<
          Join, Join.Builder> implements
      // @@protoc_insertion_point(message_implements:party.Join)
      JoinOrBuilder {
    private Join() {
    }
    public static final int PARTYID_FIELD_NUMBER = 1;
    private long partyId_;
    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    @java.lang.Override
    public long getPartyId() {
      return partyId_;
    }
    /**
     * <code>int64 partyId = 1;</code>
     * @param value The partyId to set.
     */
    private void setPartyId(long value) {
      
      partyId_ = value;
    }
    /**
     * <code>int64 partyId = 1;</code>
     */
    private void clearPartyId() {
      
      partyId_ = 0L;
    }

    public static final int MEMBERID_FIELD_NUMBER = 2;
    private long memberId_;
    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }
    /**
     * <code>int64 memberId = 2;</code>
     * @param value The memberId to set.
     */
    private void setMemberId(long value) {
      
      memberId_ = value;
    }
    /**
     * <code>int64 memberId = 2;</code>
     */
    private void clearMemberId() {
      
      memberId_ = 0L;
    }

    public static net.runelite.client.party.Party.Join parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Join parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Join parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Join parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Join parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Join parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.Join prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * <pre>
     * c-&gt;s
     * </pre>
     *
     * Protobuf type {@code party.Join}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.Join, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.Join)
        net.runelite.client.party.Party.JoinOrBuilder {
      // Construct using net.runelite.client.party.Party.Join.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>int64 partyId = 1;</code>
       * @return The partyId.
       */
      @java.lang.Override
      public long getPartyId() {
        return instance.getPartyId();
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @param value The partyId to set.
       * @return This builder for chaining.
       */
      public Builder setPartyId(long value) {
        copyOnWrite();
        instance.setPartyId(value);
        return this;
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartyId() {
        copyOnWrite();
        instance.clearPartyId();
        return this;
      }

      /**
       * <code>int64 memberId = 2;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return instance.getMemberId();
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        copyOnWrite();
        instance.setMemberId(value);
        return this;
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        copyOnWrite();
        instance.clearMemberId();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.Join)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.Join();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "partyId_",
              "memberId_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0002\u0002\u0002" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.Join> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.Join.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.Join>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.Join)
    private static final net.runelite.client.party.Party.Join DEFAULT_INSTANCE;
    static {
      Join defaultInstance = new Join();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Join.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.Join getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Join> PARSER;

    public static com.google.protobuf.Parser<Join> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface PartOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.Part)
      com.google.protobuf.MessageLiteOrBuilder {
  }
  /**
   * Protobuf type {@code party.Part}
   */
  public  static final class Part extends
      com.google.protobuf.GeneratedMessageLite<
          Part, Part.Builder> implements
      // @@protoc_insertion_point(message_implements:party.Part)
      PartOrBuilder {
    private Part() {
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Part parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Part parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Part parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Part parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Part parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.Part prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.Part}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.Part, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.Part)
        net.runelite.client.party.Party.PartOrBuilder {
      // Construct using net.runelite.client.party.Party.Part.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      // @@protoc_insertion_point(builder_scope:party.Part)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.Part();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = null;java.lang.String info =
                "\u0000\u0000";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.Part> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.Part.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.Part>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.Part)
    private static final net.runelite.client.party.Party.Part DEFAULT_INSTANCE;
    static {
      Part defaultInstance = new Part();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Part.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.Part getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Part> PARSER;

    public static com.google.protobuf.Parser<Part> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface DataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.Data)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>string type = 2;</code>
     * @return The type.
     */
    java.lang.String getType();
    /**
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <code>bytes data = 1;</code>
     * @return The data.
     */
    com.google.protobuf.ByteString getData();
  }
  /**
   * Protobuf type {@code party.Data}
   */
  public  static final class Data extends
      com.google.protobuf.GeneratedMessageLite<
          Data, Data.Builder> implements
      // @@protoc_insertion_point(message_implements:party.Data)
      DataOrBuilder {
    private Data() {
      type_ = "";
      data_ = com.google.protobuf.ByteString.EMPTY;
    }
    public static final int TYPE_FIELD_NUMBER = 2;
    private java.lang.String type_;
    /**
     * <code>string type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public java.lang.String getType() {
      return type_;
    }
    /**
     * <code>string type = 2;</code>
     * @return The bytes for type.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTypeBytes() {
      return com.google.protobuf.ByteString.copyFromUtf8(type_);
    }
    /**
     * <code>string type = 2;</code>
     * @param value The type to set.
     */
    private void setType(
        java.lang.String value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      type_ = value;
    }
    /**
     * <code>string type = 2;</code>
     */
    private void clearType() {
      
      type_ = getDefaultInstance().getType();
    }
    /**
     * <code>string type = 2;</code>
     * @param value The bytes for type to set.
     */
    private void setTypeBytes(
        com.google.protobuf.ByteString value) {
      checkByteStringIsUtf8(value);
      type_ = value.toStringUtf8();
      
    }

    public static final int DATA_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString data_;
    /**
     * <code>bytes data = 1;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getData() {
      return data_;
    }
    /**
     * <code>bytes data = 1;</code>
     * @param value The data to set.
     */
    private void setData(com.google.protobuf.ByteString value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      data_ = value;
    }
    /**
     * <code>bytes data = 1;</code>
     */
    private void clearData() {
      
      data_ = getDefaultInstance().getData();
    }

    public static net.runelite.client.party.Party.Data parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Data parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Data parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Data parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Data parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.Data parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.Data prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.Data}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.Data, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.Data)
        net.runelite.client.party.Party.DataOrBuilder {
      // Construct using net.runelite.client.party.Party.Data.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>string type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        return instance.getType();
      }
      /**
       * <code>string type = 2;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        return instance.getTypeBytes();
      }
      /**
       * <code>string type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(
          java.lang.String value) {
        copyOnWrite();
        instance.setType(value);
        return this;
      }
      /**
       * <code>string type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        copyOnWrite();
        instance.clearType();
        return this;
      }
      /**
       * <code>string type = 2;</code>
       * @param value The bytes for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setTypeBytes(value);
        return this;
      }

      /**
       * <code>bytes data = 1;</code>
       * @return The data.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getData() {
        return instance.getData();
      }
      /**
       * <code>bytes data = 1;</code>
       * @param value The data to set.
       * @return This builder for chaining.
       */
      public Builder setData(com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setData(value);
        return this;
      }
      /**
       * <code>bytes data = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearData() {
        copyOnWrite();
        instance.clearData();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.Data)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.Data();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "data_",
              "type_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\n\u0002\u0208" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.Data> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.Data.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.Data>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.Data)
    private static final net.runelite.client.party.Party.Data DEFAULT_INSTANCE;
    static {
      Data defaultInstance = new Data();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        Data.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.Data getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<Data> PARSER;

    public static com.google.protobuf.Parser<Data> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.C2S)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>.party.Join join = 1;</code>
     * @return Whether the join field is set.
     */
    boolean hasJoin();
    /**
     * <code>.party.Join join = 1;</code>
     * @return The join.
     */
    net.runelite.client.party.Party.Join getJoin();

    /**
     * <code>.party.Part part = 2;</code>
     * @return Whether the part field is set.
     */
    boolean hasPart();
    /**
     * <code>.party.Part part = 2;</code>
     * @return The part.
     */
    net.runelite.client.party.Party.Part getPart();

    /**
     * <code>.party.Data data = 3;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>.party.Data data = 3;</code>
     * @return The data.
     */
    net.runelite.client.party.Party.Data getData();

    public net.runelite.client.party.Party.C2S.MsgCase getMsgCase();
  }
  /**
   * Protobuf type {@code party.C2S}
   */
  public  static final class C2S extends
      com.google.protobuf.GeneratedMessageLite<
          C2S, C2S.Builder> implements
      // @@protoc_insertion_point(message_implements:party.C2S)
      C2SOrBuilder {
    private C2S() {
    }
    private int msgCase_ = 0;
    private java.lang.Object msg_;
    public enum MsgCase {
      JOIN(1),
      PART(2),
      DATA(3),
      MSG_NOT_SET(0);
      private final int value;
      private MsgCase(int value) {
        this.value = value;
      }
      /**
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static MsgCase valueOf(int value) {
        return forNumber(value);
      }

      public static MsgCase forNumber(int value) {
        switch (value) {
          case 1: return JOIN;
          case 2: return PART;
          case 3: return DATA;
          case 0: return MSG_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    @java.lang.Override
    public MsgCase
    getMsgCase() {
      return MsgCase.forNumber(
          msgCase_);
    }

    private void clearMsg() {
      msgCase_ = 0;
      msg_ = null;
    }

    public static final int JOIN_FIELD_NUMBER = 1;
    /**
     * <code>.party.Join join = 1;</code>
     */
    @java.lang.Override
    public boolean hasJoin() {
      return msgCase_ == 1;
    }
    /**
     * <code>.party.Join join = 1;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.Join getJoin() {
      if (msgCase_ == 1) {
         return (net.runelite.client.party.Party.Join) msg_;
      }
      return net.runelite.client.party.Party.Join.getDefaultInstance();
    }
    /**
     * <code>.party.Join join = 1;</code>
     */
    private void setJoin(net.runelite.client.party.Party.Join value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 1;
    }
    /**
     * <code>.party.Join join = 1;</code>
     */
    private void mergeJoin(net.runelite.client.party.Party.Join value) {
      value.getClass();
  if (msgCase_ == 1 &&
          msg_ != net.runelite.client.party.Party.Join.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.Join.newBuilder((net.runelite.client.party.Party.Join) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 1;
    }
    /**
     * <code>.party.Join join = 1;</code>
     */
    private void clearJoin() {
      if (msgCase_ == 1) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static final int PART_FIELD_NUMBER = 2;
    /**
     * <code>.party.Part part = 2;</code>
     */
    @java.lang.Override
    public boolean hasPart() {
      return msgCase_ == 2;
    }
    /**
     * <code>.party.Part part = 2;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.Part getPart() {
      if (msgCase_ == 2) {
         return (net.runelite.client.party.Party.Part) msg_;
      }
      return net.runelite.client.party.Party.Part.getDefaultInstance();
    }
    /**
     * <code>.party.Part part = 2;</code>
     */
    private void setPart(net.runelite.client.party.Party.Part value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 2;
    }
    /**
     * <code>.party.Part part = 2;</code>
     */
    private void mergePart(net.runelite.client.party.Party.Part value) {
      value.getClass();
  if (msgCase_ == 2 &&
          msg_ != net.runelite.client.party.Party.Part.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.Part.newBuilder((net.runelite.client.party.Party.Part) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 2;
    }
    /**
     * <code>.party.Part part = 2;</code>
     */
    private void clearPart() {
      if (msgCase_ == 2) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static final int DATA_FIELD_NUMBER = 3;
    /**
     * <code>.party.Data data = 3;</code>
     */
    @java.lang.Override
    public boolean hasData() {
      return msgCase_ == 3;
    }
    /**
     * <code>.party.Data data = 3;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.Data getData() {
      if (msgCase_ == 3) {
         return (net.runelite.client.party.Party.Data) msg_;
      }
      return net.runelite.client.party.Party.Data.getDefaultInstance();
    }
    /**
     * <code>.party.Data data = 3;</code>
     */
    private void setData(net.runelite.client.party.Party.Data value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 3;
    }
    /**
     * <code>.party.Data data = 3;</code>
     */
    private void mergeData(net.runelite.client.party.Party.Data value) {
      value.getClass();
  if (msgCase_ == 3 &&
          msg_ != net.runelite.client.party.Party.Data.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.Data.newBuilder((net.runelite.client.party.Party.Data) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 3;
    }
    /**
     * <code>.party.Data data = 3;</code>
     */
    private void clearData() {
      if (msgCase_ == 3) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static net.runelite.client.party.Party.C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.C2S prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.C2S, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.C2S)
        net.runelite.client.party.Party.C2SOrBuilder {
      // Construct using net.runelite.client.party.Party.C2S.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }

      @java.lang.Override
      public MsgCase
          getMsgCase() {
        return instance.getMsgCase();
      }

      public Builder clearMsg() {
        copyOnWrite();
        instance.clearMsg();
        return this;
      }


      /**
       * <code>.party.Join join = 1;</code>
       */
      @java.lang.Override
      public boolean hasJoin() {
        return instance.hasJoin();
      }
      /**
       * <code>.party.Join join = 1;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.Join getJoin() {
        return instance.getJoin();
      }
      /**
       * <code>.party.Join join = 1;</code>
       */
      public Builder setJoin(net.runelite.client.party.Party.Join value) {
        copyOnWrite();
        instance.setJoin(value);
        return this;
      }
      /**
       * <code>.party.Join join = 1;</code>
       */
      public Builder setJoin(
          net.runelite.client.party.Party.Join.Builder builderForValue) {
        copyOnWrite();
        instance.setJoin(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.Join join = 1;</code>
       */
      public Builder mergeJoin(net.runelite.client.party.Party.Join value) {
        copyOnWrite();
        instance.mergeJoin(value);
        return this;
      }
      /**
       * <code>.party.Join join = 1;</code>
       */
      public Builder clearJoin() {
        copyOnWrite();
        instance.clearJoin();
        return this;
      }

      /**
       * <code>.party.Part part = 2;</code>
       */
      @java.lang.Override
      public boolean hasPart() {
        return instance.hasPart();
      }
      /**
       * <code>.party.Part part = 2;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.Part getPart() {
        return instance.getPart();
      }
      /**
       * <code>.party.Part part = 2;</code>
       */
      public Builder setPart(net.runelite.client.party.Party.Part value) {
        copyOnWrite();
        instance.setPart(value);
        return this;
      }
      /**
       * <code>.party.Part part = 2;</code>
       */
      public Builder setPart(
          net.runelite.client.party.Party.Part.Builder builderForValue) {
        copyOnWrite();
        instance.setPart(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.Part part = 2;</code>
       */
      public Builder mergePart(net.runelite.client.party.Party.Part value) {
        copyOnWrite();
        instance.mergePart(value);
        return this;
      }
      /**
       * <code>.party.Part part = 2;</code>
       */
      public Builder clearPart() {
        copyOnWrite();
        instance.clearPart();
        return this;
      }

      /**
       * <code>.party.Data data = 3;</code>
       */
      @java.lang.Override
      public boolean hasData() {
        return instance.hasData();
      }
      /**
       * <code>.party.Data data = 3;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.Data getData() {
        return instance.getData();
      }
      /**
       * <code>.party.Data data = 3;</code>
       */
      public Builder setData(net.runelite.client.party.Party.Data value) {
        copyOnWrite();
        instance.setData(value);
        return this;
      }
      /**
       * <code>.party.Data data = 3;</code>
       */
      public Builder setData(
          net.runelite.client.party.Party.Data.Builder builderForValue) {
        copyOnWrite();
        instance.setData(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.Data data = 3;</code>
       */
      public Builder mergeData(net.runelite.client.party.Party.Data value) {
        copyOnWrite();
        instance.mergeData(value);
        return this;
      }
      /**
       * <code>.party.Data data = 3;</code>
       */
      public Builder clearData() {
        copyOnWrite();
        instance.clearData();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.C2S)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.C2S();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "msg_",
              "msgCase_",
              net.runelite.client.party.Party.Join.class,
              net.runelite.client.party.Party.Part.class,
              net.runelite.client.party.Party.Data.class,
            };
            java.lang.String info =
                "\u0000\u0003\u0001\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001<\u0000\u0002<" +
                "\u0000\u0003<\u0000";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.C2S> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.C2S.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.C2S>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.C2S)
    private static final net.runelite.client.party.Party.C2S DEFAULT_INSTANCE;
    static {
      C2S defaultInstance = new C2S();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        C2S.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<C2S> PARSER;

    public static com.google.protobuf.Parser<C2S> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface UserJoinOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.UserJoin)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    long getPartyId();

    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    long getMemberId();
  }
  /**
   * <pre>
   * s-&gt;c
   * </pre>
   *
   * Protobuf type {@code party.UserJoin}
   */
  public  static final class UserJoin extends
      com.google.protobuf.GeneratedMessageLite<
          UserJoin, UserJoin.Builder> implements
      // @@protoc_insertion_point(message_implements:party.UserJoin)
      UserJoinOrBuilder {
    private UserJoin() {
    }
    public static final int PARTYID_FIELD_NUMBER = 1;
    private long partyId_;
    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    @java.lang.Override
    public long getPartyId() {
      return partyId_;
    }
    /**
     * <code>int64 partyId = 1;</code>
     * @param value The partyId to set.
     */
    private void setPartyId(long value) {
      
      partyId_ = value;
    }
    /**
     * <code>int64 partyId = 1;</code>
     */
    private void clearPartyId() {
      
      partyId_ = 0L;
    }

    public static final int MEMBERID_FIELD_NUMBER = 2;
    private long memberId_;
    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }
    /**
     * <code>int64 memberId = 2;</code>
     * @param value The memberId to set.
     */
    private void setMemberId(long value) {
      
      memberId_ = value;
    }
    /**
     * <code>int64 memberId = 2;</code>
     */
    private void clearMemberId() {
      
      memberId_ = 0L;
    }

    public static net.runelite.client.party.Party.UserJoin parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserJoin parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserJoin parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserJoin parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.UserJoin prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * <pre>
     * s-&gt;c
     * </pre>
     *
     * Protobuf type {@code party.UserJoin}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.UserJoin, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.UserJoin)
        net.runelite.client.party.Party.UserJoinOrBuilder {
      // Construct using net.runelite.client.party.Party.UserJoin.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>int64 partyId = 1;</code>
       * @return The partyId.
       */
      @java.lang.Override
      public long getPartyId() {
        return instance.getPartyId();
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @param value The partyId to set.
       * @return This builder for chaining.
       */
      public Builder setPartyId(long value) {
        copyOnWrite();
        instance.setPartyId(value);
        return this;
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartyId() {
        copyOnWrite();
        instance.clearPartyId();
        return this;
      }

      /**
       * <code>int64 memberId = 2;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return instance.getMemberId();
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        copyOnWrite();
        instance.setMemberId(value);
        return this;
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        copyOnWrite();
        instance.clearMemberId();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.UserJoin)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.UserJoin();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "partyId_",
              "memberId_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0002\u0002\u0002" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.UserJoin> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.UserJoin.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.UserJoin>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.UserJoin)
    private static final net.runelite.client.party.Party.UserJoin DEFAULT_INSTANCE;
    static {
      UserJoin defaultInstance = new UserJoin();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        UserJoin.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.UserJoin getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<UserJoin> PARSER;

    public static com.google.protobuf.Parser<UserJoin> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface UserPartOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.UserPart)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    long getPartyId();

    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    long getMemberId();
  }
  /**
   * Protobuf type {@code party.UserPart}
   */
  public  static final class UserPart extends
      com.google.protobuf.GeneratedMessageLite<
          UserPart, UserPart.Builder> implements
      // @@protoc_insertion_point(message_implements:party.UserPart)
      UserPartOrBuilder {
    private UserPart() {
    }
    public static final int PARTYID_FIELD_NUMBER = 1;
    private long partyId_;
    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    @java.lang.Override
    public long getPartyId() {
      return partyId_;
    }
    /**
     * <code>int64 partyId = 1;</code>
     * @param value The partyId to set.
     */
    private void setPartyId(long value) {
      
      partyId_ = value;
    }
    /**
     * <code>int64 partyId = 1;</code>
     */
    private void clearPartyId() {
      
      partyId_ = 0L;
    }

    public static final int MEMBERID_FIELD_NUMBER = 2;
    private long memberId_;
    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }
    /**
     * <code>int64 memberId = 2;</code>
     * @param value The memberId to set.
     */
    private void setMemberId(long value) {
      
      memberId_ = value;
    }
    /**
     * <code>int64 memberId = 2;</code>
     */
    private void clearMemberId() {
      
      memberId_ = 0L;
    }

    public static net.runelite.client.party.Party.UserPart parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserPart parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserPart parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.UserPart parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.UserPart prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.UserPart}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.UserPart, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.UserPart)
        net.runelite.client.party.Party.UserPartOrBuilder {
      // Construct using net.runelite.client.party.Party.UserPart.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>int64 partyId = 1;</code>
       * @return The partyId.
       */
      @java.lang.Override
      public long getPartyId() {
        return instance.getPartyId();
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @param value The partyId to set.
       * @return This builder for chaining.
       */
      public Builder setPartyId(long value) {
        copyOnWrite();
        instance.setPartyId(value);
        return this;
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartyId() {
        copyOnWrite();
        instance.clearPartyId();
        return this;
      }

      /**
       * <code>int64 memberId = 2;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return instance.getMemberId();
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        copyOnWrite();
        instance.setMemberId(value);
        return this;
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        copyOnWrite();
        instance.clearMemberId();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.UserPart)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.UserPart();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "partyId_",
              "memberId_",
            };
            java.lang.String info =
                "\u0000\u0002\u0000\u0000\u0001\u0002\u0002\u0000\u0000\u0000\u0001\u0002\u0002\u0002" +
                "";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.UserPart> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.UserPart.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.UserPart>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.UserPart)
    private static final net.runelite.client.party.Party.UserPart DEFAULT_INSTANCE;
    static {
      UserPart defaultInstance = new UserPart();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        UserPart.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.UserPart getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<UserPart> PARSER;

    public static com.google.protobuf.Parser<UserPart> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface PartyDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.PartyData)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    long getPartyId();

    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    long getMemberId();

    /**
     * <code>string type = 4;</code>
     * @return The type.
     */
    java.lang.String getType();
    /**
     * <code>string type = 4;</code>
     * @return The bytes for type.
     */
    com.google.protobuf.ByteString
        getTypeBytes();

    /**
     * <code>bytes data = 3;</code>
     * @return The data.
     */
    com.google.protobuf.ByteString getData();
  }
  /**
   * Protobuf type {@code party.PartyData}
   */
  public  static final class PartyData extends
      com.google.protobuf.GeneratedMessageLite<
          PartyData, PartyData.Builder> implements
      // @@protoc_insertion_point(message_implements:party.PartyData)
      PartyDataOrBuilder {
    private PartyData() {
      type_ = "";
      data_ = com.google.protobuf.ByteString.EMPTY;
    }
    public static final int PARTYID_FIELD_NUMBER = 1;
    private long partyId_;
    /**
     * <code>int64 partyId = 1;</code>
     * @return The partyId.
     */
    @java.lang.Override
    public long getPartyId() {
      return partyId_;
    }
    /**
     * <code>int64 partyId = 1;</code>
     * @param value The partyId to set.
     */
    private void setPartyId(long value) {
      
      partyId_ = value;
    }
    /**
     * <code>int64 partyId = 1;</code>
     */
    private void clearPartyId() {
      
      partyId_ = 0L;
    }

    public static final int MEMBERID_FIELD_NUMBER = 2;
    private long memberId_;
    /**
     * <code>int64 memberId = 2;</code>
     * @return The memberId.
     */
    @java.lang.Override
    public long getMemberId() {
      return memberId_;
    }
    /**
     * <code>int64 memberId = 2;</code>
     * @param value The memberId to set.
     */
    private void setMemberId(long value) {
      
      memberId_ = value;
    }
    /**
     * <code>int64 memberId = 2;</code>
     */
    private void clearMemberId() {
      
      memberId_ = 0L;
    }

    public static final int TYPE_FIELD_NUMBER = 4;
    private java.lang.String type_;
    /**
     * <code>string type = 4;</code>
     * @return The type.
     */
    @java.lang.Override
    public java.lang.String getType() {
      return type_;
    }
    /**
     * <code>string type = 4;</code>
     * @return The bytes for type.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTypeBytes() {
      return com.google.protobuf.ByteString.copyFromUtf8(type_);
    }
    /**
     * <code>string type = 4;</code>
     * @param value The type to set.
     */
    private void setType(
        java.lang.String value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      type_ = value;
    }
    /**
     * <code>string type = 4;</code>
     */
    private void clearType() {
      
      type_ = getDefaultInstance().getType();
    }
    /**
     * <code>string type = 4;</code>
     * @param value The bytes for type to set.
     */
    private void setTypeBytes(
        com.google.protobuf.ByteString value) {
      checkByteStringIsUtf8(value);
      type_ = value.toStringUtf8();
      
    }

    public static final int DATA_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString data_;
    /**
     * <code>bytes data = 3;</code>
     * @return The data.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getData() {
      return data_;
    }
    /**
     * <code>bytes data = 3;</code>
     * @param value The data to set.
     */
    private void setData(com.google.protobuf.ByteString value) {
      java.lang.Class<?> valueClass = value.getClass();
  
      data_ = value;
    }
    /**
     * <code>bytes data = 3;</code>
     */
    private void clearData() {
      
      data_ = getDefaultInstance().getData();
    }

    public static net.runelite.client.party.Party.PartyData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.PartyData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.PartyData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.PartyData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.PartyData prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.PartyData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.PartyData, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.PartyData)
        net.runelite.client.party.Party.PartyDataOrBuilder {
      // Construct using net.runelite.client.party.Party.PartyData.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }


      /**
       * <code>int64 partyId = 1;</code>
       * @return The partyId.
       */
      @java.lang.Override
      public long getPartyId() {
        return instance.getPartyId();
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @param value The partyId to set.
       * @return This builder for chaining.
       */
      public Builder setPartyId(long value) {
        copyOnWrite();
        instance.setPartyId(value);
        return this;
      }
      /**
       * <code>int64 partyId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartyId() {
        copyOnWrite();
        instance.clearPartyId();
        return this;
      }

      /**
       * <code>int64 memberId = 2;</code>
       * @return The memberId.
       */
      @java.lang.Override
      public long getMemberId() {
        return instance.getMemberId();
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @param value The memberId to set.
       * @return This builder for chaining.
       */
      public Builder setMemberId(long value) {
        copyOnWrite();
        instance.setMemberId(value);
        return this;
      }
      /**
       * <code>int64 memberId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMemberId() {
        copyOnWrite();
        instance.clearMemberId();
        return this;
      }

      /**
       * <code>string type = 4;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        return instance.getType();
      }
      /**
       * <code>string type = 4;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        return instance.getTypeBytes();
      }
      /**
       * <code>string type = 4;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(
          java.lang.String value) {
        copyOnWrite();
        instance.setType(value);
        return this;
      }
      /**
       * <code>string type = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        copyOnWrite();
        instance.clearType();
        return this;
      }
      /**
       * <code>string type = 4;</code>
       * @param value The bytes for type to set.
       * @return This builder for chaining.
       */
      public Builder setTypeBytes(
          com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setTypeBytes(value);
        return this;
      }

      /**
       * <code>bytes data = 3;</code>
       * @return The data.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getData() {
        return instance.getData();
      }
      /**
       * <code>bytes data = 3;</code>
       * @param value The data to set.
       * @return This builder for chaining.
       */
      public Builder setData(com.google.protobuf.ByteString value) {
        copyOnWrite();
        instance.setData(value);
        return this;
      }
      /**
       * <code>bytes data = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearData() {
        copyOnWrite();
        instance.clearData();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.PartyData)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.PartyData();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "partyId_",
              "memberId_",
              "data_",
              "type_",
            };
            java.lang.String info =
                "\u0000\u0004\u0000\u0000\u0001\u0004\u0004\u0000\u0000\u0000\u0001\u0002\u0002\u0002" +
                "\u0003\n\u0004\u0208";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.PartyData> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.PartyData.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.PartyData>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.PartyData)
    private static final net.runelite.client.party.Party.PartyData DEFAULT_INSTANCE;
    static {
      PartyData defaultInstance = new PartyData();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        PartyData.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.PartyData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<PartyData> PARSER;

    public static com.google.protobuf.Parser<PartyData> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }

  public interface S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:party.S2C)
      com.google.protobuf.MessageLiteOrBuilder {

    /**
     * <code>.party.UserJoin join = 1;</code>
     * @return Whether the join field is set.
     */
    boolean hasJoin();
    /**
     * <code>.party.UserJoin join = 1;</code>
     * @return The join.
     */
    net.runelite.client.party.Party.UserJoin getJoin();

    /**
     * <code>.party.UserPart part = 2;</code>
     * @return Whether the part field is set.
     */
    boolean hasPart();
    /**
     * <code>.party.UserPart part = 2;</code>
     * @return The part.
     */
    net.runelite.client.party.Party.UserPart getPart();

    /**
     * <code>.party.PartyData data = 3;</code>
     * @return Whether the data field is set.
     */
    boolean hasData();
    /**
     * <code>.party.PartyData data = 3;</code>
     * @return The data.
     */
    net.runelite.client.party.Party.PartyData getData();

    public net.runelite.client.party.Party.S2C.MsgCase getMsgCase();
  }
  /**
   * Protobuf type {@code party.S2C}
   */
  public  static final class S2C extends
      com.google.protobuf.GeneratedMessageLite<
          S2C, S2C.Builder> implements
      // @@protoc_insertion_point(message_implements:party.S2C)
      S2COrBuilder {
    private S2C() {
    }
    private int msgCase_ = 0;
    private java.lang.Object msg_;
    public enum MsgCase {
      JOIN(1),
      PART(2),
      DATA(3),
      MSG_NOT_SET(0);
      private final int value;
      private MsgCase(int value) {
        this.value = value;
      }
      /**
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static MsgCase valueOf(int value) {
        return forNumber(value);
      }

      public static MsgCase forNumber(int value) {
        switch (value) {
          case 1: return JOIN;
          case 2: return PART;
          case 3: return DATA;
          case 0: return MSG_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    @java.lang.Override
    public MsgCase
    getMsgCase() {
      return MsgCase.forNumber(
          msgCase_);
    }

    private void clearMsg() {
      msgCase_ = 0;
      msg_ = null;
    }

    public static final int JOIN_FIELD_NUMBER = 1;
    /**
     * <code>.party.UserJoin join = 1;</code>
     */
    @java.lang.Override
    public boolean hasJoin() {
      return msgCase_ == 1;
    }
    /**
     * <code>.party.UserJoin join = 1;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.UserJoin getJoin() {
      if (msgCase_ == 1) {
         return (net.runelite.client.party.Party.UserJoin) msg_;
      }
      return net.runelite.client.party.Party.UserJoin.getDefaultInstance();
    }
    /**
     * <code>.party.UserJoin join = 1;</code>
     */
    private void setJoin(net.runelite.client.party.Party.UserJoin value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 1;
    }
    /**
     * <code>.party.UserJoin join = 1;</code>
     */
    private void mergeJoin(net.runelite.client.party.Party.UserJoin value) {
      value.getClass();
  if (msgCase_ == 1 &&
          msg_ != net.runelite.client.party.Party.UserJoin.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.UserJoin.newBuilder((net.runelite.client.party.Party.UserJoin) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 1;
    }
    /**
     * <code>.party.UserJoin join = 1;</code>
     */
    private void clearJoin() {
      if (msgCase_ == 1) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static final int PART_FIELD_NUMBER = 2;
    /**
     * <code>.party.UserPart part = 2;</code>
     */
    @java.lang.Override
    public boolean hasPart() {
      return msgCase_ == 2;
    }
    /**
     * <code>.party.UserPart part = 2;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.UserPart getPart() {
      if (msgCase_ == 2) {
         return (net.runelite.client.party.Party.UserPart) msg_;
      }
      return net.runelite.client.party.Party.UserPart.getDefaultInstance();
    }
    /**
     * <code>.party.UserPart part = 2;</code>
     */
    private void setPart(net.runelite.client.party.Party.UserPart value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 2;
    }
    /**
     * <code>.party.UserPart part = 2;</code>
     */
    private void mergePart(net.runelite.client.party.Party.UserPart value) {
      value.getClass();
  if (msgCase_ == 2 &&
          msg_ != net.runelite.client.party.Party.UserPart.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.UserPart.newBuilder((net.runelite.client.party.Party.UserPart) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 2;
    }
    /**
     * <code>.party.UserPart part = 2;</code>
     */
    private void clearPart() {
      if (msgCase_ == 2) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static final int DATA_FIELD_NUMBER = 3;
    /**
     * <code>.party.PartyData data = 3;</code>
     */
    @java.lang.Override
    public boolean hasData() {
      return msgCase_ == 3;
    }
    /**
     * <code>.party.PartyData data = 3;</code>
     */
    @java.lang.Override
    public net.runelite.client.party.Party.PartyData getData() {
      if (msgCase_ == 3) {
         return (net.runelite.client.party.Party.PartyData) msg_;
      }
      return net.runelite.client.party.Party.PartyData.getDefaultInstance();
    }
    /**
     * <code>.party.PartyData data = 3;</code>
     */
    private void setData(net.runelite.client.party.Party.PartyData value) {
      value.getClass();
  msg_ = value;
      msgCase_ = 3;
    }
    /**
     * <code>.party.PartyData data = 3;</code>
     */
    private void mergeData(net.runelite.client.party.Party.PartyData value) {
      value.getClass();
  if (msgCase_ == 3 &&
          msg_ != net.runelite.client.party.Party.PartyData.getDefaultInstance()) {
        msg_ = net.runelite.client.party.Party.PartyData.newBuilder((net.runelite.client.party.Party.PartyData) msg_)
            .mergeFrom(value).buildPartial();
      } else {
        msg_ = value;
      }
      msgCase_ = 3;
    }
    /**
     * <code>.party.PartyData data = 3;</code>
     */
    private void clearData() {
      if (msgCase_ == 3) {
        msgCase_ = 0;
        msg_ = null;
      }
    }

    public static net.runelite.client.party.Party.S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, data, extensionRegistry);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return parseDelimitedFrom(DEFAULT_INSTANCE, input, extensionRegistry);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input);
    }
    public static net.runelite.client.party.Party.S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageLite.parseFrom(
          DEFAULT_INSTANCE, input, extensionRegistry);
    }

    public static Builder newBuilder() {
      return (Builder) DEFAULT_INSTANCE.createBuilder();
    }
    public static Builder newBuilder(net.runelite.client.party.Party.S2C prototype) {
      return (Builder) DEFAULT_INSTANCE.createBuilder(prototype);
    }

    /**
     * Protobuf type {@code party.S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageLite.Builder<
          net.runelite.client.party.Party.S2C, Builder> implements
        // @@protoc_insertion_point(builder_implements:party.S2C)
        net.runelite.client.party.Party.S2COrBuilder {
      // Construct using net.runelite.client.party.Party.S2C.newBuilder()
      private Builder() {
        super(DEFAULT_INSTANCE);
      }

      @java.lang.Override
      public MsgCase
          getMsgCase() {
        return instance.getMsgCase();
      }

      public Builder clearMsg() {
        copyOnWrite();
        instance.clearMsg();
        return this;
      }


      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      @java.lang.Override
      public boolean hasJoin() {
        return instance.hasJoin();
      }
      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.UserJoin getJoin() {
        return instance.getJoin();
      }
      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      public Builder setJoin(net.runelite.client.party.Party.UserJoin value) {
        copyOnWrite();
        instance.setJoin(value);
        return this;
      }
      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      public Builder setJoin(
          net.runelite.client.party.Party.UserJoin.Builder builderForValue) {
        copyOnWrite();
        instance.setJoin(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      public Builder mergeJoin(net.runelite.client.party.Party.UserJoin value) {
        copyOnWrite();
        instance.mergeJoin(value);
        return this;
      }
      /**
       * <code>.party.UserJoin join = 1;</code>
       */
      public Builder clearJoin() {
        copyOnWrite();
        instance.clearJoin();
        return this;
      }

      /**
       * <code>.party.UserPart part = 2;</code>
       */
      @java.lang.Override
      public boolean hasPart() {
        return instance.hasPart();
      }
      /**
       * <code>.party.UserPart part = 2;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.UserPart getPart() {
        return instance.getPart();
      }
      /**
       * <code>.party.UserPart part = 2;</code>
       */
      public Builder setPart(net.runelite.client.party.Party.UserPart value) {
        copyOnWrite();
        instance.setPart(value);
        return this;
      }
      /**
       * <code>.party.UserPart part = 2;</code>
       */
      public Builder setPart(
          net.runelite.client.party.Party.UserPart.Builder builderForValue) {
        copyOnWrite();
        instance.setPart(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.UserPart part = 2;</code>
       */
      public Builder mergePart(net.runelite.client.party.Party.UserPart value) {
        copyOnWrite();
        instance.mergePart(value);
        return this;
      }
      /**
       * <code>.party.UserPart part = 2;</code>
       */
      public Builder clearPart() {
        copyOnWrite();
        instance.clearPart();
        return this;
      }

      /**
       * <code>.party.PartyData data = 3;</code>
       */
      @java.lang.Override
      public boolean hasData() {
        return instance.hasData();
      }
      /**
       * <code>.party.PartyData data = 3;</code>
       */
      @java.lang.Override
      public net.runelite.client.party.Party.PartyData getData() {
        return instance.getData();
      }
      /**
       * <code>.party.PartyData data = 3;</code>
       */
      public Builder setData(net.runelite.client.party.Party.PartyData value) {
        copyOnWrite();
        instance.setData(value);
        return this;
      }
      /**
       * <code>.party.PartyData data = 3;</code>
       */
      public Builder setData(
          net.runelite.client.party.Party.PartyData.Builder builderForValue) {
        copyOnWrite();
        instance.setData(builderForValue.build());
        return this;
      }
      /**
       * <code>.party.PartyData data = 3;</code>
       */
      public Builder mergeData(net.runelite.client.party.Party.PartyData value) {
        copyOnWrite();
        instance.mergeData(value);
        return this;
      }
      /**
       * <code>.party.PartyData data = 3;</code>
       */
      public Builder clearData() {
        copyOnWrite();
        instance.clearData();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:party.S2C)
    }
    @java.lang.Override
    @java.lang.SuppressWarnings({"unchecked", "fallthrough"})
    protected final java.lang.Object dynamicMethod(
        com.google.protobuf.GeneratedMessageLite.MethodToInvoke method,
        java.lang.Object arg0, java.lang.Object arg1) {
      switch (method) {
        case NEW_MUTABLE_INSTANCE: {
          return new net.runelite.client.party.Party.S2C();
        }
        case NEW_BUILDER: {
          return new Builder();
        }
        case BUILD_MESSAGE_INFO: {
            java.lang.Object[] objects = new java.lang.Object[] {
              "msg_",
              "msgCase_",
              net.runelite.client.party.Party.UserJoin.class,
              net.runelite.client.party.Party.UserPart.class,
              net.runelite.client.party.Party.PartyData.class,
            };
            java.lang.String info =
                "\u0000\u0003\u0001\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001<\u0000\u0002<" +
                "\u0000\u0003<\u0000";
            return newMessageInfo(DEFAULT_INSTANCE, info, objects);
        }
        // fall through
        case GET_DEFAULT_INSTANCE: {
          return DEFAULT_INSTANCE;
        }
        case GET_PARSER: {
          com.google.protobuf.Parser<net.runelite.client.party.Party.S2C> parser = PARSER;
          if (parser == null) {
            synchronized (net.runelite.client.party.Party.S2C.class) {
              parser = PARSER;
              if (parser == null) {
                parser =
                    new DefaultInstanceBasedParser<net.runelite.client.party.Party.S2C>(
                        DEFAULT_INSTANCE);
                PARSER = parser;
              }
            }
          }
          return parser;
      }
      case GET_MEMOIZED_IS_INITIALIZED: {
        return (byte) 1;
      }
      case SET_MEMOIZED_IS_INITIALIZED: {
        return null;
      }
      }
      throw new UnsupportedOperationException();
    }


    // @@protoc_insertion_point(class_scope:party.S2C)
    private static final net.runelite.client.party.Party.S2C DEFAULT_INSTANCE;
    static {
      S2C defaultInstance = new S2C();
      // New instances are implicitly immutable so no need to make
      // immutable.
      DEFAULT_INSTANCE = defaultInstance;
      com.google.protobuf.GeneratedMessageLite.registerDefaultInstance(
        S2C.class, defaultInstance);
    }

    public static net.runelite.client.party.Party.S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static volatile com.google.protobuf.Parser<S2C> PARSER;

    public static com.google.protobuf.Parser<S2C> parser() {
      return DEFAULT_INSTANCE.getParserForType();
    }
  }


  static {
  }

  // @@protoc_insertion_point(outer_class_scope)
}
