package net.runelite.client;

public class TickConstants {

//FARMING TICKS - Types of Seeds planted in patches
    public static int WEEDS = 2;
    public static int SCARECROW = 2;
    // Allotment crops
    public static int POTATO = 3;
    public static int ONION = 3;
    public static int CABBAGE = 3;
    public static int TOMATO = 3;
    public static int SWEETCORN = 3;
    public static int STRAWBERRY = 3;
    public static int WATERMELON = 3;
    public static int SNAPE_GRASS = 3;
    // Flower crops
    public static int MARIGOLD = 2;
    public static int ROSEMARY = 2;
    public static int NASTURTIUM = 2;
    public static int WOAD = 2;
    public static int LIMPWURT = 2;
    public static int WHITE_LILY = 2;
    // Bush crops
    public static int REDBERRIES = 5;
    public static int CADAVABERRIES = 5;
    public static int DWELLBERRIES = 5;
    public static int JANGERBERRIES = 5;
    public static int WHITEBERRIES = 5;
    public static int POISON_IVY = 5;
    // Hop crops
    public static int BARLEY = 3;
    public static int HAMMERSTONE = 3;
    public static int ASGARNIAN = 3;
    public static int JUTE = 3;
    public static int YANILLIAN = 3;
    public static int KRANDORIAN = 3;
    public static int WILDBLOOD = 3;
    // Herb crops
    public static int GUAM = 2;
    public static int MARRENTILL = 2;
    public static int TARROMIN = 2;
    public static int HARRALANDER = 2;
    public static int RANARR = 2;
    public static int TOADFLAX = 2;
    public static int IRIT = 2;
    public static int AVANTOE = 2;
    public static int KWUARM = 2;
    public static int SNAPDRAGON = 2;
    public static int CADANTINE = 2;
    public static int LANTADYME = 2;
    public static int DWARF_WEED = 2;
    public static int TORSTOL = 2;
    public static int GOUTWEED = 2;
    public static int ANYHERB = 2;
    // Tree crops
    public static int OAK = 10;
    public static int WILLOW = 10;
    public static int MAPLE = 10;
    public static int YEW = 10;
    public static int MAGIC = 10;
    // Fruit tree crops
    public static int APPLE = 40;
    public static int BANANA = 40;
    public static int ORANGE = 40;
    public static int CURRY = 40;
    public static int PINEAPPLE = 40;
    public static int PAPAYA = 40;
    public static int PALM = 40;
    public static int DRAGONFRUIT = 40;
    // Cactus
    public static int CACTUS = 20;
    public static int POTATO_CACTUS = 20;
    // Hardwood
    public static int TEAK = 160;
    public static int MAHOGANY = 160;
    // Anima
    public static int ATTAS = 160;
    public static int IASOR = 160;
    public static int KRONOS = 160;
    // Special crops
    public static int SEAWEED = 3;
    public static int GRAPE = 2;
    public static int MUSHROOM = 11;
    public static int BELLADONNA = 20;
    public static int CALQUAT = 41;
    public static int SPIRIT_TREE = 80;
    public static int CELASTRUS = 41;
    public static int REDWOOD = 160;
    public static int HESPORI = 160;
    public static int CRYSTAL_TREE = 20;
    // Compost bins
    public static int EMPTY_COMPOST_BIN = 0;
    public static int COMPOST = 9;
    public static int SUPERCOMPOST = 9;
    public static int ULTRACOMPOST = 9;
    public static int ROTTEN_TOMATO = 9;
    public static int EMPTY_BIG_COMPOST_BIN = 0;
    public static int BIG_COMPOST = 9;
    public static int BIG_SUPERCOMPOST = 9;
    public static int BIG_ULTRACOMPOST = 9;
    public static int BIG_ROTTEN_TOMATO = 9;

//WOODCUTTING TICKS - Used for woodcutting plugin (trees)
    public static int REGULAR_TREE = 6;
    public static int OAK_TREE = 13;
    public static int WILLOW_TREE = 16;
    public static int MAPLE_TREE = 21;
    public static int TEAK_TREE = 18;
    public static int MAHOGANY_TREE = 23;
    public static int YEW_TREE = 28;
    public static int MAGIC_TREE = 36;
    public static int REDWOOD_TREE = 76;

//MINING TICKS - Used for mining plugin (rocks)
    public static int TIN = 3;
    public static int COPPER = 3;
    public static int IRON = 4;
    public static int COAL = 13;
    public static int SILVER = 18;
    public static int SANDSTONE = 9;
    public static int GOLD = 18;
    public static int GRANITE = 9;
    public static int MITHRIL = 34;
    public static int ADAMANTITE = 68;
    public static int RUNITE = 151;
    public static int AMETHYST = 43;
    public static int ASH_VEIN = 50;
    public static int GEM_ROCK = 131;
    public static int URT_SALT = 16;
    public static int EFH_SALT = 16;
    public static int TE_SALT = 16;
    public static int BASALT = 16;
    public static int BARRONITE = 89;
    public static int MINERAL_VEIN = 100;


}
