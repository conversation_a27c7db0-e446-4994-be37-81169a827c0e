.id                 125
.int_stack_count    9
.string_stack_count 0
.int_var_count      16
.string_var_count   2
; callback "friendsChatSetText"
;   Fired just before the client pops the name off the stack
;     Modified by the friendnotes plugin to show the icon
; callback "friendsChatSetPosition"
;   Fired just before the client sets the position of "friend changed their name" icon
;     Modified by the friendnotes plugin to offset the name changed icon
   iload                  1
   iconst                 2
   iconst                 3
   iconst                 2
   sconst                 "Sort by name"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   invoke                 1669
   iload                  2
   iconst                 8
   iconst                 9
   iconst                 9
   sconst                 "Sort by last world change"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   invoke                 1669
   iload                  3
   iconst                 4
   iconst                 5
   iconst                 4
   sconst                 "Sort by world"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   invoke                 1669
   iload                  4
   iconst                 0
   iconst                 1
   iconst                 0
   sconst                 "Legacy sort"
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   invoke                 1669
   iload                  5
   cc_deleteall          
   iconst                 0
   istore                 9
   iconst                 0
   istore                 10
   sconst                 ""
   sstore                 0
   sconst                 ""
   sstore                 1
   iconst                 0
   istore                 11
   iconst                 0
   istore                 12
   iconst                 15
   istore                 13
   iconst                 -1
   istore                 14
   friend_count          
   istore                 15
   iload                  15
   iconst                 -2
   if_icmple              LABEL84
   jump                   LABEL105
LABEL84:
   get_varbit             8119
   iconst                 1
   if_icmpeq              LABEL88
   jump                   LABEL95
LABEL88:
   sconst                 "Loading friends list"
   sconst                 "<br>"
   sconst                 "Please wait..."
   join_string            3
   iload                  7
   if_settext            
   jump                   LABEL101
LABEL95:
   sconst                 "You must set a name"
   sconst                 "<br>"
   sconst                 "before using this."
   join_string            3
   iload                  7
   if_settext            
LABEL101:
   iconst                 1
   iload                  0
   if_sethide            
   jump                   LABEL484
LABEL105:
   iload                  15
   iconst                 -1
   if_icmpeq              LABEL109
   jump                   LABEL130
LABEL109:
   get_varbit             8119
   iconst                 1
   if_icmpeq              LABEL113
   jump                   LABEL120
LABEL113:
   sconst                 "Loading friends list"
   sconst                 "<br>"
   sconst                 "Please wait..."
   join_string            3
   iload                  7
   if_settext            
   jump                   LABEL126
LABEL120:
   sconst                 "You must set a name"
   sconst                 "<br>"
   sconst                 "before using this."
   join_string            3
   iload                  7
   if_settext            
LABEL126:
   iconst                 1
   iload                  0
   if_sethide            
   jump                   LABEL484
LABEL130:
   iload                  15
   iconst                 0
   if_icmpeq              LABEL134
   jump                   LABEL146
LABEL134:
   sconst                 "You may add friends by using the button below, or by "
   sconst                 "right-clicking"
   sconst                 "long pressing"
   invoke                 1971
   sconst                 " on a message from them and selecting to add them as a friend."
   join_string            3
   iload                  7
   if_settext            
   iconst                 1
   iload                  0
   if_sethide            
   jump                   LABEL484
LABEL146:
   invoke                 1972
   istore                 14
   iload                  14
   iconst                 1
   if_icmpeq              LABEL152
   jump                   LABEL157
LABEL152:
   iconst                 8
   iconst                 5
   iload                  13
   scale                 
   istore                 13
LABEL157:
   sconst                 ""
   iload                  7
   if_settext            
   iconst                 0
   iload                  0
   if_sethide            
   3628                  
   get_varc_int           183
   switch                
      1: LABEL167
      2: LABEL170
      3: LABEL175
      8: LABEL180
      9: LABEL185
      4: LABEL190
      5: LABEL210
   jump                   LABEL229
LABEL167:
   iconst                 0
   3629                  
   jump                   LABEL229
LABEL170:
   iconst                 1
   3633                  
   iconst                 1
   3630                  
   jump                   LABEL229
LABEL175:
   iconst                 1
   3633                  
   iconst                 0
   3630                  
   jump                   LABEL229
LABEL180:
   iconst                 1
   3633                  
   iconst                 1
   3632                  
   jump                   LABEL229
LABEL185:
   iconst                 1
   3633                  
   iconst                 0
   3632                  
   jump                   LABEL229
LABEL190:
   iconst                 1
   3633                  
   iconst                 1
   3636                  
   iconst                 1
   3631                  
   get_varc_int           205
   switch                
      3: LABEL201
      8: LABEL204
      9: LABEL207
   iconst                 1
   3630                  
   jump                   LABEL209
LABEL201:
   iconst                 0
   3630                  
   jump                   LABEL209
LABEL204:
   iconst                 1
   3632                  
   jump                   LABEL209
LABEL207:
   iconst                 0
   3632                  
LABEL209:
   jump                   LABEL229
LABEL210:
   iconst                 1
   3633                  
   iconst                 1
   3636                  
   iconst                 0
   3631                  
   get_varc_int           205
   switch                
      3: LABEL221
      8: LABEL224
      9: LABEL227
   iconst                 1
   3630                  
   jump                   LABEL229
LABEL221:
   iconst                 0
   3630                  
   jump                   LABEL229
LABEL224:
   iconst                 1
   3632                  
   jump                   LABEL229
LABEL227:
   iconst                 0
   3632                  
LABEL229:
   3639                  
LABEL230:
   iload                  9
   iload                  15
   if_icmplt              LABEL234
   jump                   LABEL476
LABEL234:
   iload                  9
   friend_getname        
   sstore                 1
   sstore                 0
   iload                  5
   iconst                 4
   iload                  10
   cc_create             
   iload                  10
   iconst                 1
   add                   
   istore                 10
   sload                  0
   sconst                 "friendsChatSetText"
   runelite_callback     
   cc_settext            
   iconst                 0
   iload                  13
   iconst                 1
   iconst                 0
   cc_setsize            
   iconst                 0
   iload                  12
   iconst                 1
   iconst                 0
   cc_setposition        
   iconst                 16777215
   cc_setcolour          
   iconst                 495
   cc_settextfont        
   iconst                 0
   iconst                 1
   iconst                 0
   cc_settextalign       
   iconst                 1
   cc_settextshadow      
   sconst                 "<col=ff9040>"
   sload                  0
   sconst                 "</col>"
   join_string            3
   cc_setopbase          
   iload                  9
   friend_getworld       
   istore                 11
   iload                  11
   iconst                 0
   if_icmpne              LABEL280
   jump                   LABEL287
LABEL280:
   iconst                 1
   sconst                 "Message"
   cc_setop              
   iconst                 2
   sconst                 ""
   cc_setop              
   jump                   LABEL293
LABEL287:
   iconst                 1
   sconst                 ""
   cc_setop              
   iconst                 2
   sconst                 "Message"
   cc_setop              
LABEL293:
   iconst                 3
   sconst                 "Delete"
   cc_setop              
   iload                  5
   iconst                 5
   iload                  10
   cc_create              1
   iload                  10
   iconst                 1
   add                   
   istore                 10
   iconst                 14
   iconst                 14
   iconst                 0
   iconst                 0
   cc_setsize             1
   sload                  0
   iconst                 190
   iconst                 495
   parawidth             
   iconst                 3
   add                   
   iload                  12
   iload                  13
   iconst                 14
   sub                   
   iconst                 2
   div                   
   add                   
   iconst                 0
   iconst                 0
   sconst                 "friendsChatSetPosition"
   runelite_callback     
   cc_setposition         1
   iconst                 1093
   cc_setgraphic          1
   iconst                 3355443
   cc_setgraphicshadow    1
   sload                  1
   string_length         
   iconst                 0
   if_icmpgt              LABEL334
   jump                   LABEL383
LABEL334:
   iload                  14
   iconst                 1
   if_icmpeq              LABEL338
   jump                   LABEL352
LABEL338:
   iconst                 10
   sconst                 "Reveal previous name"
   cc_setop              
   iconst                 126
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -2147483645
   cc_getid              
   cc_getid               1
   sload                  1
   sload                  0
   sconst                 "isIiiss"
   cc_setonop            
   jump                   LABEL380
LABEL352:
   sconst                 "Previous name:"
   sconst                 "<br>"
   sload                  1
   join_string            3
   sstore                 1
   iconst                 526
   iconst                 -2147483645
   iconst                 -2147483643
   iload                  8
   sload                  1
   iconst                 25
   iconst                 190
   sconst                 "IiIsii"
   cc_setonmouserepeat   
   iconst                 40
   iload                  8
   sconst                 "I"
   cc_setonmouseleave    
   iconst                 126
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -1
   iconst                 -1
   iconst                 -1
   sconst                 "null"
   sconst                 "null"
   sconst                 "isIiiss"
   cc_setonop            
LABEL380:
   iconst                 0
   cc_sethide             1
   jump                   LABEL399
LABEL383:
   iconst                 40
   iload                  8
   sconst                 "I"
   cc_setonmouseover     
   iconst                 1
   cc_sethide             1
   iconst                 126
   iconst                 -2147483644
   sconst                 "event_opbase"
   iconst                 -1
   iconst                 -1
   iconst                 -1
   sconst                 "null"
   sconst                 "null"
   sconst                 "isIiiss"
   cc_setonop            
LABEL399:
   iload                  5
   iconst                 4
   iload                  10
   cc_create             
   iload                  10
   iconst                 1
   add                   
   istore                 10
   iconst                 0
   iload                  13
   iconst                 1
   iconst                 0
   cc_setsize            
   iconst                 0
   iload                  12
   iconst                 1
   iconst                 0
   cc_setposition        
   iconst                 495
   cc_settextfont        
   iconst                 2
   iconst                 1
   iconst                 0
   cc_settextalign       
   iconst                 1
   cc_settextshadow      
   iload                  11
   iconst                 0
   if_icmpeq              LABEL429
   jump                   LABEL434
LABEL429:
   sconst                 "Offline"
   cc_settext            
   iconst                 16711680
   cc_setcolour          
   jump                   LABEL467
LABEL434:
   iload                  11
   map_world             
   if_icmpeq              LABEL438
   jump                   LABEL446
LABEL438:
   sconst                 "World "
   iload                  11
   tostring              
   join_string            1
   cc_settext            
   iconst                 901389
   cc_setcolour          
   jump                   LABEL467
LABEL446:
   iload                  11
   iconst                 5000
   if_icmpgt              LABEL450
   jump                   LABEL459
LABEL450:
   sconst                 "<col=ffff00>"
   sconst                 "Classic "
   iload                  11
   iconst                 5000
   sub                   
   tostring              
   join_string            1
   cc_settext            
   jump                   LABEL465
LABEL459:
   sconst                 "<col=ffff00>"
   sconst                 "World "
   iload                  11
   tostring              
   join_string            1
   cc_settext            
LABEL465:
   iconst                 16776960
   cc_setcolour          
LABEL467:
   iload                  9
   iconst                 1
   add                   
   iload                  12
   iload                  13
   add                   
   istore                 12
   istore                 9
   jump                   LABEL230
LABEL476:
   iload                  15
   iconst                 1
   if_icmpge              LABEL480
   jump                   LABEL484
LABEL480:
   iload                  12
   iconst                 5
   add                   
   istore                 12
LABEL484:
   iload                  12
   iload                  5
   if_getheight          
   if_icmpgt              LABEL489
   jump                   LABEL498
LABEL489:
   iconst                 0
   iload                  12
   iload                  5
   if_setscrollsize      
   iload                  6
   iload                  5
   get_varc_int           9
   invoke                 72
   jump                   LABEL506
LABEL498:
   iconst                 0
   iconst                 0
   iload                  5
   if_setscrollsize      
   iload                  6
   iload                  5
   iconst                 0
   invoke                 72
LABEL506:
   return                
