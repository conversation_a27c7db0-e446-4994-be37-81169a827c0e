/*
 * Copyright (c) 2022, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

public final class HitsplatID
{
	public static final int BLOCK_ME = 12;
	public static final int BLOCK_OTHER = 13;
	public static final int DAMAGE_ME = 16;
	public static final int DAMAGE_OTHER = 17;
	public static final int POISON = 2;
	public static final int DISEASE = 4;
	public static final int VENOM = 5;
	public static final int HEAL = 6;
	public static final int DAMAGE_ME_CYAN = 18;
	public static final int DAMAGE_OTHER_CYAN = 19;
	public static final int DAMAGE_ME_ORANGE = 20;
	public static final int DAMAGE_OTHER_ORANGE = 21;
	public static final int DAMAGE_ME_YELLOW = 22;
	public static final int DAMAGE_OTHER_YELLOW = 23;
	public static final int DAMAGE_ME_WHITE = 24;
	public static final int DAMAGE_OTHER_WHITE = 25;
	public static final int DAMAGE_MAX_ME = 43;
	public static final int DAMAGE_MAX_ME_CYAN = 44;
	public static final int DAMAGE_MAX_ME_ORANGE = 45;
	public static final int DAMAGE_MAX_ME_YELLOW = 46;
	public static final int DAMAGE_MAX_ME_WHITE = 47;
}