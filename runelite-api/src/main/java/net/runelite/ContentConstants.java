package net.runelite;

public final class ContentConstants {

    public static final String SERVER_NAME = "Exiles";
    public static final String SERVER_WEBSITE = "https://exiles-ps.com";
    public static final String SERVER_WEBSITE_SHORT = "exiles-ps.com";

    /**
     * Flag to enable loading jav_config.ws from local files instead of network.
     * When true, bypasses firewall restrictions by using local config files.
     */
    public static final boolean LoadLocalJAV_CONFIG = false;

    /**
     * Flag to enable loading prices.js from local resources instead of network.
     * Created because Runelite API one is locked down now
     * When true, uses local prices.js file from resources.
     * When false, loads from http://exiles-ps.com/game/prices.js
     */
    public static final boolean LOADLOCALPRICES = false;

    private ContentConstants() {
        throw new UnsupportedOperationException();
    }

}
