/*
 * Copyright (c) 2018, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api.vars;

/**
 * An enumeration of possible account types.
 */
public enum AccountType
{
	/**
	 * Normal account type.
	 */
	NORMAL,
	/**
	 * Ironman account type.
	 */
	IRONMAN,
	/**
	 * Ultimate ironman account type.
	 */
	ULTIMATE_IRONMAN,
	/**
	 * Hardcore ironman account type.
	 */
	HARDCORE_IRONMAN,
	/**
	 * Group ironman account type
	 */
	GROUP_IRONMAN,
	/**
	 * Hardcore group ironman account type
	 */
	HARDCORE_GROUP_IRONMAN;

	/**
	 * Checks whether this type is a non-group ironman.
	 *
	 * @return {@code true} if the type is any of the non-group ironman types.
	 */
	public boolean isIronman()
	{
		return this.ordinal() >= IRONMAN.ordinal() && this.ordinal() <= HARDCORE_IRONMAN.ordinal();
	}

	/**
	 * Checks whether this type is a group ironman.
	 *
	 * @return {@code true} if the type is either of the group ironman types.
	 */
	public boolean isGroupIronman()
	{
		return this.ordinal() >= GROUP_IRONMAN.ordinal() && this.ordinal() <= HARDCORE_GROUP_IRONMAN.ordinal();
	}
}
