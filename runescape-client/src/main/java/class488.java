import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("rk")
public class class488 {
   @ObfuscatedName("f")
   @ObfuscatedGetter(
      intValue = 40815511
   )
   @Export("SpriteBuffer_spriteCount")
   public static int SpriteBuffer_spriteCount;
   @ObfuscatedName("w")
   @ObfuscatedGetter(
      intValue = -274714745
   )
   @Export("SpriteBuffer_spriteWidth")
   public static int SpriteBuffer_spriteWidth;
   @ObfuscatedName("v")
   @ObfuscatedGetter(
      intValue = -1370648099
   )
   @Export("SpriteBuffer_spriteHeight")
   public static int SpriteBuffer_spriteHeight;
   @ObfuscatedName("s")
   @Export("SpriteBuffer_xOffsets")
   public static int[] SpriteBuffer_xOffsets;
}
