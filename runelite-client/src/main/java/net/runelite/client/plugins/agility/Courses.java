package net.runelite.client.plugins.agility;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import java.util.function.Function;
import lombok.Getter;
import net.runelite.api.Client;
import net.runelite.api.coords.WorldPoint;

enum Courses
{
	GNOME(552.5, 9781, new WorldPoint(2484, 3437, 0), new WorldPoint(2487, 3437, 0)),
	SHAYZIEN_BASIC(767.5, 6200, new WorldPoint(1554, 3640, 0)),
	DRAYNOR(600.0, 12338, new WorldPoint(3103, 3261, 0)),
	AL_KHARID(1080.0, 13105, new WorldPoint(3299, 3194, 0)),
	PYRAMID(3610.0, 13356, new WorldPoint(3364, 2830, 0)),
	VARROCK(1350.0, 12853, new WorldPoint(3236, 3417, 0)),
	PENGUIN(2700.0, 10559, new WorldPoint(2652, 4039, 1)),
	BARBARIAN(766.0, 10039, new WorldPoint(2543, 3553, 0)),
	CANIFIS(1200.0, 13878, new WorldPoint(3510, 3485, 0)),
	APE_ATOLL(2900.0, 11050, new WorldPoint(2770, 2747, 0)),
	SHAYZIEN_ADVANCED(2537.5, 5944, new WorldPoint(1522, 3625, 0)),
	FALADOR(2930.0, 12084, new WorldPoint(3029, 3332, 0), new WorldPoint(3029, 3333, 0), new WorldPoint(3029, 3334, 0), new WorldPoint(3029, 3335, 0)),
	WILDERNESS(4500.0, 11837, new WorldPoint(2993, 3933, 0), new WorldPoint(2994, 3933, 0), new WorldPoint(2995, 3933, 0)),
	WEREWOLF(3650.0, 14234, new WorldPoint(3528, 9873, 0)),
	SEERS(2850.0, 10806, new WorldPoint(2704, 3464, 0)),
	POLLNIVNEACH(4450.0, 13358, new WorldPoint(3363, 2998, 0)),
	RELLEKA(3900.0, 10553, new WorldPoint(2653, 3676, 0)),
	PRIFDDINAS(6685.0, 12895, new WorldPoint(3240, 6109, 0)),
	ARDOUGNE(4445.0, 10547, new WorldPoint(2668, 3297, 0));

	private final static Map<Integer, Courses> coursesByRegion;

	@Getter
	private final Function<Client, Double> totalXpProvider;

	@Getter
	private final int regionId;

	@Getter
	private final WorldPoint[] courseEndWorldPoints;

	static
	{
		ImmutableMap.Builder<Integer, Courses> builder = new ImmutableMap.Builder<>();

		for (Courses course : values())
		{
			if (course.regionId == -1)
			{
				continue;
			}
			builder.put(course.regionId, course);
		}

		coursesByRegion = builder.build();
	}

	Courses(double totalXp)
	{
		this(totalXp, -1, null);
	}

	Courses(double totalXp, int regionId, WorldPoint... courseEndWorldPoints)
	{
		this((client) -> totalXp, regionId, courseEndWorldPoints);
	}

	Courses(Function<Client, Double> totalXpProvider, int regionId, WorldPoint... courseEndWorldPoints)
	{
		this.totalXpProvider = totalXpProvider;
		this.regionId = regionId;
		this.courseEndWorldPoints = courseEndWorldPoints;
	}

	static Courses getCourse(int regionId)
	{
		return coursesByRegion.get(regionId);
	}
}