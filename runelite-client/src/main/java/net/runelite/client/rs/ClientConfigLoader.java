/*
 * Copyright (c) 2016-2017, <PERSON> <<EMAIL>>
 * Copyright (c) 2018, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.rs;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.runelite.ContentConstants;
import net.runelite.api.Constants;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

@Slf4j
@AllArgsConstructor
class ClientConfigLoader
{
	private final OkHttpClient okHttpClient;

	RSConfig fetch(HttpUrl url) throws IOException
	{
		// Only try local files if the flag is enabled
		if (ContentConstants.LoadLocalJAV_CONFIG)
		{
			// Try to read from local jav_config.ws first - check multiple locations
			java.io.File[] possibleLocations = {
				new java.io.File("jav_config.ws"), // project root
				new java.io.File("runelite-client/src/test/resources/net/runelite/client/rs/jav_config.ws"), // test resources
				new java.io.File("src/test/resources/net/runelite/client/rs/jav_config.ws") // relative to runelite-client
			};

			for (java.io.File localConfig : possibleLocations)
			{
				if (localConfig.exists())
				{
					log.info("Using local jav_config.ws from: {}", localConfig.getAbsolutePath());
					return fetchFromFile(localConfig);
				}
			}

			// Try to load from classpath resources as well
			try (java.io.InputStream resourceStream = ClientConfigLoader.class.getResourceAsStream("/net/runelite/client/rs/jav_config.ws"))
			{
				if (resourceStream != null)
				{
					log.info("Using jav_config.ws from classpath resources");
					return parseConfigStream(new InputStreamReader(resourceStream, StandardCharsets.UTF_8));
				}
			}
			catch (Exception e)
			{
				log.debug("Could not load jav_config.ws from classpath", e);
			}

			log.info("No local jav_config.ws found, falling back to network download");
		}

		// Standard network request (original behavior)
		final Request request = new Request.Builder()
			.url(url)
			.build();

		try (Response response = okHttpClient.newCall(request).execute())
		{
			if (!response.isSuccessful())
			{
				throw new IOException("Unsuccessful response: " + response.message());
			}

			return parseConfigStream(new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8));
		}
	}

	private RSConfig fetchFromFile(java.io.File file) throws IOException
	{
		try (java.io.FileReader reader = new java.io.FileReader(file))
		{
			return parseConfigStream(reader);
		}
	}

	private RSConfig parseConfigStream(java.io.Reader reader) throws IOException
	{
		final RSConfig config = new RSConfig();
		String str;
		final BufferedReader in = new BufferedReader(reader);

		while ((str = in.readLine()) != null)
		{
			int idx = str.indexOf('=');

			if (idx == -1)
			{
				continue;
			}

			String s = str.substring(0, idx);

			switch (s)
			{
				case "param":
					str = str.substring(idx + 1);
					idx = str.indexOf('=');
					s = str.substring(0, idx);

					config.getAppletProperties().put(s, str.substring(idx + 1));
					break;
				case "msg":
					// ignore
					break;
				default:
					config.getClassLoaderProperties().put(s, str.substring(idx + 1));
					break;
			}
		}

		return config;
	}
}
