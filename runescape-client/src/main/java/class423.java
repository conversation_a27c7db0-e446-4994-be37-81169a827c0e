import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("pp")
public class class423 extends class424 {
   @ObfuscatedName("c")
   @Export("musicTrackBoolean")
   public static boolean musicTrackBoolean;

   public class423(int var1) {
      super(var1);
   }

   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(Lrd;IS)V",
      garbageValue = "-22677"
   )
   void vmethod8027(Buffer var1, int var2) {
   }
}
