import java.math.BigInteger;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("bk")
public class class68 {
   @ObfuscatedName("z")
   static final BigInteger field883 = new BigInteger("80782894952180643741752986186714059433953886149239752893425047584684715842049");
   @ObfuscatedName("j")
   static final BigInteger field880 = new BigInteger("7237300117305667488707183861728052766358166655052137727439795191253340127955075499635575104901523446809299097934591732635674173519120047404024393881551683");

   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(Lln;B)V",
      garbageValue = "-17"
   )
   public static void method2074(AbstractArchive var0) {
      VarcInt.VarcInt_archive = var0;
   }
}
