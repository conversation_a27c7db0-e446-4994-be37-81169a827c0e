/*
 * Copyright (c) 2018, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.mixins;

import net.runelite.api.WorldType;
import net.runelite.api.events.WorldListLoad;
import net.runelite.api.mixins.*;
import net.runelite.rs.api.RSClient;
import net.runelite.rs.api.RSUrlRequest;
import net.runelite.rs.api.RSWorld;

import java.util.EnumSet;

@Mixin(RSWorld.class)
public abstract class RSWorldMixin implements RSWorld
{
	@Shadow("client")
	private static RSClient client;

	@Inject
	@Override
	public EnumSet<WorldType> getTypes()
	{
		return WorldType.fromMask(getMask());
	}

	@Inject
	@Override
	public void setTypes(final EnumSet<WorldType> types)
	{
		setMask(WorldType.toMask(types));
	}

	@Inject
	@FieldHook("population")
	public void playerCountChanged(int idx)
	{
		RSWorld[] worlds = client.getWorldList();
		if (worlds != null && worlds.length > 0 && worlds[worlds.length - 1] == this)
		{
			// this is the last world in the list.
			WorldListLoad worldLoad = new WorldListLoad(worlds);
			client.getCallbacks().post(worldLoad);
		}
	}


}
