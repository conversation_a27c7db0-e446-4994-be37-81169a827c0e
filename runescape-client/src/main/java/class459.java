import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("qk")
public interface class459 {
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(Ljava/lang/Object;Lrd;B)V",
      garbageValue = "10"
   )
   void vmethod8518(Object var1, Buffer var2);

   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "(Lrd;B)Ljava/lang/Object;",
      garbageValue = "4"
   )
   Object vmethod8517(Buffer var1);
}
