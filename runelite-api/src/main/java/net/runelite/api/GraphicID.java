/*
 * Copyright (c) 2018, <PERSON> <<EMAIL>>
 * Copyright (c) 2019, Ganom <https://github.com/Ganom>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

public final class GraphicID
{
	public static final int WINE_MAKE = 47;
	public static final int CANNONBALL = 53;
	public static final int SPLASH = 85;
	public static final int GREY_BUBBLE_TELEPORT = 86;
	public static final int TELEPORT = 111;
	public static final int TELEKINETIC_SPELL = 143;
	public static final int ENTANGLE = 179;
	public static final int SNARE = 180;
	public static final int BIND = 181;
	public static final int POISON_SPLAT = 184;
	public static final int ICE_RUSH = 361;
	public static final int ICE_BURST = 363;
	public static final int ICE_BLITZ = 367;
	public static final int ICE_BARRAGE = 369;
	public static final int VENGEANCE_OTHER = 725;
	public static final int VENGEANCE = 726;
	public static final int NPC_CONTACT = 728;
	public static final int POT_SHARE = 733;
	public static final int BAKE_PIE = 746;
	public static final int BOOK_HOME_TELEPORT_1 = 800;
	public static final int BOOK_HOME_TELEPORT_2 = 802;
	public static final int BOOK_HOME_TELEPORT_3 = 803;
	public static final int BOOK_HOME_TELEPORT_4 = 804;
	public static final int STAFF_OF_THE_DEAD = 1228;
	public static final int FLYING_FISH = 1387;
	public static final int OLM_BURN = 1351;
	public static final int OLM_LIGHTNING = 1356;
	public static final int OLM_TELEPORT = 1359;
	public static final int OLM_HEAL = 1363;
	public static final int GRANITE_CANNONBALL = 1443;
	public static final int OLM_CRYSTAL = 1447;
	public static final int XERIC_TELEPORT = 1612;
	public static final int MELEE_NYLO_DEATH = 1562;
	public static final int RANGE_NYLO_DEATH = 1563;
	public static final int MAGE_NYLO_DEATH = 1564;
	public static final int MELEE_NYLO_EXPLOSION = 1565;
	public static final int RANGE_NYLO_EXPLOSION = 1566;
	public static final int MAGE_NYLO_EXPLOSION = 1567;
	public static final int GRAPHICS_OBJECT_ROCKFALL = 1727;
	public static final int ZALCANO_PROJECTILE_FIREBALL = 1728;
	public static final int CANNONBALL_OR = 2018;
	public static final int GRANITE_CANNONBALL_OR = 2019;

	public static final int LIZARDMAN_SHAMAN_AOE = 1293;
	public static final int CRAZY_ARCHAEOLOGIST_AOE = 1260;
	public static final int ICE_DEMON_RANGED_AOE = 1324;
	public static final int ICE_DEMON_ICE_BARRAGE_AOE = 366;
	public static final int VASA_AWAKEN_AOE = 1327;
	public static final int VASA_RANGED_AOE = 1329;
	public static final int TEKTON_METEOR_AOE = 660;

	public static final int OLM_FALLING_CRYSTAL = 1357;
	public static final int OLM_BURNING = 1349;
	public static final int OLM_FALLING_CRYSTAL_TRAIL = 1352;
	public static final int OLM_ACID_TRAIL = 1354;
	public static final int OLM_FIRE_LINE = 1347;
	public static final int OLM_MAGE_ATTACK = 1339;
	public static final int OLM_RANGE_ATTACK = 1340;

	public static final int VORKATH_BOMB_AOE = 1481;
	public static final int VORKATH_POISON_POOL_AOE = 1483;
	public static final int VORKATH_TICK_FIRE_AOE = 1482;
	public static final int VORKATH_SPAWN_AOE = 1484;

	public static final int ADDY_DRAG_POISON = 1486;

	public static final int GALVEK_MINE = 1495;
	public static final int GALVEK_BOMB = 1491;

	public static final int DAWN_FREEZE = 1445;
	public static final int DUSK_CEILING = 1435;

	public static final int VETION_LIGHTNING = 280;

	public static final int CHAOS_FANATIC_AOE = 551;

	public static final int CORPOREAL_BEAST_AOE = 315;
	public static final int CORPOREAL_BEAST_DARK_CORE_AOE = 319;

	public static final int WINTERTODT_SNOW_FALL_AOE = 1310;

	public static final int DEMONIC_GORILLA_RANGED = 1302;
	public static final int DEMONIC_GORILLA_MAGIC = 1304;
	public static final int DEMONIC_GORILLA_BOULDER = 856;

	public static final int XARPUS_ACID = 1555;
	public static final int VERZIK_PURPLE_SPAWN = 1586;
	public static final int CERB_FIRE = 1247;

	public static final int DERWEN_HEALING_BALL = 1512;
	public static final int JUSTICIAR_LEASH = 1515;
	public static final int MAGE_ARENA_BOSS_FREEZE = 368;

	/**
	 * missing: superior dark beast
	 */
	public static final int MARBLE_GARGOYLE_AOE = 1453;
	/**
	 * non AOE, regular projectiles
	 */
	public static final int VORKATH_DRAGONBREATH = 393;
	public static final int VORKATH_RANGED = 1477;
	public static final int VORKATH_MAGIC = 1479;
	public static final int VORKATH_PRAYER_DISABLE = 1471;
	public static final int VORKATH_VENOM = 1470;
	public static final int VORKATH_ICE = 395;

	public static final int SOTETSEG_BOMB = 1604;
	public static final int SOTETSEG_RANGE = 1607;
	public static final int SOTETSEG_MAGE = 1606;

	public static final int HYDRA_MAGIC = 1662;
	public static final int HYDRA_RANGED = 1663;
	public static final int HYDRA_POISON = 1644;
	public static final int HYDRA_LIGHTNING = 1664;
	public static final int HYDRA_LIGHTNING_2 = 1665;
	public static final int DRAKE_BREATH = 1637;

	public static final int HUNLLEF_MAGE_ATTACK = 1707;
	public static final int HUNLLEF_CORRUPTED_MAGE_ATTACK = 1708;
	public static final int HUNLLEF_RANGE_ATTACK = 1711;
	public static final int HUNLLEF_CORRUPTED_RANGE_ATTACK = 1712;
	public static final int HUNLLEF_PRAYER_ATTACK = 1713;
	public static final int HUNLLEF_CORRUPTED_PRAYER_ATTACK = 1714;

	public static final int PORAZDIR_ENERGY_BALL = 1514;
}
