.id                 909
.int_stack_count    2
.string_stack_count 0
.int_var_count      23
.string_var_count   0
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551386
   enum                  
   istore                 2
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551388
   enum                  
   istore                 3
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551303
   enum                  
   istore                 4
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551311
   enum                  
   istore                 5
   iconst                 -1
   istore                 6
   iconst                 103
   iconst                 105
   iconst                 1960
   iload                  1
   enum                  
   istore                 7
   iconst                 103
   iconst                 105
   iconst                 1961
   iload                  1
   enum                  
   istore                 8
   iconst                 103
   iconst                 105
   iconst                 1135
   iload                  1
   enum                  
   istore                 9
   iconst                 103
   iconst                 105
   iconst                 1136
   iload                  1
   enum                  
   istore                 10
   iconst                 0
   istore                 11
   iconst                 0
   istore                 12
   iconst                 0
   istore                 13
   iconst                 0
   istore                 14
   iconst                 0
   istore                 15
   iconst                 0
   istore                 16
   iload                  0
   if_getwidth           
   istore                 17
   iload                  0
   if_getheight          
   istore                 18
   iconst                 0
   istore                 19
   iload                  1
   iconst                 1745
   if_icmpeq              LABEL74
   jump                   LABEL111
LABEL74:
   iconst                 0
   iload                  17
   iconst                 39387155
   if_getwidth           
   sub                   
   invoke                 1045
   istore                 15
   iconst                 0
   iload                  18
   iconst                 39387155
   if_getheight          
   sub                   
   invoke                 1045
   istore                 16
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551329
   enum                  
   istore                 6
   iload                  6
   iconst                 -1
   if_icmpne              LABEL98
   jump                   LABEL111
LABEL98:
   iconst                 -1
   invoke                 5357
   iconst                 1
   if_icmpeq              LABEL103
   jump                   LABEL111
LABEL103:
   get_varc_int           381
   iconst                 16384
   iload                  6
   if_getwidth           
   scale                 
   iconst                 42
   add                   
   istore                 19
LABEL111:
   get_varbit             4606
   iconst                 0
   if_icmpne              LABEL115
   jump                   LABEL308
LABEL115:
   get_varbit             4606
   iconst                 2
   if_icmpeq              LABEL119
   jump                   LABEL128
LABEL119:
   iconst                 512
   iconst                 220
   viewport_setfov       
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   viewport_clampfov     
   jump                   LABEL159
LABEL128:
   get_varbit             4606
   iconst                 3
   if_icmpeq              LABEL132
   jump                   LABEL151
LABEL132:
   iconst                 256
   iconst                 256
   viewport_setfov       
   iload                  1
   iconst                 1129
   if_icmpeq              LABEL139
   jump                   LABEL145
LABEL139:
   iconst                 512
   iconst                 512
   iconst                 512
   iconst                 512
   viewport_clampfov     
   jump                   LABEL150
LABEL145:
   iconst                 640
   iconst                 768
   iconst                 512
   iconst                 512
   viewport_clampfov     
LABEL150:
   jump                   LABEL159
LABEL151:
   iconst                 256
   iconst                 256
   viewport_setfov       
   iconst                 512
   iconst                 512
   iconst                 512
   iconst                 512
   viewport_clampfov     
LABEL159:
   iconst                 50
   cam_setfollowheight   
   iload                  2
   iconst                 -1
   if_icmpne              LABEL165
   jump                   LABEL307
LABEL165:
   iload                  3
   iconst                 -1
   if_icmpne              LABEL169
   jump                   LABEL307
LABEL169:
   viewport_geteffectivesize
   istore                 12
   istore                 11
   iconst                 0
   iload                  17
   iload                  11
   sub                   
   invoke                 1045
   iconst                 0
   iload                  18
   iload                  12
   sub                   
   invoke                 1045
   istore                 14
   istore                 13
   iload                  11
   iload                  12
   iconst                 0
   iconst                 0
   iload                  2
   if_setsize            
   iload                  11
   iconst                 0
   iload                  15
   iload                  13
   sub                   
   iload                  19
   sub                   
   invoke                 1045
   sub                   
   iload                  12
   iconst                 0
   iload                  16
   iload                  14
   sub                   
   invoke                 1045
   sub                   
   iconst                 0
   iconst                 0
   iload                  3
   if_setsize            
   iload                  4
   iconst                 -1
   if_icmpne              LABEL214
   jump                   LABEL297
LABEL214:
   iload                  5
   iconst                 -1
   if_icmpne              LABEL218
   jump                   LABEL297
LABEL218:
   iload                  13
   iload                  15
   sub                   
   iconst                 2
   div                   
   iload                  14
   iload                  16
   sub                   
   iconst                 2
   div                   
   istore                 14
   istore                 13
   iconst                 0
   iload                  7
   iload                  13
   sub                   
   invoke                 1045
   iconst                 0
   iload                  9
   iload                  13
   sub                   
   invoke                 1045
   istore                 9
   istore                 7
   iconst                 0
   iload                  8
   iload                  14
   sub                   
   invoke                 1045
   iconst                 0
   iload                  10
   iload                  14
   sub                   
   invoke                 1045
   istore                 10
   istore                 8
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  4
   if_setposition        
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  5
   if_setposition        
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  4
   if_setsize            
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  5
   if_setsize            
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  5
   iload                  9
   iload                  10
   invoke                 910
   jump                   LABEL307
LABEL297:
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  3
   iconst                 0
   iconst                 0
   invoke                 910
LABEL307:
   jump                   LABEL426
LABEL308:
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   viewport_clampfov     
   get_varc_int           73
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   if_icmpge              LABEL317
   jump                   LABEL333
LABEL317:
   get_varc_int           73
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   if_icmple              LABEL321
   jump                   LABEL333
LABEL321:
   get_varc_int           74
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   if_icmpge              LABEL325
   jump                   LABEL333
LABEL325:
   get_varc_int           74
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   if_icmple              LABEL329
   jump                   LABEL333
LABEL329:
   get_varc_int           73
   get_varc_int           74
   invoke                 42
   jump                   LABEL336
LABEL333:
   iconst                 512
   iconst                 512
   invoke                 42
LABEL336:
   viewport_geteffectivesize
   istore                 12
   istore                 11
   iload                  2
   iconst                 -1
   if_icmpne              LABEL343
   jump                   LABEL426
LABEL343:
   iload                  3
   iconst                 -1
   if_icmpne              LABEL347
   jump                   LABEL426
LABEL347:
   iload                  11
   iload                  12
   iconst                 0
   iconst                 0
   iload                  2
   if_setsize            
   iload                  11
   iload                  15
   sub                   
   iload                  19
   sub                   
   iload                  12
   iload                  16
   sub                   
   iconst                 0
   iconst                 0
   iload                  3
   if_setsize            
   iload                  4
   iconst                 -1
   if_icmpne              LABEL369
   jump                   LABEL416
LABEL369:
   iload                  5
   iconst                 -1
   if_icmpne              LABEL373
   jump                   LABEL416
LABEL373:
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  4
   if_setposition        
   iload                  7
   iload                  8
   iconst                 0
   iconst                 0
   iload                  5
   if_setposition        
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  4
   if_setsize            
   iload                  7
   iload                  9
   add                   
   iload                  8
   iload                  10
   add                   
   iconst                 1
   iconst                 1
   iload                  5
   if_setsize            
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  5
   iload                  9
   iload                  10
   invoke                 910
   jump                   LABEL426
LABEL416:
   iload                  1
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551310
   enum                  
   iload                  3
   iconst                 0
   iconst                 0
   invoke                 910
LABEL426:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551312
   enum                  
   istore                 20
   iload                  20
   iconst                 -1
   if_icmpne              LABEL436
   jump                   LABEL480
LABEL436:
   invoke                 1972
   iconst                 0
   if_icmpeq              LABEL440
   jump                   LABEL474
LABEL440:
   iload                  20
   if_hassub             
   iconst                 1
   if_icmpeq              LABEL445
   jump                   LABEL474
LABEL445:
   get_varc_int           173
   iconst                 -2
   if_icmpeq              LABEL449
   jump                   LABEL456
LABEL449:
   iconst                 512
   iconst                 0
   iconst                 0
   iconst                 1
   iload                  20
   if_setsize            
   jump                   LABEL473
LABEL456:
   get_varc_int           173
   iconst                 -3
   if_icmpeq              LABEL460
   jump                   LABEL467
LABEL460:
   iconst                 0
   iconst                 0
   iconst                 1
   iconst                 1
   iload                  20
   if_setsize            
   jump                   LABEL473
LABEL467:
   iconst                 512
   iconst                 334
   iconst                 0
   iconst                 0
   iload                  20
   if_setsize            
LABEL473:
   jump                   LABEL480
LABEL474:
   iconst                 512
   iconst                 334
   iconst                 0
   iconst                 0
   iload                  20
   if_setsize            
LABEL480:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551314
   enum                  
   istore                 20
   iconst                 0
   istore                 21
   iconst                 0
   istore                 22
   iload                  20
   iconst                 -1
   if_icmpne              LABEL494
   jump                   LABEL542
LABEL494:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551305
   enum                  
   if_hassub             
   iconst                 1
   if_icmpeq              LABEL503
   jump                   LABEL512
LABEL503:
   get_varbit             4692
   iconst                 0
   if_icmpne              LABEL507
   jump                   LABEL510
LABEL507:
   iconst                 0
   istore                 22
   jump                   LABEL512
LABEL510:
   iconst                 38
   istore                 22
LABEL512:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL516
   jump                   LABEL534
LABEL516:
   get_varbit             542
   iconst                 0
   if_icmpeq              LABEL520
   jump                   LABEL534
LABEL520:
   get_varbit             6254
   iconst                 0
   if_icmpeq              LABEL524
   jump                   LABEL529
LABEL524:
   iconst                 182
   iconst                 4
   add                   
   istore                 21
   jump                   LABEL533
LABEL529:
   iconst                 120
   iconst                 4
   add                   
   istore                 21
LABEL533:
   jump                   LABEL536
LABEL534:
   iconst                 0
   istore                 21
LABEL536:
   iload                  21
   iload                  22
   iconst                 2
   iconst                 0
   iload                  20
   if_setposition        
LABEL542:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551305
   enum                  
   istore                 20
   iconst                 0
   istore                 21
   iload                  20
   iconst                 -1
   if_icmpne              LABEL554
   jump                   LABEL576
LABEL554:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL558
   jump                   LABEL568
LABEL558:
   get_varbit             6254
   iconst                 0
   if_icmpeq              LABEL562
   jump                   LABEL565
LABEL562:
   iconst                 182
   istore                 21
   jump                   LABEL567
LABEL565:
   iconst                 120
   istore                 21
LABEL567:
   jump                   LABEL570
LABEL568:
   iconst                 0
   istore                 21
LABEL570:
   iload                  21
   iconst                 0
   iconst                 1
   iconst                 1
   iload                  20
   if_setsize            
LABEL576:
   iconst                 73
   iconst                 73
   iload                  1
   iconst                 10551329
   enum                  
   iload                  1
   invoke                 920
   return                
