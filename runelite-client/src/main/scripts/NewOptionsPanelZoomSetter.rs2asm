.id                 3899
.int_stack_count    7
.string_stack_count 0
.int_var_count      11
.string_var_count   0
   get_varbit             4606
   iconst                 0
   if_icmpne              LABEL4
   jump                   LABEL5
LABEL4:
   return                
LABEL5:
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   iload                  2
   invoke                 1046
   istore                 2
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   iload                  2
   invoke                 1045
   istore                 2
   iconst                 896
   sconst                 "innerZoomLimit"
   runelite_callback     
   iload                  3
   invoke                 1046
   istore                 3
   iconst                 128
   sconst                 "outerZoomLimit"
   runelite_callback     
   iload                  3
   invoke                 1045
   istore                 3
   iload                  2
   iload                  3
   viewport_setfov       
   iconst                 0
   istore                 7
   iconst                 0
   istore                 8
   viewport_geteffectivesize
   istore                 8
   istore                 7
   iload                  8
   iconst                 334
   sub                   
   istore                 9
   iload                  9
   iconst                 0
   if_icmplt              LABEL39
   jump                   LABEL42
LABEL39:
   iconst                 0
   istore                 9
   jump                   LABEL48
LABEL42:
   iload                  9
   iconst                 100
   if_icmpgt              LABEL46
   jump                   LABEL48
LABEL46:
   iconst                 100
   istore                 9
LABEL48:
   iload                  2
   iload                  3
   iload                  2
   sub                   
   iload                  9
   multiply              
   iconst                 100
   div                   
   add                   
   istore                 10
   iconst                 25
   iconst                 25
   iload                  10
   multiply              
   iconst                 256
   div                   
   add                   
   cam_setfollowheight   
   iload                  2
   iload                  3
   set_varc_int           74
   set_varc_int           73
   iload                  0
   iload                  1
   iload                  4
   iload                  5
   iload                  6
   invoke                 3900
   return                
