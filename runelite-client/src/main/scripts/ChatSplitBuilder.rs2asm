.id                 89
.int_stack_count    1
.string_stack_count 0
.int_var_count      20
.string_var_count   7
   iconst                 0
   istore                 1
   iconst                 2
   istore                 2
   iconst                 103
   istore                 3
   iconst                 4
   istore                 4
   iconst                 23
   istore                 5
   invoke                 900
   istore                 6
   iconst                 103
   iconst                 105
   iconst                 1136
   iload                  6
   enum                  
   iconst                 0
   if_icmpgt              LABEL20
   jump                   LABEL58
LABEL20:
   iload                  6
   iconst                 1745
   if_icmpeq              LABEL24
   jump                   LABEL36
LABEL24:
   iconst                 0
   iconst                 102
   iconst                 103
   iconst                 105
   iconst                 1960
   iload                  6
   enum                  
   iconst                 30
   istore                 5
   istore                 1
   istore                 3
   istore                 2
LABEL36:
   get_varc_int           41
   iconst                 1337
   if_icmpeq              LABEL40
   jump                   LABEL49
LABEL40:
   invoke                 922
   iconst                 1
   if_icmpeq              LABEL44
   jump                   LABEL49
LABEL44:
   iload                  4
   iload                  5
   add                   
   istore                 4
   jump                   LABEL58
LABEL49:
   iload                  4
   iconst                 73
   iconst                 73
   iload                  6
   iconst                 10551390
   enum                  
   if_getheight          
   add                   
   istore                 4
LABEL58:
   iload                  4
   istore                 7
   iconst                 10682368
   if_getwidth           
   istore                 8
   iconst                 0
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
   iconst                 0
   istore                 11
   sconst                 ""
   sstore                 0
   reboottimer           
   iconst                 0
   if_icmpgt              LABEL79
   jump                   LABEL156
LABEL79:
   reboottimer           
   iconst                 50
   div                   
   iconst                 60
   mod                   
   istore                 11
   iload                  11
   iconst                 10
   if_icmplt              LABEL89
   jump                   LABEL100
LABEL89:
   sconst                 "System update in: "
   reboottimer           
   iconst                 3000
   div                   
   tostring              
   sconst                 ":0"
   iload                  11
   tostring              
   join_string            4
   sstore                 0
   jump                   LABEL110
LABEL100:
   sconst                 "System update in: "
   reboottimer           
   iconst                 3000
   div                   
   tostring              
   sconst                 ":"
   iload                  11
   tostring              
   join_string            4
   sstore                 0
LABEL110:
   iload                  7
   sload                  0
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 16776960
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  10
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  10
   if_setsize            
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL156:
   iconst                 -1
   istore                 12
   iconst                 -1
   istore                 13
   sconst                 ""
   sstore                 1
   iconst                 0
   istore                 14
   sconst                 ""
   sstore                 2
   iconst                 0
   istore                 15
   sconst                 ""
   sstore                 3
   sconst                 ""
   sstore                 4
   iconst                 -1
   istore                 16
   iconst                 0
   istore                 17
   sconst                 "<col=00ffff>"
   sstore                 5
   sconst                 "<col=ffff00>"
   sstore                 6
   sload                  5
   sload                  6
   invoke                 4485
   sstore                 6
   sstore                 5
   get_varc_int           55
   get_varc_int           202
   if_icmpge              LABEL189
   jump                   LABEL320
LABEL189:
   get_varc_int           55
   clientclock           
   iconst                 3000
   sub                   
   if_icmpgt              LABEL195
   jump                   LABEL320
LABEL195:
   iconst                 14
   chat_gethistorylength 
   iconst                 0
   if_icmpgt              LABEL200
   jump                   LABEL320
LABEL200:
   iconst                 14
   iconst                 0
   5030                  
   istore                 15
   sstore                 2
   istore                 14
   sstore                 0
   sstore                 3
   sstore                 1
   istore                 13
   istore                 12
   iload                  12
   iconst                 -1
   if_icmpne              LABEL215
   jump                   LABEL320
LABEL215:
   sload                  0
   invoke                 2066
   istore                 16
   sstore                 4
   sstore                 0
   iload                  16
   iconst                 4
   if_icmpne              LABEL227
   reboottimer           
   iconst                 0
   if_icmple              LABEL227
   jump                   LABEL320
LABEL227:
   iload                  7
   sload                  2
   sload                  6
   sload                  0
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 16776960
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  10
   if_clearops           
   sload                  4
   string_length         
   iconst                 0
   if_icmpgt              LABEL256
   jump                   LABEL285
LABEL256:
   iload                  16
   iconst                 -1
   if_icmpne              LABEL260
   jump                   LABEL285
LABEL260:
   iconst                 6
   sconst                 "Open"
   iload                  10
   if_setop              
   iconst                 7
   sconst                 "Check"
   iload                  10
   if_setop              
   iconst                 2065
   iload                  10
   if_getlayer           
   iload                  9
   iconst                 16777215
   sconst                 "Iii"
   iload                  10
   if_setonmouserepeat   
   iconst                 2065
   iload                  10
   if_getlayer           
   iload                  9
   iconst                 16776960
   sconst                 "Iii"
   iload                  10
   if_setonmouseleave    
   jump                   LABEL293
LABEL285:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
LABEL293:
   iconst                 9
   sconst                 "Clear history"
   iload                  10
   if_setop              
   sconst                 "<col=ff9040>"
   sconst                 "Notification"
   sconst                 "</col>"
   join_string            3
   iload                  10
   if_setopbase          
   iconst                 2064
   iconst                 -2147483644
   sload                  4
   iload                  16
   sconst                 "isi"
   iload                  10
   if_setonop            
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL320:
   iload                  0
   istore                 12
   iconst                 0
   istore                 18
   invoke                 4487
   istore                 19
   get_varp               287
   iconst                 1
   if_icmpeq              LABEL330
   jump                   LABEL559
LABEL330:
   get_varc_int           41
   iconst                 1337
   if_icmpne              LABEL337
   get_varbit             4089
   iconst                 0
   if_icmpeq              LABEL337
   jump                   LABEL559
LABEL337:
   iload                  12
   iconst                 -1
   if_icmpne              LABEL341
   jump                   LABEL559
LABEL341:
   iload                  10
   iconst                 -1
   if_icmpne              LABEL345
   jump                   LABEL559
LABEL345:
   iload                  7
   iload                  4
   sub                   
   iconst                 57
   if_icmplt              LABEL351
   jump                   LABEL559
LABEL351:
   iload                  12
   5031                  
   istore                 15
   sstore                 2                 ; timestamp
   istore                 14
   sstore                 0
   sstore                 3
   sstore                 1
   istore                 13
   istore                 18
   iload                  18
   sload                  1
   iload                  13
   iload                  14
   invoke                 91
   iconst                 1
   if_icmpeq              CHAT_FILTER       ; Jump to our new label instead
   jump                   LABEL555
CHAT_FILTER:
   sload                  0                 ; Load the message
   iconst                 1                 ; Gets changed to 0 if message is blocked
   iload                  18                ; Load the messageType
   iload                  12                ; Load the id of the messageNode
   sconst                 "chatFilterCheck"
   runelite_callback     
   pop_int               ; Pop the id of the messageNode
   pop_int               ; Pop the messageType
   iconst                 1                 ; 2nd half of conditional
   sstore                 0                 ; Override the message with our filtered message
   if_icmpeq              LABEL369          ; Check if we are building this message
   jump                   LABEL555
LABEL369:
   iconst                  1 ; splitpmbox
   iload                  12 ; message uid
   sconst                 "" ; message channel
   sload                  1 ; message name
   sload                  0 ; message
   sload                  2 ; message timestamp
   sconst                 "chatMessageBuilding"
   runelite_callback
   pop_int                  ; uid
   pop_int                  ; splitpmbox
   sstore                 2 ; message timestamp
   sstore                 0 ; message
   sstore                 1 ; message name
   pop_string               ; message channel
   iload                  18
   switch                
      3: LABEL372
      7: LABEL372
      6: LABEL401
      5: LABEL430
   jump                   LABEL468
LABEL372:
   iload                  7
   sload                  2
   sload                  5
   sconst                 "splitPrivChatUsernameColor"
   runelite_callback     
   sconst                 "From "
   sload                  1
   sconst                 ":"
   sconst                 "</col>"
   join_string            5
   sload                  5
   invoke                 4742
   sload                  5
   sload                  0
   sconst                 "</col>"
   join_string            3
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 203
   add                   
   istore                 7
   jump                   LABEL487
LABEL401:
   iload                  7
   sload                  2
   sload                  5
   sconst                 "splitPrivChatUsernameColor"
   runelite_callback     
   sconst                 "To "
   sload                  1
   sconst                 ":"
   sconst                 "</col>"
   join_string            5
   sload                  5
   invoke                 4742
   sload                  5
   sload                  0
   sconst                 "</col>"
   join_string            3
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 203
   add                   
   istore                 7
   jump                   LABEL487
LABEL430:
   iload                  7
   sload                  2
   sload                  5
   sload                  0
   sconst                 "</col>"
   join_string            3
   sload                  5
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 199
   add                   
   istore                 7
   iload                  19
   iconst                 0
   if_icmpeq              LABEL456
   jump                   LABEL467
LABEL456:
   iload                  13
   iconst                 500
   add                   
   iconst                 1
   add                   
   set_varc_int           65
   iconst                 664
   iconst                 0
   sconst                 "1"
   iconst                 10616832
   if_setontimer         
LABEL467:
   jump                   LABEL487
LABEL468:
   iload                  7
   sload                  2
   sload                  0
   sconst                 "null"
   invoke                 4742
   iload                  9
   iload                  10
   iconst                 10682368
   iload                  8
   iload                  1
   iconst                 13
   iload                  7
   iload                  2
   iload                  3
   iconst                 65535
   iconst                 1
   invoke                 199
   add                   
   istore                 7
LABEL487:
   iload                  10
   if_clearops           
   iload                  18
   iconst                 3
   if_icmpeq              LABEL499
   iload                  18
   iconst                 6
   if_icmpeq              LABEL499
   iload                  18
   iconst                 7
   if_icmpeq              LABEL499
   jump                   LABEL533
LABEL499:
   iload                  14
   iconst                 1
   if_icmpeq              LABEL503
   jump                   LABEL508
LABEL503:
   iconst                 8
   sconst                 "Message"
   iload                  10
   if_setop              
   jump                   LABEL516
LABEL508:
   iconst                 8
   sconst                 "Add friend"
   iload                  10
   if_setop              
   iconst                 9
   sconst                 "Add ignore"
   iload                  10
   if_setop              
LABEL516:
   iconst                 10
   sconst                 "Report"
   iload                  10
   if_setop              
   sconst                 "<col=ffffff>"
   sload                  1
   sconst                 "</col>"
   join_string            3
   iload                  10
   if_setopbase          
   iconst                 88
   iconst                 -2147483644
   sconst                 "event_opbase"
   sconst                 "is"
   iload                  10
   if_setonop            
   jump                   LABEL537
LABEL533:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
LABEL537:
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
LABEL555:
   iload                  12
   chat_getprevuid       
   istore                 12
   jump                   LABEL337
LABEL559:
   iload                  10
   iconst                 -1
   if_icmpne              LABEL563
   jump                   LABEL646
LABEL563:
   iload                  10
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iload                  10
   if_setonmouseleave    
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  10
   if_setsize            
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   cc_find               
   iconst                 1
   if_icmpeq              LABEL591
   jump                   LABEL595
LABEL591:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL595:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 1
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL605
   jump                   LABEL609
LABEL605:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL609:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 2
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL619
   jump                   LABEL623
LABEL619:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL623:
   iconst                 10682368
   iload                  9
   iconst                 4
   multiply              
   iconst                 3
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL633
   jump                   LABEL635
LABEL633:
   iconst                 1
   cc_sethide            
LABEL635:
   iload                  9
   iconst                 1
   add                   
   istore                 9
   iconst                 105
   iconst                 73
   iconst                 580
   iload                  9
   enum                  
   istore                 10
   jump                   LABEL559
LABEL646:
   return                
