.id                 277
.int_stack_count    17
.string_stack_count 0
.int_var_count      36
.string_var_count   1
; callback "beforeBankLayout"
;   Fired before the bank starts its layout
;	Used by the TabInterface to hide fake bank items for tag tabs
;
; callback "skipBankLayout"
;	Fired before bank is built
;   Used by the TabInterface to show fake bank items for tag tabs
   sconst                 "beforeBankLayout"
   runelite_callback     
   get_varbit             5102
   iconst                 1
   if_icmpeq              LABEL4
   jump                   LABEL8
LABEL4:
   iconst                 0
   iload                  9
   if_sethide            
   jump                   LABEL13
LABEL8:
   iconst                 1
   iload                  9
   if_sethide            
   iload                  11
   invoke                 41
LABEL13:
   iconst                 0
   istore                 17
   get_varbit             5364
   iconst                 1
   if_icmpeq              LABEL19
   jump                   LABEL21
LABEL19:
   iconst                 1
   istore                 17
LABEL21:
   iload                  17
   iload                  14
   if_sethide            
   iload                  17
   iload                  15
   if_sethide            
   get_varbit             8352
   iconst                 1
   if_icmpeq              LABEL31
   jump                   LABEL34
LABEL31:
   iconst                 1
   istore                 17
   jump                   LABEL36
LABEL34:
   iconst                 0
   istore                 17
LABEL36:
   iload                  17
   iload                  12
   if_sethide            
   iload                  17
   iload                  13
   if_sethide            
   iload                  16
   invoke                 3369
   iconst                 3368
   iload                  16
   iconst                 1141
   iconst                 1
   sconst                 "IY"
   iload                  16
   if_setonvartransmit   
   iconst                 441
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  14
   if_setposition        
   iconst                 444
   iconst                 7
   iconst                 0
   iconst                 0
   iload                  15
   if_setposition        
   get_varbit             8352
   iconst                 1
   if_icmpeq              LABEL67
   jump                   LABEL94
LABEL67:
   get_varbit             5364
   iconst                 0
   if_icmpeq              LABEL71
   jump                   LABEL94
LABEL71:
   iload                  12
   if_getx               
   iload                  12
   if_gety               
   iconst                 0
   iconst                 0
   iload                  14
   if_setposition        
   iload                  13
   if_getx               
   iload                  13
   if_gety               
   iconst                 0
   iconst                 0
   iload                  15
   if_setposition        
   iconst                 37
   iconst                 37
   iconst                 1
   iconst                 0
   iload                  4
   if_setsize            
   jump                   LABEL130
LABEL94:
   get_varbit             8352
   iconst                 0
   if_icmpeq              LABEL98
   jump                   LABEL109
LABEL98:
   get_varbit             5364
   iconst                 1
   if_icmpeq              LABEL102
   jump                   LABEL109
LABEL102:
   iconst                 37
   iconst                 37
   iconst                 1
   iconst                 0
   iload                  4
   if_setsize            
   jump                   LABEL130
LABEL109:
   get_varbit             8352
   iconst                 1
   if_icmpeq              LABEL113
   jump                   LABEL124
LABEL113:
   get_varbit             5364
   iconst                 1
   if_icmpeq              LABEL117
   jump                   LABEL124
LABEL117:
   iconst                 74
   iconst                 37
   iconst                 1
   iconst                 0
   iload                  4
   if_setsize            
   jump                   LABEL130
LABEL124:
   iconst                 0
   iconst                 37
   iconst                 1
   iconst                 0
   iload                  4
   if_setsize            
LABEL130:
   iconst                 1
   iload                  10
   if_sethide            
   iload                  10
   cc_deleteall          
   iconst                 0
   istore                 18
   get_varbit             4170
   iconst                 3
   if_icmpeq              LABEL141
   jump                   LABEL174
LABEL141:
   get_varbit             4171
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4172
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4173
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4174
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4175
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4176
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4177
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4178
   iconst                 0
   if_icmpgt              LABEL169
   get_varbit             4179
   iconst                 0
   if_icmpgt              LABEL169
   jump                   LABEL172
LABEL169:
   iconst                 0
   istore                 18
   jump                   LABEL174
LABEL172:
   iconst                 1
   istore                 18
LABEL174:
   iconst                 0
   istore                 19
   iload                  18
   iconst                 1
   if_icmpeq              LABEL180
   jump                   LABEL204
LABEL180:
   iconst                 1
   iload                  8
   if_sethide            
   iconst                 2
   istore                 19
   iconst                 460
   iconst                 39
   iconst                 0
   iconst                 1
   iload                  2
   if_setsize            
   iconst                 16
   iconst                 39
   iconst                 0
   iconst                 1
   iload                  3
   if_setsize            
   iconst                 28
   iconst                 42
   iconst                 2
   iconst                 0
   iload                  1
   if_setposition        
   jump                   LABEL225
LABEL204:
   iconst                 0
   iload                  8
   if_sethide            
   iconst                 460
   iconst                 81
   iconst                 0
   iconst                 1
   iload                  2
   if_setsize            
   iconst                 16
   iconst                 81
   iconst                 0
   iconst                 1
   iload                  3
   if_setsize            
   iconst                 12
   iconst                 42
   iconst                 2
   iconst                 0
   iload                  1
   if_setposition        
LABEL225:
   iload                  3
   iload                  2
   invoke                 231
   iconst                 1200
   istore                 20
   iconst                 1200
   iconst                 9
   iconst                 3
   multiply              
   add                   
   istore                 21
LABEL236:
   iload                  20
   iload                  21
   if_icmple              LABEL240
   jump                   LABEL253
LABEL240:
   iload                  2
   iload                  20
   cc_find               
   iconst                 1
   if_icmpeq              LABEL246
   jump                   LABEL248
LABEL246:
   iconst                 1
   cc_sethide            
LABEL248:
   iload                  20
   iconst                 1
   add                   
   istore                 20
   jump                   LABEL236
LABEL253:
   iconst                 0
   istore                 20
   iconst                 8
   iconst                 1
   sub                   
   istore                 22
   iload                  2
   if_getwidth           
   iconst                 51
   sub                   
   iconst                 35
   sub                   
   istore                 23
   iload                  23
   iconst                 8
   iconst                 36
   multiply              
   sub                   
   iload                  22
   div                   
   istore                 24
   iconst                 -1
   istore                 25
   iconst                 0
   istore                 26
   iconst                 0
   istore                 27
   iconst                 0
   istore                 28
   iconst                 0
   istore                 29
   iconst                 -1
   istore                 30
   iconst                 0
   istore                 31
   sconst                 ""
   sstore                 0
   get_varbit             4150
   iconst                 0
   if_icmple              LABEL297
   get_varbit             4150
   iconst                 9
   if_icmpgt              LABEL297
   jump                   LABEL750
LABEL297:
   iload                  20
   iconst                 1200
   if_icmplt              LABEL301
   jump                   LABEL326
LABEL301:
   iload                  2
   iload                  20
   cc_find               
   iconst                 1
   if_icmpeq              LABEL307
   jump                   LABEL309
LABEL307:
   iconst                 1
   cc_sethide            
LABEL309:
   iconst                 95
   iload                  20
   inv_getobj            
   iconst                 -1
   if_icmpne              LABEL315
   jump                   LABEL321
LABEL315:
   iload                  29
   iconst                 1
   add                   
   iload                  20
   istore                 30
   istore                 29
LABEL321:
   iload                  20
   iconst                 1
   add                   
   istore                 20
   jump                   LABEL297
LABEL326:
   get_varbit             4171
   get_varbit             4172
   add                   
   get_varbit             4173
   add                   
   get_varbit             4174
   add                   
   get_varbit             4175
   add                   
   get_varbit             4176
   add                   
   get_varbit             4177
   add                   
   get_varbit             4178
   add                   
   get_varbit             4179
   add                   
   istore                 31
   iload                  31
   iconst                 0
   if_icmple              LABEL348
   jump                   LABEL352
LABEL348:
   iconst                 1200
   iconst                 1
   sub                   
   istore                 30
LABEL352:
   iconst                 0               	; Compare variable
   iconst                 0               	;
   sconst                 "skipBankLayout"   ; Show fake bank items for tag tabs
   runelite_callback     ; If tag tab menu search isn't active
   if_icmpeq              CONTINUE_SEARCH 	; continue to normal bank search
   jump                   GetTabRange     	; Skip normal bank layout
CONTINUE_SEARCH:
   iload                  31
   iload                  30
   iconst                 1
   add                   
   iconst                 0
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iconst                 0
   istore                 20
   get_varbit             4171
   iconst                 0
   if_icmpgt              LABEL379
   jump                   LABEL409
LABEL379:
   iconst                 1
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4171
   add                   
   iconst                 1
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4171
   add                   
   istore                 20
LABEL409:
   get_varbit             4172
   iconst                 0
   if_icmpgt              LABEL413
   jump                   LABEL443
LABEL413:
   iconst                 2
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4172
   add                   
   iconst                 2
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4172
   add                   
   istore                 20
LABEL443:
   get_varbit             4173
   iconst                 0
   if_icmpgt              LABEL447
   jump                   LABEL477
LABEL447:
   iconst                 3
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4173
   add                   
   iconst                 3
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4173
   add                   
   istore                 20
LABEL477:
   get_varbit             4174
   iconst                 0
   if_icmpgt              LABEL481
   jump                   LABEL511
LABEL481:
   iconst                 4
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4174
   add                   
   iconst                 4
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4174
   add                   
   istore                 20
LABEL511:
   get_varbit             4175
   iconst                 0
   if_icmpgt              LABEL515
   jump                   LABEL545
LABEL515:
   iconst                 5
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4175
   add                   
   iconst                 5
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4175
   add                   
   istore                 20
LABEL545:
   get_varbit             4176
   iconst                 0
   if_icmpgt              LABEL549
   jump                   LABEL579
LABEL549:
   iconst                 6
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4176
   add                   
   iconst                 6
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4176
   add                   
   istore                 20
LABEL579:
   get_varbit             4177
   iconst                 0
   if_icmpgt              LABEL583
   jump                   LABEL613
LABEL583:
   iconst                 7
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4177
   add                   
   iconst                 7
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4177
   add                   
   istore                 20
LABEL613:
   get_varbit             4178
   iconst                 0
   if_icmpgt              LABEL617
   jump                   LABEL647
LABEL617:
   iconst                 8
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4178
   add                   
   iconst                 8
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4178
   add                   
   istore                 20
LABEL647:
   get_varbit             4179
   iconst                 0
   if_icmpgt              LABEL651
   jump                   LABEL681
LABEL651:
   iconst                 9
   iload                  2
   iload                  28
   invoke                 510
   istore                 19
   iload                  20
   iload                  20
   get_varbit             4179
   add                   
   iconst                 9
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   iload                  19
   iload                  24
   iload                  22
   iload                  18
   invoke                 509
   istore                 26
   istore                 28
   iload                  27
   iload                  26
   add                   
   istore                 27
   iload                  20
   get_varbit             4179
   add                   
   istore                 20
LABEL681:
   invoke                 514
   iconst                 1
   if_icmpeq              LABEL685
   jump                   LABEL726
LABEL685:
   get_varc_string        359                ; Skip truncating of varcstr 22 by not calling 280
   lowercase             ; instead get the var directly and lowercase it
   sstore                 0
   sload                  0
   string_length         
   iconst                 0
   if_icmpgt              LABEL692
   jump                   LABEL711
LABEL692:
   sconst                 "Showing items: "
   sconst                 "<col=ff0000>"
   sload                  0
   sconst                 "</col>"
   join_string            4
   iload                  5
   if_settext            
   get_varc_int           5
   iconst                 11
   if_icmpeq              LABEL703
   jump                   LABEL710
LABEL703:
   sconst                 "Show items whose names contain the following text: ("
   iload                  27
   tostring              
   sconst                 " found)"
   join_string            3
   iload                  27                             ; load number of matches
   sconst                 "setSearchBankInputTextFound"  ; load event name
   runelite_callback     ; invoke callback
   pop_int               ; pop number of matches
   iconst                 ********
   if_settext            
LABEL710:
   jump                   LABEL725
LABEL711:
   sconst                 "Showing items: "
   sconst                 "<col=ff0000>"
   sconst                 "*"
   sconst                 "</col>"
   join_string            4
   iload                  5
   if_settext            
   get_varc_int           5
   iconst                 11
   if_icmpeq              LABEL722
   jump                   LABEL725
LABEL722:
   sconst                 "Show items whose names contain the following text:"
   sconst                 "setSearchBankInputText"  ; load event name
   runelite_callback     ; invoke callback
   iconst                 ********
   if_settext            
LABEL725:
   jump                   LABEL729
LABEL726:
   sconst                 "The Bank of Gielinor"
   iload                  5
   if_settext            
LABEL729:
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   iload                  9
   iload                  10
   iload                  11
   iload                  28
   iload                  29
   iload                  12
   iload                  13
   iload                  14
   iload                  15
   iload                  16
   invoke                 505 ; [proc,bankmain_finishbuilding]
   return                
LABEL750:
   invoke                 514
   iconst                 1
   if_icmpeq              LABEL754
   jump                   LABEL758
LABEL754:
   iconst                 1
   iconst                 1
   iconst                 1
   invoke                 299
GetTabRange:
LABEL758:
   iconst                 -1
   istore                 32
   iconst                 -1
   istore                 33
   get_varbit             4150
   invoke                 513
   istore                 33
   istore                 32
   iconst                 0
   istore                 34
   iconst                 0
   istore                 35
LABEL770:
   iload                  20
   iconst                 1200
   if_icmplt              LABEL774
   jump                   LABEL854
LABEL774:
   iload                  2
   iload                  20
   cc_find               
   iconst                 1
   if_icmpeq              LABEL780
   jump                   LABEL849
LABEL780:
   iconst                 95
   iload                  20
   inv_getobj            
   istore                 25
   iload                  25
   iconst                 -1
   if_icmpne              LABEL788
   jump                   LABEL792
LABEL788:
   iload                  29
   iconst                 1
   add                   
   istore                 29
LABEL792:
   iload                  20
   iload                  32
   if_icmpge              LABEL796
   jump                   LABEL847
LABEL796:
   iload                  20
   iload                  33
   if_icmplt              LABEL800
   jump                   LABEL847
LABEL800:
   iconst                 0
   cc_sethide            
   iload                  25
   iconst                 95
   iload                  20
   inv_getnum            
   iload                  2
   iload                  3
   iload                  9
   iload                  10
   iload                  11
   invoke                 278
   iload                  35
   iconst                 36
   multiply              
   istore                 28
   iconst                 51
   iload                  34
   iconst                 36
   iload                  24
   add                   
   multiply              
   add                   
   iload                  28
   iconst                 0
   iconst                 0
   cc_setposition        
   iload                  28
   iconst                 32
   add                   
   istore                 28
   iload                  34
   iload                  22
   if_icmplt              LABEL835
   jump                   LABEL840
LABEL835:
   iload                  34
   iconst                 1
   add                   
   istore                 34
   jump                   LABEL846
LABEL840:
   iconst                 0
   iload                  35
   iconst                 1
   add                   
   istore                 35
   istore                 34
LABEL846:
   jump                   LABEL849
LABEL847:
   iconst                 1
   cc_sethide            
LABEL849:
   iload                  20
   iconst                 1
   add                   
   istore                 20
   jump                   LABEL770
LABEL854:
   get_varbit             4170
   iconst                 2
   if_icmpeq              LABEL858
   jump                   LABEL868
LABEL858:
   sconst                 "Tab "
   iconst                 105
   iconst                 115
   iconst                 207
   get_varbit             4150
   enum                  
   join_string            2
   iload                  5
   if_settext            
   jump                   LABEL874
LABEL868:
   sconst                 "Tab "
   get_varbit             4150
   tostring              
   join_string            2
   iload                  5
   if_settext            
FinishBuilding:
LABEL874:
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   iload                  9
   iload                  10
   iload                  11
   iload                  28
   iload                  29
   iload                  12
   iload                  13
   iload                  14
   iload                  15
   iload                  16
   invoke                 505
   return                
