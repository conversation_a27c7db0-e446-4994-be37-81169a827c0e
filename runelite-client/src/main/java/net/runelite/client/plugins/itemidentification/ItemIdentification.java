/*
 * Copyright (c) 2019, Hydrox6 <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.itemidentification;

import com.google.common.collect.ImmutableMap;
import java.util.Map;
import java.util.function.Predicate;
import lombok.AllArgsConstructor;
import net.runelite.api.ItemID;

enum ItemIdentification
{
	//Seeds
	GUAM_SEED(Type.SEED_HERB, "Guam", "G", ItemID.GUAM_SEED),
	MARRENTILL_SEED(Type.SEED_HERB, "Marren", "M", ItemID.MARRENTILL_SEED),
	TARROMIN_SEED(Type.SEED_HERB, "Tarro", "TAR", ItemID.TARROMIN_SEED),
	HARRALANDER_SEED(Type.SEED_HERB, "Harra", "H", ItemID.HARRALANDER_SEED),
	RANARR_SEED(Type.SEED_HERB, "Ranarr", "R", ItemID.RANARR_SEED),
	TOADFLAX_SEED(Type.SEED_HERB, "Toad", "TOA", ItemID.TOADFLAX_SEED),
	IRIT_SEED(Type.SEED_HERB, "Irit", "I", ItemID.IRIT_SEED),
	AVANTOE_SEED(Type.SEED_HERB, "Avan", "A", ItemID.AVANTOE_SEED),
	KWUARM_SEED(Type.SEED_HERB, "Kwuarm", "K", ItemID.KWUARM_SEED),
	SNAPDRAGON_SEED(Type.SEED_HERB, "Snap", "S", ItemID.SNAPDRAGON_SEED),
	CADANTINE_SEED(Type.SEED_HERB, "Cadan", "C", ItemID.CADANTINE_SEED),
	LANTADYME_SEED(Type.SEED_HERB, "Lanta", "L", ItemID.LANTADYME_SEED),
	DWARF_WEED_SEED(Type.SEED_HERB, "Dwarf", "D", ItemID.DWARF_WEED_SEED),
	TORSTOL_SEED(Type.SEED_HERB, "Torstol", "TOR", ItemID.TORSTOL_SEED),

	REDBERRY_SEED(Type.SEED_BERRY, "Redberry", "RED", ItemID.REDBERRY_SEED),
	CADAVABERRY_SEED(Type.SEED_BERRY, "Cadava", "CAD", ItemID.CADAVABERRY_SEED),
	DWELLBERRY_SEED(Type.SEED_BERRY, "Dwell", "DWEL", ItemID.DWELLBERRY_SEED),
	JANGERBERRY_SEED(Type.SEED_BERRY, "Janger", "JANG", ItemID.JANGERBERRY_SEED),
	WHITEBERRY_SEED(Type.SEED_BERRY, "White", "WHIT", ItemID.WHITEBERRY_SEED),
	POISON_IVY_SEED(Type.SEED_BERRY, "Ivy", "POI", ItemID.POISON_IVY_SEED),

	GRAPE_SEED(Type.SEED_SPECIAL, "Grape", "GRA", ItemID.GRAPE_SEED),
	MUSHROOM_SPORE(Type.SEED_SPECIAL, "Mushroom", "MUSH", ItemID.MUSHROOM_SPORE),
	BELLADONNA_SEED(Type.SEED_SPECIAL, "Bella", "BELL", ItemID.BELLADONNA_SEED),
	SEAWEED_SPORE(Type.SEED_SPECIAL, "Seaweed", "SW", ItemID.SEAWEED_SPORE),
	HESPORI_SEED(Type.SEED_SPECIAL, "Hespori", "HES", ItemID.HESPORI_SEED),
	KRONOS_SEED(Type.SEED_SPECIAL, "Kronos", "KRO", ItemID.KRONOS_SEED),
	IASOR_SEED(Type.SEED_SPECIAL, "Iasor", "IA", ItemID.IASOR_SEED),
	ATTAS_SEED(Type.SEED_SPECIAL, "Attas", "AT", ItemID.ATTAS_SEED),
	CACTUS_SEED(Type.SEED_SPECIAL, "Cactus", "CAC", ItemID.CACTUS_SEED),
	POTATO_CACTUS_SEED(Type.SEED_SPECIAL, "P.Cact", "P.CAC", ItemID.POTATO_CACTUS_SEED),

	ACORN(Type.SEED_TREE, "Oak", "OAK", ItemID.ACORN),
	WILLOW_SEED(Type.SEED_TREE, "Willow", "WIL", ItemID.WILLOW_SEED),
	MAPLE_SEED(Type.SEED_TREE, "Maple", "MAP", ItemID.MAPLE_SEED),
	YEW_SEED(Type.SEED_TREE, "Yew", "YEW", ItemID.YEW_SEED),
	MAGIC_SEED(Type.SEED_TREE, "Magic", "MAG", ItemID.MAGIC_SEED),
	REDWOOD_SEED(Type.SEED_TREE, "Red", "RED", ItemID.REDWOOD_TREE_SEED),
	TEAK_SEED(Type.SEED_TREE, "Teak", "TEAK", ItemID.TEAK_SEED),
	MAHOGANY_SEED(Type.SEED_TREE, "Mahog", "MAH", ItemID.MAHOGANY_SEED),
	CRYSTAL_ACORN(Type.SEED_TREE, "Crystal", "CRY", ItemID.CRYSTAL_ACORN),
	CELASTRUS_SEED(Type.SEED_TREE, "Celas", "CEL", ItemID.CELASTRUS_SEED),
	SPIRIT_SEED(Type.SEED_TREE, "Spirit", "SPI", ItemID.SPIRIT_SEED),
	CALQUAT_SEED(Type.SEED_TREE, "Calquat", "CAL", ItemID.CALQUAT_TREE_SEED),

	APPLE_TREE_SEED(Type.SEED_FRUIT_TREE, "Apple", "APP", ItemID.APPLE_TREE_SEED),
	BANANA_TREE_SEED(Type.SEED_FRUIT_TREE, "Banana", "BAN", ItemID.BANANA_TREE_SEED),
	ORANGE_TREE_SEED(Type.SEED_FRUIT_TREE, "Orange", "ORA", ItemID.ORANGE_TREE_SEED),
	CURRY_TREE_SEED(Type.SEED_FRUIT_TREE, "Curry", "CUR", ItemID.CURRY_TREE_SEED),
	PINEAPPLE_SEED(Type.SEED_FRUIT_TREE, "Pine.A", "PINE", ItemID.PINEAPPLE_SEED),
	PAPAYA_TREE_SEED(Type.SEED_FRUIT_TREE, "Papaya", "PAPA", ItemID.PAPAYA_TREE_SEED),
	PALM_TREE_SEED(Type.SEED_FRUIT_TREE, "Palm", "PALM", ItemID.PALM_TREE_SEED),
	DRAGONFRIUT_TREE_SEED(Type.SEED_FRUIT_TREE, "Dragon", "DRA", ItemID.DRAGONFRUIT_TREE_SEED),

	POTATO_SEED(Type.SEED_ALLOTMENT, "Potato", "POT", ItemID.POTATO_SEED),
	ONION_SEED(Type.SEED_ALLOTMENT, "Onion", "ONI", ItemID.ONION_SEED),
	CABBAGE_SEED(Type.SEED_ALLOTMENT, "Cabbage", "CAB", ItemID.CABBAGE_SEED),
	TOMATO_SEED(Type.SEED_ALLOTMENT, "Tomato", "TOM", ItemID.TOMATO_SEED),
	SWEETCORN_SEED(Type.SEED_ALLOTMENT, "S.Corn", "CORN", ItemID.SWEETCORN_SEED),
	STRAWBERRY_SEED(Type.SEED_ALLOTMENT, "S.Berry", "STRA", ItemID.STRAWBERRY_SEED),
	WATERMELON_SEED(Type.SEED_ALLOTMENT, "Melon", "MEL", ItemID.WATERMELON_SEED),
	SNAPE_GRASS_SEED(Type.SEED_ALLOTMENT, "Snape", "SNA", ItemID.SNAPE_GRASS_SEED),

	MARIGOLD_SEED(Type.SEED_FLOWER, "Marigo", "MARI", ItemID.MARIGOLD_SEED),
	ROSEMARY_SEED(Type.SEED_FLOWER, "Rosemar", "ROSE", ItemID.ROSEMARY_SEED),
	NASTURTIUM_SEED(Type.SEED_FLOWER, "Nastur", "NAS", ItemID.NASTURTIUM_SEED),
	WOAD_SEED(Type.SEED_FLOWER, "Woad", "WOAD", ItemID.WOAD_SEED),
	LIMPWURT_SEED(Type.SEED_FLOWER, "Limpwurt", "LIMP", ItemID.LIMPWURT_SEED),
	WHITE_LILY_SEED(Type.SEED_FLOWER, "Lily", "LILY", ItemID.WHITE_LILY_SEED),

	BARLEY_SEED(Type.HOPS_SEED, "Barley", "BAR", ItemID.BARLEY_SEED),
	HAMMERSTONE_SEED(Type.HOPS_SEED, "Hammer", "HAMM", ItemID.HAMMERSTONE_SEED),
	ASGARNIAN_SEED(Type.HOPS_SEED, "Asgar", "ASG", ItemID.ASGARNIAN_SEED),
	JUTE_SEED(Type.HOPS_SEED, "Jute", "JUTE", ItemID.JUTE_SEED),
	YANILLIAN_SEED(Type.HOPS_SEED, "Yani", "YAN", ItemID.YANILLIAN_SEED),
	KRANDORIAN_SEED(Type.HOPS_SEED, "Krand", "KRA", ItemID.KRANDORIAN_SEED),
	WILDBLOOD_SEED(Type.HOPS_SEED, "Wild.B", "WILD", ItemID.WILDBLOOD_SEED),

	//Sacks
	SACK(Type.SACK, "Empty", "EMP", ItemID.EMPTY_SACK),
	CABBAGE_SACK(Type.SACK, "Cabbage", "CAB", ItemID.CABBAGES1, ItemID.CABBAGES2, ItemID.CABBAGES3, ItemID.CABBAGES4, ItemID.CABBAGES5, ItemID.CABBAGES6, ItemID.CABBAGES7, ItemID.CABBAGES8, ItemID.CABBAGES9, ItemID.CABBAGES10),
	ONION_SACK(Type.SACK, "Onion", "ONI", ItemID.ONIONS1, ItemID.ONIONS2, ItemID.ONIONS3, ItemID.ONIONS4, ItemID.ONIONS5, ItemID.ONIONS6, ItemID.ONIONS7, ItemID.ONIONS8, ItemID.ONIONS9, ItemID.ONIONS10),
	POTATO_SACK(Type.SACK, "Potato", "POT", ItemID.POTATOES1, ItemID.POTATOES2, ItemID.POTATOES3, ItemID.POTATOES4, ItemID.POTATOES5, ItemID.POTATOES6, ItemID.POTATOES7, ItemID.POTATOES8, ItemID.POTATOES9, ItemID.POTATOES10),

	//Herbs
	GUAM(Type.HERB, "Guam", "G", ItemID.GUAM_LEAF, ItemID.GRIMY_GUAM_LEAF),
	MARRENTILL(Type.HERB, "Marren", "M", ItemID.MARRENTILL, ItemID.GRIMY_MARRENTILL),
	TARROMIN(Type.HERB, "Tarro", "TAR", ItemID.TARROMIN, ItemID.GRIMY_TARROMIN),
	HARRALANDER(Type.HERB, "Harra", "H", ItemID.HARRALANDER, ItemID.GRIMY_HARRALANDER),
	RANARR(Type.HERB, "Ranarr", "R", ItemID.RANARR_WEED, ItemID.GRIMY_RANARR_WEED),
	TOADFLAX(Type.HERB, "Toad", "TOA", ItemID.TOADFLAX, ItemID.GRIMY_TOADFLAX),
	IRIT(Type.HERB, "Irit", "I", ItemID.IRIT_LEAF, ItemID.GRIMY_IRIT_LEAF),
	AVANTOE(Type.HERB, "Avan", "A", ItemID.AVANTOE, ItemID.GRIMY_AVANTOE),
	KWUARM(Type.HERB, "Kwuarm", "K", ItemID.KWUARM, ItemID.GRIMY_KWUARM),
	SNAPDRAGON(Type.HERB, "Snap", "S", ItemID.SNAPDRAGON, ItemID.GRIMY_SNAPDRAGON),
	CADANTINE(Type.HERB, "Cadan", "C", ItemID.CADANTINE, ItemID.GRIMY_CADANTINE),
	LANTADYME(Type.HERB, "Lanta", "L", ItemID.LANTADYME, ItemID.GRIMY_LANTADYME),
	DWARF_WEED(Type.HERB, "Dwarf", "D", ItemID.DWARF_WEED, ItemID.GRIMY_DWARF_WEED),
	TORSTOL(Type.HERB, "Torstol", "TOR", ItemID.TORSTOL, ItemID.GRIMY_TORSTOL),

	ARDRIGAL(Type.HERB, "Ardrig", "ARD", ItemID.ARDRIGAL, ItemID.GRIMY_ARDRIGAL),
	ROGUES_PURSE(Type.HERB, "Rogue", "ROG", ItemID.ROGUES_PURSE, ItemID.GRIMY_ROGUES_PURSE),
	SITO_FOIL(Type.HERB, "Sito", "SF", ItemID.SITO_FOIL, ItemID.GRIMY_SITO_FOIL),
	SNAKE_WEED(Type.HERB, "Snake", "SW", ItemID.SNAKE_WEED, ItemID.GRIMY_SNAKE_WEED),
	VOLENCIA_MOSS(Type.HERB, "Volenc", "V", ItemID.VOLENCIA_MOSS, ItemID.GRIMY_VOLENCIA_MOSS),
	
	//Logs
	RED_LOGS(Type.LOGS, "Red", "RED", ItemID.RED_LOGS),
	GREEN_LOGS(Type.LOGS, "Green", "GRE", ItemID.GREEN_LOGS),
	BLUE_LOGS(Type.LOGS, "Blue", "BLU", ItemID.BLUE_LOGS),
	WHITE_LOGS(Type.LOGS, "White", "WHI", ItemID.WHITE_LOGS),
	PURPLE_LOGS(Type.LOGS, "Purple", "PUR", ItemID.PURPLE_LOGS),

	SCRAPEY_TREE_LOGS(Type.LOGS, "Scrapey", "SCRAP", ItemID.SCRAPEY_TREE_LOGS),

	LOG(Type.LOGS, "Log", "LOG", ItemID.LOGS),
	ACHEY_TREE_LOG(Type.LOGS, "Achey", "ACH", ItemID.ACHEY_TREE_LOGS),
	OAK_LOG(Type.LOGS, "Oak", "OAK", ItemID.OAK_LOGS),
	WILLOW_LOG(Type.LOGS, "Willow", "WIL", ItemID.WILLOW_LOGS),
	TEAK_LOG(Type.LOGS, "Teak", "TEAK", ItemID.TEAK_LOGS),
	JUNIPER_LOG(Type.LOGS, "Juniper", "JUN", ItemID.JUNIPER_LOGS),
	MAPLE_LOG(Type.LOGS, "Maple", "MAP", ItemID.MAPLE_LOGS),
	MAHOGANY_LOG(Type.LOGS, "Mahog", "MAH", ItemID.MAHOGANY_LOGS),
	ARCTIC_PINE_LOG(Type.LOGS, "Arctic", "ARC", ItemID.ARCTIC_PINE_LOGS),
	YEW_LOG(Type.LOGS, "Yew", "YEW", ItemID.YEW_LOGS),
	BLISTERWOOD_LOG(Type.LOGS, "Blister", "BLI", ItemID.BLISTERWOOD_LOGS),
	MAGIC_LOG(Type.LOGS, "Magic", "MAG", ItemID.MAGIC_LOGS),
	REDWOOD_LOG(Type.LOGS, "Red", "RED", ItemID.REDWOOD_LOGS),

	PYRE_LOGS(Type.LOGS_PYRE, "Pyre", "P", ItemID.PYRE_LOGS),
	ARCTIC_PYRE_LOGS(Type.LOGS_PYRE, "Art P", "AP", ItemID.ARCTIC_PYRE_LOGS),
	OAK_PYRE_LOGS(Type.LOGS_PYRE, "Oak P", "OAKP", ItemID.OAK_PYRE_LOGS),
	WILLOW_PYRE_LOGS(Type.LOGS_PYRE, "Wil P", "WILP", ItemID.WILLOW_PYRE_LOGS),
	TEAK_PYRE_LOGS(Type.LOGS_PYRE, "Teak P", "TEAKP", ItemID.TEAK_PYRE_LOGS),
	MAPLE_PYRE_LOGS(Type.LOGS_PYRE, "Map P", "MAPP", ItemID.MAPLE_PYRE_LOGS),
	MAHOGANY_PYRE_LOGS(Type.LOGS_PYRE, "Mah P", "MAHP", ItemID.MAHOGANY_PYRE_LOGS),
	YEW_PYRE_LOGS(Type.LOGS_PYRE, "Yew P", "YEWP", ItemID.YEW_PYRE_LOGS),
	MAGIC_PYRE_LOGS(Type.LOGS_PYRE, "Mag P", "MAGP", ItemID.MAGIC_PYRE_LOGS),
	REDWOOD_PYRE_LOGS(Type.LOGS_PYRE, "Red P", "REDP", ItemID.REDWOOD_PYRE_LOGS),

	//Planks
	PLANK(Type.PLANK, "Plank", "PLANK", ItemID.PLANK),
	OAK_PLANK(Type.PLANK, "Oak", "OAK", ItemID.OAK_PLANK),
	TEAK_PLANK(Type.PLANK, "Teak", "TEAK", ItemID.TEAK_PLANK),
	MAHOGANY_PLANK(Type.PLANK, "Mahog", "MAH", ItemID.MAHOGANY_PLANK),
	WAXWOOD_PLANK(Type.PLANK, "Wax", "WAX", ItemID.WAXWOOD_PLANK),
	MALLIGNUM_ROOT_PLANK(Type.PLANK, "Mallig", "MALL", ItemID.MALLIGNUM_ROOT_PLANK),

	//Saplings
	OAK_SAPLING(Type.SAPLING, "Oak", "OAK", ItemID.OAK_SAPLING, ItemID.OAK_SEEDLING, ItemID.OAK_SEEDLING_W),
	WILLOW_SAPLING(Type.SAPLING, "Willow", "WIL", ItemID.WILLOW_SAPLING, ItemID.WILLOW_SEEDLING, ItemID.WILLOW_SEEDLING_W),
	MAPLE_SAPLING(Type.SAPLING, "Maple", "MAP", ItemID.MAPLE_SAPLING, ItemID.MAPLE_SEEDLING, ItemID.MAPLE_SEEDLING_W),
	YEW_SAPLING(Type.SAPLING, "Yew", "YEW", ItemID.YEW_SAPLING, ItemID.YEW_SEEDLING, ItemID.YEW_SEEDLING_W),
	MAGIC_SAPLING(Type.SAPLING, "Magic", "MAG", ItemID.MAGIC_SAPLING, ItemID.MAGIC_SEEDLING, ItemID.MAGIC_SEEDLING_W),
	REDWOOD_SAPLING(Type.SAPLING, "Red", "RED", ItemID.REDWOOD_SAPLING, ItemID.REDWOOD_SEEDLING, ItemID.REDWOOD_SEEDLING_W),
	SPIRIT_SAPLING(Type.SAPLING, "Spirit", "SPI", ItemID.SPIRIT_SAPLING, ItemID.SPIRIT_SEEDLING, ItemID.SPIRIT_SEEDLING_W),
	CRYSTAL_SAPLING(Type.SAPLING, "Crystal", "CRY", ItemID.CRYSTAL_SAPLING, ItemID.CRYSTAL_SEEDLING, ItemID.CRYSTAL_SEEDLING_W),

	APPLE_SAPLING(Type.SAPLING, "Apple", "APP", ItemID.APPLE_SAPLING, ItemID.APPLE_SEEDLING, ItemID.APPLE_SEEDLING_W),
	BANANA_SAPLING(Type.SAPLING, "Banana", "BAN", ItemID.BANANA_SAPLING, ItemID.BANANA_SEEDLING, ItemID.BANANA_SEEDLING_W),
	ORANGE_SAPLING(Type.SAPLING, "Orange", "ORA", ItemID.ORANGE_SAPLING, ItemID.ORANGE_SEEDLING, ItemID.ORANGE_SEEDLING_W),
	CURRY_SAPLING(Type.SAPLING, "Curry", "CUR", ItemID.CURRY_SAPLING, ItemID.CURRY_SEEDLING, ItemID.CURRY_SEEDLING_W),
	PINEAPPLE_SAPLING(Type.SAPLING, "Pine", "PINE", ItemID.PINEAPPLE_SAPLING, ItemID.PINEAPPLE_SEEDLING, ItemID.PINEAPPLE_SEEDLING_W),
	PAPAYA_SAPLING(Type.SAPLING, "Papaya", "PAP", ItemID.PAPAYA_SAPLING, ItemID.PAPAYA_SEEDLING, ItemID.PAPAYA_SEEDLING_W),
	PALM_SAPLING(Type.SAPLING, "Palm", "PALM", ItemID.PALM_SAPLING, ItemID.PALM_SEEDLING, ItemID.PALM_SEEDLING_W),
	DRAGONFRUIT_SAPLING(Type.SAPLING, "Dragon", "DRAG", ItemID.DRAGONFRUIT_SAPLING, ItemID.DRAGONFRUIT_SEEDLING, ItemID.DRAGONFRUIT_SEEDLING_W),

	TEAK_SAPLING(Type.SAPLING, "Teak", "TEAK", ItemID.TEAK_SAPLING, ItemID.TEAK_SEEDLING, ItemID.TEAK_SEEDLING_W),
	MAHOGANY_SAPLING(Type.SAPLING, "Mahog", "MAHOG", ItemID.MAHOGANY_SAPLING, ItemID.MAHOGANY_SEEDLING, ItemID.MAHOGANY_SEEDLING_W),
	CALQUAT_SAPLING(Type.SAPLING, "Calquat", "CALQ", ItemID.CALQUAT_SAPLING, ItemID.CALQUAT_SEEDLING, ItemID.CALQUAT_SEEDLING_W),
	CELASTRUS_SAPLING(Type.SAPLING, "Celas", "CEL", ItemID.CELASTRUS_SAPLING, ItemID.CELASTRUS_SEEDLING, ItemID.CELASTRUS_SEEDLING_W),

	//Compost
	COMPOST(Type.COMPOST, "Compost", "COM", ItemID.COMPOST),
	SUPERCOMPOST(Type.COMPOST, "Sup Com", "SCOM", ItemID.SUPERCOMPOST),
	ULTRACOMPOST(Type.COMPOST, "Ult Com", "UCOM", ItemID.ULTRACOMPOST),

	//Ores
	COPPER_ORE(Type.ORE, "Copper", "COP", ItemID.COPPER_ORE),
	TIN_ORE(Type.ORE, "Tin", "TIN", ItemID.TIN_ORE),
	IRON_ORE(Type.ORE, "Iron", "IRO", ItemID.IRON_ORE),
	SILVER_ORE(Type.ORE, "Silver", "SIL", ItemID.SILVER_ORE),
	COAL_ORE(Type.ORE, "Coal", "COA", ItemID.COAL),
	GOLD_ORE(Type.ORE, "Gold", "GOL", ItemID.GOLD_ORE),
	MITHRIL_ORE(Type.ORE, "Mithril", "MIT", ItemID.MITHRIL_ORE),
	ADAMANTITE_ORE(Type.ORE, "Adaman", "ADA", ItemID.ADAMANTITE_ORE),
	RUNITE_ORE(Type.ORE, "Runite", "RUN", ItemID.RUNITE_ORE),

	RUNE_ESSENCE(Type.ORE, "R.Ess", "R.E.", ItemID.RUNE_ESSENCE),
	PURE_ESSENCE(Type.ORE, "P.Ess", "P.E.", ItemID.PURE_ESSENCE),

	PAYDIRT(Type.ORE, "Paydirt", "PAY", ItemID.PAYDIRT),
	AMETHYST(Type.ORE, "Amethy", "AME", ItemID.AMETHYST),
	LOVAKITE_ORE(Type.ORE, "Lovakit", "LOV", ItemID.LOVAKITE_ORE),
	BLURITE_ORE(Type.ORE, "Blurite", "BLU", ItemID.BLURITE_ORE),
	ELEMENTAL_ORE(Type.ORE, "Element", "ELE", ItemID.ELEMENTAL_ORE),
	DAEYALT_ORE(Type.ORE, "Daeyalt", "DAE", ItemID.DAEYALT_ORE),
	LUNAR_ORE(Type.ORE, "Lunar", "LUN", ItemID.LUNAR_ORE),

	//Bars
	BRONZE_BAR(Type.BAR, "Bronze", "BRO", ItemID.BRONZE_BAR),
	IRON_BAR(Type.BAR, "Iron", "IRO", ItemID.IRON_BAR),
	SILVER_BAR(Type.BAR, "Silver", "SIL", ItemID.SILVER_BAR),
	STEEL_BAR(Type.BAR, "Steel", "STE", ItemID.STEEL_BAR),
	GOLD_BAR(Type.BAR, "Gold", "GOL", ItemID.GOLD_BAR),
	MITHRIL_BAR(Type.BAR, "Mithril", "MIT", ItemID.MITHRIL_BAR),
	ADAMANTITE_BAR(Type.BAR, "Adaman", "ADA", ItemID.ADAMANTITE_BAR),
	RUNITE_BAR(Type.BAR, "Runite", "RUN", ItemID.RUNITE_BAR),

	//Gems
	SAPPHIRE(Type.GEM, "Sapphir", "S", ItemID.UNCUT_SAPPHIRE, ItemID.SAPPHIRE),
	EMERALD(Type.GEM, "Emerald", "E", ItemID.UNCUT_EMERALD, ItemID.EMERALD),
	RUBY(Type.GEM, "Ruby", "R", ItemID.UNCUT_RUBY, ItemID.RUBY),
	DIAMOND(Type.GEM, "Diamon", "DI", ItemID.UNCUT_DIAMOND, ItemID.DIAMOND),
	OPAL(Type.GEM, "Opal", "OP", ItemID.UNCUT_OPAL, ItemID.OPAL),
	JADE(Type.GEM, "Jade", "J", ItemID.UNCUT_JADE, ItemID.JADE),
	RED_TOPAZ(Type.GEM, "Topaz", "T", ItemID.UNCUT_RED_TOPAZ, ItemID.RED_TOPAZ),
	DRAGONSTONE(Type.GEM, "Dragon", "DR", ItemID.UNCUT_DRAGONSTONE, ItemID.DRAGONSTONE),
	ONYX(Type.GEM, "Onyx", "ON", ItemID.UNCUT_ONYX, ItemID.ONYX),
	ZENYTE(Type.GEM, "Zenyte", "Z", ItemID.UNCUT_ZENYTE, ItemID.ZENYTE),
	SHADOW_DIAMOND(Type.GEM, "Shadow", "SHD", ItemID.SHADOW_DIAMOND),
	BLOOD_DIAMOND(Type.GEM, "Blood", "BD", ItemID.BLOOD_DIAMOND),
	ICE_DIAMOND(Type.GEM, "Ice", "ID", ItemID.ICE_DIAMOND),
	SMOKE_DIAMOND(Type.GEM, "Smoke", "SMD", ItemID.SMOKE_DIAMOND),

	// Potions
	ATTACK(Type.POTION, "Att", "A", ItemID.ATTACK_POTION4, ItemID.ATTACK_POTION3, ItemID.ATTACK_POTION2, ItemID.ATTACK_POTION1),
	STRENGTH(Type.POTION, "Str", "S", ItemID.STRENGTH_POTION4, ItemID.STRENGTH_POTION3, ItemID.STRENGTH_POTION2, ItemID.STRENGTH_POTION1),
	DEFENCE(Type.POTION, "Def", "D", ItemID.DEFENCE_POTION4, ItemID.DEFENCE_POTION3, ItemID.DEFENCE_POTION2, ItemID.DEFENCE_POTION1),
	COMBAT(Type.POTION, "Com", "C", ItemID.COMBAT_POTION4, ItemID.COMBAT_POTION3, ItemID.COMBAT_POTION2, ItemID.COMBAT_POTION1),
	MAGIC(Type.POTION, "Magic", "M", ItemID.MAGIC_POTION4, ItemID.MAGIC_POTION3, ItemID.MAGIC_POTION2, ItemID.MAGIC_POTION1),
	RANGING(Type.POTION, "Range", "R", ItemID.RANGING_POTION4, ItemID.RANGING_POTION3, ItemID.RANGING_POTION2, ItemID.RANGING_POTION1),
	BASTION(Type.POTION, "Bastion", "B", ItemID.BASTION_POTION4, ItemID.BASTION_POTION3, ItemID.BASTION_POTION2, ItemID.BASTION_POTION1),
	BATTLEMAGE(Type.POTION, "BatMage", "B.M", ItemID.BATTLEMAGE_POTION4, ItemID.BATTLEMAGE_POTION3, ItemID.BATTLEMAGE_POTION2, ItemID.BATTLEMAGE_POTION1),

	SUPER_ATTACK(Type.POTION, "S.Att", "S.A", ItemID.SUPER_ATTACK4, ItemID.SUPER_ATTACK3, ItemID.SUPER_ATTACK2, ItemID.SUPER_ATTACK1),
	SUPER_STRENGTH(Type.POTION, "S.Str", "S.S", ItemID.SUPER_STRENGTH4, ItemID.SUPER_STRENGTH3, ItemID.SUPER_STRENGTH2, ItemID.SUPER_STRENGTH1),
	SUPER_DEFENCE(Type.POTION, "S.Def", "S.D", ItemID.SUPER_DEFENCE4, ItemID.SUPER_DEFENCE3, ItemID.SUPER_DEFENCE2, ItemID.SUPER_DEFENCE1),
	SUPER_COMBAT(Type.POTION, "S.Com", "S.C", ItemID.SUPER_COMBAT_POTION4, ItemID.SUPER_COMBAT_POTION3, ItemID.SUPER_COMBAT_POTION2, ItemID.SUPER_COMBAT_POTION1),
	SUPER_RANGING(Type.POTION, "S.Range", "S.Ra", ItemID.SUPER_RANGING_4, ItemID.SUPER_RANGING_3, ItemID.SUPER_RANGING_2, ItemID.SUPER_RANGING_1),
	SUPER_MAGIC(Type.POTION, "S.Magic", "S.M", ItemID.SUPER_MAGIC_POTION_4, ItemID.SUPER_MAGIC_POTION_3, ItemID.SUPER_MAGIC_POTION_2, ItemID.SUPER_MAGIC_POTION_1),

	DIVINE_SUPER_ATTACK(Type.POTION, "S.Att", "S.A", ItemID.DIVINE_SUPER_ATTACK_POTION4, ItemID.DIVINE_SUPER_ATTACK_POTION3, ItemID.DIVINE_SUPER_ATTACK_POTION2, ItemID.DIVINE_SUPER_ATTACK_POTION1),
	DIVINE_SUPER_DEFENCE(Type.POTION, "S.Def", "S.D", ItemID.DIVINE_SUPER_DEFENCE_POTION4, ItemID.DIVINE_SUPER_DEFENCE_POTION3, ItemID.DIVINE_SUPER_DEFENCE_POTION2, ItemID.DIVINE_SUPER_DEFENCE_POTION1),
	DIVINE_SUPER_STRENGTH(Type.POTION, "S.Str", "S.S", ItemID.DIVINE_SUPER_STRENGTH_POTION4, ItemID.DIVINE_SUPER_STRENGTH_POTION3, ItemID.DIVINE_SUPER_STRENGTH_POTION2, ItemID.DIVINE_SUPER_STRENGTH_POTION1),
	DIVINE_SUPER_COMBAT(Type.POTION, "S.Com", "S.C", ItemID.DIVINE_SUPER_COMBAT_POTION4, ItemID.DIVINE_SUPER_COMBAT_POTION3, ItemID.DIVINE_SUPER_COMBAT_POTION2, ItemID.DIVINE_SUPER_COMBAT_POTION1),
	DIVINE_RANGING(Type.POTION, "Range", "R", ItemID.DIVINE_RANGING_POTION4, ItemID.DIVINE_RANGING_POTION3, ItemID.DIVINE_RANGING_POTION2, ItemID.DIVINE_RANGING_POTION1),
	DIVINE_MAGIC(Type.POTION, "Magic", "M", ItemID.DIVINE_MAGIC_POTION4, ItemID.DIVINE_MAGIC_POTION3, ItemID.DIVINE_MAGIC_POTION2, ItemID.DIVINE_MAGIC_POTION1),
	DIVINE_BASTION(Type.POTION, "Bastion", "B", ItemID.DIVINE_BASTION_POTION4, ItemID.DIVINE_BASTION_POTION3, ItemID.DIVINE_BASTION_POTION2, ItemID.DIVINE_BASTION_POTION1),
	DIVINE_BATTLEMAGE(Type.POTION, "BatMage", "B.M", ItemID.DIVINE_BATTLEMAGE_POTION4, ItemID.DIVINE_BATTLEMAGE_POTION3, ItemID.DIVINE_BATTLEMAGE_POTION2, ItemID.DIVINE_BATTLEMAGE_POTION1),

	RESTORE(Type.POTION, "Restore", "Re", ItemID.RESTORE_POTION4, ItemID.RESTORE_POTION3, ItemID.RESTORE_POTION2, ItemID.RESTORE_POTION1),
	GUTHIX_BALANCE(Type.POTION, "GuthBal", "G.B.", ItemID.GUTHIX_BALANCE4, ItemID.GUTHIX_BALANCE3, ItemID.GUTHIX_BALANCE2, ItemID.GUTHIX_BALANCE1),
	SUPER_RESTORE(Type.POTION, "S.Rest", "S.Re", ItemID.SUPER_RESTORE4, ItemID.SUPER_RESTORE3, ItemID.SUPER_RESTORE2, ItemID.SUPER_RESTORE1),
	PRAYER(Type.POTION, "Prayer", "P", ItemID.PRAYER_POTION4, ItemID.PRAYER_POTION3, ItemID.PRAYER_POTION2, ItemID.PRAYER_POTION1),
	ENERGY(Type.POTION, "Energy", "En", ItemID.ENERGY_POTION4, ItemID.ENERGY_POTION3, ItemID.ENERGY_POTION2, ItemID.ENERGY_POTION1),
	SUPER_ENERGY(Type.POTION, "S.Energ", "S.En", ItemID.SUPER_ENERGY4, ItemID.SUPER_ENERGY3, ItemID.SUPER_ENERGY2, ItemID.SUPER_ENERGY1),
	STAMINA(Type.POTION, "Stamina", "St", ItemID.STAMINA_POTION4, ItemID.STAMINA_POTION3, ItemID.STAMINA_POTION2, ItemID.STAMINA_POTION1),
	OVERLOAD(Type.POTION, "Overloa", "OL", ItemID.OVERLOAD_4, ItemID.OVERLOAD_3, ItemID.OVERLOAD_2, ItemID.OVERLOAD_1),
	ABSORPTION(Type.POTION, "Absorb", "Ab", ItemID.ABSORPTION_4, ItemID.ABSORPTION_3, ItemID.ABSORPTION_2, ItemID.ABSORPTION_1),

	ZAMORAK_BREW(Type.POTION, "ZammyBr", "Za", ItemID.ZAMORAK_BREW4, ItemID.ZAMORAK_BREW3, ItemID.ZAMORAK_BREW2, ItemID.ZAMORAK_BREW1),
	SARADOMIN_BREW(Type.POTION, "SaraBr", "Sa", ItemID.SARADOMIN_BREW4, ItemID.SARADOMIN_BREW3, ItemID.SARADOMIN_BREW2, ItemID.SARADOMIN_BREW1),
	ANCIENT_BREW(Type.POTION, "AncBr", "A.Br", ItemID.ANCIENT_BREW4, ItemID.ANCIENT_BREW3, ItemID.ANCIENT_BREW2, ItemID.ANCIENT_BREW1),

	ANTIPOISON(Type.POTION, "AntiP", "AP", ItemID.ANTIPOISON4, ItemID.ANTIPOISON3, ItemID.ANTIPOISON2, ItemID.ANTIPOISON1),
	SUPERANTIPOISON(Type.POTION, "S.AntiP", "S.AP", ItemID.SUPERANTIPOISON4, ItemID.SUPERANTIPOISON3, ItemID.SUPERANTIPOISON2, ItemID.SUPERANTIPOISON1),
	ANTIDOTE_P(Type.POTION, "Antid+", "A+", ItemID.ANTIDOTE4, ItemID.ANTIDOTE3, ItemID.ANTIDOTE2, ItemID.ANTIDOTE1),
	ANTIDOTE_PP(Type.POTION, "Antid++", "A++", ItemID.ANTIDOTE4_5952, ItemID.ANTIDOTE3_5954, ItemID.ANTIDOTE2_5956, ItemID.ANTIDOTE1_5958),
	ANTIVENOM(Type.POTION, "Anti-V", "AV", ItemID.ANTIVENOM4, ItemID.ANTIVENOM3, ItemID.ANTIVENOM2, ItemID.ANTIVENOM1),
	ANTIVENOM_P(Type.POTION, "Anti-V+", "AV+", ItemID.ANTIVENOM4_12913, ItemID.ANTIVENOM3_12915, ItemID.ANTIVENOM2_12917, ItemID.ANTIVENOM1_12919),

	RELICYMS_BALM(Type.POTION, "Relicym", "R.B", ItemID.RELICYMS_BALM4, ItemID.RELICYMS_BALM3, ItemID.RELICYMS_BALM2, ItemID.RELICYMS_BALM1),
	SANFEW_SERUM(Type.POTION, "Sanfew", "Sf", ItemID.SANFEW_SERUM4, ItemID.SANFEW_SERUM3, ItemID.SANFEW_SERUM2, ItemID.SANFEW_SERUM1),
	ANTIFIRE(Type.POTION, "Antif", "Af", ItemID.ANTIFIRE_POTION4, ItemID.ANTIFIRE_POTION3, ItemID.ANTIFIRE_POTION2, ItemID.ANTIFIRE_POTION1),
	EXTENDED_ANTIFIRE(Type.POTION, "E.Antif", "E.Af", ItemID.EXTENDED_ANTIFIRE4, ItemID.EXTENDED_ANTIFIRE3, ItemID.EXTENDED_ANTIFIRE2, ItemID.EXTENDED_ANTIFIRE1),
	SUPER_ANTIFIRE(Type.POTION, "S.Antif", "S.Af", ItemID.SUPER_ANTIFIRE_POTION4, ItemID.SUPER_ANTIFIRE_POTION3, ItemID.SUPER_ANTIFIRE_POTION2, ItemID.SUPER_ANTIFIRE_POTION1),
	EXTENDED_SUPER_ANTIFIRE(Type.POTION, "ES.Antif", "ES.Af", ItemID.EXTENDED_SUPER_ANTIFIRE4, ItemID.EXTENDED_SUPER_ANTIFIRE3, ItemID.EXTENDED_SUPER_ANTIFIRE2, ItemID.EXTENDED_SUPER_ANTIFIRE1),

	SERUM_207(Type.POTION, "Ser207", "S7", ItemID.SERUM_207_4, ItemID.SERUM_207_3, ItemID.SERUM_207_2, ItemID.SERUM_207_1),
	SERUM_208(Type.POTION, "Ser208", "S8", ItemID.SERUM_208_4, ItemID.SERUM_208_3, ItemID.SERUM_208_2, ItemID.SERUM_208_1),
	COMPOST_POTION(Type.POTION, "Compost", "COM", ItemID.COMPOST_POTION4, ItemID.COMPOST_POTION3, ItemID.COMPOST_POTION2, ItemID.COMPOST_POTION1),

	AGILITY(Type.POTION, "Agility", "Ag", ItemID.AGILITY_POTION4, ItemID.AGILITY_POTION3, ItemID.AGILITY_POTION2, ItemID.AGILITY_POTION1),
	FISHING(Type.POTION, "Fishing", "Fi", ItemID.FISHING_POTION4, ItemID.FISHING_POTION3, ItemID.FISHING_POTION2, ItemID.FISHING_POTION1),
	HUNTER(Type.POTION, "Hunter", "Hu", ItemID.HUNTER_POTION4, ItemID.HUNTER_POTION3, ItemID.HUNTER_POTION2, ItemID.HUNTER_POTION1),

	GOBLIN(Type.POTION, "Goblin", "G", ItemID.GOBLIN_POTION4, ItemID.GOBLIN_POTION3, ItemID.GOBLIN_POTION2, ItemID.GOBLIN_POTION1),
	MAGIC_ESS(Type.POTION, "MagEss", "M.E", ItemID.MAGIC_ESSENCE4, ItemID.MAGIC_ESSENCE3, ItemID.MAGIC_ESSENCE2, ItemID.MAGIC_ESSENCE1),
	REJUVENATION(Type.POTION, "Rejuv", "Rj", ItemID.REJUVENATION_POTION_4, ItemID.REJUVENATION_POTION_3, ItemID.REJUVENATION_POTION_2, ItemID.REJUVENATION_POTION_1),

	NECTAR(Type.POTION, "Nectar", "N", ItemID.NECTAR_4, ItemID.NECTAR_3, ItemID.NECTAR_2, ItemID.NECTAR_1),
	AMBROSIA(Type.POTION, "Ambros", "A", ItemID.AMBROSIA_2, ItemID.AMBROSIA_1),
	LIQUID_ADRENALINE(Type.POTION, "L.Adrn", "L.A", ItemID.LIQUID_ADRENALINE_2, ItemID.LIQUID_ADRENALINE_1),
	TEARS_OF_ELIDINIS(Type.POTION, "Tears", "ToE", ItemID.TEARS_OF_ELIDINIS_4, ItemID.TEARS_OF_ELIDINIS_3, ItemID.TEARS_OF_ELIDINIS_2, ItemID.TEARS_OF_ELIDINIS_1),
	MENAPHITE_REMEDY(Type.POTION, "MenRem", "M.R", ItemID.MENAPHITE_REMEDY4, ItemID.MENAPHITE_REMEDY3, ItemID.MENAPHITE_REMEDY2, ItemID.MENAPHITE_REMEDY1),

	// Unfinished Potions
	GUAM_POTION(Type.POTION, "Guam", "G", ItemID.GUAM_POTION_UNF),
	MARRENTILL_POTION(Type.POTION, "Marren", "M", ItemID.MARRENTILL_POTION_UNF),
	TARROMIN_POTION(Type.POTION, "Tarro", "TAR", ItemID.TARROMIN_POTION_UNF),
	HARRALANDER_POTION(Type.POTION, "Harra", "H", ItemID.HARRALANDER_POTION_UNF),
	RANARR_POTION(Type.POTION, "Ranarr", "R", ItemID.RANARR_POTION_UNF),
	TOADFLAX_POTION(Type.POTION, "Toad", "TOA", ItemID.TOADFLAX_POTION_UNF),
	IRIT_POTION(Type.POTION, "Irit", "I", ItemID.IRIT_POTION_UNF),
	AVANTOE_POTION(Type.POTION, "Avan", "A", ItemID.AVANTOE_POTION_UNF),
	KWUARM_POTION(Type.POTION, "Kwuarm", "K", ItemID.KWUARM_POTION_UNF),
	SNAPDRAGON_POTION(Type.POTION, "Snap", "S", ItemID.SNAPDRAGON_POTION_UNF),
	CADANTINE_POTION(Type.POTION, "Cadan", "C", ItemID.CADANTINE_POTION_UNF),
	LANTADYME_POTION(Type.POTION, "Lanta", "L", ItemID.LANTADYME_POTION_UNF),
	DWARF_WEED_POTION(Type.POTION, "Dwarf", "D", ItemID.DWARF_WEED_POTION_UNF),
	TORSTOL_POTION(Type.POTION, "Torstol", "TOR", ItemID.TORSTOL_POTION_UNF),

	// Impling jars
	BABY_IMPLING(Type.IMPLING_JAR, "Baby", "B", ItemID.BABY_IMPLING_JAR),
	YOUNG_IMPLING(Type.IMPLING_JAR, "Young", "Y", ItemID.YOUNG_IMPLING_JAR),
	GOURMET_IMPLING(Type.IMPLING_JAR, "Gourmet", "G", ItemID.GOURMET_IMPLING_JAR),
	EARTH_IMPLING(Type.IMPLING_JAR, "Earth", "EAR", ItemID.EARTH_IMPLING_JAR),
	ESSENCE_IMPLING(Type.IMPLING_JAR, "Essen", "ESS", ItemID.ESSENCE_IMPLING_JAR),
	ECLECTIC_IMPLING(Type.IMPLING_JAR, "Eclect", "ECL", ItemID.ECLECTIC_IMPLING_JAR),
	NATURE_IMPLING(Type.IMPLING_JAR, "Nature", "NAT", ItemID.NATURE_IMPLING_JAR),
	MAGPIE_IMPLING(Type.IMPLING_JAR, "Magpie", "M", ItemID.MAGPIE_IMPLING_JAR),
	NINJA_IMPLING(Type.IMPLING_JAR, "Ninja", "NIN", ItemID.NINJA_IMPLING_JAR),
	CRYSTAL_IMPLING(Type.IMPLING_JAR, "Crystal", "C", ItemID.CRYSTAL_IMPLING_JAR),
	DRAGON_IMPLING(Type.IMPLING_JAR, "Dragon", "D", ItemID.DRAGON_IMPLING_JAR),
	LUCKY_IMPLING(Type.IMPLING_JAR, "Lucky", "L", ItemID.LUCKY_IMPLING_JAR),

	// Tablets
	VARROCK_TELEPORT(Type.TABLET, "Varro", "VAR", ItemID.VARROCK_TELEPORT),
	LUMBRIDGE_TELEPORT(Type.TABLET, "Lumbr", "LUM", ItemID.LUMBRIDGE_TELEPORT),
	FALADOR_TELEPORT(Type.TABLET, "Fala", "FAL", ItemID.FALADOR_TELEPORT),
	CAMELOT_TELEPORT(Type.TABLET, "Cammy", "CAM", ItemID.CAMELOT_TELEPORT),
	ARDOUGNE_TELEPORT(Type.TABLET, "Ardoug", "ARD", ItemID.ARDOUGNE_TELEPORT),
	WATCHTOWER_TELEPORT(Type.TABLET, "W.tow", "WT", ItemID.WATCHTOWER_TELEPORT),
	TELEPORT_TO_HOUSE(Type.TABLET, "House", "POH", ItemID.TELEPORT_TO_HOUSE),

	ENCHANT_SAPPHIRE_OR_OPAL(Type.TABLET, "E.Saph", "E SO", ItemID.ENCHANT_SAPPHIRE_OR_OPAL),
	ENCHANT_EMERALD_OR_JADE(Type.TABLET, "E.Emer", "E EJ", ItemID.ENCHANT_EMERALD_OR_JADE),
	ENCHANT_RUBY_OR_TOPAZ(Type.TABLET, "E.Ruby", "E RT", ItemID.ENCHANT_RUBY_OR_TOPAZ),
	ENCHANT_DIAMOND(Type.TABLET, "E.Diam", "E DIA", ItemID.ENCHANT_DIAMOND),
	ENCHANT_DRAGONSTONE(Type.TABLET, "E.Dstn", "E DS", ItemID.ENCHANT_DRAGONSTONE),
	ENCHANT_ONYX(Type.TABLET, "E.Onyx", "E ONX", ItemID.ENCHANT_ONYX),

	TELEKINETIC_GRAB(Type.TABLET, "T.grab", "T.GRB", ItemID.TELEKINETIC_GRAB),
	BONES_TO_PEACHES(Type.TABLET, "Peach", "BtP", ItemID.BONES_TO_PEACHES_8015),
	BONES_TO_BANANAS(Type.TABLET, "Banana", "BtB", ItemID.BONES_TO_BANANAS),

	RIMMINGTON_TELEPORT(Type.TABLET, "Rimmi", "RIM", ItemID.RIMMINGTON_TELEPORT),
	TAVERLEY_TELEPORT(Type.TABLET, "Taver", "TAV", ItemID.TAVERLEY_TELEPORT),
	POLLNIVNEACH_TELEPORT(Type.TABLET, "Pollnv", "POL", ItemID.POLLNIVNEACH_TELEPORT),
	RELLEKKA_TELEPORT(Type.TABLET, "Rell", "REL", ItemID.RELLEKKA_TELEPORT),
	BRIMHAVEN_TELEPORT(Type.TABLET, "Brimh", "BRIM", ItemID.BRIMHAVEN_TELEPORT),
	YANILLE_TELEPORT(Type.TABLET, "Yanille", "YAN", ItemID.YANILLE_TELEPORT),
	TROLLHEIM_TELEPORT(Type.TABLET, "Trollh", "T.HM", ItemID.TROLLHEIM_TELEPORT),
	PRIFDDINAS_TELEPORT(Type.TABLET, "Prifd", "PRIF", ItemID.PRIFDDINAS_TELEPORT),
	HOSIDIUS_TELEPORT(Type.TABLET, "Hosid", "HOS", ItemID.HOSIDIUS_TELEPORT),

	ANNAKARL_TELEPORT(Type.TABLET, "Annak", "GDZ", ItemID.ANNAKARL_TELEPORT),
	CARRALLANGAR_TELEPORT(Type.TABLET, "Carra", "CAR", ItemID.CARRALLANGAR_TELEPORT),
	DAREEYAK_TELEPORT(Type.TABLET, "Dareey", "DAR", ItemID.DAREEYAK_TELEPORT),
	GHORROCK_TELEPORT(Type.TABLET, "Ghorr", "GHRK", ItemID.GHORROCK_TELEPORT),
	KHARYRLL_TELEPORT(Type.TABLET, "Khary", "KHRL", ItemID.KHARYRLL_TELEPORT),
	LASSAR_TELEPORT(Type.TABLET, "Lass", "LSR", ItemID.LASSAR_TELEPORT),
	PADDEWWA_TELEPORT(Type.TABLET, "Paddew", "PDW", ItemID.PADDEWWA_TELEPORT),
	SENNTISTEN_TELEPORT(Type.TABLET, "Sennt", "SNT", ItemID.SENNTISTEN_TELEPORT),

	ARCEUUS_LIBRARY_TELEPORT(Type.TABLET, "Arc.Lib", "A.LIB", ItemID.ARCEUUS_LIBRARY_TELEPORT),
	DRAYNOR_MANOR_TELEPORT(Type.TABLET, "D.Manor", "D.MNR", ItemID.DRAYNOR_MANOR_TELEPORT),
	MIND_ALTAR_TELEPORT(Type.TABLET, "M.Altar", "M.ALT", ItemID.MIND_ALTAR_TELEPORT),
	SALVE_GRAVEYARD_TELEPORT(Type.TABLET, "S.Grave", "S.GRV", ItemID.SALVE_GRAVEYARD_TELEPORT),
	FENKENSTRAINS_CASTLE_TELEPORT(Type.TABLET, "Fenk", "FNK", ItemID.FENKENSTRAINS_CASTLE_TELEPORT),
	WEST_ARDOUGNE_TELEPORT(Type.TABLET, "W.Ardy", "W.ARD", ItemID.WEST_ARDOUGNE_TELEPORT),
	HARMONY_ISLAND_TELEPORT(Type.TABLET, "H.Isle", "HRM", ItemID.HARMONY_ISLAND_TELEPORT),
	CEMETERY_TELEPORT(Type.TABLET, "Cemet", "CEM", ItemID.CEMETERY_TELEPORT),
	BARROWS_TELEPORT(Type.TABLET, "Barrow", "BAR", ItemID.BARROWS_TELEPORT),
	APE_ATOLL_TELEPORT(Type.TABLET, "Atoll", "APE", ItemID.APE_ATOLL_TELEPORT),
	BATTLEFRONT_TELEPORT(Type.TABLET, "B.Front", "BF", ItemID.BATTLEFRONT_TELEPORT),

	MOONCLAN_TELEPORT(Type.TABLET, "Moon", "MOON", ItemID.MOONCLAN_TELEPORT),
	OURANIA_TELEPORT(Type.TABLET, "Ourania", "ZMI", ItemID.OURANIA_TELEPORT),
	WATERBIRTH_TELEPORT(Type.TABLET, "W.Birth", "WAT", ItemID.WATERBIRTH_TELEPORT),
	BARBARIAN_TELEPORT(Type.TABLET, "Barb", "BARB", ItemID.BARBARIAN_TELEPORT),
	KHAZARD_TELEPORT(Type.TABLET, "Khaz", "KHA", ItemID.KHAZARD_TELEPORT),
	FISHING_GUILD_TELEPORT(Type.TABLET, "Fish G.", "FIS", ItemID.FISHING_GUILD_TELEPORT),
	CATHERBY_TELEPORT(Type.TABLET, "Cathy", "CATH", ItemID.CATHERBY_TELEPORT),
	ICE_PLATEAU(Type.TABLET, "Ice Pl.", "ICE", ItemID.ICE_PLATEAU_TELEPORT),

	TARGET_TELEPORT(Type.TABLET, "Target", "TRG", ItemID.TARGET_TELEPORT),
	VOLCANIC_MINE_TELEPORT(Type.TABLET, "V.Mine", "VM", ItemID.VOLCANIC_MINE_TELEPORT),
	WILDERNESS_CRABS_TELEPORT(Type.TABLET, "W.Crab", "CRAB", ItemID.WILDERNESS_CRABS_TELEPORT),

	// Scrolls
	NARDAH_TELEPORT(Type.SCROLL, "Nardah", "NAR", ItemID.NARDAH_TELEPORT),
	DIGSITE_TELEPORT(Type.SCROLL, "Digsite", "DIG", ItemID.DIGSITE_TELEPORT),
	FELDIP_HILLS_TELEPORT(Type.SCROLL, "F.Hills", "F.H", ItemID.FELDIP_HILLS_TELEPORT),
	LUNAR_ISLE_TELEPORT(Type.SCROLL, "L.Isle", "L.I", ItemID.LUNAR_ISLE_TELEPORT),
	MORTTON_TELEPORT(Type.SCROLL, "Mort'ton", "MORT", ItemID.MORTTON_TELEPORT),
	PEST_CONTROL_TELEPORT(Type.SCROLL, "P.Cont", "PEST", ItemID.PEST_CONTROL_TELEPORT),
	PISCATORIS_TELEPORT(Type.SCROLL, "Pisca", "PISC", ItemID.PISCATORIS_TELEPORT),
	TAI_BWO_WANNAI_TELEPORT(Type.SCROLL, "TaiBwo", "TAI", ItemID.TAI_BWO_WANNAI_TELEPORT),
	IORWERTH_CAMP_TELEPORT(Type.SCROLL, "Iorwerth", "IOR", ItemID.IORWERTH_CAMP_TELEPORT),
	MOS_LEHARMLESS_TELEPORT(Type.SCROLL, "M.LeHarm", "M.L", ItemID.MOS_LEHARMLESS_TELEPORT),
	LUMBERYARD_TELEPORT(Type.SCROLL, "Lumber", "LUMB", ItemID.LUMBERYARD_TELEPORT),

	ZUL_ANDRA_TELEPORT(Type.SCROLL, "Zul-andra", "ZUL", ItemID.ZULANDRA_TELEPORT),
	KEY_MASTER_TELEPORT(Type.SCROLL, "Key master", "KEY", ItemID.KEY_MASTER_TELEPORT),
	REVENANT_CAVE_TELEPORT(Type.SCROLL, "Rev cave", "REV", ItemID.REVENANT_CAVE_TELEPORT),
	WATSON_TELEPORT(Type.SCROLL, "Watson", "WATS", ItemID.WATSON_TELEPORT),

	// Jewellery
	OPAL_JEWELLERY(Type.JEWELLERY, "Opal", "OP", ItemID.OPAL_RING, ItemID.OPAL_NECKLACE, ItemID.OPAL_BRACELET, ItemID.OPAL_AMULET_U, ItemID.OPAL_AMULET),
	JADE_JEWELLERY(Type.JEWELLERY, "Jade", "J", ItemID.JADE_RING, ItemID.JADE_NECKLACE, ItemID.JADE_BRACELET, ItemID.JADE_AMULET_U, ItemID.JADE_AMULET),
	TOPAZ_JEWELLERY(Type.JEWELLERY, "Topaz", "T", ItemID.TOPAZ_RING, ItemID.TOPAZ_NECKLACE, ItemID.TOPAZ_BRACELET, ItemID.TOPAZ_AMULET_U, ItemID.TOPAZ_AMULET),
	GOLD_JEWELLERY(Type.JEWELLERY, "Gold", "GOL", ItemID.GOLD_RING, ItemID.GOLD_NECKLACE, ItemID.GOLD_BRACELET, ItemID.GOLD_AMULET_U, ItemID.GOLD_AMULET),
	SAPPHIRE_JEWELLERY(Type.JEWELLERY, "Sapphir", "S", ItemID.SAPPHIRE_RING, ItemID.SAPPHIRE_NECKLACE, ItemID.SAPPHIRE_BRACELET, ItemID.SAPPHIRE_AMULET_U, ItemID.SAPPHIRE_AMULET),
	EMERALD_JEWELLERY(Type.JEWELLERY, "Emerald", "E", ItemID.EMERALD_RING, ItemID.EMERALD_NECKLACE, ItemID.EMERALD_BRACELET, ItemID.EMERALD_AMULET_U, ItemID.EMERALD_AMULET),
	RUBY_JEWELLERY(Type.JEWELLERY, "Ruby", "R", ItemID.RUBY_RING, ItemID.RUBY_NECKLACE, ItemID.RUBY_BRACELET, ItemID.RUBY_AMULET_U, ItemID.RUBY_AMULET),
	DIAMOND_JEWELLERY(Type.JEWELLERY, "Diamon", "DI", ItemID.DIAMOND_RING, ItemID.DIAMOND_NECKLACE, ItemID.DIAMOND_BRACELET, ItemID.DIAMOND_AMULET_U, ItemID.DIAMOND_AMULET),
	DRAGONSTONE_JEWELLERY(Type.JEWELLERY, "Dragon", "DR", ItemID.DRAGONSTONE_RING, ItemID.DRAGON_NECKLACE, ItemID.DRAGONSTONE_BRACELET, ItemID.DRAGONSTONE_AMULET_U, ItemID.DRAGONSTONE_AMULET),
	ONYX_JEWELLERY(Type.JEWELLERY, "Onyx", "ON", ItemID.ONYX_RING, ItemID.ONYX_NECKLACE, ItemID.ONYX_BRACELET, ItemID.ONYX_AMULET_U, ItemID.ONYX_AMULET),
	ZENYTE_JEWELLERY(Type.JEWELLERY, "Zenyte", "Z", ItemID.ZENYTE_RING, ItemID.ZENYTE_NECKLACE, ItemID.ZENYTE_BRACELET, ItemID.ZENYTE_AMULET_U, ItemID.ZENYTE_AMULET),

	// Enchanted Jewellery
	RING_OF_PURSUIT(Type.JEWELLERY_ENCHANTED, "Pursuit", "PUR", ItemID.RING_OF_PURSUIT),
	DODGY_NECKLACE(Type.JEWELLERY_ENCHANTED, "Dodgy", "DOD", ItemID.DODGY_NECKLACE),
	EXPEDITIOUS_BRACELET(Type.JEWELLERY_ENCHANTED, "Exped", "EXP", ItemID.EXPEDITIOUS_BRACELET),
	AMULET_OF_BOUNTY(Type.JEWELLERY_ENCHANTED, "Bounty", "BOU", ItemID.AMULET_OF_BOUNTY),

	RING_OF_RETURNING(Type.JEWELLERY_ENCHANTED, "Return", "RET", ItemID.RING_OF_RETURNING1, ItemID.RING_OF_RETURNING2, ItemID.RING_OF_RETURNING3, ItemID.RING_OF_RETURNING4, ItemID.RING_OF_RETURNING5),
	NECKLACE_OF_PASSAGE(Type.JEWELLERY_ENCHANTED, "Passage", "PASS", ItemID.NECKLACE_OF_PASSAGE1, ItemID.NECKLACE_OF_PASSAGE2, ItemID.NECKLACE_OF_PASSAGE3, ItemID.NECKLACE_OF_PASSAGE4, ItemID.NECKLACE_OF_PASSAGE5),
	FLAMTAER_BRACELET(Type.JEWELLERY_ENCHANTED, "Flamta", "FLA", ItemID.FLAMTAER_BRACELET),
	AMULET_OF_CHEMISTRY(Type.JEWELLERY_ENCHANTED, "Chem", "CH", ItemID.AMULET_OF_CHEMISTRY),

	EFARITAYS_AID(Type.JEWELLERY_ENCHANTED, "Efarit", "EFA", ItemID.EFARITAYS_AID),
	NECKLACE_OF_FAITH(Type.JEWELLERY_ENCHANTED, "Faith", "FAI", ItemID.NECKLACE_OF_FAITH),
	BRACELET_OF_SLAUGHTER(Type.JEWELLERY_ENCHANTED, "Slaught", "SLA", ItemID.BRACELET_OF_SLAUGHTER),
	BURNING_AMULET(Type.JEWELLERY_ENCHANTED, "Burning", "BUR", ItemID.BURNING_AMULET1, ItemID.BURNING_AMULET2, ItemID.BURNING_AMULET3, ItemID.BURNING_AMULET4, ItemID.BURNING_AMULET5),

	RING_OF_RECOIL(Type.JEWELLERY_ENCHANTED, "Recoil", "REC", ItemID.RING_OF_RECOIL),
	GAMES_NECKLACE(Type.JEWELLERY_ENCHANTED, "Games", "GAME", ItemID.GAMES_NECKLACE1, ItemID.GAMES_NECKLACE2, ItemID.GAMES_NECKLACE3, ItemID.GAMES_NECKLACE4, ItemID.GAMES_NECKLACE5, ItemID.GAMES_NECKLACE6, ItemID.GAMES_NECKLACE7, ItemID.GAMES_NECKLACE8),
	BRACELET_OF_CLAY(Type.JEWELLERY_ENCHANTED, "Clay", "CLA", ItemID.BRACELET_OF_CLAY),
	AMULET_OF_MAGIC(Type.JEWELLERY_ENCHANTED, "Magic", "MAG", ItemID.AMULET_OF_MAGIC),

	RING_OF_DUELING(Type.JEWELLERY_ENCHANTED, "Duel", "DU", ItemID.RING_OF_DUELING1, ItemID.RING_OF_DUELING2, ItemID.RING_OF_DUELING3, ItemID.RING_OF_DUELING4, ItemID.RING_OF_DUELING5, ItemID.RING_OF_DUELING6, ItemID.RING_OF_DUELING7, ItemID.RING_OF_DUELING8),
	BINDING_NECKLACE(Type.JEWELLERY_ENCHANTED, "Binding", "BIND", ItemID.BINDING_NECKLACE),
	CASTLE_WARS_BRACELET(Type.JEWELLERY_ENCHANTED, "Castle", "CAS", ItemID.CASTLE_WARS_BRACELET1, ItemID.CASTLE_WARS_BRACELET2, ItemID.CASTLE_WARS_BRACELET3),
	AMULET_OF_DEFENCE(Type.JEWELLERY_ENCHANTED, "Defence", "DEF", ItemID.AMULET_OF_DEFENCE),
	AMULET_OF_NATURE(Type.JEWELLERY_ENCHANTED, "Nature", "NAT", ItemID.AMULET_OF_NATURE),

	RING_OF_FORGING(Type.JEWELLERY_ENCHANTED, "Forging", "FOR", ItemID.RING_OF_FORGING),
	DIGSITE_PENDANT(Type.JEWELLERY_ENCHANTED, "Digsite", "DIG", ItemID.DIGSITE_PENDANT_1, ItemID.DIGSITE_PENDANT_2, ItemID.DIGSITE_PENDANT_3, ItemID.DIGSITE_PENDANT_4, ItemID.DIGSITE_PENDANT_5),
	INOCULATION_BRACELET(Type.JEWELLERY_ENCHANTED, "Inocul", "INO", ItemID.INOCULATION_BRACELET),
	AMULET_OF_STRENGTH(Type.JEWELLERY_ENCHANTED, "Strengt", "STR", ItemID.AMULET_OF_STRENGTH),

	RING_OF_LIFE(Type.JEWELLERY_ENCHANTED, "Life", "LI", ItemID.RING_OF_LIFE),
	PHOENIX_NECKLACE(Type.JEWELLERY_ENCHANTED, "Phoenix", "PHO", ItemID.PHOENIX_NECKLACE),
	ABYSSAL_BRACELET(Type.JEWELLERY_ENCHANTED, "Abyss", "ABY", ItemID.ABYSSAL_BRACELET1, ItemID.ABYSSAL_BRACELET2, ItemID.ABYSSAL_BRACELET3, ItemID.ABYSSAL_BRACELET4, ItemID.ABYSSAL_BRACELET5),
	AMULET_OF_POWER(Type.JEWELLERY_ENCHANTED, "Power", "POW", ItemID.AMULET_OF_POWER),

	RING_OF_WEALTH(Type.JEWELLERY_ENCHANTED, "Wealth", "WE", ItemID.RING_OF_WEALTH, ItemID.RING_OF_WEALTH_1, ItemID.RING_OF_WEALTH_2, ItemID.RING_OF_WEALTH_3, ItemID.RING_OF_WEALTH_4, ItemID.RING_OF_WEALTH_5),
	SKILLS_NECKLACE(Type.JEWELLERY_ENCHANTED, "Skill", "SK", ItemID.SKILLS_NECKLACE, ItemID.SKILLS_NECKLACE1, ItemID.SKILLS_NECKLACE2, ItemID.SKILLS_NECKLACE3, ItemID.SKILLS_NECKLACE4, ItemID.SKILLS_NECKLACE5, ItemID.SKILLS_NECKLACE6),
	COMBAT_BRACELET(Type.JEWELLERY_ENCHANTED, "Combat", "COM", ItemID.COMBAT_BRACELET, ItemID.COMBAT_BRACELET1, ItemID.COMBAT_BRACELET2, ItemID.COMBAT_BRACELET3, ItemID.COMBAT_BRACELET4, ItemID.COMBAT_BRACELET5, ItemID.COMBAT_BRACELET6),
	AMULET_OF_GLORY(Type.JEWELLERY_ENCHANTED, "Glory", "GLO", ItemID.AMULET_OF_GLORY, ItemID.AMULET_OF_GLORY1, ItemID.AMULET_OF_GLORY2, ItemID.AMULET_OF_GLORY3, ItemID.AMULET_OF_GLORY4, ItemID.AMULET_OF_GLORY5, ItemID.AMULET_OF_GLORY6, ItemID.AMULET_OF_ETERNAL_GLORY),

	RING_OF_STONE(Type.JEWELLERY_ENCHANTED, "Stone", "STO", ItemID.RING_OF_STONE),
	BERSERKER_NECKLACE(Type.JEWELLERY_ENCHANTED, "Berserk", "BER", ItemID.BERSERKER_NECKLACE),
	REGEN_BRACELET(Type.JEWELLERY_ENCHANTED, "Regen", "REG", ItemID.REGEN_BRACELET),
	AMULET_OF_FURY(Type.JEWELLERY_ENCHANTED, "Fury", "FU", ItemID.AMULET_OF_FURY),

	RING_OF_SUFFERING(Type.JEWELLERY_ENCHANTED, "Suffer", "SUF", ItemID.RING_OF_SUFFERING),
	NECKLACE_OF_ANGUISH(Type.JEWELLERY_ENCHANTED, "Anguish", "ANG", ItemID.NECKLACE_OF_ANGUISH),
	TORMENTED_BRACELET(Type.JEWELLERY_ENCHANTED, "Torment", "TOR", ItemID.TORMENTED_BRACELET),
	AMULET_OF_TORTURE(Type.JEWELLERY_ENCHANTED, "Torture", "TOR", ItemID.AMULET_OF_TORTURE),

	OCCULT_NECKLACE(Type.JEWELLERY_ENCHANTED, "Occult", "OCC", ItemID.OCCULT_NECKLACE),
	DRAGONBONE_NECKLACE(Type.JEWELLERY_ENCHANTED, "Dragon", "DRA", ItemID.DRAGONBONE_NECKLACE),
	SLAYER_RING(Type.JEWELLERY_ENCHANTED, "Slayer", "SLA", ItemID.SLAYER_RING_1, ItemID.SLAYER_RING_2, ItemID.SLAYER_RING_3, ItemID.SLAYER_RING_4, ItemID.SLAYER_RING_5, ItemID.SLAYER_RING_6, ItemID.SLAYER_RING_7, ItemID.SLAYER_RING_8, ItemID.SLAYER_RING_ETERNAL);

	final Type type;
	final String medName;
	final String shortName;
	final int[] itemIDs;

	ItemIdentification(Type type, String medName, String shortName, int... ids)
	{
		this.type = type;
		this.medName = medName;
		this.shortName = shortName;
		this.itemIDs = ids;
	}

	private static final Map<Integer, ItemIdentification> itemIdentifications;

	static
	{
		ImmutableMap.Builder<Integer, ItemIdentification> builder = new ImmutableMap.Builder<>();

		for (ItemIdentification i : values())
		{
			for (int id : i.itemIDs)
			{
				builder.put(id, i);
			}
		}

		itemIdentifications = builder.build();
	}

	static ItemIdentification get(int id)
	{
		return itemIdentifications.get(id);
	}

	@AllArgsConstructor
	enum Type
	{
		SEED_HERB(ItemIdentificationConfig::showHerbSeeds),
		SEED_BERRY(ItemIdentificationConfig::showBerrySeeds),
		SEED_ALLOTMENT(ItemIdentificationConfig::showAllotmentSeeds),
		SEED_SPECIAL(ItemIdentificationConfig::showSpecialSeeds),
		SEED_TREE(ItemIdentificationConfig::showTreeSeeds),
		SEED_FRUIT_TREE(ItemIdentificationConfig::showFruitTreeSeeds),
		SEED_FLOWER(ItemIdentificationConfig::showFlowerSeeds),
		HOPS_SEED(ItemIdentificationConfig::showHopsSeeds),
		SACK(ItemIdentificationConfig::showSacks),
		HERB(ItemIdentificationConfig::showHerbs),
		LOGS(ItemIdentificationConfig::showLogs),
		LOGS_PYRE(ItemIdentificationConfig::showPyreLogs),
		PLANK(ItemIdentificationConfig::showPlanks),
		SAPLING(ItemIdentificationConfig::showSaplings),
		COMPOST(ItemIdentificationConfig::showComposts),
		ORE(ItemIdentificationConfig::showOres),
		BAR(ItemIdentificationConfig::showBars),
		GEM(ItemIdentificationConfig::showGems),
		POTION(ItemIdentificationConfig::showPotions),
		IMPLING_JAR(ItemIdentificationConfig::showImplingJars),
		TABLET(ItemIdentificationConfig::showTablets),
		SCROLL(ItemIdentificationConfig::showTeleportScrolls),
		JEWELLERY(ItemIdentificationConfig::showJewellery),
		JEWELLERY_ENCHANTED(ItemIdentificationConfig::showEnchantedJewellery);

		final Predicate<ItemIdentificationConfig> enabled;
	}
}
