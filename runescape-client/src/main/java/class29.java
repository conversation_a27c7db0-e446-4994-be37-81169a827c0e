import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("ag")
public interface class29 {
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(IB)Z",
      garbageValue = "110"
   )
   boolean vmethod4135(int var1);

   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "(IB)Z",
      garbageValue = "11"
   )
   boolean vmethod4136(int var1);

   @ObfuscatedName("v")
   @ObfuscatedSignature(
      descriptor = "(CI)Z",
      garbageValue = "783044550"
   )
   boolean vmethod4179(char var1);

   @ObfuscatedName("s")
   @ObfuscatedSignature(
      descriptor = "(ZI)Z",
      garbageValue = "653857297"
   )
   boolean vmethod4139(boolean var1);
}
