import java.io.UnsupportedEncodingException;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("pm")
public interface class428 {
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "(B)Lpf;",
      garbageValue = "1"
   )
   class427 vmethod8059();

   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "(B)[B",
      garbageValue = "1"
   )
   byte[] vmethod8061() throws UnsupportedEncodingException;
}
