.id                 6004
.int_stack_count    7
.string_stack_count 1
.int_var_count      7
.string_var_count   1
   iload                  1
   iconst                 100000
   if_icmpge              LABEL4
   jump                   LABEL23
LABEL4:
   iload                  1
   sconst                 ","
   invoke                 46
   sconst                 " x "
   iload                  0
   oc_name
   sconst                 "."
   join_string            4
   iconst                 27 ; ITEM_EXAMINE
   sconst                 "mes"
   runelite_callback
   ; ehc examine info
   ;iload                  0
   ;iload                  1
   ;iconst                 0
   ;iload                  2
   ;iload                  3
   ;iload                  4
   ;iload                  5
   ;iload                  6
   ;invoke                 6005
   jump                   LABEL34
LABEL23:
   sload                  0
   iconst                 27 ; ITEM_EXAMINE
   sconst                 "mes"
   runelite_callback
   ; ehc examine info
   ;iload                  0
   ;iload                  1
   ;iconst                 1
   ;iload                  2
   ;iload                  3
   ;iload                  4
   ;iload                  5
   ;iload                  6
   ;invoke                 6005
LABEL34:
   return
