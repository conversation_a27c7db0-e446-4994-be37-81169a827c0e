/*
 * Copyright (c) 2021, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.skillcalculator.skills;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.runelite.api.ItemID;
import net.runelite.client.game.ItemManager;

@AllArgsConstructor
@Getter
public enum CookingAction implements ItemSkillAction
{
	SINEW(ItemID.SINEW, 1, 3),
	SHRIMPS(ItemID.SHRIMPS, 1, 30),
	COOKED_CHICKEN(ItemID.COOKED_CHICKEN, 1, 30),
	COOKED_MEAT(ItemID.COOKED_MEAT, 1, 30),
	COOKED_RABBIT(ItemID.COOKED_RABBIT, 1, 30),
	ANCHOVIES(ItemID.ANCHOVIES, 1, 30),
	SARDINE(ItemID.SARDINE, 1, 40),
	POISON_KARAMBWAN(ItemID.POISON_KARAMBWAN, 1, 80),
	UGTHANKI_MEAT(ItemID.UGTHANKI_MEAT, 1, 40),
	BREAD(ItemID.BREAD, 1, 40),
	HERRING(ItemID.HERRING, 5, 50),
	FRUIT_BLAST(ItemID.FRUIT_BLAST, 6, 50),
	BAKED_POTATO(ItemID.BAKED_POTATO, 7, 15),
	GUPPY(ItemID.GUPPY, 7, 12),
	PINEAPPLE_PUNCH(ItemID.PINEAPPLE_PUNCH, 8, 70),
	SPICY_SAUCE(ItemID.SPICY_SAUCE, 9, 25),
	MACKEREL(ItemID.MACKEREL, 10, 60),
	REDBERRY_PIE(ItemID.REDBERRY_PIE, 10, 78),
	TOAD_CRUNCHIES(ItemID.TOAD_CRUNCHIES, 10, 100),
	CHILLI_CON_CARNE(ItemID.CHILLI_CON_CARNE, 11, 55),
	ROAST_BIRD_MEAT(ItemID.ROAST_BIRD_MEAT, 11, 62.5f),
	THIN_SNAIL_MEAT(ItemID.THIN_SNAIL_MEAT, 12, 70),
	SPICY_CRUNCHIES(ItemID.SPICY_CRUNCHIES, 12, 100),
	SCRAMBLED_EGG(ItemID.SCRAMBLED_EGG, 13, 50),
	CIDER(ItemID.CIDER, 14, 182),
	WORM_CRUNCHIES(ItemID.WORM_CRUNCHIES, 14, 104),
	TROUT(ItemID.TROUT, 15, 70),
	SPIDER_ON_STICK(ItemID.SPIDER_ON_STICK, 16, 80),
	SPIDER_ON_SHAFT(ItemID.SPIDER_ON_SHAFT, 16, 80),
	ROAST_RABBIT(ItemID.ROAST_RABBIT, 16, 72.5f),
	CHOCCHIP_CRUNCHIES(ItemID.CHOCCHIP_CRUNCHIES, 16, 100),
	LEAN_SNAIL_MEAT(ItemID.LEAN_SNAIL_MEAT, 17, 80),
	COD(ItemID.COD, 18, 75),
	WIZARD_BLIZZARD(ItemID.WIZARD_BLIZZARD, 18, 110),
	DWARVEN_STOUT(ItemID.DWARVEN_STOUT, 19, 215, true),
	SHORT_GREEN_GUY(ItemID.SHORT_GREEN_GUY, 20, 120),
	MEAT_PIE(ItemID.MEAT_PIE, 20, 110),
	PIKE(ItemID.PIKE, 20, 80),
	CUP_OF_TEA(ItemID.CUP_OF_TEA, 20, 52),
	CAVEFISH(ItemID.CAVEFISH, 20, 23),
	ROAST_BEAST_MEAT(ItemID.ROAST_BEAST_MEAT, 21, 82.5f),
	COOKED_CRAB_MEAT(ItemID.COOKED_CRAB_MEAT, 21, 100),
	POT_OF_CREAM(ItemID.POT_OF_CREAM, 21, 18),
	FAT_SNAIL_MEAT(ItemID.FAT_SNAIL_MEAT, 22, 95),
	EGG_AND_TOMATO(ItemID.EGG_AND_TOMATO, 23, 50),
	ASGARNIAN_ALE(ItemID.ASGARNIAN_ALE, 24, 248, true),
	SALMON(ItemID.SALMON, 25, 90),
	STEW(ItemID.STEW, 25, 117),
	FRUIT_BATTA(ItemID.FRUIT_BATTA, 25, 150),
	TOAD_BATTA(ItemID.TOAD_BATTA, 26, 152),
	WORM_BATTA(ItemID.WORM_BATTA, 27, 154),
	VEGETABLE_BATTA(ItemID.VEGETABLE_BATTA, 28, 156),
	SWEETCORN(ItemID.COOKED_SWEETCORN, 28, 104),
	COOKED_SLIMY_EEL(ItemID.COOKED_SLIMY_EEL, 28, 95),
	MUD_PIE(ItemID.MUD_PIE, 29, 128),
	GREENMANS_ALE(ItemID.GREENMANS_ALE, 29, 281),
	CHEESE_AND_TOMATO_BATTA(ItemID.CHEESETOM_BATTA, 29, 158),
	TUNA(ItemID.TUNA, 30, 100),
	APPLE_PIE(ItemID.APPLE_PIE, 30, 130),
	WORM_HOLE(ItemID.WORM_HOLE, 30, 170),
	COOKED_KARAMBWAN(ItemID.COOKED_KARAMBWAN, 30, 190),
	ROASTED_CHOMPY(ItemID.COOKED_CHOMPY, 30, 100),
	FISHCAKE(ItemID.COOKED_FISHCAKE, 31, 100),
	DRUNK_DRAGON(ItemID.DRUNK_DRAGON, 32, 160),
	CHOC_SATURDAY(ItemID.CHOC_SATURDAY, 33, 170),
	TETRA(ItemID.TETRA, 33, 31),
	GARDEN_PIE(ItemID.GARDEN_PIE, 34, 138),
	WIZARDS_MIND_BOMB(ItemID.WIZARDS_MIND_BOMB, 34, 314, true),
	JUG_OF_WINE(ItemID.JUG_OF_WINE, 35, 200),
	PLAIN_PIZZA(ItemID.PLAIN_PIZZA, 35, 143),
	RAINBOW_FISH(ItemID.RAINBOW_FISH, 35, 110),
	VEG_BALL(ItemID.VEG_BALL, 35, 175),
	BLURBERRY_SPECIAL(ItemID.BLURBERRY_SPECIAL, 37, 180),
	CAVE_EEL(ItemID.CAVE_EEL, 38, 115),
	PAT_OF_BUTTER(ItemID.PAT_OF_BUTTER, 38, 40.5f),
	DRAGON_BITTER(ItemID.DRAGON_BITTER, 39, 347),
	POTATO_WITH_BUTTER(ItemID.POTATO_WITH_BUTTER, 39, 40),
	LOBSTER(ItemID.LOBSTER, 40, 120),
	CAKE(ItemID.CAKE, 40, 180),
	TANGLED_TOADS_LEGS(ItemID.TANGLED_TOADS_LEGS, 40, 185),
	CHILLI_POTATO(ItemID.CHILLI_POTATO, 41, 165.5f),
	COOKED_JUBBLY(ItemID.COOKED_JUBBLY, 41, 160),
	CHOCOLATE_BOMB(ItemID.CHOCOLATE_BOMB, 42, 190),
	FRIED_ONIONS(ItemID.FRIED_ONIONS, 42, 60),
	BASS(ItemID.BASS, 43, 130),
	MOONLIGHT_MEAD(ItemID.MOONLIGHT_MEAD, 44, 380),
	SWORDFISH(ItemID.SWORDFISH, 45, 140),
	MEAT_PIZZA(ItemID.MEAT_PIZZA, 45, 169),
	FRIED_MUSHROOMS(ItemID.FRIED_MUSHROOMS, 46, 60),
	CATFISH(ItemID.CATFISH, 46, 43),
	FISH_PIE(ItemID.FISH_PIE, 47, 164),
	POTATO_WITH_CHEESE(ItemID.POTATO_WITH_CHEESE, 47, 40),
	CHEESE(ItemID.CHEESE, 48, 64, true),
	AXEMANS_FOLLY(ItemID.AXEMANS_FOLLY, 49, 413),
	COOKED_OOMLIE_WRAP(ItemID.COOKED_OOMLIE_WRAP, 50, 30),
	CHOCOLATE_CAKE(ItemID.CHOCOLATE_CAKE, 50, 210),
	EGG_POTATO(ItemID.EGG_POTATO, 51, 195.5f),
	BOTANICAL_PIE(ItemID.BOTANICAL_PIE, 52, 180),
	LAVA_EEL(ItemID.LAVA_EEL, 53, 30),
	CHEFS_DELIGHT(ItemID.CHEFS_DELIGHT, 54, 446),
	ANCHOVY_PIZZA(ItemID.ANCHOVY_PIZZA, 55, 182),
	MUSHROOM_AND_ONION(ItemID.MUSHROOM__ONION, 57, 120),
	UGTHANKI_KEBAB_FRESH(ItemID.UGTHANKI_KEBAB, 58, 80),
	PITTA_BREAD(ItemID.PITTA_BREAD, 58, 40),
	SLAYERS_RESPITE(ItemID.SLAYERS_RESPITE, 59, 479),
	CURRY(ItemID.CURRY, 60, 280),
	MUSHROOM_PIE(ItemID.MUSHROOM_PIE, 60, 200),
	MONKFISH(ItemID.MONKFISH, 62, 150),
	MUSHROOM_POTATO(ItemID.MUSHROOM_POTATO, 64, 270.5f),
	PINEAPPLE_PIZZA(ItemID.PINEAPPLE_PIZZA, 65, 188),
	WINE_OF_ZAMORAK(ItemID.WINE_OF_ZAMORAK, 65, 200, true),
	TUNA_AND_CORN(ItemID.TUNA_AND_CORN, 67, 204),
	TUNA_POTATO(ItemID.TUNA_POTATO, 68, 309.5f),
	ADMIRAL_PIE(ItemID.ADMIRAL_PIE, 70, 210),
	SACRED_EEL(ItemID.SACRED_EEL, 72, 109),
	DRAGONFRUIT_PIE(ItemID.DRAGONFRUIT_PIE, 73, 220),
	SHARK(ItemID.SHARK, 80, 210),
	SEA_TURTLE(ItemID.SEA_TURTLE, 82, 211.3f),
	ANGLERFISH(ItemID.ANGLERFISH, 84, 230),
	WILD_PIE(ItemID.WILD_PIE, 85, 240),
	DARK_CRAB(ItemID.DARK_CRAB, 90, 215),
	MANTA_RAY(ItemID.MANTA_RAY, 91, 216.3f),
	SUMMER_PIE(ItemID.SUMMER_PIE, 95, 260),
	;

	private final int itemId;
	private final int level;
	private final float xp;
	private final boolean isMembersOverride;

	CookingAction(int itemId, int level, float xp)
	{
		this(itemId, level, xp, false);
	}

	@Override
	public boolean isMembers(final ItemManager itemManager)
	{
		return isMembersOverride() || ItemSkillAction.super.isMembers(itemManager);
	}
}
