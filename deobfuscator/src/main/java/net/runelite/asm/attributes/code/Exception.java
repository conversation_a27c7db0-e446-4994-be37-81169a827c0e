/*
 * Copyright (c) 2016-2017, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

package net.runelite.asm.attributes.code;

import net.runelite.asm.ClassFile;
import net.runelite.asm.pool.Class;

public class Exception implements Cloneable
{
	private Exceptions exceptions;

	private Label start, end, handler;
	private Class catchType;

	public Exception(Exceptions exceptions)
	{
		this.exceptions = exceptions;
	}
	
	@Override
	public Exception clone()
	{
		try
		{
			return (Exception) super.clone();
		}
		catch (CloneNotSupportedException ex)
		{
			throw new RuntimeException();
		}
	}
	
	public Exceptions getExceptions()
	{
		return exceptions;
	}
	
	public void setExceptions(Exceptions exceptions)
	{
		this.exceptions = exceptions;
	}
	
	public Label getStart()
	{
		return start;
	}
	
	public void setStart(Label ins)
	{
		start = ins;
	}
	
	public Label getEnd()
	{
		return end;
	}

	public void setEnd(Label end)
	{
		this.end = end;
	}
	
	public Label getHandler()
	{
		return handler;
	}

	public void setHandler(Label handler)
	{
		this.handler = handler;
	}
	
	public Class getCatchType()
	{
		return catchType;
	}

	public void setCatchType(Class catchType)
	{
		this.catchType = catchType;
	}
	
	public void renameClass(ClassFile cf, String name)
	{
		if (catchType != null && cf.getName().equals(catchType.getName()))
			catchType = new Class(name);
	}
}
