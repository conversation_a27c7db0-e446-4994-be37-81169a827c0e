/* This file is automatically generated. Do not edit. */
package rs117.hd.data;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.util.HashSet;

import static rs117.hd.utils.GsonUtils.parseIDArray;
import static rs117.hd.utils.GsonUtils.writeIDArray;

public final class NpcID
{
    public static class JsonAdapter extends TypeAdapter<HashSet<Integer>>
    {
        @Override
        public HashSet<Integer> read(Json<PERSON>eader in) throws IOException
        {
            return parseIDArray(in, NpcID.class);
        }

        @Override
        public void write(JsonWriter out, HashSet<Integer> value) throws IOException
        {
            writeIDArray(out, value, NpcID.class);
        }
    }

    // HD NPC names
    public static final int LESSER_GHOSTLY_THRALL = 10878;
    public static final int SUPERIOR_GHOSTLY_THRALL = 10879;
    public static final int GREATER_GHOSTLY_THRALL = 10880;
    public static final int LESSER_SKELETAL_THRALL = 10881;
    public static final int SUPERIOR_SKELETAL_THRALL = 10882;
    public static final int GREATER_SKELETAL_THRALL = 10883;
    public static final int LESSER_ZOMBIFIED_THRALL = 10884;
    public static final int SUPERIOR_ZOMBIFIED_THRALL = 10885;
    public static final int GREATER_ZOMBIFIED_THRALL = 10886;

    // RuneLite NPC names. Do not edit.
    public static final int TOOL_LEPRECHAUN = 0;
    public static final int MOLANISK = 1;
    public static final int ABERRANT_SPECTRE = 2;
    public static final int ABERRANT_SPECTRE_3 = 3;
    public static final int ABERRANT_SPECTRE_4 = 4;
    public static final int ABERRANT_SPECTRE_5 = 5;
    public static final int ABERRANT_SPECTRE_6 = 6;
    public static final int ABERRANT_SPECTRE_7 = 7;
    public static final int NECHRYAEL = 8;
    public static final int TWIG = 9;
    public static final int DEATH_SPAWN = 10;
    public static final int NECHRYAEL_11 = 11;
    public static final int HUDO = 12;
    public static final int PILES = 13;
    public static final int ROMETTI = 14;
    public static final int GULLUCK = 15;
    public static final int HECKEL_FUNCH = 16;
    public static final int RUG_MERCHANT = 17;
    public static final int RUG_MERCHANT_18 = 18;
    public static final int RUG_MERCHANT_19 = 19;
    public static final int RUG_MERCHANT_20 = 20;
    public static final int RUG_MERCHANT_22 = 22;
    public static final int MONKEY = 23;
    public static final int ZOMBIE = 26;
    public static final int ZOMBIE_27 = 27;
    public static final int ZOMBIE_28 = 28;
    public static final int ZOMBIE_29 = 29;
    public static final int ZOMBIE_30 = 30;
    public static final int ZOMBIE_31 = 31;
    public static final int ZOMBIE_32 = 32;
    public static final int ZOMBIE_33 = 33;
    public static final int ZOMBIE_34 = 34;
    public static final int ZOMBIE_35 = 35;
    public static final int ZOMBIE_36 = 36;
    public static final int ZOMBIE_37 = 37;
    public static final int ZOMBIE_38 = 38;
    public static final int ZOMBIE_39 = 39;
    public static final int ZOMBIE_40 = 40;
    public static final int ZOMBIE_41 = 41;
    public static final int ZOMBIE_42 = 42;
    public static final int ZOMBIE_43 = 43;
    public static final int ZOMBIE_44 = 44;
    public static final int ZOMBIE_45 = 45;
    public static final int ZOMBIE_46 = 46;
    public static final int ZOMBIE_47 = 47;
    public static final int ZOMBIE_48 = 48;
    public static final int ZOMBIE_49 = 49;
    public static final int ZOMBIE_50 = 50;
    public static final int ZOMBIE_51 = 51;
    public static final int ZOMBIE_52 = 52;
    public static final int ZOMBIE_53 = 53;
    public static final int ZOMBIE_54 = 54;
    public static final int ZOMBIE_55 = 55;
    public static final int ZOMBIE_56 = 56;
    public static final int ZOMBIE_57 = 57;
    public static final int ZOMBIE_58 = 58;
    public static final int ZOMBIE_59 = 59;
    public static final int ZOMBIE_60 = 60;
    public static final int ZOMBIE_61 = 61;
    public static final int ZOMBIE_62 = 62;
    public static final int ZOMBIE_63 = 63;
    public static final int ZOMBIE_64 = 64;
    public static final int ZOMBIE_65 = 65;
    public static final int ZOMBIE_66 = 66;
    public static final int ZOMBIE_67 = 67;
    public static final int ZOMBIE_68 = 68;
    public static final int SUMMONED_ZOMBIE = 69;
    public static final int SKELETON = 70;
    public static final int SKELETON_71 = 71;
    public static final int SKELETON_72 = 72;
    public static final int SKELETON_73 = 73;
    public static final int SKELETON_74 = 74;
    public static final int SKELETON_75 = 75;
    public static final int SKELETON_76 = 76;
    public static final int SKELETON_77 = 77;
    public static final int SKELETON_78 = 78;
    public static final int SKELETON_79 = 79;
    public static final int SKELETON_80 = 80;
    public static final int SKELETON_81 = 81;
    public static final int SKELETON_82 = 82;
    public static final int SKELETON_83 = 83;
    public static final int SKELETON_MAGE = 84;
    public static final int GHOST = 85;
    public static final int GHOST_86 = 86;
    public static final int GHOST_87 = 87;
    public static final int GHOST_88 = 88;
    public static final int GHOST_89 = 89;
    public static final int GHOST_90 = 90;
    public static final int GHOST_91 = 91;
    public static final int GHOST_92 = 92;
    public static final int GHOST_93 = 93;
    public static final int GHOST_94 = 94;
    public static final int GHOST_95 = 95;
    public static final int GHOST_96 = 96;
    public static final int GHOST_97 = 97;
    public static final int GHOST_98 = 98;
    public static final int GHOST_99 = 99;
    public static final int ROCK_CRAB = 100;
    public static final int ROCKS = 101;
    public static final int ROCK_CRAB_102 = 102;
    public static final int ROCKS_103 = 103;
    public static final int HELLHOUND = 104;
    public static final int HELLHOUND_105 = 105;
    public static final int WOLF = 106;
    public static final int WHITE_WOLF = 107;
    public static final int WHITE_WOLF_108 = 108;
    public static final int BIG_WOLF = 109;
    public static final int WOLF_110 = 110;
    public static final int DOG = 111;
    public static final int WILD_DOG = 112;
    public static final int WILD_DOG_113 = 113;
    public static final int GUARD_DOG = 114;
    public static final int BIG_WOLF_115 = 115;
    public static final int WOLF_116 = 116;
    public static final int WOLF_117 = 117;
    public static final int IGNATIUS_VULCAN = 118;
    public static final int CRAWLING_HAND = 120;
    public static final int COCKATRICE = 121;
    public static final int BASILISK = 122;
    public static final int KURASK = 123;
    public static final int ABYSSAL_DEMON = 124;
    public static final int LEFT_HEAD = 125;
    public static final int MIDDLE_HEAD = 126;
    public static final int RIGHT_HEAD = 127;
    public static final int KALPHITE_QUEEN = 128;
    public static final int TENTACLE = 129;
    public static final int SKELETON_130 = 130;
    public static final int GUARD_DOG_131 = 131;
    public static final int HOBGOBLIN = 132;
    public static final int TROLL = 133;
    public static final int HUGE_SPIDER = 134;
    public static final int HELLHOUND_135 = 135;
    public static final int OGRE = 136;
    public static final int BABY_RED_DRAGON = 137;
    public static final int KALPHITE_SOLDIER = 138;
    public static final int STEEL_DRAGON = 139;
    public static final int DAGANNOTH = 140;
    public static final int TOKXIL = 141;
    public static final int DEMON = 142;
    public static final int ROCNAR = 143;
    public static final int HANGMAN_GAME = 144;
    public static final int HANGMAN_GAME_145 = 145;
    public static final int HANGMAN_GAME_146 = 146;
    public static final int HANGMAN_GAME_147 = 147;
    public static final int HANGMAN_GAME_148 = 148;
    public static final int HANGMAN_GAME_149 = 149;
    public static final int HANGMAN_GAME_150 = 150;
    public static final int HANGMAN_GAME_151 = 151;
    public static final int HANGMAN_GAME_152 = 152;
    public static final int HANGMAN_GAME_153 = 153;
    public static final int TREASURE_FAIRY = 154;
    public static final int JACKY_JESTER = 155;
    public static final int COMBAT_STONE = 156;
    public static final int COMBAT_STONE_157 = 157;
    public static final int COMBAT_STONE_158 = 158;
    public static final int COMBAT_STONE_159 = 159;
    public static final int COMBAT_STONE_160 = 160;
    public static final int COMBAT_STONE_161 = 161;
    public static final int COMBAT_STONE_162 = 162;
    public static final int COMBAT_STONE_163 = 163;
    public static final int COMBAT_STONE_164 = 164;
    public static final int COMBAT_STONE_165 = 165;
    public static final int COMBAT_STONE_166 = 166;
    public static final int COMBAT_STONE_167 = 167;
    public static final int COMBAT_STONE_168 = 168;
    public static final int COMBAT_STONE_169 = 169;
    public static final int COMBAT_STONE_170 = 170;
    public static final int COMBAT_STONE_171 = 171;
    public static final int COMBAT_STONE_172 = 172;
    public static final int COMBAT_STONE_173 = 173;
    public static final int COMBAT_STONE_174 = 174;
    public static final int COMBAT_STONE_175 = 175;
    public static final int COMBAT_STONE_176 = 176;
    public static final int COMBAT_STONE_177 = 177;
    public static final int COMBAT_STONE_178 = 178;
    public static final int COMBAT_STONE_179 = 179;
    public static final int COMBAT_STONE_180 = 180;
    public static final int COMBAT_STONE_181 = 181;
    public static final int COMBAT_STONE_182 = 182;
    public static final int COMBAT_STONE_183 = 183;
    public static final int COMBAT_STONE_184 = 184;
    public static final int COMBAT_STONE_185 = 185;
    public static final int COMBAT_STONE_186 = 186;
    public static final int COMBAT_STONE_187 = 187;
    public static final int COMBAT_STONE_188 = 188;
    public static final int COMBAT_STONE_189 = 189;
    public static final int COMBAT_STONE_190 = 190;
    public static final int COMBAT_STONE_191 = 191;
    public static final int COMBAT_STONE_192 = 192;
    public static final int COMBAT_STONE_193 = 193;
    public static final int COMBAT_STONE_194 = 194;
    public static final int COMBAT_STONE_195 = 195;
    public static final int COMBAT_STONE_196 = 196;
    public static final int COMBAT_STONE_197 = 197;
    public static final int COMBAT_STONE_198 = 198;
    public static final int COMBAT_STONE_199 = 199;
    public static final int COMBAT_STONE_200 = 200;
    public static final int COMBAT_STONE_201 = 201;
    public static final int COMBAT_STONE_202 = 202;
    public static final int COMBAT_STONE_203 = 203;
    public static final int COMBAT_STONE_204 = 204;
    public static final int COMBAT_STONE_205 = 205;
    public static final int COMBAT_STONE_206 = 206;
    public static final int COMBAT_STONE_207 = 207;
    public static final int COMBAT_STONE_208 = 208;
    public static final int COMBAT_STONE_209 = 209;
    public static final int COMBAT_STONE_210 = 210;
    public static final int COMBAT_STONE_211 = 211;
    public static final int COMBAT_STONE_212 = 212;
    public static final int COMBAT_STONE_213 = 213;
    public static final int COMBAT_STONE_214 = 214;
    public static final int COMBAT_STONE_215 = 215;
    public static final int COMBAT_STONE_216 = 216;
    public static final int COMBAT_STONE_217 = 217;
    public static final int COMBAT_STONE_218 = 218;
    public static final int COMBAT_STONE_219 = 219;
    public static final int COMBAT_STONE_220 = 220;
    public static final int RICK = 221;
    public static final int MAID = 223;
    public static final int COOK = 225;
    public static final int BUTLER = 227;
    public static final int DEMON_BUTLER = 229;
    public static final int WOLF_231 = 231;
    public static final int JUNGLE_WOLF = 232;
    public static final int MACARONI_PENGUIN = 233;
    public static final int BUTTERFLY = 234;
    public static final int BUTTERFLY_235 = 235;
    public static final int BUTTERFLY_236 = 236;
    public static final int BUTTERFLY_237 = 237;
    public static final int BUTTERFLY_238 = 238;
    public static final int KING_BLACK_DRAGON = 239;
    public static final int BLACK_DEMON = 240;
    public static final int BABY_BLUE_DRAGON = 241;
    public static final int BABY_BLUE_DRAGON_242 = 242;
    public static final int BABY_BLUE_DRAGON_243 = 243;
    public static final int BABY_RED_DRAGON_244 = 244;
    public static final int BABY_RED_DRAGON_245 = 245;
    public static final int BABY_RED_DRAGON_246 = 246;
    public static final int RED_DRAGON = 247;
    public static final int RED_DRAGON_248 = 248;
    public static final int RED_DRAGON_249 = 249;
    public static final int RED_DRAGON_250 = 250;
    public static final int RED_DRAGON_251 = 251;
    public static final int BLACK_DRAGON = 252;
    public static final int BLACK_DRAGON_253 = 253;
    public static final int BLACK_DRAGON_254 = 254;
    public static final int BLACK_DRAGON_255 = 255;
    public static final int BLACK_DRAGON_256 = 256;
    public static final int BLACK_DRAGON_257 = 257;
    public static final int BLACK_DRAGON_258 = 258;
    public static final int BLACK_DRAGON_259 = 259;
    public static final int GREEN_DRAGON = 260;
    public static final int GREEN_DRAGON_261 = 261;
    public static final int GREEN_DRAGON_262 = 262;
    public static final int GREEN_DRAGON_263 = 263;
    public static final int GREEN_DRAGON_264 = 264;
    public static final int BLUE_DRAGON = 265;
    public static final int BLUE_DRAGON_266 = 266;
    public static final int BLUE_DRAGON_267 = 267;
    public static final int BLUE_DRAGON_268 = 268;
    public static final int BLUE_DRAGON_269 = 269;
    public static final int BRONZE_DRAGON = 270;
    public static final int BRONZE_DRAGON_271 = 271;
    public static final int IRON_DRAGON = 272;
    public static final int IRON_DRAGON_273 = 273;
    public static final int STEEL_DRAGON_274 = 274;
    public static final int STEEL_DRAGON_275 = 275;
    public static final int TOWN_CRIER = 276;
    public static final int TOWN_CRIER_277 = 277;
    public static final int TOWN_CRIER_278 = 278;
    public static final int TOWN_CRIER_279 = 279;
    public static final int TOWN_CRIER_280 = 280;
    public static final int GULL = 281;
    public static final int CORMORANT = 282;
    public static final int PELICAN = 283;
    public static final int GULL_284 = 284;
    public static final int GULL_285 = 285;
    public static final int GULL_286 = 286;
    public static final int CORMORANT_287 = 287;
    public static final int PELICAN_288 = 288;
    public static final int GHOUL = 289;
    public static final int DWARF = 290;
    public static final int CHAOS_DWARF = 291;
    public static final int DWARF_292 = 292;
    public static final int ADVENTURER_JON = 293;
    public static final int DWARF_294 = 294;
    public static final int DWARF_295 = 295;
    public static final int DWARF_296 = 296;
    public static final int THORDUR = 297;
    public static final int THORDUR_298 = 298;
    public static final int GUNTHOR_THE_BRAVE = 299;
    public static final int JAILER = 300;
    public static final int BLACK_HEATHER = 301;
    public static final int DONNY_THE_LAD = 302;
    public static final int SPEEDY_KEITH = 303;
    public static final int SALARIN_THE_TWISTED = 304;
    public static final int JENNIFER = 305;
    public static final int LUMBRIDGE_GUIDE = 306;
    public static final int DR_JEKYLL = 307;
    public static final int EMBLEM_TRADER = 308;
    public static final int REACHER = 309;
    public static final int AYESHA = 310;
    public static final int IRON_MAN_TUTOR = 311;
    public static final int FROG = 312;
    public static final int REACHER_313 = 313;
    public static final int DR_JEKYLL_314 = 314;
    public static final int LEAGUES_TUTOR = 315;
    public static final int LEAGUES_TUTOR_316 = 316;
    public static final int DARK_CORE = 318;
    public static final int CORPOREAL_BEAST = 319;
    public static final int DARK_ENERGY_CORE = 320;
    public static final int MIME = 321;
    public static final int DRUNKEN_DWARF = 322;
    public static final int STRANGE_PLANT = 323;
    public static final int GENIE = 326;
    public static final int GENIE_327 = 327;
    public static final int REACHER_328 = 328;
    public static final int REACHER_329 = 329;
    public static final int SWARM = 330;
    public static final int REACHER_331 = 331;
    public static final int STRANGE_WATCHER = 332;
    public static final int STRANGE_WATCHER_333 = 333;
    public static final int STRANGE_WATCHER_334 = 334;
    public static final int REACHER_335 = 335;
    public static final int REACHER_336 = 336;
    public static final int SERGEANT_DAMIEN = 337;
    public static final int SUSPECT = 338;
    public static final int SUSPECT_339 = 339;
    public static final int SUSPECT_340 = 340;
    public static final int SUSPECT_341 = 341;
    public static final int MOLLY = 342;
    public static final int SUSPECT_343 = 343;
    public static final int SUSPECT_344 = 344;
    public static final int SUSPECT_345 = 345;
    public static final int SUSPECT_346 = 346;
    public static final int SUSPECT_347 = 347;
    public static final int SUSPECT_348 = 348;
    public static final int SUSPECT_349 = 349;
    public static final int SUSPECT_350 = 350;
    public static final int SUSPECT_351 = 351;
    public static final int MOLLY_352 = 352;
    public static final int SUSPECT_353 = 353;
    public static final int SUSPECT_354 = 354;
    public static final int SUSPECT_355 = 355;
    public static final int MOLLY_356 = 356;
    public static final int SUSPECT_357 = 357;
    public static final int SUSPECT_358 = 358;
    public static final int SUSPECT_359 = 359;
    public static final int SUSPECT_360 = 360;
    public static final int MOLLY_361 = 361;
    public static final int MOLLY_362 = 362;
    public static final int MOLLY_363 = 363;
    public static final int MOLLY_364 = 364;
    public static final int MOLLY_365 = 365;
    public static final int MOLLY_366 = 366;
    public static final int MOLLY_367 = 367;
    public static final int PRISON_PETE = 368;
    public static final int BALLOON_ANIMAL = 369;
    public static final int BALLOON_ANIMAL_370 = 370;
    public static final int BALLOON_ANIMAL_371 = 371;
    public static final int FREAKY_FORESTER = 372;
    public static final int PHEASANT = 373;
    public static final int PHEASANT_374 = 374;
    public static final int RICK_TURPENTINE = 375;
    public static final int RICK_TURPENTINE_376 = 376;
    public static final int PHOSANIS_NIGHTMARE = 377;
    public static final int THE_NIGHTMARE = 378;
    public static final int QUIZ_MASTER = 379;
    public static final int PILLORY_GUARD = 380;
    public static final int TRAMP = 381;
    public static final int TRAMP_382 = 382;
    public static final int TRAMP_383 = 383;
    public static final int MAN = 385;
    public static final int AVAN = 386;
    public static final int AVAN_387 = 387;
    public static final int DARK_CORE_388 = 388;
    public static final int REACHER_389 = 389;
    public static final int EVIL_BOB = 390;
    public static final int EVIL_BOB_391 = 391;
    public static final int SERVANT = 392;
    public static final int SERVANT_393 = 393;
    public static final int ROD_FISHING_SPOT = 394;
    public static final int CAT = 395;
    public static final int JON = 396;
    public static final int GUARD = 397;
    public static final int GUARD_398 = 398;
    public static final int GUARD_399 = 399;
    public static final int GUARD_400 = 400;
    public static final int TURAEL = 401;
    public static final int MAZCHNA = 402;
    public static final int VANNAKA = 403;
    public static final int CHAELDAR = 404;
    public static final int DURADEL = 405;
    public static final int CAVE_CRAWLER = 406;
    public static final int CAVE_CRAWLER_407 = 407;
    public static final int CAVE_CRAWLER_408 = 408;
    public static final int CAVE_CRAWLER_409 = 409;
    public static final int KURASK_410 = 410;
    public static final int KURASK_411 = 411;
    public static final int GARGOYLE = 412;
    public static final int GARGOYLE_413 = 413;
    public static final int BANSHEE = 414;
    public static final int ABYSSAL_DEMON_415 = 415;
    public static final int ABYSSAL_DEMON_416 = 416;
    public static final int BASILISK_417 = 417;
    public static final int BASILISK_418 = 418;
    public static final int COCKATRICE_419 = 419;
    public static final int COCKATRICE_420 = 420;
    public static final int ROCKSLUG = 421;
    public static final int ROCKSLUG_422 = 422;
    public static final int DUST_DEVIL = 423;
    public static final int SKOTOS = 425;
    public static final int TUROTH = 426;
    public static final int TUROTH_427 = 427;
    public static final int TUROTH_428 = 428;
    public static final int TUROTH_429 = 429;
    public static final int TUROTH_430 = 430;
    public static final int TUROTH_431 = 431;
    public static final int TUROTH_432 = 432;
    public static final int PYREFIEND = 433;
    public static final int PYREFIEND_434 = 434;
    public static final int PYREFIEND_435 = 435;
    public static final int PYREFIEND_436 = 436;
    public static final int JELLY = 437;
    public static final int JELLY_438 = 438;
    public static final int JELLY_439 = 439;
    public static final int JELLY_440 = 440;
    public static final int JELLY_441 = 441;
    public static final int JELLY_442 = 442;
    public static final int INFERNAL_MAGE = 443;
    public static final int INFERNAL_MAGE_444 = 444;
    public static final int INFERNAL_MAGE_445 = 445;
    public static final int INFERNAL_MAGE_446 = 446;
    public static final int INFERNAL_MAGE_447 = 447;
    public static final int CRAWLING_HAND_448 = 448;
    public static final int CRAWLING_HAND_449 = 449;
    public static final int CRAWLING_HAND_450 = 450;
    public static final int CRAWLING_HAND_451 = 451;
    public static final int CRAWLING_HAND_452 = 452;
    public static final int CRAWLING_HAND_453 = 453;
    public static final int CRAWLING_HAND_454 = 454;
    public static final int CRAWLING_HAND_455 = 455;
    public static final int CRAWLING_HAND_456 = 456;
    public static final int CRAWLING_HAND_457 = 457;
    public static final int LIZARD = 458;
    public static final int DESERT_LIZARD = 459;
    public static final int DESERT_LIZARD_460 = 460;
    public static final int DESERT_LIZARD_461 = 461;
    public static final int SMALL_LIZARD = 462;
    public static final int SMALL_LIZARD_463 = 463;
    public static final int HARPIE_BUG_SWARM = 464;
    public static final int SKELETAL_WYVERN = 465;
    public static final int SKELETAL_WYVERN_466 = 466;
    public static final int SKELETAL_WYVERN_467 = 467;
    public static final int SKELETAL_WYVERN_468 = 468;
    public static final int KILLERWATT = 469;
    public static final int KILLERWATT_470 = 470;
    public static final int GHOST_472 = 472;
    public static final int GHOST_473 = 473;
    public static final int GHOST_474 = 474;
    public static final int HOLE_IN_THE_WALL = 475;
    public static final int WALL_BEAST = 476;
    public static final int GIANT_FROG = 477;
    public static final int BIG_FROG = 478;
    public static final int FROG_479 = 479;
    public static final int CAVE_SLIME = 480;
    public static final int CAVE_BUG = 481;
    public static final int CAVE_BUG_LARVA = 482;
    public static final int CAVE_BUG_483 = 483;
    public static final int BLOODVELD = 484;
    public static final int BLOODVELD_485 = 485;
    public static final int BLOODVELD_486 = 486;
    public static final int BLOODVELD_487 = 487;
    public static final int STORM_CLOUD = 488;
    public static final int STORM_CLOUD_489 = 489;
    public static final int ENTOMOLOGIST = 491;
    public static final int CAVE_KRAKEN = 492;
    public static final int WHIRLPOOL = 493;
    public static final int KRAKEN = 494;
    public static final int VENENATIS_SPIDERLING = 495;
    public static final int WHIRLPOOL_496 = 496;
    public static final int CALLISTO_CUB = 497;
    public static final int SMOKE_DEVIL = 498;
    public static final int THERMONUCLEAR_SMOKE_DEVIL = 499;
    public static final int OLIVIA = 500;
    public static final int SARAH = 501;
    public static final int VANESSA = 502;
    public static final int RICHARD = 503;
    public static final int ALICE = 504;
    public static final int GHOST_505 = 505;
    public static final int GHOST_506 = 506;
    public static final int GHOST_507 = 507;
    public static final int SOULLESS = 508;
    public static final int DEATH_WING = 509;
    public static final int DARK_WIZARD = 510;
    public static final int INVRIGAR_THE_NECROMANCER = 511;
    public static final int DARK_WIZARD_512 = 512;
    public static final int MUGGER = 513;
    public static final int WITCH = 514;
    public static final int WITCH_515 = 515;
    public static final int BLACK_KNIGHT = 516;
    public static final int BLACK_KNIGHT_517 = 517;
    public static final int HIGHWAYMAN = 518;
    public static final int HIGHWAYMAN_519 = 519;
    public static final int CHAOS_DRUID = 520;
    public static final int PIRATE = 521;
    public static final int PIRATE_522 = 522;
    public static final int PIRATE_523 = 523;
    public static final int PIRATE_524 = 524;
    public static final int THUG = 525;
    public static final int ROGUE = 526;
    public static final int MONK_OF_ZAMORAK = 527;
    public static final int MONK_OF_ZAMORAK_528 = 528;
    public static final int MONK_OF_ZAMORAK_529 = 529;
    public static final int TRIBESMAN = 530;
    public static final int DARK_WARRIOR = 531;
    public static final int CHAOS_DRUID_WARRIOR = 532;
    public static final int FUNGI = 533;
    public static final int FUNGI_535 = 535;
    public static final int ZYGOMITE = 537;
    public static final int FUNGI_538 = 538;
    public static final int ELFINLOCKS = 539;
    public static final int CLOCKWORK_CAT = 540;
    public static final int CLOCKWORK_CAT_541 = 541;
    public static final int MONK = 542;
    public static final int MONK_543 = 543;
    public static final int MONK_544 = 544;
    public static final int MONK_545 = 545;
    public static final int RUFUS = 546;
    public static final int MIGOR = 547;
    public static final int PUFFIN = 548;
    public static final int PUFFIN_549 = 549;
    public static final int BROTHER_TRANQUILITY = 550;
    public static final int BROTHER_TRANQUILITY_551 = 551;
    public static final int BROTHER_TRANQUILITY_552 = 552;
    public static final int MONK_553 = 553;
    public static final int MONK_554 = 554;
    public static final int MONK_555 = 555;
    public static final int MONK_556 = 556;
    public static final int ZOMBIE_MONK = 557;
    public static final int ZOMBIE_MONK_558 = 558;
    public static final int ZOMBIE_MONK_559 = 559;
    public static final int ZOMBIE_MONK_560 = 560;
    public static final int SOREBONES = 561;
    public static final int SOREBONES_562 = 562;
    public static final int ZOMBIE_PIRATE = 563;
    public static final int ZOMBIE_PIRATE_564 = 564;
    public static final int ZOMBIE_PIRATE_565 = 565;
    public static final int ZOMBIE_PIRATE_566 = 566;
    public static final int ZOMBIE_PIRATE_567 = 567;
    public static final int ZOMBIE_PIRATE_568 = 568;
    public static final int ZOMBIE_PIRATE_569 = 569;
    public static final int ZOMBIE_PIRATE_570 = 570;
    public static final int ZOMBIE_PIRATE_571 = 571;
    public static final int ZOMBIE_PIRATE_572 = 572;
    public static final int ZOMBIE_PIRATE_573 = 573;
    public static final int ZOMBIE_PIRATE_574 = 574;
    public static final int ZOMBIE_PIRATE_575 = 575;
    public static final int ZOMBIE_PIRATE_576 = 576;
    public static final int ZOMBIE_PIRATE_577 = 577;
    public static final int ZOMBIE_PIRATE_578 = 578;
    public static final int ZOMBIE_PIRATE_579 = 579;
    public static final int ZOMBIE_PIRATE_580 = 580;
    public static final int ZOMBIE_PIRATE_581 = 581;
    public static final int ZOMBIE_PIRATE_582 = 582;
    public static final int ZOMBIE_PIRATE_583 = 583;
    public static final int ZOMBIE_PIRATE_584 = 584;
    public static final int ZOMBIE_PIRATE_585 = 585;
    public static final int ZOMBIE_PIRATE_586 = 586;
    public static final int ZOMBIE_PIRATE_587 = 587;
    public static final int ZOMBIE_PIRATE_588 = 588;
    public static final int ZOMBIE_PIRATE_589 = 589;
    public static final int ZOMBIE_PIRATE_590 = 590;
    public static final int ZOMBIE_PIRATE_591 = 591;
    public static final int ZOMBIE_PIRATE_592 = 592;
    public static final int ZOMBIE_PIRATE_593 = 593;
    public static final int ZOMBIE_PIRATE_594 = 594;
    public static final int ZOMBIE_PIRATE_595 = 595;
    public static final int ZOMBIE_PIRATE_596 = 596;
    public static final int ZOMBIE_PIRATE_597 = 597;
    public static final int ZOMBIE_PIRATE_598 = 598;
    public static final int ZOMBIE_PIRATE_599 = 599;
    public static final int BARRELCHEST = 600;
    public static final int PIRATE_PETE = 601;
    public static final int PIRATE_PETE_602 = 602;
    public static final int CAPTAIN_BRAINDEATH = 603;
    public static final int _50_LUKE = 604;
    public static final int DAVEY = 605;
    public static final int CAPTAIN_DONNIE = 606;
    public static final int ZOMBIE_PROTESTER = 607;
    public static final int ZOMBIE_PROTESTER_608 = 608;
    public static final int ZOMBIE_PROTESTER_609 = 609;
    public static final int ZOMBIE_PROTESTER_610 = 610;
    public static final int ZOMBIE_PROTESTER_611 = 611;
    public static final int ZOMBIE_PROTESTER_612 = 612;
    public static final int ZOMBIE_PIRATE_613 = 613;
    public static final int ZOMBIE_PIRATE_614 = 614;
    public static final int ZOMBIE_PIRATE_615 = 615;
    public static final int ZOMBIE_PIRATE_616 = 616;
    public static final int ZOMBIE_PIRATE_617 = 617;
    public static final int ZOMBIE_PIRATE_618 = 618;
    public static final int ZOMBIE_SWAB = 619;
    public static final int ZOMBIE_SWAB_620 = 620;
    public static final int ZOMBIE_SWAB_621 = 621;
    public static final int ZOMBIE_SWAB_622 = 622;
    public static final int ZOMBIE_SWAB_623 = 623;
    public static final int ZOMBIE_SWAB_624 = 624;
    public static final int EVIL_SPIRIT = 625;
    public static final int FEVER_SPIDER = 626;
    public static final int BREWER = 627;
    public static final int BREWER_628 = 628;
    public static final int BREWER_629 = 629;
    public static final int BREWER_630 = 630;
    public static final int BREWER_631 = 631;
    public static final int BREWER_632 = 632;
    public static final int BREWER_633 = 633;
    public static final int BREWER_634 = 634;
    public static final int FISHING_SPOT = 635;
    public static final int KARAMTHULHU = 636;
    public static final int FUNGI_637 = 637;
    public static final int TYRAS_GUARD = 639;
    public static final int UG = 640;
    public static final int AGA = 641;
    public static final int ARRG = 642;
    public static final int ARRG_643 = 643;
    public static final int UG_644 = 644;
    public static final int ICE_WOLF = 645;
    public static final int ICE_WOLF_646 = 646;
    public static final int ICE_WOLF_647 = 647;
    public static final int ICE_TROLL = 648;
    public static final int ICE_TROLL_649 = 649;
    public static final int ICE_TROLL_650 = 650;
    public static final int ICE_TROLL_651 = 651;
    public static final int ICE_TROLL_652 = 652;
    public static final int ICE_TROLL_653 = 653;
    public static final int ICE_TROLL_654 = 654;
    public static final int GOBLIN = 655;
    public static final int GOBLIN_656 = 656;
    public static final int GOBLIN_657 = 657;
    public static final int GOBLIN_658 = 658;
    public static final int GOBLIN_659 = 659;
    public static final int GOBLIN_660 = 660;
    public static final int GOBLIN_661 = 661;
    public static final int GOBLIN_662 = 662;
    public static final int GOBLIN_663 = 663;
    public static final int GOBLIN_664 = 664;
    public static final int GOBLIN_665 = 665;
    public static final int GOBLIN_666 = 666;
    public static final int GOBLIN_667 = 667;
    public static final int GOBLIN_668 = 668;
    public static final int GENERAL_BENTNOZE = 669;
    public static final int GENERAL_WARTFACE = 670;
    public static final int GRUBFOOT = 671;
    public static final int GRUBFOOT_672 = 672;
    public static final int GRUBFOOT_673 = 673;
    public static final int GOBLIN_674 = 674;
    public static final int GENERAL_BENTNOZE_675 = 675;
    public static final int GENERAL_WARTFACE_676 = 676;
    public static final int GOBLIN_677 = 677;
    public static final int GOBLIN_678 = 678;
    public static final int RASOLO = 679;
    public static final int GIANT_SKELETON = 680;
    public static final int GIANT_SKELETON_681 = 681;
    public static final int DAMIS = 682;
    public static final int DAMIS_683 = 683;
    public static final int ARCHAEOLOGIST = 684;
    public static final int STRANGER = 685;
    public static final int MALAK = 686;
    public static final int BARTENDER = 687;
    public static final int EBLIS = 688;
    public static final int EBLIS_689 = 689;
    public static final int BANDIT = 690;
    public static final int BANDIT_691 = 691;
    public static final int BANDIT_692 = 692;
    public static final int BANDIT_693 = 693;
    public static final int BANDIT_694 = 694;
    public static final int BANDIT_695 = 695;
    public static final int TROLL_CHILD = 696;
    public static final int TROLL_CHILD_697 = 697;
    public static final int ICE_TROLL_698 = 698;
    public static final int ICE_TROLL_699 = 699;
    public static final int ICE_TROLL_700 = 700;
    public static final int ICE_TROLL_701 = 701;
    public static final int ICE_TROLL_702 = 702;
    public static final int ICE_TROLL_703 = 703;
    public static final int ICE_TROLL_704 = 704;
    public static final int ICE_TROLL_705 = 705;
    public static final int ICE_BLOCK = 706;
    public static final int ICE_BLOCK_707 = 707;
    public static final int TROLL_FATHER = 708;
    public static final int TROLL_MOTHER = 709;
    public static final int ICE_WOLF_710 = 710;
    public static final int ICE_WOLF_711 = 711;
    public static final int ICE_WOLF_712 = 712;
    public static final int ICE_WOLF_713 = 713;
    public static final int ICE_WOLF_714 = 714;
    public static final int ICE_WOLF_715 = 715;
    public static final int MUMMY = 717;
    public static final int MUMMY_ASHES = 718;
    public static final int MUMMY_ASHES_719 = 719;
    public static final int MUMMY_720 = 720;
    public static final int MUMMY_721 = 721;
    public static final int MUMMY_722 = 722;
    public static final int MUMMY_723 = 723;
    public static final int MUMMY_724 = 724;
    public static final int MUMMY_725 = 725;
    public static final int MUMMY_726 = 726;
    public static final int MUMMY_727 = 727;
    public static final int MUMMY_728 = 728;
    public static final int SCARABS = 729;
    public static final int AZZANADRA = 730;
    public static final int SHEEP = 731;
    public static final int FRED_THE_FARMER = 732;
    public static final int BANDIT_LEADER = 733;
    public static final int BANDIT_734 = 734;
    public static final int BANDIT_735 = 735;
    public static final int BANDIT_736 = 736;
    public static final int BANDIT_737 = 737;
    public static final int BANDIT_CHAMPION = 738;
    public static final int COWARDLY_BANDIT = 739;
    public static final int MY_ARM = 741;
    public static final int MY_ARM_742 = 742;
    public static final int MY_ARM_750 = 750;
    public static final int MY_ARM_751 = 751;
    public static final int MY_ARM_752 = 752;
    public static final int ADVENTURER = 753;
    public static final int CAPTAIN_BARNABY = 754;
    public static final int MURCAILY = 755;
    public static final int JAGBAKOBA = 756;
    public static final int TOOL_LEPRECHAUN_757 = 757;
    public static final int FLIES = 758;
    public static final int UNNAMED_TROLL_CHILD = 759;
    public static final int DRUNKEN_DWARFS_LEG = 760;
    public static final int BABY_ROC = 762;
    public static final int GIANT_ROC = 763;
    public static final int SHADOW = 764;
    public static final int QUEEN_SIGRID = 765;
    public static final int BANKER = 766;
    public static final int ARNOR = 767;
    public static final int HAMING = 768;
    public static final int MOLDOF = 769;
    public static final int HELGA = 770;
    public static final int MATILDA = 771;
    public static final int ASHILD = 772;
    public static final int SKRAELING = 773;
    public static final int SKRAELING_774 = 774;
    public static final int FISHMONGER = 775;
    public static final int GREENGROCER = 776;
    public static final int ETHEREAL_MAN = 777;
    public static final int ETHEREAL_LADY = 778;
    public static final int ETHEREAL_NUMERATOR = 779;
    public static final int ETHEREAL_EXPERT = 780;
    public static final int ETHEREAL_PERCEPTIVE = 781;
    public static final int ETHEREAL_GUIDE = 782;
    public static final int ETHEREAL_FLUKE = 783;
    public static final int ETHEREAL_MIMIC = 784;
    public static final int ME = 785;
    public static final int ME_786 = 786;
    public static final int SUQAH = 787;
    public static final int SUQAH_788 = 788;
    public static final int SUQAH_789 = 789;
    public static final int SUQAH_790 = 790;
    public static final int SUQAH_791 = 791;
    public static final int SUQAH_792 = 792;
    public static final int SUQAH_793 = 793;
    public static final int SCARAB_MAGE = 794;
    public static final int LOCUST_RIDER = 795;
    public static final int LOCUST_RIDER_796 = 796;
    public static final int GIANT_SCARAB = 797;
    public static final int GIANT_SCARAB_798 = 798;
    public static final int SCARAB_MAGE_799 = 799;
    public static final int LOCUST_RIDER_800 = 800;
    public static final int LOCUST_RIDER_801 = 801;
    public static final int OLAF_THE_BARD = 802;
    public static final int LALLI = 803;
    public static final int GOLDEN_SHEEP = 804;
    public static final int GOLDEN_SHEEP_805 = 805;
    public static final int GOLDEN_SHEEP_806 = 806;
    public static final int GOLDEN_SHEEP_807 = 807;
    public static final int FOSSEGRIMEN = 808;
    public static final int OSPAK = 809;
    public static final int STYRMIR = 810;
    public static final int TORBRUND = 811;
    public static final int FRIDGEIR = 812;
    public static final int LONGHALL_BOUNCER = 813;
    public static final int GUILDMASTER = 814;
    public static final int DUKE_HORACIO = 815;
    public static final int ELVARG = 817;
    public static final int KLARENSE = 819;
    public static final int WORMBRAIN = 820;
    public static final int ORACLE = 821;
    public static final int OZIACH = 822;
    public static final int MELZAR_THE_MAD = 823;
    public static final int CAPTAIN_NED = 824;
    public static final int CABIN_BOY_JENKINS = 825;
    public static final int CABIN_BOY_JENKINS_826 = 826;
    public static final int LARRY = 827;
    public static final int LARRY_828 = 828;
    public static final int LARRY_829 = 829;
    public static final int PENGUIN = 830;
    public static final int PENGUIN_831 = 831;
    public static final int PENGUIN_832 = 832;
    public static final int KGP_GUARD = 833;
    public static final int PESCALING_PAX = 834;
    public static final int PING = 835;
    public static final int PING_836 = 836;
    public static final int PONG = 837;
    public static final int PONG_838 = 838;
    public static final int PING_839 = 839;
    public static final int PONG_840 = 840;
    public static final int KGP_AGENT = 841;
    public static final int KGP_AGENT_842 = 842;
    public static final int NOODLE = 844;
    public static final int PENGUIN_845 = 845;
    public static final int PENGUIN_SUIT = 846;
    public static final int AGILITY_INSTRUCTOR = 847;
    public static final int ARMY_COMMANDER = 848;
    public static final int PENGUIN_849 = 849;
    public static final int PENGUIN_850 = 850;
    public static final int PENGUIN_851 = 851;
    public static final int ICELORD = 852;
    public static final int ICELORD_853 = 853;
    public static final int ICELORD_854 = 854;
    public static final int ICELORD_855 = 855;
    public static final int CRUSHER = 856;
    public static final int CRUSHER_857 = 857;
    public static final int CRUSHER_858 = 858;
    public static final int CRUSHER_859 = 859;
    public static final int GRISH = 860;
    public static final int UGLUG_NAR = 861;
    public static final int PILG = 862;
    public static final int GRUG = 863;
    public static final int OGRE_GUARD = 864;
    public static final int OGRE_GUARD_865 = 865;
    public static final int ZOGRE = 866;
    public static final int ZOGRE_867 = 867;
    public static final int ZOGRE_868 = 868;
    public static final int ZOGRE_869 = 869;
    public static final int ZOGRE_870 = 870;
    public static final int ZOGRE_871 = 871;
    public static final int SKOGRE = 872;
    public static final int ZOGRE_873 = 873;
    public static final int ZOGRE_874 = 874;
    public static final int ZOGRE_875 = 875;
    public static final int ZOGRE_876 = 876;
    public static final int ZOGRE_877 = 877;
    public static final int SKOGRE_878 = 878;
    public static final int SKOGRE_879 = 879;
    public static final int ZOMBIE_880 = 880;
    public static final int ZAVISTIC_RARVE = 881;
    public static final int SLASH_BASH = 882;
    public static final int SITHIK_INTS = 883;
    public static final int SITHIK_INTS_884 = 884;
    public static final int GARGH = 885;
    public static final int SCARG = 886;
    public static final int GRUH = 887;
    public static final int IRWIN_FEASELBAUM = 888;
    public static final int MOSS_GUARDIAN = 891;
    public static final int GOLRIE = 892;
    public static final int FATHER_REEN = 893;
    public static final int FATHER_REEN_894 = 894;
    public static final int FATHER_BADDEN = 895;
    public static final int FATHER_BADDEN_896 = 896;
    public static final int DENATH = 897;
    public static final int DENATH_898 = 898;
    public static final int ERIC = 899;
    public static final int ERIC_900 = 900;
    public static final int EVIL_DAVE = 901;
    public static final int EVIL_DAVE_902 = 902;
    public static final int MATTHEW = 903;
    public static final int MATTHEW_904 = 904;
    public static final int JENNIFER_905 = 905;
    public static final int JENNIFER_906 = 906;
    public static final int TANYA = 907;
    public static final int TANYA_908 = 908;
    public static final int PATRICK = 909;
    public static final int PATRICK_910 = 910;
    public static final int AGRITH_NAAR = 911;
    public static final int SAND_STORM = 912;
    public static final int CLAY_GOLEM = 917;
    public static final int CLAY_GOLEM_918 = 918;
    public static final int GHOST_920 = 920;
    public static final int DONIE = 921;
    public static final int RESTLESS_GHOST = 922;
    public static final int FATHER_URHNEY = 923;
    public static final int SKELETON_924 = 924;
    public static final int ROCK = 925;
    public static final int STICK = 926;
    public static final int PEE_HAT = 927;
    public static final int KRAKA = 928;
    public static final int DUNG = 929;
    public static final int ASH = 930;
    public static final int THROWER_TROLL = 931;
    public static final int THROWER_TROLL_932 = 932;
    public static final int THROWER_TROLL_933 = 933;
    public static final int THROWER_TROLL_934 = 934;
    public static final int THROWER_TROLL_935 = 935;
    public static final int MOUNTAIN_TROLL = 936;
    public static final int MOUNTAIN_TROLL_937 = 937;
    public static final int MOUNTAIN_TROLL_938 = 938;
    public static final int MOUNTAIN_TROLL_939 = 939;
    public static final int MOUNTAIN_TROLL_940 = 940;
    public static final int MOUNTAIN_TROLL_941 = 941;
    public static final int MOUNTAIN_TROLL_942 = 942;
    public static final int FILLIMAN_TARLOCK = 943;
    public static final int NATURE_SPIRIT = 944;
    public static final int GHAST = 945;
    public static final int GHAST_946 = 946;
    public static final int ULIZIUS = 947;
    public static final int KLENTER = 948;
    public static final int MUMMY_949 = 949;
    public static final int MUMMY_950 = 950;
    public static final int MUMMY_951 = 951;
    public static final int MUMMY_952 = 952;
    public static final int MUMMY_953 = 953;
    public static final int WEIRD_OLD_MAN = 954;
    public static final int KALPHITE_WORKER = 955;
    public static final int KALPHITE_WORKER_956 = 956;
    public static final int KALPHITE_SOLDIER_957 = 957;
    public static final int KALPHITE_SOLDIER_958 = 958;
    public static final int KALPHITE_GUARDIAN = 959;
    public static final int KALPHITE_GUARDIAN_960 = 960;
    public static final int KALPHITE_WORKER_961 = 961;
    public static final int KALPHITE_GUARDIAN_962 = 962;
    public static final int KALPHITE_QUEEN_963 = 963;
    public static final int HELLPUPPY = 964;
    public static final int KALPHITE_QUEEN_965 = 965;
    public static final int KALPHITE_LARVA = 966;
    public static final int ANNA = 967;
    public static final int DAVID = 968;
    public static final int ANNA_969 = 969;
    public static final int DAGANNOTH_970 = 970;
    public static final int DAGANNOTH_971 = 971;
    public static final int DAGANNOTH_972 = 972;
    public static final int DAGANNOTH_973 = 973;
    public static final int DAGANNOTH_974 = 974;
    public static final int DAGANNOTH_975 = 975;
    public static final int DAGANNOTH_976 = 976;
    public static final int DAGANNOTH_977 = 977;
    public static final int DAGANNOTH_978 = 978;
    public static final int DAGANNOTH_979 = 979;
    public static final int DAGANNOTH_MOTHER = 980;
    public static final int DAGANNOTH_MOTHER_981 = 981;
    public static final int DAGANNOTH_MOTHER_982 = 982;
    public static final int DAGANNOTH_MOTHER_983 = 983;
    public static final int DAGANNOTH_MOTHER_984 = 984;
    public static final int DAGANNOTH_MOTHER_985 = 985;
    public static final int DAGANNOTH_MOTHER_986 = 986;
    public static final int DAGANNOTH_MOTHER_987 = 987;
    public static final int DAGANNOTH_MOTHER_988 = 988;
    public static final int VELIAF_HURTZ = 989;
    public static final int SIGMUND = 990;
    public static final int SIGMUND_991 = 991;
    public static final int SIGMUND_992 = 992;
    public static final int SIGMUND_993 = 993;
    public static final int SIGMUND_994 = 994;
    public static final int GUARD_995 = 995;
    public static final int NARDOK = 996;
    public static final int DARTOG = 997;
    public static final int GUARD_998 = 998;
    public static final int GUARD_999 = 999;
    public static final int GUARD_1000 = 1000;
    public static final int GUARD_1001 = 1001;
    public static final int GUARD_1002 = 1002;
    public static final int GUARD_1003 = 1003;
    public static final int GUARD_1004 = 1004;
    public static final int GUARD_1005 = 1005;
    public static final int GUARD_1006 = 1006;
    public static final int GUARD_1007 = 1007;
    public static final int GUARD_1008 = 1008;
    public static final int GUARD_1009 = 1009;
    public static final int GERTRUDES_CAT = 1010;
    public static final int GAMER = 1011;
    public static final int GAMER_1012 = 1012;
    public static final int BARMAN = 1013;
    public static final int GAMER_1014 = 1014;
    public static final int GAMER_1015 = 1015;
    public static final int GAMER_1016 = 1016;
    public static final int GAMER_1017 = 1017;
    public static final int GAMER_1018 = 1018;
    public static final int GAMER_1019 = 1019;
    public static final int RAT = 1020;
    public static final int RAT_1021 = 1021;
    public static final int RAT_1022 = 1022;
    public static final int ZYGOMITE_1024 = 1024;
    public static final int NECROMANCER = 1025;
    public static final int BANDIT_1026 = 1026;
    public static final int GUARD_BANDIT = 1027;
    public static final int BARBARIAN_GUARD = 1028;
    public static final int SLEEPWALKER = 1029;
    public static final int SLEEPWALKER_1030 = 1030;
    public static final int SLEEPWALKER_1031 = 1031;
    public static final int SLEEPWALKER_1032 = 1032;
    public static final int SNAKE = 1037;
    public static final int MONKEY_1038 = 1038;
    public static final int ALBINO_BAT = 1039;
    public static final int CRAB = 1040;
    public static final int GIANT_MOSQUITO = 1041;
    public static final int JUNGLE_HORROR = 1042;
    public static final int JUNGLE_HORROR_1043 = 1043;
    public static final int JUNGLE_HORROR_1044 = 1044;
    public static final int JUNGLE_HORROR_1045 = 1045;
    public static final int JUNGLE_HORROR_1046 = 1046;
    public static final int CAVE_HORROR = 1047;
    public static final int CAVE_HORROR_1048 = 1048;
    public static final int CAVE_HORROR_1049 = 1049;
    public static final int CAVE_HORROR_1050 = 1050;
    public static final int CAVE_HORROR_1051 = 1051;
    public static final int CAVEY_DAVEY = 1052;
    public static final int PATCHY = 1053;
    public static final int LAUNA = 1054;
    public static final int LAUNA_1055 = 1055;
    public static final int BRANA = 1056;
    public static final int TOLNA = 1057;
    public static final int TOLNA_1058 = 1058;
    public static final int TOLNA_1059 = 1059;
    public static final int ANGRY_BEAR = 1060;
    public static final int ANGRY_UNICORN = 1061;
    public static final int ANGRY_GIANT_RAT = 1062;
    public static final int ANGRY_GOBLIN = 1065;
    public static final int FEAR_REAPER = 1066;
    public static final int CONFUSION_BEAST = 1067;
    public static final int CONFUSION_BEAST_1068 = 1068;
    public static final int CONFUSION_BEAST_1069 = 1069;
    public static final int CONFUSION_BEAST_1070 = 1070;
    public static final int CONFUSION_BEAST_1071 = 1071;
    public static final int HOPELESS_CREATURE = 1072;
    public static final int HOPELESS_CREATURE_1073 = 1073;
    public static final int HOPELESS_CREATURE_1074 = 1074;
    public static final int TOLNA_1075 = 1075;
    public static final int TOLNA_1076 = 1076;
    public static final int TOLNA_1077 = 1077;
    public static final int RUNA = 1078;
    public static final int HALLA = 1079;
    public static final int FINN = 1080;
    public static final int OSVALD = 1081;
    public static final int RUNOLF = 1082;
    public static final int TJORVI = 1083;
    public static final int INGRID = 1084;
    public static final int THORA = 1085;
    public static final int SIGNY = 1086;
    public static final int HILD = 1087;
    public static final int ARMOD = 1088;
    public static final int BEIGARTH = 1089;
    public static final int REINN = 1090;
    public static final int ALVISS = 1091;
    public static final int FULLANGR = 1092;
    public static final int JARI = 1093;
    public static final int THORODIN = 1094;
    public static final int FERD = 1095;
    public static final int DONAL = 1096;
    public static final int SEA_SNAKE_YOUNG = 1097;
    public static final int SEA_SNAKE_HATCHLING = 1098;
    public static final int GUARD_1099 = 1099;
    public static final int GUARD_1100 = 1100;
    public static final int GIANT_SEA_SNAKE = 1101;
    public static final int ELENA = 1102;
    public static final int DA_VINCI = 1103;
    public static final int DA_VINCI_1104 = 1104;
    public static final int CHANCY = 1105;
    public static final int CHANCY_1106 = 1106;
    public static final int HOPS = 1107;
    public static final int HOPS_1108 = 1108;
    public static final int JULIE = 1109;
    public static final int GUIDOR = 1110;
    public static final int GUARD_1111 = 1111;
    public static final int GUARD_1112 = 1112;
    public static final int GUARD_1113 = 1113;
    public static final int MAN_1118 = 1118;
    public static final int WOMAN = 1119;
    public static final int DOMINIC_ONION = 1120;
    public static final int BARRELCHEST_HARD = 1126;
    public static final int GIANT_SCARAB_HARD = 1127;
    public static final int DESSOUS_HARD = 1128;
    public static final int KAMIL_HARD = 1129;
    public static final int WOMAN_1130 = 1130;
    public static final int WOMAN_1131 = 1131;
    public static final int CHILD = 1132;
    public static final int CHILD_1133 = 1133;
    public static final int DAMIS_HARD = 1134;
    public static final int DAMIS_HARD_1135 = 1135;
    public static final int PRIEST = 1137;
    public static final int MAN_1138 = 1138;
    public static final int WOMAN_1139 = 1139;
    public static final int WOMAN_1140 = 1140;
    public static final int WOMAN_1141 = 1141;
    public static final int WOMAN_1142 = 1142;
    public static final int PALADIN = 1144;
    public static final int JERICO = 1145;
    public static final int CHEMIST = 1146;
    public static final int GUARD_1147 = 1147;
    public static final int NURSE_SARAH = 1152;
    public static final int OGRE_1153 = 1153;
    public static final int SHARK = 1154;
    public static final int SHARK_1155 = 1155;
    public static final int SHARK_1156 = 1156;
    public static final int ARCHER = 1157;
    public static final int WARRIOR = 1158;
    public static final int MONK_1159 = 1159;
    public static final int WIZARD = 1160;
    public static final int FAIRY_QUEEN = 1161;
    public static final int SHAMUS = 1162;
    public static final int TREE_SPIRIT = 1163;
    public static final int CAVE_MONK = 1164;
    public static final int MONK_OF_ENTRANA = 1165;
    public static final int MONK_OF_ENTRANA_1166 = 1166;
    public static final int MONK_OF_ENTRANA_1167 = 1167;
    public static final int MONK_OF_ENTRANA_1168 = 1168;
    public static final int MONK_OF_ENTRANA_1169 = 1169;
    public static final int MONK_OF_ENTRANA_1170 = 1170;
    public static final int MONK_1171 = 1171;
    public static final int CHICKEN = 1173;
    public static final int CHICKEN_1174 = 1174;
    public static final int ROOSTER = 1175;
    public static final int LIL_LAMB = 1176;
    public static final int LAMB = 1177;
    public static final int SHEEP_1178 = 1178;
    public static final int LUMBRIDGE_GUIDE_1179 = 1179;
    public static final int LUMBRIDGE_GUIDE_1181 = 1181;
    public static final int ___ = 1182;
    public static final int ____1183 = 1183;
    public static final int ____1184 = 1184;
    public static final int ____1185 = 1185;
    public static final int ____1186 = 1186;
    public static final int ____1187 = 1187;
    public static final int ____1188 = 1188;
    public static final int ____1189 = 1189;
    public static final int ____1190 = 1190;
    public static final int ____1191 = 1191;
    public static final int GENERAL_WARTFACE_1192 = 1192;
    public static final int GENERAL_BENTNOZE_1193 = 1193;
    public static final int GENERAL_WARTFACE_1195 = 1195;
    public static final int GENERAL_BENTNOZE_1197 = 1197;
    public static final int CERIL_CARNILLEAN = 1198;
    public static final int CLAUS_THE_CHEF = 1199;
    public static final int GUARD_1200 = 1200;
    public static final int PHILIPE_CARNILLEAN = 1201;
    public static final int HENRYETA_CARNILLEAN = 1202;
    public static final int BUTLER_JONES = 1203;
    public static final int ALOMONE = 1204;
    public static final int HAZEEL = 1205;
    public static final int CLIVET = 1206;
    public static final int HAZEEL_CULTIST = 1207;
    public static final int KHAZARD_GUARD = 1208;
    public static final int KHAZARD_GUARD_1209 = 1209;
    public static final int KHAZARD_GUARD_1210 = 1210;
    public static final int KHAZARD_GUARD_1211 = 1211;
    public static final int KHAZARD_GUARD_1212 = 1212;
    public static final int GENERAL_KHAZARD = 1213;
    public static final int KHAZARD_BARMAN = 1214;
    public static final int KELVIN = 1215;
    public static final int JOE = 1216;
    public static final int FIGHTSLAVE = 1217;
    public static final int HENGRAD = 1218;
    public static final int LADY_SERVIL = 1219;
    public static final int SAMMY_SERVIL = 1220;
    public static final int SAMMY_SERVIL_1221 = 1221;
    public static final int JUSTIN_SERVIL = 1222;
    public static final int LOCAL = 1223;
    public static final int BOUNCER = 1224;
    public static final int KHAZARD_OGRE = 1225;
    public static final int KHAZARD_SCORPION = 1226;
    public static final int ARZINIAN_AVATAR_OF_STRENGTH = 1227;
    public static final int ARZINIAN_AVATAR_OF_STRENGTH_1228 = 1228;
    public static final int ARZINIAN_AVATAR_OF_STRENGTH_1229 = 1229;
    public static final int ARZINIAN_AVATAR_OF_RANGING = 1230;
    public static final int ARZINIAN_AVATAR_OF_RANGING_1231 = 1231;
    public static final int ARZINIAN_AVATAR_OF_RANGING_1232 = 1232;
    public static final int ARZINIAN_AVATAR_OF_MAGIC = 1233;
    public static final int ARZINIAN_AVATAR_OF_MAGIC_1234 = 1234;
    public static final int ARZINIAN_AVATAR_OF_MAGIC_1235 = 1235;
    public static final int ARZINIAN_BEING_OF_BORDANZAN = 1236;
    public static final int SABOTEUR = 1237;
    public static final int GNOME_SHOP_KEEPER = 1238;
    public static final int CUTE_CREATURE = 1240;
    public static final int EVIL_CREATURE = 1241;
    public static final int CUTE_CREATURE_1243 = 1243;
    public static final int EVIL_CREATURE_1244 = 1244;
    public static final int CUTE_CREATURE_1246 = 1246;
    public static final int EVIL_CREATURE_1247 = 1247;
    public static final int CUTE_CREATURE_1249 = 1249;
    public static final int EVIL_CREATURE_1250 = 1250;
    public static final int CUTE_CREATURE_1252 = 1252;
    public static final int EVIL_CREATURE_1253 = 1253;
    public static final int CUTE_CREATURE_1255 = 1255;
    public static final int EVIL_CREATURE_1256 = 1256;
    public static final int FLUFFIE = 1257;
    public static final int FLUFFIE_1258 = 1258;
    public static final int ODD_OLD_MAN = 1259;
    public static final int FORTUNATO = 1260;
    public static final int RAM = 1261;
    public static final int RAM_1262 = 1262;
    public static final int RAM_1263 = 1263;
    public static final int RAM_1264 = 1264;
    public static final int RAM_1265 = 1265;
    public static final int BONES = 1266;
    public static final int VULTURE = 1267;
    public static final int VULTURE_1268 = 1268;
    public static final int DR_FENKENSTRAIN = 1269;
    public static final int FENKENSTRAINS_MONSTER = 1270;
    public static final int LORD_ROLOGARTH = 1271;
    public static final int GARDENER_GHOST = 1272;
    public static final int EXPERIMENT = 1273;
    public static final int EXPERIMENT_1274 = 1274;
    public static final int EXPERIMENT_1275 = 1275;
    public static final int LOAR_SHADOW = 1276;
    public static final int LOAR_SHADE = 1277;
    public static final int SHADE_SPIRIT = 1278;
    public static final int PHRIN_SHADOW = 1279;
    public static final int PHRIN_SHADE = 1280;
    public static final int RIYL_SHADOW = 1281;
    public static final int RIYL_SHADE = 1282;
    public static final int ASYN_SHADOW = 1283;
    public static final int ASYN_SHADE = 1284;
    public static final int FIYR_SHADOW = 1285;
    public static final int FIYR_SHADE = 1286;
    public static final int AFFLICTEDULSQUIRE = 1287;
    public static final int ULSQUIRE_SHAUNCY = 1288;
    public static final int AFFLICTEDRAZMIRE = 1289;
    public static final int RAZMIRE_KEELGAN = 1290;
    public static final int MORTTON_LOCAL = 1291;
    public static final int MORTTON_LOCAL_1292 = 1292;
    public static final int AFFLICTED = 1293;
    public static final int AFFLICTED_1294 = 1294;
    public static final int MORTTON_LOCAL_1295 = 1295;
    public static final int MORTTON_LOCAL_1296 = 1296;
    public static final int AFFLICTED_1297 = 1297;
    public static final int AFFLICTED_1298 = 1298;
    public static final int SHEEP_1299 = 1299;
    public static final int SHEEP_1300 = 1300;
    public static final int SHEEP_1301 = 1301;
    public static final int SHEEP_1302 = 1302;
    public static final int SHEEP_1303 = 1303;
    public static final int SHEEP_1304 = 1304;
    public static final int HAIRDRESSER = 1305;
    public static final int MAKEOVER_MAGE = 1306;
    public static final int MAKEOVER_MAGE_1307 = 1307;
    public static final int SHEEP_1308 = 1308;
    public static final int SHEEP_1309 = 1309;
    public static final int BARTENDER_1310 = 1310;
    public static final int BARTENDER_1311 = 1311;
    public static final int BARTENDER_1312 = 1312;
    public static final int BARTENDER_1313 = 1313;
    public static final int BARTENDER_1314 = 1314;
    public static final int EMILY = 1315;
    public static final int KAYLEE = 1316;
    public static final int TINA = 1317;
    public static final int BARTENDER_1318 = 1318;
    public static final int BARTENDER_1319 = 1319;
    public static final int BARTENDER_1320 = 1320;
    public static final int TARQUIN = 1323;
    public static final int SIGURD = 1324;
    public static final int HARI = 1325;
    public static final int BARFY_BILL = 1326;
    public static final int TYRAS_GUARD_1327 = 1327;
    public static final int JACK_SEAGULL = 1335;
    public static final int LONGBOW_BEN = 1336;
    public static final int AHAB = 1337;
    public static final int SEAGULL = 1338;
    public static final int SEAGULL_1339 = 1339;
    public static final int MATTHIAS = 1340;
    public static final int MATTHIAS_1341 = 1341;
    public static final int GYR_FALCON = 1342;
    public static final int GYR_FALCON_1343 = 1343;
    public static final int GYR_FALCON_1344 = 1344;
    public static final int GYR_FALCON_1345 = 1345;
    public static final int PRICKLY_KEBBIT = 1346;
    public static final int SABRETOOTHED_KEBBIT = 1347;
    public static final int BARBTAILED_KEBBIT = 1348;
    public static final int WILD_KEBBIT = 1349;
    public static final int ARTIMEUS = 1350;
    public static final int SETH_GROATS = 1351;
    public static final int TASSIE_SLIPCAST = 1352;
    public static final int HAMMERSPIKE_STOUTBEARD = 1353;
    public static final int DWARF_GANG_MEMBER = 1354;
    public static final int DWARF_GANG_MEMBER_1355 = 1355;
    public static final int DWARF_GANG_MEMBER_1356 = 1356;
    public static final int PHANTUWTI_FANSTUWI_FARSIGHT = 1357;
    public static final int TINDEL_MARCHANT = 1358;
    public static final int PETRA_FIYED = 1360;
    public static final int JIMMY_THE_CHISEL = 1361;
    public static final int SLAGILITH = 1362;
    public static final int ROCK_PILE = 1363;
    public static final int SLAGILITH_1364 = 1364;
    public static final int FIRE_ELEMENTAL = 1365;
    public static final int EARTH_ELEMENTAL = 1366;
    public static final int EARTH_ELEMENTAL_1367 = 1367;
    public static final int ELEMENTAL_ROCK = 1368;
    public static final int AIR_ELEMENTAL = 1369;
    public static final int WATER_ELEMENTAL = 1370;
    public static final int GUARD_1371 = 1371;
    public static final int GUARD_1372 = 1372;
    public static final int HAMAL_THE_CHIEFTAIN = 1373;
    public static final int RAGNAR = 1374;
    public static final int SVIDI = 1375;
    public static final int JOKUL = 1376;
    public static final int THE_KENDAL = 1377;
    public static final int THE_KENDAL_1378 = 1378;
    public static final int CAMP_DWELLER = 1379;
    public static final int CAMP_DWELLER_1380 = 1380;
    public static final int CAMP_DWELLER_1381 = 1381;
    public static final int CAMP_DWELLER_1382 = 1382;
    public static final int CAMP_DWELLER_1383 = 1383;
    public static final int MOUNTAIN_GOAT = 1384;
    public static final int MOUNTAIN_GOAT_1385 = 1385;
    public static final int MOUNTAIN_GOAT_1386 = 1386;
    public static final int MOUNTAIN_GOAT_1387 = 1387;
    public static final int BALD_HEADED_EAGLE = 1388;
    public static final int BERNALD = 1389;
    public static final int QUEEN_ELLAMARIA = 1390;
    public static final int TROLLEY = 1391;
    public static final int TROLLEY_1392 = 1392;
    public static final int TROLLEY_1393 = 1393;
    public static final int BILLY_A_GUARD_OF_FALADOR = 1395;
    public static final int BOB_ANOTHER_GUARD_OF_FALADOR = 1396;
    public static final int BROTHER_ALTHRIC = 1397;
    public static final int PKMASTER0036 = 1398;
    public static final int KING_ROALD = 1399;
    public static final int NULODION = 1400;
    public static final int DWARF_1401 = 1401;
    public static final int DWARF_1402 = 1402;
    public static final int DWARF_1403 = 1403;
    public static final int DWARF_1404 = 1404;
    public static final int DWARF_1405 = 1405;
    public static final int DWARF_1406 = 1406;
    public static final int DWARF_1407 = 1407;
    public static final int DWARF_1408 = 1408;
    public static final int BLACK_GUARD = 1409;
    public static final int BLACK_GUARD_1410 = 1410;
    public static final int BLACK_GUARD_1411 = 1411;
    public static final int BLACK_GUARD_1412 = 1412;
    public static final int ENGINEERING_ASSISTANT = 1413;
    public static final int ENGINEERING_ASSISTANT_1414 = 1414;
    public static final int ENGINEER = 1415;
    public static final int SQUIRREL = 1416;
    public static final int SQUIRREL_1417 = 1417;
    public static final int SQUIRREL_1418 = 1418;
    public static final int RACCOON = 1419;
    public static final int RACCOON_1420 = 1420;
    public static final int RACCOON_1421 = 1421;
    public static final int HAZELMERE = 1422;
    public static final int GLOUGH = 1425;
    public static final int CHARLIE = 1428;
    public static final int FOREMAN = 1429;
    public static final int SHIPYARD_WORKER = 1430;
    public static final int FEMI = 1431;
    public static final int BLACK_DEMON_1432 = 1432;
    public static final int GNOME_GUARD = 1433;
    public static final int GARKOR = 1434;
    public static final int LUMO = 1435;
    public static final int BUNKDO = 1436;
    public static final int CARADO = 1437;
    public static final int LUMDO = 1438;
    public static final int KARAM = 1439;
    public static final int BUNKWICKET = 1440;
    public static final int WAYMOTTIN = 1441;
    public static final int ZOOKNOCK = 1442;
    public static final int JUNGLE_DEMON = 1443;
    public static final int DAERO = 1444;
    public static final int DAERO_1445 = 1445;
    public static final int WAYDAR = 1446;
    public static final int PIRATE_1447 = 1447;
    public static final int THIEF = 1448;
    public static final int LUMDO_1453 = 1453;
    public static final int LUMDO_1454 = 1454;
    public static final int GLO_CARANOCK = 1460;
    public static final int MUGGER_1461 = 1461;
    public static final int SMALL_NINJA_MONKEY = 1462;
    public static final int MEDIUM_NINJA_MONKEY = 1463;
    public static final int GORILLA = 1464;
    public static final int BEARDED_GORILLA = 1465;
    public static final int ANCIENT_MONKEY = 1466;
    public static final int SMALL_ZOMBIE_MONKEY = 1467;
    public static final int LARGE_ZOMBIE_MONKEY = 1468;
    public static final int MONKEY_1469 = 1469;
    public static final int RANTZ = 1470;
    public static final int FYCIE = 1471;
    public static final int BUGS = 1472;
    public static final int SWAMP_TOAD = 1473;
    public static final int BLOATED_TOAD = 1474;
    public static final int CHOMPY_BIRD = 1475;
    public static final int CHOMPY_BIRD_1476 = 1476;
    public static final int EUDAV = 1477;
    public static final int ORONWEN = 1478;
    public static final int BANKER_1479 = 1479;
    public static final int BANKER_1480 = 1480;
    public static final int DALLDAV = 1481;
    public static final int GETHIN = 1482;
    public static final int NICKOLAUS = 1483;
    public static final int NICKOLAUS_1484 = 1484;
    public static final int NICKOLAUS_1485 = 1485;
    public static final int NICKOLAUS_1486 = 1486;
    public static final int DESERT_EAGLE = 1487;
    public static final int JUNGLE_EAGLE = 1488;
    public static final int POLAR_EAGLE = 1489;
    public static final int EAGLE = 1490;
    public static final int KEBBIT = 1494;
    public static final int CHARLIE_1495 = 1495;
    public static final int BOULDER = 1496;
    public static final int FISHING_SPOT_1497 = 1497;
    public static final int FISHING_SPOT_1498 = 1498;
    public static final int FISHING_SPOT_1499 = 1499;
    public static final int FISHING_SPOT_1500 = 1500;
    public static final int ALECK = 1501;
    public static final int LEON = 1502;
    public static final int HUNTING_EXPERT = 1503;
    public static final int HUNTING_EXPERT_1504 = 1504;
    public static final int FERRET = 1505;
    public static final int ROD_FISHING_SPOT_1506 = 1506;
    public static final int ROD_FISHING_SPOT_1507 = 1507;
    public static final int ROD_FISHING_SPOT_1508 = 1508;
    public static final int ROD_FISHING_SPOT_1509 = 1509;
    public static final int FISHING_SPOT_1510 = 1510;
    public static final int FISHING_SPOT_1511 = 1511;
    public static final int ROD_FISHING_SPOT_1512 = 1512;
    public static final int ROD_FISHING_SPOT_1513 = 1513;
    public static final int FISHING_SPOT_1514 = 1514;
    public static final int ROD_FISHING_SPOT_1515 = 1515;
    public static final int ROD_FISHING_SPOT_1516 = 1516;
    public static final int FISHING_SPOT_1517 = 1517;
    public static final int FISHING_SPOT_1518 = 1518;
    public static final int FISHING_SPOT_1519 = 1519;
    public static final int FISHING_SPOT_1520 = 1520;
    public static final int FISHING_SPOT_1521 = 1521;
    public static final int FISHING_SPOT_1522 = 1522;
    public static final int FISHING_SPOT_1523 = 1523;
    public static final int FISHING_SPOT_1524 = 1524;
    public static final int FISHING_SPOT_1525 = 1525;
    public static final int ROD_FISHING_SPOT_1526 = 1526;
    public static final int ROD_FISHING_SPOT_1527 = 1527;
    public static final int FISHING_SPOT_1528 = 1528;
    public static final int ROD_FISHING_SPOT_1529 = 1529;
    public static final int FISHING_SPOT_1530 = 1530;
    public static final int ROD_FISHING_SPOT_1531 = 1531;
    public static final int FISHING_SPOT_1532 = 1532;
    public static final int FISHING_SPOT_1533 = 1533;
    public static final int FISHING_SPOT_1534 = 1534;
    public static final int FISHING_SPOT_1535 = 1535;
    public static final int FISHING_SPOT_1536 = 1536;
    public static final int SKELETON_HERO = 1537;
    public static final int SKELETON_BRUTE = 1538;
    public static final int SKELETON_WARLORD = 1539;
    public static final int SKELETON_HEAVY = 1540;
    public static final int SKELETON_THUG = 1541;
    public static final int FISHING_SPOT_1542 = 1542;
    public static final int GARGOYLE_1543 = 1543;
    public static final int FISHING_SPOT_1544 = 1544;
    public static final int BLACK_KNIGHT_1545 = 1545;
    public static final int GUARD_1546 = 1546;
    public static final int GUARD_1547 = 1547;
    public static final int GUARD_1548 = 1548;
    public static final int GUARD_1549 = 1549;
    public static final int GUARD_1550 = 1550;
    public static final int GUARD_1551 = 1551;
    public static final int GUARD_1552 = 1552;
    public static final int CRAB_1553 = 1553;
    public static final int SEAGULL_1554 = 1554;
    public static final int SEAGULL_1555 = 1555;
    public static final int FIRE_WIZARD = 1556;
    public static final int WATER_WIZARD = 1557;
    public static final int EARTH_WIZARD = 1558;
    public static final int AIR_WIZARD = 1559;
    public static final int ORDAN = 1560;
    public static final int JORZIK = 1561;
    public static final int SMIDDI_RYAK_HARD = 1562;
    public static final int ROLAYNE_TWICKIT_HARD = 1563;
    public static final int JAYENE_KLIYN_MEDIUM = 1564;
    public static final int VALANTAY_EPPEL_MEDIUM = 1565;
    public static final int DALCIAN_FANG_EASY = 1566;
    public static final int FYIONA_FRAY_EASY = 1567;
    public static final int ABIDOR_CRANK = 1568;
    public static final int BENJAMIN = 1569;
    public static final int LIAM = 1570;
    public static final int MIALA = 1571;
    public static final int VERAK = 1572;
    public static final int FORESTER_HARD = 1573;
    public static final int WOMANATARMS_HARD = 1574;
    public static final int APPRENTICE_MEDIUM = 1575;
    public static final int RANGER_MEDIUM = 1576;
    public static final int ADVENTURER_EASY = 1577;
    public static final int MAGE_EASY = 1578;
    public static final int HIYLIK_MYNA = 1579;
    public static final int DUMMY = 1580;
    public static final int DUMMY_1581 = 1581;
    public static final int DUMMY_1582 = 1582;
    public static final int DUMMY_1583 = 1583;
    public static final int DUMMY_1584 = 1584;
    public static final int DUMMY_1585 = 1585;
    public static final int DUMMY_1586 = 1586;
    public static final int DUMMY_1587 = 1587;
    public static final int DUMMY_1588 = 1588;
    public static final int DUMMY_1589 = 1589;
    public static final int DUMMY_1590 = 1590;
    public static final int DUMMY_1591 = 1591;
    public static final int DUMMY_1592 = 1592;
    public static final int DUMMY_1593 = 1593;
    public static final int DUMMY_1594 = 1594;
    public static final int DUMMY_1595 = 1595;
    public static final int DUMMY_1596 = 1596;
    public static final int DUMMY_1597 = 1597;
    public static final int DUMMY_1598 = 1598;
    public static final int DUMMY_1599 = 1599;
    public static final int GUNDAI = 1600;
    public static final int LUNDAIL = 1601;
    public static final int CHAMBER_GUARDIAN = 1602;
    public static final int KOLODION = 1603;
    public static final int KOLODION_1604 = 1604;
    public static final int KOLODION_1605 = 1605;
    public static final int KOLODION_1606 = 1606;
    public static final int KOLODION_1607 = 1607;
    public static final int KOLODION_1608 = 1608;
    public static final int KOLODION_1609 = 1609;
    public static final int BATTLE_MAGE = 1610;
    public static final int BATTLE_MAGE_1611 = 1611;
    public static final int BATTLE_MAGE_1612 = 1612;
    public static final int BANKER_1613 = 1613;
    public static final int PHIALS = 1614;
    public static final int BANKNOTE_EXCHANGE_MERCHANT = 1615;
    public static final int HIGH_PRIESTESS_ZULHARCINQA = 1616;
    public static final int PRIESTESS_ZULGWENWYNIG = 1617;
    public static final int BANKER_1618 = 1618;
    public static final int CAT_1619 = 1619;
    public static final int CAT_1620 = 1620;
    public static final int CAT_1621 = 1621;
    public static final int CAT_1622 = 1622;
    public static final int CAT_1623 = 1623;
    public static final int CAT_1624 = 1624;
    public static final int HELLCAT = 1625;
    public static final int LAZY_CAT = 1626;
    public static final int LAZY_CAT_1627 = 1627;
    public static final int LAZY_CAT_1628 = 1628;
    public static final int LAZY_CAT_1629 = 1629;
    public static final int LAZY_CAT_1630 = 1630;
    public static final int LAZY_CAT_1631 = 1631;
    public static final int LAZY_HELLCAT = 1632;
    public static final int BANKER_1633 = 1633;
    public static final int BANKER_1634 = 1634;
    public static final int BABY_IMPLING = 1635;
    public static final int YOUNG_IMPLING = 1636;
    public static final int GOURMET_IMPLING = 1637;
    public static final int EARTH_IMPLING = 1638;
    public static final int ESSENCE_IMPLING = 1639;
    public static final int ECLECTIC_IMPLING = 1640;
    public static final int NATURE_IMPLING = 1641;
    public static final int MAGPIE_IMPLING = 1642;
    public static final int NINJA_IMPLING = 1643;
    public static final int DRAGON_IMPLING = 1644;
    public static final int BABY_IMPLING_1645 = 1645;
    public static final int YOUNG_IMPLING_1646 = 1646;
    public static final int GOURMET_IMPLING_1647 = 1647;
    public static final int EARTH_IMPLING_1648 = 1648;
    public static final int ESSENCE_IMPLING_1649 = 1649;
    public static final int ECLECTIC_IMPLING_1650 = 1650;
    public static final int NATURE_IMPLING_1651 = 1651;
    public static final int MAGPIE_IMPLING_1652 = 1652;
    public static final int NINJA_IMPLING_1653 = 1653;
    public static final int DRAGON_IMPLING_1654 = 1654;
    public static final int EGG_LAUNCHER = 1655;
    public static final int COMMANDER_CONNAD = 1656;
    public static final int CAPTAIN_CAIN = 1657;
    public static final int PRIVATE_PALDO = 1658;
    public static final int PRIVATE_PENDRON = 1659;
    public static final int PRIVATE_PIERREB = 1660;
    public static final int PRIVATE_PALDON = 1661;
    public static final int MAJOR_ATTACK = 1662;
    public static final int MAJOR_COLLECT = 1663;
    public static final int MAJOR_DEFEND = 1664;
    public static final int MAJOR_HEAL = 1665;
    public static final int SERGEANT_SAMBUR = 1666;
    public static final int PENANCE_FIGHTER = 1667;
    public static final int PENANCE_RANGER = 1668;
    public static final int PENANCE_RUNNER = 1669;
    public static final int PENANCE_HEALER = 1670;
    public static final int STRANGE_OLD_MAN = 1671;
    public static final int AHRIM_THE_BLIGHTED = 1672;
    public static final int DHAROK_THE_WRETCHED = 1673;
    public static final int GUTHAN_THE_INFESTED = 1674;
    public static final int KARIL_THE_TAINTED = 1675;
    public static final int TORAG_THE_CORRUPTED = 1676;
    public static final int VERAC_THE_DEFILED = 1677;
    public static final int BLOODWORM = 1678;
    public static final int CRYPT_RAT = 1679;
    public static final int GIANT_CRYPT_RAT = 1680;
    public static final int GIANT_CRYPT_RAT_1681 = 1681;
    public static final int GIANT_CRYPT_RAT_1682 = 1682;
    public static final int CRYPT_SPIDER = 1683;
    public static final int GIANT_CRYPT_SPIDER = 1684;
    public static final int SKELETON_1685 = 1685;
    public static final int SKELETON_1686 = 1686;
    public static final int SKELETON_1687 = 1687;
    public static final int SKELETON_1688 = 1688;
    public static final int SPLATTER = 1689;
    public static final int SPLATTER_1690 = 1690;
    public static final int SPLATTER_1691 = 1691;
    public static final int SPLATTER_1692 = 1692;
    public static final int SPLATTER_1693 = 1693;
    public static final int SHIFTER = 1694;
    public static final int SHIFTER_1695 = 1695;
    public static final int SHIFTER_1696 = 1696;
    public static final int SHIFTER_1697 = 1697;
    public static final int SHIFTER_1698 = 1698;
    public static final int SHIFTER_1699 = 1699;
    public static final int SHIFTER_1700 = 1700;
    public static final int SHIFTER_1701 = 1701;
    public static final int SHIFTER_1702 = 1702;
    public static final int SHIFTER_1703 = 1703;
    public static final int RAVAGER = 1704;
    public static final int RAVAGER_1705 = 1705;
    public static final int RAVAGER_1706 = 1706;
    public static final int RAVAGER_1707 = 1707;
    public static final int RAVAGER_1708 = 1708;
    public static final int SPINNER = 1709;
    public static final int SPINNER_1710 = 1710;
    public static final int SPINNER_1711 = 1711;
    public static final int SPINNER_1712 = 1712;
    public static final int SPINNER_1713 = 1713;
    public static final int TORCHER = 1714;
    public static final int TORCHER_1715 = 1715;
    public static final int TORCHER_1716 = 1716;
    public static final int TORCHER_1717 = 1717;
    public static final int TORCHER_1718 = 1718;
    public static final int TORCHER_1719 = 1719;
    public static final int TORCHER_1720 = 1720;
    public static final int TORCHER_1721 = 1721;
    public static final int TORCHER_1722 = 1722;
    public static final int TORCHER_1723 = 1723;
    public static final int DEFILER = 1724;
    public static final int DEFILER_1725 = 1725;
    public static final int DEFILER_1726 = 1726;
    public static final int DEFILER_1727 = 1727;
    public static final int DEFILER_1728 = 1728;
    public static final int DEFILER_1729 = 1729;
    public static final int DEFILER_1730 = 1730;
    public static final int DEFILER_1731 = 1731;
    public static final int DEFILER_1732 = 1732;
    public static final int DEFILER_1733 = 1733;
    public static final int BRAWLER = 1734;
    public static final int BRAWLER_1735 = 1735;
    public static final int BRAWLER_1736 = 1736;
    public static final int BRAWLER_1737 = 1737;
    public static final int BRAWLER_1738 = 1738;
    public static final int PORTAL = 1739;
    public static final int PORTAL_1740 = 1740;
    public static final int PORTAL_1741 = 1741;
    public static final int PORTAL_1742 = 1742;
    public static final int PORTAL_1743 = 1743;
    public static final int PORTAL_1744 = 1744;
    public static final int PORTAL_1745 = 1745;
    public static final int PORTAL_1746 = 1746;
    public static final int PORTAL_1747 = 1747;
    public static final int PORTAL_1748 = 1748;
    public static final int PORTAL_1749 = 1749;
    public static final int PORTAL_1750 = 1750;
    public static final int PORTAL_1751 = 1751;
    public static final int PORTAL_1752 = 1752;
    public static final int PORTAL_1753 = 1753;
    public static final int PORTAL_1754 = 1754;
    public static final int VOID_KNIGHT = 1755;
    public static final int VOID_KNIGHT_1756 = 1756;
    public static final int VOID_KNIGHT_1757 = 1757;
    public static final int VOID_KNIGHT_1758 = 1758;
    public static final int SQUIRE = 1759;
    public static final int SQUIRE_1760 = 1760;
    public static final int SQUIRE_1761 = 1761;
    public static final int SQUIRE_1762 = 1762;
    public static final int SQUIRE_1764 = 1764;
    public static final int SQUIRE_1765 = 1765;
    public static final int SQUIRE_1766 = 1766;
    public static final int SQUIRE_1767 = 1767;
    public static final int SQUIRE_1768 = 1768;
    public static final int SQUIRE_1769 = 1769;
    public static final int SQUIRE_1770 = 1770;
    public static final int SQUIRE_NOVICE = 1771;
    public static final int SQUIRE_INTERMEDIATE = 1772;
    public static final int SQUIRE_VETERAN = 1773;
    public static final int URI = 1774;
    public static final int URI_1775 = 1775;
    public static final int URI_1776 = 1776;
    public static final int DOUBLE_AGENT = 1777;
    public static final int DOUBLE_AGENT_1778 = 1778;
    public static final int GUARDIAN_MUMMY = 1779;
    public static final int ANNOYED_GUARDIAN_MUMMY = 1780;
    public static final int TARIK = 1781;
    public static final int SCARAB_SWARM = 1782;
    public static final int MALIGNIUS_MORTIFER = 1783;
    public static final int ZOMBIE_1784 = 1784;
    public static final int SKELETON_1785 = 1785;
    public static final int GHOST_1786 = 1786;
    public static final int SKELETON_MAGE_1787 = 1787;
    public static final int BETTY = 1788;
    public static final int GRUM = 1789;
    public static final int GERRANT = 1790;
    public static final int WYDIN = 1791;
    public static final int GOAT = 1792;
    public static final int GOAT_1793 = 1793;
    public static final int BILLY_GOAT = 1794;
    public static final int GOAT_1795 = 1795;
    public static final int GOAT_1796 = 1796;
    public static final int BILLY_GOAT_1797 = 1797;
    public static final int WHITE_KNIGHT = 1798;
    public static final int WHITE_KNIGHT_1799 = 1799;
    public static final int WHITE_KNIGHT_1800 = 1800;
    public static final int SUMMER_ELEMENTAL = 1801;
    public static final int SUMMER_ELEMENTAL_1802 = 1802;
    public static final int SUMMER_ELEMENTAL_1803 = 1803;
    public static final int SUMMER_ELEMENTAL_1804 = 1804;
    public static final int SUMMER_ELEMENTAL_1805 = 1805;
    public static final int SUMMER_ELEMENTAL_1806 = 1806;
    public static final int SORCERESS = 1807;
    public static final int APPRENTICE = 1808;
    public static final int OSMAN = 1809;
    public static final int OSMAN_1810 = 1810;
    public static final int OSMAN_1811 = 1811;
    public static final int DELMONTY = 1813;
    public static final int SAN_FAN = 1814;
    public static final int FANCY_DAN = 1815;
    public static final int HONEST_JIMMY = 1816;
    public static final int MONKEY_1817 = 1817;
    public static final int SWARM_1818 = 1818;
    public static final int BLUE_MONKEY = 1825;
    public static final int RED_MONKEY = 1826;
    public static final int PARROT = 1827;
    public static final int PARROT_1828 = 1828;
    public static final int WHITE_KNIGHT_1829 = 1829;
    public static final int SHARK_1830 = 1830;
    public static final int SWAN = 1831;
    public static final int BLACK_SWAN = 1832;
    public static final int BANDIT_SHOPKEEPER = 1833;
    public static final int GORAK = 1834;
    public static final int COSMIC_BEING = 1835;
    public static final int DUCK = 1838;
    public static final int DUCK_1839 = 1839;
    public static final int FAIRY_GODFATHER = 1840;
    public static final int FAIRY_NUFF = 1841;
    public static final int FAIRY_QUEEN_1842 = 1842;
    public static final int CENTAUR = 1843;
    public static final int CENTAUR_1844 = 1844;
    public static final int STAG = 1845;
    public static final int WOOD_DRYAD = 1846;
    public static final int FAIRY_VERY_WISE = 1847;
    public static final int FAIRY = 1848;
    public static final int FAIRY_1849 = 1849;
    public static final int FAIRY_1850 = 1850;
    public static final int FAIRY_1851 = 1851;
    public static final int RABBIT = 1852;
    public static final int RABBIT_1853 = 1853;
    public static final int BUTTERFLY_1854 = 1854;
    public static final int BUTTERFLY_1855 = 1855;
    public static final int STARFLOWER = 1856;
    public static final int STARFLOWER_1857 = 1857;
    public static final int TREE_SPIRIT_1861 = 1861;
    public static final int TREE_SPIRIT_1862 = 1862;
    public static final int TREE_SPIRIT_1863 = 1863;
    public static final int TREE_SPIRIT_1864 = 1864;
    public static final int TREE_SPIRIT_1865 = 1865;
    public static final int TREE_SPIRIT_1866 = 1866;
    public static final int SIR_AMIK_VARZE = 1867;
    public static final int SIR_AMIK_VARZE_1869 = 1869;
    public static final int EVIL_CHICKEN = 1870;
    public static final int BABY_BLACK_DRAGON = 1871;
    public static final int BABY_BLACK_DRAGON_1872 = 1872;
    public static final int KKLIK = 1873;
    public static final int ICE_TROLL_RUNT = 1874;
    public static final int ICE_TROLL_MALE = 1875;
    public static final int ICE_TROLL_FEMALE = 1876;
    public static final int ICE_TROLL_GRUNT = 1877;
    public static final int MAWNIS_BUROWGAR = 1878;
    public static final int MAWNIS_BUROWGAR_1879 = 1879;
    public static final int FRIDLEIF_SHIELDSON = 1880;
    public static final int THAKKRAD_SIGMUNDSON = 1881;
    public static final int MARIA_GUNNARS = 1882;
    public static final int MARIA_GUNNARS_1883 = 1883;
    public static final int JOFRIDR_MORDSTATTER = 1884;
    public static final int MORTEN_HOLDSTROM = 1885;
    public static final int GUNNAR_HOLDSTROM = 1886;
    public static final int ANNE_ISAAKSON = 1887;
    public static final int LISSE_ISAAKSON = 1888;
    public static final int HONOUR_GUARD = 1889;
    public static final int HONOUR_GUARD_1890 = 1890;
    public static final int HONOUR_GUARD_1891 = 1891;
    public static final int HONOUR_GUARD_1892 = 1892;
    public static final int KJEDELIG_UPPSEN = 1893;
    public static final int TROGEN_KONUNGARDE = 1894;
    public static final int SLUG_HEMLIGSSEN = 1895;
    public static final int CANDLE_SELLER = 1896;
    public static final int KING_GJUKI_SORVOTT_IV = 1897;
    public static final int HRH_HRAFN = 1898;
    public static final int THORKEL_SILKBEARD = 1899;
    public static final int MORD_GUNNARS = 1900;
    public static final int ART_CRITIC_JACQUES = 1901;
    public static final int HISTORIAN_MINAS = 1902;
    public static final int BARNABUS_HURMA = 1903;
    public static final int MARIUS_GISTE = 1904;
    public static final int CADEN_AZRO = 1905;
    public static final int THIAS_LEACKE = 1906;
    public static final int SINCO_DOAR = 1907;
    public static final int TINSE_TORPE = 1908;
    public static final int INFORMATION_CLERK = 1909;
    public static final int MUSEUM_GUARD = 1910;
    public static final int MUSEUM_GUARD_1911 = 1911;
    public static final int MUSEUM_GUARD_1912 = 1912;
    public static final int TEACHER_AND_PUPIL = 1913;
    public static final int SCHOOLGIRL = 1914;
    public static final int SCHOOLGIRL_1915 = 1915;
    public static final int SCHOOLGIRL_1916 = 1916;
    public static final int SCHOOLGIRL_1917 = 1917;
    public static final int SCHOOLGIRL_1918 = 1918;
    public static final int SCHOOLBOY = 1919;
    public static final int SCHOOLBOY_1920 = 1920;
    public static final int SCHOOLGIRL_1921 = 1921;
    public static final int TEACHER_AND_PUPIL_1922 = 1922;
    public static final int TEACHER_AND_PUPIL_1923 = 1923;
    public static final int SCHOOLBOY_1924 = 1924;
    public static final int TEACHER = 1925;
    public static final int SCHOOLGIRL_1926 = 1926;
    public static final int WORKMAN = 1927;
    public static final int WORKMAN_1928 = 1928;
    public static final int WORKMAN_1929 = 1929;
    public static final int WORKMAN_1930 = 1930;
    public static final int WORKMAN_1931 = 1931;
    public static final int WORKMAN_1932 = 1932;
    public static final int DIG_SITE_WORKMAN = 1933;
    public static final int BARGE_WORKMAN = 1934;
    public static final int BARGE_WORKMAN_1935 = 1935;
    public static final int BARGE_WORKMAN_1936 = 1936;
    public static final int BARGE_WORKMAN_1937 = 1937;
    public static final int BARGE_FOREMAN = 1938;
    public static final int ED_WOOD = 1939;
    public static final int MORD_GUNNARS_1940 = 1940;
    public static final int HRING_HRING = 1941;
    public static final int FLOSI_DALKSSON = 1942;
    public static final int RAUM_URDASTEIN = 1943;
    public static final int SKULI_MYRKA = 1944;
    public static final int KEEPA_KETTILON = 1945;
    public static final int GUARD_1947 = 1947;
    public static final int GUARD_1948 = 1948;
    public static final int GUARD_1949 = 1949;
    public static final int GUARD_1950 = 1950;
    public static final int FREYGERD = 1951;
    public static final int LENSA = 1952;
    public static final int VANLIGGA_GASTFRIHET = 1990;
    public static final int SASSILIK = 1991;
    public static final int MINER = 1992;
    public static final int MINER_1993 = 1993;
    public static final int ERIC_1997 = 1997;
    public static final int GRUVA_PATRULL = 1998;
    public static final int BRENDT = 1999;
    public static final int GRUNDT = 2000;
    public static final int DUCKLING = 2001;
    public static final int DUCKLINGS = 2002;
    public static final int DUCK_2003 = 2003;
    public static final int DRAKE = 2004;
    public static final int LESSER_DEMON = 2005;
    public static final int LESSER_DEMON_2006 = 2006;
    public static final int LESSER_DEMON_2007 = 2007;
    public static final int LESSER_DEMON_2008 = 2008;
    public static final int LESSER_DEMON_2018 = 2018;
    public static final int GREATER_DEMON = 2025;
    public static final int GREATER_DEMON_2026 = 2026;
    public static final int GREATER_DEMON_2027 = 2027;
    public static final int GREATER_DEMON_2028 = 2028;
    public static final int GREATER_DEMON_2029 = 2029;
    public static final int GREATER_DEMON_2030 = 2030;
    public static final int GREATER_DEMON_2031 = 2031;
    public static final int GREATER_DEMON_2032 = 2032;
    public static final int PRIESTESS_ZULGWENWYNIG_2033 = 2033;
    public static final int ZULURGISH = 2034;
    public static final int ZULCHERAY = 2035;
    public static final int ZULGUTUSOLLY = 2036;
    public static final int SACRIFICE = 2037;
    public static final int ZULONAN = 2038;
    public static final int ZULANIEL = 2039;
    public static final int ZULARETH = 2040;
    public static final int BROKEN_HANDZ = 2041;
    public static final int ZULRAH = 2042;
    public static final int ZULRAH_2043 = 2043;
    public static final int ZULRAH_2044 = 2044;
    public static final int SNAKELING = 2045;
    public static final int SNAKELING_2046 = 2046;
    public static final int SNAKELING_2047 = 2047;
    public static final int BLACK_DEMON_2048 = 2048;
    public static final int BLACK_DEMON_2049 = 2049;
    public static final int BLACK_DEMON_2050 = 2050;
    public static final int BLACK_DEMON_2051 = 2051;
    public static final int BLACK_DEMON_2052 = 2052;
    public static final int FRITZ_THE_GLASSBLOWER = 2053;
    public static final int CHAOS_ELEMENTAL = 2054;
    public static final int CHAOS_ELEMENTAL_JR = 2055;
    public static final int DARK_WIZARD_2056 = 2056;
    public static final int DARK_WIZARD_2057 = 2057;
    public static final int DARK_WIZARD_2058 = 2058;
    public static final int DARK_WIZARD_2059 = 2059;
    public static final int DODGY_SQUIRE = 2060;
    public static final int GLOUGH_2061 = 2061;
    public static final int OOMLIE_BIRD = 2062;
    public static final int PENGUIN_2063 = 2063;
    public static final int TERRORBIRD = 2064;
    public static final int TERRORBIRD_2065 = 2065;
    public static final int TERRORBIRD_2066 = 2066;
    public static final int MOUNTED_TERRORBIRD_GNOME = 2067;
    public static final int MOUNTED_TERRORBIRD_GNOME_2068 = 2068;
    public static final int CROW = 2069;
    public static final int CROW_2070 = 2070;
    public static final int CROW_2071 = 2071;
    public static final int CROW_2072 = 2072;
    public static final int CROW_2073 = 2073;
    public static final int CROW_2074 = 2074;
    public static final int FIRE_GIANT = 2075;
    public static final int FIRE_GIANT_2076 = 2076;
    public static final int FIRE_GIANT_2077 = 2077;
    public static final int FIRE_GIANT_2078 = 2078;
    public static final int FIRE_GIANT_2079 = 2079;
    public static final int FIRE_GIANT_2080 = 2080;
    public static final int FIRE_GIANT_2081 = 2081;
    public static final int FIRE_GIANT_2082 = 2082;
    public static final int FIRE_GIANT_2083 = 2083;
    public static final int FIRE_GIANT_2084 = 2084;
    public static final int ICE_GIANT = 2085;
    public static final int ICE_GIANT_2086 = 2086;
    public static final int ICE_GIANT_2087 = 2087;
    public static final int ICE_GIANT_2088 = 2088;
    public static final int ICE_GIANT_2089 = 2089;
    public static final int MOSS_GIANT = 2090;
    public static final int MOSS_GIANT_2091 = 2091;
    public static final int MOSS_GIANT_2092 = 2092;
    public static final int MOSS_GIANT_2093 = 2093;
    public static final int JOGRE = 2094;
    public static final int OGRE_2095 = 2095;
    public static final int OGRE_2096 = 2096;
    public static final int CYCLOPS = 2097;
    public static final int HILL_GIANT = 2098;
    public static final int HILL_GIANT_2099 = 2099;
    public static final int HILL_GIANT_2100 = 2100;
    public static final int HILL_GIANT_2101 = 2101;
    public static final int HILL_GIANT_2102 = 2102;
    public static final int HILL_GIANT_2103 = 2103;
    public static final int ORK = 2104;
    public static final int ORK_2105 = 2105;
    public static final int ORK_2106 = 2106;
    public static final int ORK_2107 = 2107;
    public static final int WISE_OLD_MAN = 2108;
    public static final int WISE_OLD_MAN_2110 = 2110;
    public static final int WISE_OLD_MAN_2111 = 2111;
    public static final int WISE_OLD_MAN_2112 = 2112;
    public static final int WISE_OLD_MAN_2113 = 2113;
    public static final int BED = 2114;
    public static final int THING_UNDER_THE_BED = 2115;
    public static final int MISS_SCHISM = 2116;
    public static final int BANKER_2117 = 2117;
    public static final int BANKER_2118 = 2118;
    public static final int BANKER_2119 = 2119;
    public static final int MARKET_GUARD = 2120;
    public static final int OLIVIA_2121 = 2121;
    public static final int PILLORY_GUARD_2122 = 2122;
    public static final int BANK_GUARD = 2123;
    public static final int BADGER = 2125;
    public static final int BADGER_2126 = 2126;
    public static final int SNAKELING_2127 = 2127;
    public static final int SNAKELING_2128 = 2128;
    public static final int SNAKELING_2129 = 2129;
    public static final int SNAKELING_2130 = 2130;
    public static final int SNAKELING_2131 = 2131;
    public static final int SNAKELING_2132 = 2132;
    public static final int TILES = 2133;
    public static final int AISLES = 2134;
    public static final int LORELAI = 2135;
    public static final int RORY = 2136;
    public static final int CYCLOPS_2137 = 2137;
    public static final int CYCLOPS_2138 = 2138;
    public static final int CYCLOPS_2139 = 2139;
    public static final int CYCLOPS_2140 = 2140;
    public static final int CYCLOPS_2141 = 2141;
    public static final int CYCLOPS_2142 = 2142;
    public static final int SRARACHA = 2143;
    public static final int SRARACHA_2144 = 2144;
    public static final int UNDEAD_DRUID = 2145;
    public static final int FISHING_SPOT_2146 = 2146;
    public static final int GRAND_EXCHANGE_CLERK = 2148;
    public static final int GRAND_EXCHANGE_CLERK_2149 = 2149;
    public static final int GRAND_EXCHANGE_CLERK_2150 = 2150;
    public static final int GRAND_EXCHANGE_CLERK_2151 = 2151;
    public static final int BRUGSEN_BURSEN = 2152;
    public static final int GUNNJORN = 2153;
    public static final int TZHAARMEJ = 2154;
    public static final int TZHAARMEJ_2155 = 2155;
    public static final int TZHAARMEJ_2156 = 2156;
    public static final int TZHAARMEJ_2157 = 2157;
    public static final int TZHAARMEJ_2158 = 2158;
    public static final int TZHAARMEJ_2159 = 2159;
    public static final int TZHAARMEJ_2160 = 2160;
    public static final int TZHAARHUR = 2161;
    public static final int TZHAARHUR_2162 = 2162;
    public static final int TZHAARHUR_2163 = 2163;
    public static final int TZHAARHUR_2164 = 2164;
    public static final int TZHAARHUR_2165 = 2165;
    public static final int TZHAARHUR_2166 = 2166;
    public static final int TZHAARXIL = 2167;
    public static final int TZHAARXIL_2168 = 2168;
    public static final int TZHAARXIL_2169 = 2169;
    public static final int TZHAARXIL_2170 = 2170;
    public static final int TZHAARXIL_2171 = 2171;
    public static final int TZHAARXIL_2172 = 2172;
    public static final int TZHAARKET = 2173;
    public static final int TZHAARKET_2174 = 2174;
    public static final int TZHAARKET_2175 = 2175;
    public static final int TZHAARKET_2176 = 2176;
    public static final int TZHAARKET_2177 = 2177;
    public static final int TZHAARKET_2178 = 2178;
    public static final int TZHAARKET_2179 = 2179;
    public static final int TZHAARMEJJAL = 2180;
    public static final int TZHAARMEJKAH = 2181;
    public static final int ROCK_GOLEM = 2182;
    public static final int TZHAARHURTEL = 2183;
    public static final int TZHAARHURLEK = 2184;
    public static final int TZHAARMEJROH = 2185;
    public static final int TZHAARKET_2186 = 2186;
    public static final int TZHAARKET_2187 = 2187;
    public static final int ROCKS_2188 = 2188;
    public static final int TZKIH = 2189;
    public static final int TZKIH_2190 = 2190;
    public static final int TZKEK = 2191;
    public static final int TZKEK_2192 = 2192;
    public static final int TOKXIL_2193 = 2193;
    public static final int TOKXIL_2194 = 2194;
    public static final int WILLIAM = 2195;
    public static final int IAN = 2196;
    public static final int LARRY_2197 = 2197;
    public static final int DARREN = 2198;
    public static final int EDWARD = 2199;
    public static final int RICHARD_2200 = 2200;
    public static final int NEIL = 2201;
    public static final int EDMOND = 2202;
    public static final int SIMON = 2203;
    public static final int SAM = 2204;
    public static final int COMMANDER_ZILYANA = 2205;
    public static final int STARLIGHT = 2206;
    public static final int GROWLER = 2207;
    public static final int BREE = 2208;
    public static final int SARADOMIN_PRIEST = 2209;
    public static final int SPIRITUAL_WARRIOR = 2210;
    public static final int SPIRITUAL_RANGER = 2211;
    public static final int SPIRITUAL_MAGE = 2212;
    public static final int KNIGHT_OF_SARADOMIN = 2213;
    public static final int KNIGHT_OF_SARADOMIN_2214 = 2214;
    public static final int GENERAL_GRAARDOR = 2215;
    public static final int SERGEANT_STRONGSTACK = 2216;
    public static final int SERGEANT_STEELWILL = 2217;
    public static final int SERGEANT_GRIMSPIKE = 2218;
    public static final int PURPLE_PEWTER_DIRECTOR = 2219;
    public static final int PURPLE_PEWTER_DIRECTOR_2220 = 2220;
    public static final int BLUE_OPAL_DIRECTOR = 2221;
    public static final int YELLOW_FORTUNE_DIRECTOR = 2222;
    public static final int GREEN_GEMSTONE_DIRECTOR = 2223;
    public static final int WHITE_CHISEL_DIRECTOR = 2224;
    public static final int SILVER_COG_DIRECTOR = 2225;
    public static final int BROWN_ENGINE_DIRECTOR = 2226;
    public static final int RED_AXE_DIRECTOR = 2227;
    public static final int COMMANDER_VELDABAN = 2228;
    public static final int RED_AXE_CAT = 2229;
    public static final int RED_AXE_CAT_2230 = 2230;
    public static final int BLACK_GUARD_BERSERKER = 2232;
    public static final int OGRE_2233 = 2233;
    public static final int JOGRE_2234 = 2234;
    public static final int CYCLOPS_2235 = 2235;
    public static final int CYCLOPS_2236 = 2236;
    public static final int ORK_2237 = 2237;
    public static final int ORK_2238 = 2238;
    public static final int ORK_2239 = 2239;
    public static final int ORK_2240 = 2240;
    public static final int HOBGOBLIN_2241 = 2241;
    public static final int SPIRITUAL_RANGER_2242 = 2242;
    public static final int SPIRITUAL_WARRIOR_2243 = 2243;
    public static final int SPIRITUAL_MAGE_2244 = 2244;
    public static final int GOBLIN_2245 = 2245;
    public static final int GOBLIN_2246 = 2246;
    public static final int GOBLIN_2247 = 2247;
    public static final int GOBLIN_2248 = 2248;
    public static final int GOBLIN_2249 = 2249;
    public static final int DOORSUPPORT = 2250;
    public static final int DOOR = 2251;
    public static final int DOOR_2252 = 2252;
    public static final int DOORSUPPORT_2253 = 2253;
    public static final int DOOR_2254 = 2254;
    public static final int DOOR_2255 = 2255;
    public static final int DOORSUPPORT_2256 = 2256;
    public static final int DOOR_2257 = 2257;
    public static final int DOOR_2258 = 2258;
    public static final int DAGANNOTH_2259 = 2259;
    public static final int GIANT_ROCK_CRAB = 2261;
    public static final int BOULDER_2262 = 2262;
    public static final int BARDUR = 2263;
    public static final int DAGANNOTH_FLEDGELING = 2264;
    public static final int DAGANNOTH_SUPREME = 2265;
    public static final int DAGANNOTH_PRIME = 2266;
    public static final int DAGANNOTH_REX = 2267;
    public static final int CAVE_GOBLIN = 2268;
    public static final int CAVE_GOBLIN_2269 = 2269;
    public static final int CAVE_GOBLIN_2270 = 2270;
    public static final int CAVE_GOBLIN_2271 = 2271;
    public static final int CAVE_GOBLIN_2272 = 2272;
    public static final int CAVE_GOBLIN_2273 = 2273;
    public static final int CAVE_GOBLIN_2274 = 2274;
    public static final int CAVE_GOBLIN_2275 = 2275;
    public static final int CAVE_GOBLIN_2276 = 2276;
    public static final int CAVE_GOBLIN_2277 = 2277;
    public static final int CAVE_GOBLIN_2278 = 2278;
    public static final int CAVE_GOBLIN_2279 = 2279;
    public static final int CAVE_GOBLIN_2280 = 2280;
    public static final int CAVE_GOBLIN_2281 = 2281;
    public static final int CAVE_GOBLIN_2282 = 2282;
    public static final int CAVE_GOBLIN_2283 = 2283;
    public static final int CAVE_GOBLIN_2284 = 2284;
    public static final int CAVE_GOBLIN_2285 = 2285;
    public static final int URZEK = 2286;
    public static final int URVASS = 2287;
    public static final int URTAAL = 2288;
    public static final int URMEG = 2289;
    public static final int URLUN = 2290;
    public static final int URPEL = 2291;
    public static final int BANKER_2292 = 2292;
    public static final int BANKER_2293 = 2293;
    public static final int BARTAK = 2294;
    public static final int TURGALL = 2295;
    public static final int RELDAK = 2296;
    public static final int MILTOG = 2297;
    public static final int MERNIK = 2298;
    public static final int CAVE_GOBLIN_2299 = 2299;
    public static final int CRATE_GOBLIN = 2300;
    public static final int CAVE_GOBLIN_2301 = 2301;
    public static final int GOBLIN_SCRIBE = 2302;
    public static final int GOURMET = 2304;
    public static final int GOURMET_2305 = 2305;
    public static final int GOURMET_2306 = 2306;
    public static final int GOURMET_2307 = 2307;
    public static final int TURGOK = 2308;
    public static final int MARKOG = 2309;
    public static final int DURGOK = 2310;
    public static final int TINDAR = 2311;
    public static final int GUNDIK = 2312;
    public static final int ZENKOG = 2313;
    public static final int LURGON = 2314;
    public static final int URTAG = 2315;
    public static final int GUARD_2316 = 2316;
    public static final int GUARD_2317 = 2317;
    public static final int ZANIK = 2318;
    public static final int YOUNG_UN = 2319;
    public static final int TYKE = 2320;
    public static final int NIPPER = 2321;
    public static final int NIPPER_2322 = 2322;
    public static final int CAVE_GOBLIN_CHILD = 2323;
    public static final int CAVE_GOBLIN_CHILD_2324 = 2324;
    public static final int CAVE_GOBLIN_CHILD_2325 = 2325;
    public static final int CAVE_GOBLIN_CHILD_2326 = 2326;
    public static final int CAVE_GOBLIN_CHILD_2327 = 2327;
    public static final int CAVE_GOBLIN_CHILD_2328 = 2328;
    public static final int CAVE_GOBLIN_CHILD_2329 = 2329;
    public static final int CAVE_GOBLIN_CHILD_2330 = 2330;
    public static final int CAVE_GOBLIN_CHILD_2331 = 2331;
    public static final int CAVE_GOBLIN_CHILD_2332 = 2332;
    public static final int CAVE_GOBLIN_CHILD_2333 = 2333;
    public static final int CAVE_GOBLIN_CHILD_2334 = 2334;
    public static final int CAVE_GOBLIN_CHILD_2335 = 2335;
    public static final int CAVE_GOBLIN_CHILD_2336 = 2336;
    public static final int CAVE_GOBLIN_CHILD_2337 = 2337;
    public static final int CAVE_GOBLIN_CHILD_2338 = 2338;
    public static final int SPIT_GOBLIN = 2339;
    public static final int GOBLIN_FISH = 2340;
    public static final int MOVARIO = 2341;
    public static final int DARVE = 2342;
    public static final int MOTHS = 2343;
    public static final int BARLAK = 2344;
    public static final int SANIBOCH = 2345;
    public static final int DROMUNDS_CAT = 2346;
    public static final int BLASIDAR_THE_SCULPTOR = 2347;
    public static final int RIKI_THE_SCULPTORS_MODEL = 2348;
    public static final int RIKI_THE_SCULPTORS_MODEL_2349 = 2349;
    public static final int RIKI_THE_SCULPTORS_MODEL_2350 = 2350;
    public static final int RIKI_THE_SCULPTORS_MODEL_2351 = 2351;
    public static final int RIKI_THE_SCULPTORS_MODEL_2352 = 2352;
    public static final int RIKI_THE_SCULPTORS_MODEL_2353 = 2353;
    public static final int RIKI_THE_SCULPTORS_MODEL_2354 = 2354;
    public static final int RIKI_THE_SCULPTORS_MODEL_2355 = 2355;
    public static final int VIGR = 2356;
    public static final int SANTIRI = 2357;
    public static final int SARO = 2358;
    public static final int GUNSLIK = 2359;
    public static final int WEMUND = 2360;
    public static final int RANDIVOR = 2361;
    public static final int HERVI = 2362;
    public static final int NOLAR = 2363;
    public static final int GULLDAMAR = 2364;
    public static final int TATI = 2365;
    public static final int AGMUNDI = 2366;
    public static final int VERMUNDI = 2367;
    public static final int BANKER_2368 = 2368;
    public static final int BANKER_2369 = 2369;
    public static final int LIBRARIAN = 2370;
    public static final int ASSISTANT = 2371;
    public static final int CUSTOMER = 2372;
    public static final int CUSTOMER_2373 = 2373;
    public static final int DROMUND = 2374;
    public static final int RIND_THE_GARDENER = 2375;
    public static final int FACTORY_MANAGER = 2376;
    public static final int FACTORY_WORKER = 2377;
    public static final int FACTORY_WORKER_2378 = 2378;
    public static final int FACTORY_WORKER_2379 = 2379;
    public static final int FACTORY_WORKER_2380 = 2380;
    public static final int INN_KEEPER = 2381;
    public static final int INN_KEEPER_2382 = 2382;
    public static final int BARMAID = 2383;
    public static final int BARMAN_2384 = 2384;
    public static final int CART_CONDUCTOR = 2385;
    public static final int CART_CONDUCTOR_2386 = 2386;
    public static final int CART_CONDUCTOR_2387 = 2387;
    public static final int CART_CONDUCTOR_2388 = 2388;
    public static final int CART_CONDUCTOR_2389 = 2389;
    public static final int CART_CONDUCTOR_2390 = 2390;
    public static final int CART_CONDUCTOR_2391 = 2391;
    public static final int CART_CONDUCTOR_2392 = 2392;
    public static final int ROWDY_DWARF = 2393;
    public static final int HEGIR = 2394;
    public static final int HAERA = 2395;
    public static final int RUNVASTR = 2396;
    public static final int SUNE = 2397;
    public static final int BENTAMIR = 2398;
    public static final int ULIFED = 2399;
    public static final int REINALD = 2400;
    public static final int KARL = 2401;
    public static final int GAUSS = 2402;
    public static final int MYNDILL = 2403;
    public static final int KJUT = 2404;
    public static final int TOMBAR = 2405;
    public static final int ODMAR = 2406;
    public static final int AUDMANN = 2407;
    public static final int DRUNKEN_DWARF_2408 = 2408;
    public static final int DRUNKEN_DWARF_2409 = 2409;
    public static final int RED_AXE_DIRECTOR_2410 = 2410;
    public static final int RED_AXE_DIRECTOR_2411 = 2411;
    public static final int RED_AXE_HENCHMAN = 2412;
    public static final int RED_AXE_HENCHMAN_2413 = 2413;
    public static final int RED_AXE_HENCHMAN_2414 = 2414;
    public static final int COLONEL_GRIMSSON = 2415;
    public static final int COLONEL_GRIMSSON_2416 = 2416;
    public static final int OGRE_SHAMAN = 2417;
    public static final int OGRE_SHAMAN_2418 = 2418;
    public static final int GRUNSH = 2419;
    public static final int GNOME_EMISSARY = 2420;
    public static final int GNOME_COMPANION = 2421;
    public static final int GNOME_COMPANION_2422 = 2422;
    public static final int CHAOS_DWARF_2423 = 2423;
    public static final int GUNSLIK_2424 = 2424;
    public static final int NOLAR_2425 = 2425;
    public static final int FACTORY_WORKER_2426 = 2426;
    public static final int CART_CONDUCTOR_2427 = 2427;
    public static final int GAUSS_2428 = 2428;
    public static final int DRUNKEN_DWARF_2429 = 2429;
    public static final int ROWDY_DWARF_2430 = 2430;
    public static final int ULIFED_2431 = 2431;
    public static final int DWARVEN_BOATMAN = 2433;
    public static final int DWARVEN_MINER = 2434;
    public static final int DWARVEN_MINER_2435 = 2435;
    public static final int DWARVEN_MINER_2436 = 2436;
    public static final int DWARVEN_MINER_2437 = 2437;
    public static final int DWARVEN_MINER_2438 = 2438;
    public static final int DWARVEN_MINER_2439 = 2439;
    public static final int DWARVEN_MINER_2440 = 2440;
    public static final int DWARVEN_MINER_2441 = 2441;
    public static final int DWARVEN_MINER_2442 = 2442;
    public static final int DWARVEN_MINER_2443 = 2443;
    public static final int DWARVEN_MINER_2444 = 2444;
    public static final int DWARVEN_MINER_2445 = 2445;
    public static final int DWARVEN_MINER_2446 = 2446;
    public static final int DWARVEN_MINER_2447 = 2447;
    public static final int DWARVEN_MINER_2448 = 2448;
    public static final int BUINN = 2449;
    public static final int ANIMATED_BRONZE_ARMOUR = 2450;
    public static final int ANIMATED_IRON_ARMOUR = 2451;
    public static final int ANIMATED_STEEL_ARMOUR = 2452;
    public static final int ANIMATED_BLACK_ARMOUR = 2453;
    public static final int ANIMATED_MITHRIL_ARMOUR = 2454;
    public static final int ANIMATED_ADAMANT_ARMOUR = 2455;
    public static final int ANIMATED_RUNE_ARMOUR = 2456;
    public static final int GHOMMAL = 2457;
    public static final int HARRALLAK_MENAROUS = 2458;
    public static final int GAMFRED = 2459;
    public static final int AJJAT = 2460;
    public static final int KAMFREENA = 2461;
    public static final int SHANOMI = 2462;
    public static final int CYCLOPS_2463 = 2463;
    public static final int CYCLOPS_2464 = 2464;
    public static final int CYCLOPS_2465 = 2465;
    public static final int CYCLOPS_2466 = 2466;
    public static final int CYCLOPS_2467 = 2467;
    public static final int CYCLOPS_2468 = 2468;
    public static final int LIDIO = 2469;
    public static final int LILLY = 2470;
    public static final int ANTON = 2471;
    public static final int JADE = 2472;
    public static final int SLOANE = 2473;
    public static final int CATABLEPON = 2474;
    public static final int CATABLEPON_2475 = 2475;
    public static final int CATABLEPON_2476 = 2476;
    public static final int GIANT_SPIDER = 2477;
    public static final int SPIDER = 2478;
    public static final int SCORPION = 2479;
    public static final int SCORPION_2480 = 2480;
    public static final int MINOTAUR = 2481;
    public static final int MINOTAUR_2482 = 2482;
    public static final int MINOTAUR_2483 = 2483;
    public static final int GOBLIN_2484 = 2484;
    public static final int GOBLIN_2485 = 2485;
    public static final int GOBLIN_2486 = 2486;
    public static final int GOBLIN_2487 = 2487;
    public static final int GOBLIN_2488 = 2488;
    public static final int GOBLIN_2489 = 2489;
    public static final int WOLF_2490 = 2490;
    public static final int WOLF_2491 = 2491;
    public static final int RAT_2492 = 2492;
    public static final int GATE_OF_WAR = 2494;
    public static final int RICKETTY_DOOR = 2495;
    public static final int OOZING_BARRIER = 2496;
    public static final int PORTAL_OF_DEATH = 2497;
    public static final int FLESH_CRAWLER = 2498;
    public static final int FLESH_CRAWLER_2499 = 2499;
    public static final int FLESH_CRAWLER_2500 = 2500;
    public static final int ZOMBIE_2501 = 2501;
    public static final int ZOMBIE_2502 = 2502;
    public static final int ZOMBIE_2503 = 2503;
    public static final int ZOMBIE_2504 = 2504;
    public static final int ZOMBIE_2505 = 2505;
    public static final int ZOMBIE_2506 = 2506;
    public static final int ZOMBIE_2507 = 2507;
    public static final int ZOMBIE_2508 = 2508;
    public static final int ZOMBIE_2509 = 2509;
    public static final int GIANT_RAT = 2510;
    public static final int GIANT_RAT_2511 = 2511;
    public static final int GIANT_RAT_2512 = 2512;
    public static final int RAT_2513 = 2513;
    public static final int ANKOU = 2514;
    public static final int ANKOU_2515 = 2515;
    public static final int ANKOU_2516 = 2516;
    public static final int ANKOU_2517 = 2517;
    public static final int ANKOU_2518 = 2518;
    public static final int ANKOU_2519 = 2519;
    public static final int SKELETON_2520 = 2520;
    public static final int SKELETON_2521 = 2521;
    public static final int SKELETON_2522 = 2522;
    public static final int SKELETON_2523 = 2523;
    public static final int SKELETON_2524 = 2524;
    public static final int SKELETON_2525 = 2525;
    public static final int SKELETON_2526 = 2526;
    public static final int GHOST_2527 = 2527;
    public static final int GHOST_2528 = 2528;
    public static final int GHOST_2529 = 2529;
    public static final int GHOST_2530 = 2530;
    public static final int GHOST_2531 = 2531;
    public static final int GHOST_2532 = 2532;
    public static final int GHOST_2533 = 2533;
    public static final int GHOST_2534 = 2534;
    public static final int JOHANHUS_ULSBRECHT = 2535;
    public static final int HAM_GUARD = 2536;
    public static final int HAM_GUARD_2537 = 2537;
    public static final int HAM_GUARD_2538 = 2538;
    public static final int HAM_DEACON = 2539;
    public static final int HAM_MEMBER = 2540;
    public static final int HAM_MEMBER_2541 = 2541;
    public static final int HAM_MEMBER_2542 = 2542;
    public static final int HAM_MEMBER_2543 = 2543;
    public static final int MOUNTED_TERRORCHICK_GNOME = 2544;
    public static final int CAPTAIN_LAMDOO = 2545;
    public static final int TERRORCHICK_GNOME = 2546;
    public static final int GIANNE_JNR = 2547;
    public static final int TIMBLE = 2548;
    public static final int TAMBLE = 2549;
    public static final int SPANG = 2550;
    public static final int BRAMBICKLE = 2551;
    public static final int WINGSTONE = 2552;
    public static final int PENWIE = 2553;
    public static final int GENERIC_DIPLOMAT = 2554;
    public static final int AMBASSADOR_GIMBLEWAP = 2555;
    public static final int AMBASSADOR_SPANFIPPLE = 2556;
    public static final int AMBASSADOR_FERRNOOK = 2557;
    public static final int PROFESSOR_MANGLETHORP = 2558;
    public static final int DAMWIN = 2559;
    public static final int PROFESSOR_ONGLEWIP = 2560;
    public static final int PROFESSOR_IMBLEWYN = 2561;
    public static final int PERRDUR = 2562;
    public static final int DALILA = 2563;
    public static final int SORRN = 2564;
    public static final int MIMM = 2565;
    public static final int EEBEL = 2566;
    public static final int ERMIN = 2567;
    public static final int PORTOBELLO = 2568;
    public static final int CAPTAIN_NINTO = 2569;
    public static final int CAPTAIN_DAERKIN = 2570;
    public static final int MEEGLE = 2571;
    public static final int WURBEL = 2572;
    public static final int SARBLE = 2573;
    public static final int GUARD_VEMMELDO = 2574;
    public static final int BURKOR = 2575;
    public static final int FROONO = 2576;
    public static final int ABBOT_LANGLEY = 2577;
    public static final int BROTHER_JERED = 2578;
    public static final int MONK_2579 = 2579;
    public static final int MAGE_OF_ZAMORAK = 2580;
    public static final int MAGE_OF_ZAMORAK_2581 = 2581;
    public static final int MAGE_OF_ZAMORAK_2582 = 2582;
    public static final int DARK_MAGE = 2583;
    public static final int ABYSSAL_LEECH = 2584;
    public static final int ABYSSAL_GUARDIAN = 2585;
    public static final int ABYSSAL_WALKER = 2586;
    public static final int SKIPPY = 2587;
    public static final int SKIPPY_2588 = 2588;
    public static final int SKIPPY_2589 = 2589;
    public static final int SKIPPY_2590 = 2590;
    public static final int A_PILE_OF_BROKEN_GLASS = 2591;
    public static final int MOGRE = 2592;
    public static final int WEREWOLF = 2593;
    public static final int WEREWOLF_2594 = 2594;
    public static final int WEREWOLF_2595 = 2595;
    public static final int WEREWOLF_2596 = 2596;
    public static final int WEREWOLF_2597 = 2597;
    public static final int WEREWOLF_2598 = 2598;
    public static final int WEREWOLF_2599 = 2599;
    public static final int WEREWOLF_2600 = 2600;
    public static final int WEREWOLF_2601 = 2601;
    public static final int WEREWOLF_2602 = 2602;
    public static final int WEREWOLF_2603 = 2603;
    public static final int WEREWOLF_2604 = 2604;
    public static final int WEREWOLF_2605 = 2605;
    public static final int WEREWOLF_2606 = 2606;
    public static final int WEREWOLF_2607 = 2607;
    public static final int WEREWOLF_2608 = 2608;
    public static final int WEREWOLF_2609 = 2609;
    public static final int WEREWOLF_2610 = 2610;
    public static final int WEREWOLF_2611 = 2611;
    public static final int WEREWOLF_2612 = 2612;
    public static final int BORIS = 2613;
    public static final int IMRE = 2614;
    public static final int YURI = 2615;
    public static final int JOSEPH = 2616;
    public static final int NIKOLAI = 2617;
    public static final int EDUARD = 2618;
    public static final int LEV = 2619;
    public static final int GEORGY = 2620;
    public static final int SVETLANA = 2621;
    public static final int IRINA = 2622;
    public static final int ALEXIS = 2623;
    public static final int MILLA = 2624;
    public static final int GALINA = 2625;
    public static final int SOFIYA = 2626;
    public static final int KSENIA = 2627;
    public static final int YADVIGA = 2628;
    public static final int NIKITA = 2629;
    public static final int VERA = 2630;
    public static final int ZOJA = 2631;
    public static final int LILIYA = 2632;
    public static final int BANKER_2633 = 2633;
    public static final int MYRE_BLAMISH_SNAIL = 2634;
    public static final int BOB = 2635;
    public static final int BOB_2636 = 2636;
    public static final int SPHINX = 2637;
    public static final int NEITE = 2638;
    public static final int ROBERT_THE_STRONG = 2639;
    public static final int ODYSSEUS = 2640;
    public static final int DRAGONKIN = 2641;
    public static final int KING_BLACK_DRAGON_2642 = 2642;
    public static final int R4NG3RNO0B889 = 2643;
    public static final int LOVE_CATS = 2644;
    public static final int BLOOD_BLAMISH_SNAIL = 2645;
    public static final int OCHRE_BLAMISH_SNAIL = 2646;
    public static final int BRUISE_BLAMISH_SNAIL = 2647;
    public static final int BARK_BLAMISH_SNAIL = 2648;
    public static final int MYRE_BLAMISH_SNAIL_2649 = 2649;
    public static final int BLOOD_BLAMISH_SNAIL_2650 = 2650;
    public static final int OCHRE_BLAMISH_SNAIL_2651 = 2651;
    public static final int BRUISE_BLAMISH_SNAIL_2652 = 2652;
    public static final int FISHING_SPOT_2653 = 2653;
    public static final int FISHING_SPOT_2654 = 2654;
    public static final int FISHING_SPOT_2655 = 2655;
    public static final int ALUFT_GIANNE_SNR = 2656;
    public static final int GNOME_WAITER = 2657;
    public static final int HEAD_CHEF = 2658;
    public static final int PUREPKER895 = 2659;
    public static final int QUTIEDOLL = 2660;
    public static final int _1337SP34KR = 2661;
    public static final int ELFINLOCKS_2662 = 2662;
    public static final int ELSTAN = 2663;
    public static final int DANTAERA = 2664;
    public static final int KRAGEN = 2665;
    public static final int LYRA = 2666;
    public static final int FRANCIS = 2667;
    public static final int COMBAT_DUMMY = 2668;
    public static final int GARTH = 2669;
    public static final int ELLENA = 2670;
    public static final int SELENA = 2671;
    public static final int VASQUEN = 2672;
    public static final int RHONEN = 2673;
    public static final int DREVEN = 2674;
    public static final int TARIA = 2675;
    public static final int RHAZIEN = 2676;
    public static final int TORRELL = 2677;
    public static final int ALAIN = 2678;
    public static final int HESKEL = 2679;
    public static final int TREZNOR = 2680;
    public static final int FAYETH = 2681;
    public static final int BOLONGO = 2682;
    public static final int GILETH = 2683;
    public static final int FRIZZY_SKERNIP = 2684;
    public static final int YULF_SQUECKS = 2685;
    public static final int PRAISTAN_EBOLA = 2686;
    public static final int PRISSY_SCILLA = 2687;
    public static final int IMIAGO = 2688;
    public static final int LILIWEN = 2689;
    public static final int COOL_MOM227 = 2690;
    public static final int SHEEP_2691 = 2691;
    public static final int SHEEP_2692 = 2692;
    public static final int SHEEP_2693 = 2693;
    public static final int SHEEP_2694 = 2694;
    public static final int SHEEP_2695 = 2695;
    public static final int SHEEP_2696 = 2696;
    public static final int SHEEP_2697 = 2697;
    public static final int SHEEP_2698 = 2698;
    public static final int SHEEP_2699 = 2699;
    public static final int REACHER_2700 = 2700;
    public static final int REACHER_2701 = 2701;
    public static final int REACHER_2702 = 2702;
    public static final int REACH = 2703;
    public static final int REACH_2704 = 2704;
    public static final int REACH_2705 = 2705;
    public static final int REACH_2706 = 2706;
    public static final int REACH_2707 = 2707;
    public static final int REACH_2708 = 2708;
    public static final int REACHER_2709 = 2709;
    public static final int REACHER_2710 = 2710;
    public static final int REACHER_2711 = 2711;
    public static final int REACHER_2712 = 2712;
    public static final int WISE_OLD_MAN_2713 = 2713;
    public static final int COMBAT_STONE_2714 = 2714;
    public static final int COMBAT_STONE_2715 = 2715;
    public static final int COMBAT_STONE_2716 = 2716;
    public static final int COMBAT_STONE_2717 = 2717;
    public static final int COMBAT_STONE_2718 = 2718;
    public static final int COMBAT_STONE_2719 = 2719;
    public static final int COMBAT_STONE_2720 = 2720;
    public static final int COMBAT_STONE_2721 = 2721;
    public static final int COMBAT_STONE_2722 = 2722;
    public static final int COMBAT_STONE_2723 = 2723;
    public static final int COMBAT_STONE_2724 = 2724;
    public static final int COMBAT_STONE_2725 = 2725;
    public static final int COMBAT_STONE_2726 = 2726;
    public static final int COMBAT_STONE_2727 = 2727;
    public static final int COMBAT_STONE_2728 = 2728;
    public static final int COMBAT_STONE_2729 = 2729;
    public static final int COMBAT_STONE_2730 = 2730;
    public static final int COMBAT_STONE_2731 = 2731;
    public static final int COMBAT_STONE_2732 = 2732;
    public static final int COMBAT_STONE_2733 = 2733;
    public static final int COMBAT_STONE_2734 = 2734;
    public static final int COMBAT_STONE_2735 = 2735;
    public static final int COMBAT_STONE_2736 = 2736;
    public static final int COMBAT_STONE_2737 = 2737;
    public static final int COMBAT_STONE_2738 = 2738;
    public static final int COMBAT_STONE_2739 = 2739;
    public static final int COMBAT_STONE_2740 = 2740;
    public static final int COMBAT_STONE_2741 = 2741;
    public static final int COMBAT_STONE_2742 = 2742;
    public static final int COMBAT_STONE_2743 = 2743;
    public static final int COMBAT_STONE_2744 = 2744;
    public static final int COMBAT_STONE_2745 = 2745;
    public static final int COMBAT_STONE_2746 = 2746;
    public static final int COMBAT_STONE_2747 = 2747;
    public static final int COMBAT_STONE_2748 = 2748;
    public static final int COMBAT_STONE_2749 = 2749;
    public static final int COMBAT_STONE_2750 = 2750;
    public static final int COMBAT_STONE_2751 = 2751;
    public static final int COMBAT_STONE_2752 = 2752;
    public static final int COMBAT_STONE_2753 = 2753;
    public static final int COMBAT_STONE_2754 = 2754;
    public static final int COMBAT_STONE_2755 = 2755;
    public static final int COMBAT_STONE_2756 = 2756;
    public static final int COMBAT_STONE_2757 = 2757;
    public static final int COMBAT_STONE_2758 = 2758;
    public static final int COMBAT_STONE_2759 = 2759;
    public static final int COMBAT_STONE_2760 = 2760;
    public static final int COMBAT_STONE_2761 = 2761;
    public static final int COMBAT_STONE_2762 = 2762;
    public static final int COMBAT_STONE_2763 = 2763;
    public static final int COMBAT_STONE_2764 = 2764;
    public static final int COMBAT_STONE_2765 = 2765;
    public static final int COMBAT_STONE_2766 = 2766;
    public static final int COMBAT_STONE_2767 = 2767;
    public static final int COMBAT_STONE_2768 = 2768;
    public static final int COMBAT_STONE_2769 = 2769;
    public static final int COMBAT_STONE_2770 = 2770;
    public static final int COMBAT_STONE_2771 = 2771;
    public static final int COMBAT_STONE_2772 = 2772;
    public static final int COMBAT_STONE_2773 = 2773;
    public static final int COMBAT_STONE_2774 = 2774;
    public static final int COMBAT_STONE_2775 = 2775;
    public static final int COMBAT_STONE_2776 = 2776;
    public static final int COMBAT_STONE_2777 = 2777;
    public static final int COMBAT_STONE_2778 = 2778;
    public static final int CLOCKWORK_CAT_2782 = 2782;
    public static final int HIRKO = 2783;
    public static final int HOLOY = 2784;
    public static final int HURA = 2785;
    public static final int SHEEP_2786 = 2786;
    public static final int SHEEP_2787 = 2787;
    public static final int SHEEP_2788 = 2788;
    public static final int SHEEP_2789 = 2789;
    public static final int COW = 2790;
    public static final int COW_2791 = 2791;
    public static final int COW_CALF = 2792;
    public static final int COW_2793 = 2793;
    public static final int COW_CALF_2794 = 2794;
    public static final int COW_2795 = 2795;
    public static final int PIG = 2796;
    public static final int PIG_2797 = 2797;
    public static final int PIGLET = 2798;
    public static final int PIGLET_2799 = 2799;
    public static final int PIGLET_2800 = 2800;
    public static final int COW_CALF_2801 = 2801;
    public static final int SHEEPDOG = 2802;
    public static final int ROOSTER_2803 = 2803;
    public static final int CHICKEN_2804 = 2804;
    public static final int CHICKEN_2805 = 2805;
    public static final int CHICKEN_2806 = 2806;
    public static final int PIG_2807 = 2807;
    public static final int PIG_2808 = 2808;
    public static final int PIGLET_2809 = 2809;
    public static final int PIGLET_2810 = 2810;
    public static final int PIGLET_2811 = 2811;
    public static final int FATHER_AERECK = 2812;
    public static final int SHOP_KEEPER = 2813;
    public static final int SHOP_ASSISTANT = 2814;
    public static final int SHOP_KEEPER_2815 = 2815;
    public static final int SHOP_ASSISTANT_2816 = 2816;
    public static final int SHOP_KEEPER_2817 = 2817;
    public static final int SHOP_ASSISTANT_2818 = 2818;
    public static final int SHOP_KEEPER_2819 = 2819;
    public static final int SHOP_ASSISTANT_2820 = 2820;
    public static final int SHOP_KEEPER_2821 = 2821;
    public static final int SHOP_ASSISTANT_2822 = 2822;
    public static final int SHOP_KEEPER_2823 = 2823;
    public static final int SHOP_ASSISTANT_2824 = 2824;
    public static final int SHOP_KEEPER_2825 = 2825;
    public static final int SHOP_ASSISTANT_2826 = 2826;
    public static final int BAT = 2827;
    public static final int DRYAD = 2828;
    public static final int FAIRY_2829 = 2829;
    public static final int MYSTERIOUS_OLD_MAN = 2830;
    public static final int CHAIR = 2832;
    public static final int LIL_CREATOR = 2833;
    public static final int GIANT_BAT = 2834;
    public static final int CAMEL = 2835;
    public static final int GOLEM = 2836;
    public static final int UNICORN = 2837;
    public static final int GRIZZLY_BEAR = 2838;
    public static final int BLACK_BEAR = 2839;
    public static final int EARTH_WARRIOR = 2840;
    public static final int ICE_WARRIOR = 2841;
    public static final int ICE_WARRIOR_2842 = 2842;
    public static final int OTHERWORLDLY_BEING = 2843;
    public static final int MAGIC_AXE = 2844;
    public static final int SNAKE_2845 = 2845;
    public static final int SKAVID = 2846;
    public static final int YETI = 2847;
    public static final int MONKEY_2848 = 2848;
    public static final int BLACK_UNICORN = 2849;
    public static final int VEOS = 2850;
    public static final int ICE_WARRIOR_2851 = 2851;
    public static final int FLY_TRAP = 2852;
    public static final int SHADOW_WARRIOR = 2853;
    public static final int RAT_2854 = 2854;
    public static final int RAT_2855 = 2855;
    public static final int GIANT_RAT_2856 = 2856;
    public static final int GIANT_RAT_2857 = 2857;
    public static final int GIANT_RAT_2858 = 2858;
    public static final int GIANT_RAT_2859 = 2859;
    public static final int GIANT_RAT_2860 = 2860;
    public static final int GIANT_RAT_2861 = 2861;
    public static final int GIANT_RAT_2862 = 2862;
    public static final int GIANT_RAT_2863 = 2863;
    public static final int GIANT_RAT_2864 = 2864;
    public static final int DUNGEON_RAT = 2865;
    public static final int DUNGEON_RAT_2866 = 2866;
    public static final int DUNGEON_RAT_2867 = 2867;
    public static final int FAIRY_SHOP_KEEPER = 2868;
    public static final int FAIRY_SHOP_ASSISTANT = 2869;
    public static final int VALAINE = 2870;
    public static final int SCAVVO = 2871;
    public static final int PEKSA = 2872;
    public static final int SILK_TRADER = 2873;
    public static final int GEM_TRADER = 2874;
    public static final int ZEKE = 2875;
    public static final int LOUIE_LEGS = 2876;
    public static final int KARIM = 2877;
    public static final int RANAEL = 2878;
    public static final int DOMMIK = 2879;
    public static final int ZAFF = 2880;
    public static final int BARAEK = 2881;
    public static final int HORVIK = 2882;
    public static final int LOWE = 2883;
    public static final int SHOP_KEEPER_2884 = 2884;
    public static final int SHOP_ASSISTANT_2885 = 2885;
    public static final int ASYFF = 2887;
    public static final int SHOP_KEEPER_2888 = 2888;
    public static final int GRUM_2889 = 2889;
    public static final int WYDIN_2890 = 2890;
    public static final int GERRANT_2891 = 2891;
    public static final int BRIAN = 2892;
    public static final int JIMINUA = 2893;
    public static final int SHOP_KEEPER_2894 = 2894;
    public static final int COOK_2895 = 2895;
    public static final int COOK_2896 = 2896;
    public static final int BANKER_2897 = 2897;
    public static final int BANKER_2898 = 2898;
    public static final int IFFIE = 2899;
    public static final int ELSIE = 2900;
    public static final int CLEANER = 2901;
    public static final int STRAY_DOG = 2902;
    public static final int ORANGE_SALAMANDER = 2903;
    public static final int RED_SALAMANDER = 2904;
    public static final int BLACK_SALAMANDER = 2905;
    public static final int SWAMP_LIZARD = 2906;
    public static final int SABRETOOTHED_KYATT = 2907;
    public static final int SPINED_LARUPIA = 2908;
    public static final int HORNED_GRAAHK = 2909;
    public static final int CHINCHOMPA = 2910;
    public static final int CARNIVOROUS_CHINCHOMPA = 2911;
    public static final int BLACK_CHINCHOMPA = 2912;
    public static final int MASTER_FISHER = 2913;
    public static final int OTTO_GODBLESSED = 2914;
    public static final int OTTO_GODBLESSED_2915 = 2915;
    public static final int WATERFIEND = 2916;
    public static final int WATERFIEND_2917 = 2917;
    public static final int BRUTAL_GREEN_DRAGON = 2918;
    public static final int MITHRIL_DRAGON = 2919;
    public static final int CONFUSED_BARBARIAN = 2920;
    public static final int LOST_BARBARIAN = 2921;
    public static final int STRAY_DOG_2922 = 2922;
    public static final int BLAST_FURNACE_FOREMAN = 2923;
    public static final int TIN_ORE = 2924;
    public static final int COPPER_ORE = 2925;
    public static final int IRON_ORE = 2926;
    public static final int MITHRIL_ORE = 2927;
    public static final int ADAMANTITE_ORE = 2928;
    public static final int RUNITE_ORE = 2929;
    public static final int SILVER_ORE = 2930;
    public static final int GOLD_ORE = 2931;
    public static final int COAL = 2932;
    public static final int PERFECT_GOLD_ORE = 2933;
    public static final int NAIL_BEAST = 2946;
    public static final int NAIL_BEAST_2947 = 2947;
    public static final int NAIL_BEAST_2948 = 2948;
    public static final int SQUIRE_2949 = 2949;
    public static final int VOID_KNIGHT_2950 = 2950;
    public static final int VOID_KNIGHT_2951 = 2951;
    public static final int VOID_KNIGHT_2952 = 2952;
    public static final int VOID_KNIGHT_2953 = 2953;
    public static final int ZAMORAK_WIZARD = 2954;
    public static final int SARADOMIN_WIZARD = 2955;
    public static final int SPRING_ELEMENTAL = 2956;
    public static final int SPRING_ELEMENTAL_2957 = 2957;
    public static final int SPRING_ELEMENTAL_2958 = 2958;
    public static final int SPRING_ELEMENTAL_2959 = 2959;
    public static final int SPRING_ELEMENTAL_2960 = 2960;
    public static final int SPRING_ELEMENTAL_2961 = 2961;
    public static final int SPRING_ELEMENTAL_2962 = 2962;
    public static final int SPRING_ELEMENTAL_2963 = 2963;
    public static final int KING_AWOWOGEI = 2972;
    public static final int KING_AWOWOGEI_2974 = 2974;
    public static final int MIZARU = 2975;
    public static final int KIKAZARU = 2976;
    public static final int IWAZARU = 2977;
    public static final int BIG_SNAKE = 2978;
    public static final int MAWNIS_BUROWGAR_2980 = 2980;
    public static final int HONOUR_GUARD_2981 = 2981;
    public static final int HONOUR_GUARD_2982 = 2982;
    public static final int FRIDLEIF_SHIELDSON_2983 = 2983;
    public static final int THAKKRAD_SIGMUNDSON_2984 = 2984;
    public static final int VELORINA = 2985;
    public static final int NECROVARUS = 2986;
    public static final int GRAVINGAS = 2987;
    public static final int GHOST_DISCIPLE = 2988;
    public static final int AKHARANU = 2989;
    public static final int UNDEAD_COW = 2992;
    public static final int UNDEAD_CHICKEN = 2993;
    public static final int GIANT_LOBSTER = 2994;
    public static final int ROBIN = 2995;
    public static final int OLD_CRONE = 2996;
    public static final int OLD_MAN = 2997;
    public static final int GHOST_VILLAGER = 2998;
    public static final int TORTURED_SOUL = 2999;
    public static final int GHOST_SHOPKEEPER = 3000;
    public static final int GHOST_INNKEEPER = 3001;
    public static final int GHOST_FARMER = 3002;
    public static final int GHOST_BANKER = 3003;
    public static final int GHOST_SAILOR = 3004;
    public static final int GHOST_CAPTAIN = 3005;
    public static final int GHOST_CAPTAIN_3006 = 3006;
    public static final int GHOST_GUARD = 3007;
    public static final int GHOST_ = 3008;
    public static final int GHOST__3009 = 3009;
    public static final int GUARD_3010 = 3010;
    public static final int GUARD_3011 = 3011;
    public static final int TRAINEE_GUARD = 3012;
    public static final int CAPTAIN = 3013;
    public static final int MAN_3014 = 3014;
    public static final int WOMAN_3015 = 3015;
    public static final int SHADOW_SPIDER = 3016;
    public static final int GIANT_SPIDER_3017 = 3017;
    public static final int GIANT_SPIDER_3018 = 3018;
    public static final int SPIDER_3019 = 3019;
    public static final int JUNGLE_SPIDER = 3020;
    public static final int DEADLY_RED_SPIDER = 3021;
    public static final int ICE_SPIDER = 3022;
    public static final int POISON_SPIDER = 3023;
    public static final int SCORPION_3024 = 3024;
    public static final int POISON_SCORPION = 3025;
    public static final int PIT_SCORPION = 3026;
    public static final int KING_SCORPION = 3027;
    public static final int GOBLIN_3028 = 3028;
    public static final int GOBLIN_3029 = 3029;
    public static final int GOBLIN_3030 = 3030;
    public static final int GOBLIN_3031 = 3031;
    public static final int GOBLIN_3032 = 3032;
    public static final int GOBLIN_3033 = 3033;
    public static final int GOBLIN_3034 = 3034;
    public static final int GOBLIN_3035 = 3035;
    public static final int GOBLIN_3036 = 3036;
    public static final int GOBLIN_3037 = 3037;
    public static final int GOBLIN_3038 = 3038;
    public static final int GOBLIN_3039 = 3039;
    public static final int GOBLIN_3040 = 3040;
    public static final int GOBLIN_3041 = 3041;
    public static final int GOBLIN_3042 = 3042;
    public static final int GOBLIN_3043 = 3043;
    public static final int GOBLIN_3044 = 3044;
    public static final int GOBLIN_3045 = 3045;
    public static final int GOBLIN_3046 = 3046;
    public static final int GOBLIN_3047 = 3047;
    public static final int GOBLIN_3048 = 3048;
    public static final int HOBGOBLIN_3049 = 3049;
    public static final int HOBGOBLIN_3050 = 3050;
    public static final int GOBLIN_3051 = 3051;
    public static final int GOBLIN_3052 = 3052;
    public static final int GOBLIN_3053 = 3053;
    public static final int GOBLIN_3054 = 3054;
    public static final int BARBARIAN = 3055;
    public static final int BARBARIAN_3056 = 3056;
    public static final int BARBARIAN_3057 = 3057;
    public static final int BARBARIAN_3058 = 3058;
    public static final int BARBARIAN_3059 = 3059;
    public static final int BARBARIAN_3060 = 3060;
    public static final int BARBARIAN_3061 = 3061;
    public static final int BARBARIAN_3062 = 3062;
    public static final int HUNDING = 3063;
    public static final int BARBARIAN_3064 = 3064;
    public static final int BARBARIAN_3065 = 3065;
    public static final int BARBARIAN_3066 = 3066;
    public static final int BARBARIAN_3067 = 3067;
    public static final int BARBARIAN_3068 = 3068;
    public static final int BARBARIAN_3069 = 3069;
    public static final int BARBARIAN_3070 = 3070;
    public static final int BARBARIAN_3071 = 3071;
    public static final int BARBARIAN_3072 = 3072;
    public static final int GOBLIN_3073 = 3073;
    public static final int GOBLIN_3074 = 3074;
    public static final int GOBLIN_3075 = 3075;
    public static final int GOBLIN_3076 = 3076;
    public static final int PHOENIX = 3077;
    public static final int PHOENIX_3078 = 3078;
    public static final int PHOENIX_3079 = 3079;
    public static final int PHOENIX_3080 = 3080;
    public static final int PHOENIX_3081 = 3081;
    public static final int PHOENIX_3082 = 3082;
    public static final int PHOENIX_3083 = 3083;
    public static final int PHOENIX_3084 = 3084;
    public static final int PORTAL_3086 = 3086;
    public static final int PORTAL_3088 = 3088;
    public static final int BANKER_3089 = 3089;
    public static final int BANKER_3090 = 3090;
    public static final int BANKER_3091 = 3091;
    public static final int BANKER_3092 = 3092;
    public static final int BANKER_3093 = 3093;
    public static final int BANKER_3094 = 3094;
    public static final int CHIEF_SERVANT = 3095;
    public static final int TAXIDERMIST = 3096;
    public static final int ESTATE_AGENT = 3097;
    public static final int STONEMASON = 3098;
    public static final int HELLPUPPY_3099 = 3099;
    public static final int SIR_RENITEE = 3100;
    public static final int SAWMILL_OPERATOR = 3101;
    public static final int GARDEN_SUPPLIER = 3102;
    public static final int GARDEN_SUPPLIER_3103 = 3103;
    public static final int HANS = 3105;
    public static final int MAN_3106 = 3106;
    public static final int MAN_3107 = 3107;
    public static final int MAN_3108 = 3108;
    public static final int MAN_3109 = 3109;
    public static final int MAN_3110 = 3110;
    public static final int WOMAN_3111 = 3111;
    public static final int WOMAN_3112 = 3112;
    public static final int WOMAN_3113 = 3113;
    public static final int FARMER = 3114;
    public static final int FARID_MORRISANE_ORES_AND_BARS = 3115;
    public static final int TZKIH_3116 = 3116;
    public static final int TZKIH_3117 = 3117;
    public static final int TZKEK_3118 = 3118;
    public static final int TZKEK_3119 = 3119;
    public static final int TZKEK_3120 = 3120;
    public static final int TOKXIL_3121 = 3121;
    public static final int TOKXIL_3122 = 3122;
    public static final int YTMEJKOT = 3123;
    public static final int YTMEJKOT_3124 = 3124;
    public static final int KETZEK = 3125;
    public static final int KETZEK_3126 = 3126;
    public static final int TZTOKJAD = 3127;
    public static final int YTHURKOT = 3128;
    public static final int KRIL_TSUTSAROTH = 3129;
    public static final int TSTANON_KARLAK = 3130;
    public static final int ZAKLN_GRITCH = 3131;
    public static final int BALFRUG_KREEYATH = 3132;
    public static final int HELLHOUND_3133 = 3133;
    public static final int IMP = 3134;
    public static final int WEREWOLF_3135 = 3135;
    public static final int WEREWOLF_3136 = 3136;
    public static final int FERAL_VAMPYRE = 3137;
    public static final int BLOODVELD_3138 = 3138;
    public static final int PYREFIEND_3139 = 3139;
    public static final int ICEFIEND = 3140;
    public static final int GORAK_3141 = 3141;
    public static final int GNOME_COACH = 3142;
    public static final int GNOME_BALLER = 3143;
    public static final int GNOME_BALLER_3144 = 3144;
    public static final int GNOME_BALLER_3145 = 3145;
    public static final int GNOME_BALLER_3146 = 3146;
    public static final int GNOME_BALLER_3147 = 3147;
    public static final int GNOME_BALLER_3148 = 3148;
    public static final int GNOME_BALLER_3149 = 3149;
    public static final int GNOME_BALLER_3150 = 3150;
    public static final int GNOME_BALLER_3151 = 3151;
    public static final int GNOME_BALLER_3152 = 3152;
    public static final int GNOME_BALLER_3153 = 3153;
    public static final int GNOME_BALLER_3154 = 3154;
    public static final int GNOME_WINGER = 3155;
    public static final int GNOME_WINGER_3156 = 3156;
    public static final int GNOME_BALL_REFEREE = 3157;
    public static final int CHEERLEADER = 3158;
    public static final int SPIRITUAL_WARRIOR_3159 = 3159;
    public static final int SPIRITUAL_RANGER_3160 = 3160;
    public static final int SPIRITUAL_MAGE_3161 = 3161;
    public static final int KREEARRA = 3162;
    public static final int WINGMAN_SKREE = 3163;
    public static final int FLOCKLEADER_GEERIN = 3164;
    public static final int FLIGHT_KILISA = 3165;
    public static final int SPIRITUAL_WARRIOR_3166 = 3166;
    public static final int SPIRITUAL_RANGER_3167 = 3167;
    public static final int SPIRITUAL_MAGE_3168 = 3168;
    public static final int AVIANSIE = 3169;
    public static final int AVIANSIE_3170 = 3170;
    public static final int AVIANSIE_3171 = 3171;
    public static final int AVIANSIE_3172 = 3172;
    public static final int AVIANSIE_3173 = 3173;
    public static final int AVIANSIE_3174 = 3174;
    public static final int AVIANSIE_3175 = 3175;
    public static final int AVIANSIE_3176 = 3176;
    public static final int AVIANSIE_3177 = 3177;
    public static final int AVIANSIE_3178 = 3178;
    public static final int AVIANSIE_3179 = 3179;
    public static final int AVIANSIE_3180 = 3180;
    public static final int AVIANSIE_3181 = 3181;
    public static final int AVIANSIE_3182 = 3182;
    public static final int AVIANSIE_3183 = 3183;
    public static final int DAGANNOTH_SPAWN = 3184;
    public static final int DAGANNOTH_3185 = 3185;
    public static final int BRIAN_ORICHARD = 3189;
    public static final int ROGUE_GUARD = 3190;
    public static final int ROGUE_GUARD_3191 = 3191;
    public static final int ROGUE_GUARD_3192 = 3192;
    public static final int MARTIN_THWAIT = 3193;
    public static final int EMERALD_BENEDICT = 3194;
    public static final int SPIN_BLADES = 3195;
    public static final int SPIN_BLADES_3196 = 3196;
    public static final int CANDLE_MAKER = 3199;
    public static final int ARHEIN = 3200;
    public static final int JUKAT = 3201;
    public static final int LUNDERWIN = 3202;
    public static final int IRKSOL = 3203;
    public static final int FAIRY_3204 = 3204;
    public static final int ZAMBO = 3205;
    public static final int GEM_MERCHANT = 3207;
    public static final int BAKER = 3208;
    public static final int SPICE_SELLER = 3209;
    public static final int FUR_TRADER = 3210;
    public static final int SILK_MERCHANT = 3211;
    public static final int HICKTON = 3212;
    public static final int HARRY = 3213;
    public static final int CASSIE = 3214;
    public static final int FRINCOS = 3215;
    public static final int MELEE_COMBAT_TUTOR = 3216;
    public static final int RANGED_COMBAT_TUTOR = 3217;
    public static final int MAGIC_COMBAT_TUTOR = 3218;
    public static final int COOKING_TUTOR = 3219;
    public static final int CRAFTING_TUTOR = 3220;
    public static final int FISHING_TUTOR = 3221;
    public static final int MINING_TUTOR = 3222;
    public static final int PRAYER_TUTOR = 3223;
    public static final int SMITHING_APPRENTICE = 3224;
    public static final int MASTER_SMITHING_TUTOR = 3225;
    public static final int WOODSMAN_TUTOR = 3226;
    public static final int BANKER_TUTOR = 3227;
    public static final int ELLIS = 3231;
    public static final int WIZARD_JALARAST = 3232;
    public static final int LEECH = 3233;
    public static final int FERAL_VAMPYRE_3234 = 3234;
    public static final int BEE_KEEPER = 3235;
    public static final int MIST = 3236;
    public static final int FERAL_VAMPYRE_3237 = 3237;
    public static final int VAMPYRIC_HOUND = 3238;
    public static final int FERAL_VAMPYRE_3239 = 3239;
    public static final int TREE = 3240;
    public static final int BLANDEBIR = 3241;
    public static final int METARIALUS = 3242;
    public static final int FARMER_3243 = 3243;
    public static final int FARMER_3244 = 3244;
    public static final int FARMER_3245 = 3245;
    public static final int WIZARD_FRUMSCONE = 3246;
    public static final int WIZARD_AKUTHA = 3247;
    public static final int WIZARD_SININA = 3249;
    public static final int FARMER_3250 = 3250;
    public static final int FARMER_3251 = 3251;
    public static final int THIEF_3252 = 3252;
    public static final int THIEF_3253 = 3253;
    public static final int GUARD_3254 = 3254;
    public static final int TRAMP_3255 = 3255;
    public static final int BARBARIAN_3256 = 3256;
    public static final int WIZARD_3257 = 3257;
    public static final int DRUID = 3258;
    public static final int DRUID_3259 = 3259;
    public static final int WARRIOR_WOMAN = 3260;
    public static final int MAN_3261 = 3261;
    public static final int BARBARIAN_3262 = 3262;
    public static final int DRUNKEN_MAN = 3263;
    public static final int MAN_3264 = 3264;
    public static final int MAN_3265 = 3265;
    public static final int NORMAN = 3266;
    public static final int CECILIA = 3267;
    public static final int WOMAN_3268 = 3268;
    public static final int GUARD_3269 = 3269;
    public static final int GUARD_3270 = 3270;
    public static final int GUARD_3271 = 3271;
    public static final int GUARD_3272 = 3272;
    public static final int GUARD_3273 = 3273;
    public static final int GUARD_3274 = 3274;
    public static final int GARDENER = 3275;
    public static final int GARDENER_3276 = 3276;
    public static final int APPRENTICE_WORKMAN = 3277;
    public static final int WORKMAN_3278 = 3278;
    public static final int CUFFS = 3279;
    public static final int NARF = 3280;
    public static final int RUSTY = 3281;
    public static final int JEFF = 3282;
    public static final int GUARD_3283 = 3283;
    public static final int HENGEL = 3284;
    public static final int ANJA = 3285;
    public static final int HOBGOBLIN_3286 = 3286;
    public static final int HOBGOBLIN_3287 = 3287;
    public static final int HOBGOBLIN_3288 = 3288;
    public static final int HOBGOBLIN_3289 = 3289;
    public static final int FROG_3290 = 3290;
    public static final int POSTIE_PETE = 3291;
    public static final int AL_KHARID_WARRIOR = 3292;
    public static final int PALADIN_3293 = 3293;
    public static final int PALADIN_3294 = 3294;
    public static final int HERO = 3295;
    public static final int FORESTER = 3296;
    public static final int KNIGHT_OF_ARDOUGNE = 3297;
    public static final int MAN_3298 = 3298;
    public static final int WOMAN_3299 = 3299;
    public static final int KNIGHT_OF_ARDOUGNE_3300 = 3300;
    public static final int ARCHER_3301 = 3301;
    public static final int ZOO_KEEPER = 3302;
    public static final int CHUCK = 3303;
    public static final int BARMAN_3304 = 3304;
    public static final int MASTER_CHEF = 3305;
    public static final int HENJA = 3306;
    public static final int COMBAT_INSTRUCTOR = 3307;
    public static final int GIELINOR_GUIDE = 3308;
    public static final int MAGIC_INSTRUCTOR = 3309;
    public static final int ACCOUNT_GUIDE = 3310;
    public static final int MINING_INSTRUCTOR = 3311;
    public static final int QUEST_GUIDE = 3312;
    public static final int GIANT_RAT_3313 = 3313;
    public static final int GIANT_RAT_3314 = 3314;
    public static final int GIANT_RAT_3315 = 3315;
    public static final int CHICKEN_3316 = 3316;
    public static final int FISHING_SPOT_3317 = 3317;
    public static final int BANKER_3318 = 3318;
    public static final int BROTHER_BRACE = 3319;
    public static final int SKIPPY_3320 = 3320;
    public static final int LARXUS = 3321;
    public static final int MYSTERY_FIGURE = 3322;
    public static final int MYSTERY_FIGURE_3323 = 3323;
    public static final int MYSTERY_FIGURE_3324 = 3324;
    public static final int MYSTERY_FIGURE_3325 = 3325;
    public static final int MYSTERY_FIGURE_3326 = 3326;
    public static final int MYSTERY_FIGURE_3327 = 3327;
    public static final int EARTH_WARRIOR_CHAMPION = 3328;
    public static final int GIANT_CHAMPION = 3329;
    public static final int GHOUL_CHAMPION = 3330;
    public static final int MUBARIZ = 3331;
    public static final int TREES = 3332;
    public static final int TREES_3333 = 3333;
    public static final int CAVEMOUTH = 3334;
    public static final int BULLRUSH = 3335;
    public static final int BULLRUSH_3336 = 3336;
    public static final int CAVE_SCENERY = 3337;
    public static final int CAVE_SCENERY_3338 = 3338;
    public static final int CAVE_SCENERY_3339 = 3339;
    public static final int FADLI = 3340;
    public static final int AABLA = 3341;
    public static final int SABREEN = 3342;
    public static final int SURGEON_GENERAL_TAFANI = 3343;
    public static final int JARAAH = 3344;
    public static final int ZAHWA = 3345;
    public static final int IMA = 3346;
    public static final int SABEIL = 3347;
    public static final int JADID = 3348;
    public static final int DALAL = 3349;
    public static final int AFRAH = 3350;
    public static final int JEED = 3351;
    public static final int HAMID = 3352;
    public static final int GOBLIN_CHAMPION = 3353;
    public static final int HOBGOBLIN_CHAMPION = 3354;
    public static final int IMP_CHAMPION = 3355;
    public static final int JOGRE_CHAMPION = 3356;
    public static final int LESSER_DEMON_CHAMPION = 3357;
    public static final int SKELETON_CHAMPION = 3358;
    public static final int ZOMBIES_CHAMPION = 3359;
    public static final int LEON_DCOUR = 3360;
    public static final int GUARD_3361 = 3361;
    public static final int LITARA = 3362;
    public static final int STANKERS = 3364;
    public static final int ANGEL = 3369;
    public static final int ELEMENTAL_BALANCE = 3370;
    public static final int ELEMENTAL_BALANCE_3371 = 3371;
    public static final int ELEMENTAL_BALANCE_3372 = 3372;
    public static final int ELEMENTAL_BALANCE_3373 = 3373;
    public static final int ELEMENTAL_BALANCE_3374 = 3374;
    public static final int ELEMENTAL_BALANCE_3375 = 3375;
    public static final int ELEMENTAL_BALANCE_3376 = 3376;
    public static final int ELEMENTAL_BALANCE_3377 = 3377;
    public static final int ELEMENTAL_BALANCE_3378 = 3378;
    public static final int ELEMENTAL_BALANCE_3379 = 3379;
    public static final int ELEMENTAL_BALANCE_3380 = 3380;
    public static final int ELEMENTAL_BALANCE_3381 = 3381;
    public static final int ELEMENTAL_BALANCE_3382 = 3382;
    public static final int ELEMENTAL_BALANCE_3383 = 3383;
    public static final int ELEMENTAL_BALANCE_3384 = 3384;
    public static final int ELEMENTAL_BALANCE_3385 = 3385;
    public static final int ELEMENTAL_BALANCE_3386 = 3386;
    public static final int ELEMENTAL_BALANCE_3387 = 3387;
    public static final int OSMAN_3388 = 3388;
    public static final int PIRATE_PETE_3389 = 3389;
    public static final int MOUNTAIN_DWARF = 3390;
    public static final int GENERAL_WARTFACE_3391 = 3391;
    public static final int GENERAL_BENTNOZE_3392 = 3392;
    public static final int LUMBRIDGE_GUIDE_3393 = 3393;
    public static final int EVIL_DAVE_3394 = 3394;
    public static final int SIR_AMIK_VARZE_3395 = 3395;
    public static final int AWOWOGEI = 3396;
    public static final int AWOWOGEI_3397 = 3397;
    public static final int SKRACH_UGLOGWEE = 3398;
    public static final int CULINAROMANCER = 3400;
    public static final int ELEMENTAL_BALANCE_3401 = 3401;
    public static final int ELEMENTAL_BALANCE_3402 = 3402;
    public static final int ELEMENTAL_BALANCE_3403 = 3403;
    public static final int ELEMENTAL_BALANCE_3404 = 3404;
    public static final int ELEMENTAL_BALANCE_3405 = 3405;
    public static final int ELEMENTAL_BALANCE_3406 = 3406;
    public static final int ELEMENTAL_BALANCE_3407 = 3407;
    public static final int ELEMENTAL_BALANCE_3408 = 3408;
    public static final int ELEMENTAL_BALANCE_3409 = 3409;
    public static final int ELEMENTAL_BALANCE_3410 = 3410;
    public static final int ELEMENTAL_BALANCE_3411 = 3411;
    public static final int ELEMENTAL_BALANCE_3412 = 3412;
    public static final int KOFTIK = 3413;
    public static final int IDRIS = 3414;
    public static final int ESSYLLT = 3415;
    public static final int MORVRAN = 3416;
    public static final int ROD_FISHING_SPOT_3417 = 3417;
    public static final int ROD_FISHING_SPOT_3418 = 3418;
    public static final int FISHING_SPOT_3419 = 3419;
    public static final int RABBIT_3420 = 3420;
    public static final int RABBIT_3421 = 3421;
    public static final int RABBIT_3422 = 3422;
    public static final int GRIZZLY_BEAR_3423 = 3423;
    public static final int GRIZZLY_BEAR_CUB = 3424;
    public static final int GRIZZLY_BEAR_CUB_3425 = 3425;
    public static final int DIRE_WOLF = 3426;
    public static final int IORWERTH_ARCHER = 3428;
    public static final int IORWERTH_WARRIOR = 3429;
    public static final int ARIANWYN = 3432;
    public static final int TYRAS_GUARD_3433 = 3433;
    public static final int TYRAS_GUARD_3434 = 3434;
    public static final int TYRAS_GUARD_3436 = 3436;
    public static final int GENERAL_HINING = 3437;
    public static final int QUARTERMASTER = 3438;
    public static final int KINGS_MESSENGER = 3439;
    public static final int WILL_O_THE_WISP = 3440;
    public static final int WILL_O_THE_WISP_3441 = 3441;
    public static final int ALI_THE_DYER = 3442;
    public static final int LUCIEN = 3443;
    public static final int LUCIEN_3444 = 3444;
    public static final int GUARDIAN_OF_ARMADYL = 3445;
    public static final int GUARDIAN_OF_ARMADYL_3446 = 3446;
    public static final int WINELDA = 3447;
    public static final int FIRE_WARRIOR_OF_LESARKUS = 3448;
    public static final int SHADOW_HOUND = 3449;
    public static final int MYSTERIOUS_GHOST = 3450;
    public static final int MYSTERIOUS_GHOST_3451 = 3451;
    public static final int MYSTERIOUS_GHOST_3452 = 3452;
    public static final int MYSTERIOUS_GHOST_3453 = 3453;
    public static final int MYSTERIOUS_GHOST_3454 = 3454;
    public static final int MYSTERIOUS_GHOST_3455 = 3455;
    public static final int FAREED = 3456;
    public static final int ELEMENTAL_BALANCE_3457 = 3457;
    public static final int KAMIL = 3458;
    public static final int DESSOUS = 3459;
    public static final int DESSOUS_3460 = 3460;
    public static final int RUANTUN = 3461;
    public static final int CYRISUS = 3462;
    public static final int CYRISUS_3463 = 3463;
    public static final int CYRISUS_3464 = 3464;
    public static final int FALLEN_MAN = 3465;
    public static final int FALLEN_MAN_3466 = 3466;
    public static final int CYRISUS_3467 = 3467;
    public static final int CYRISUS_3468 = 3468;
    public static final int CYRISUS_3469 = 3469;
    public static final int CYRISUS_3470 = 3470;
    public static final int CYRISUS_3471 = 3471;
    public static final int BIRDSEYE_JACK = 3472;
    public static final int THE_INADEQUACY = 3473;
    public static final int THE_EVERLASTING = 3474;
    public static final int THE_UNTOUCHABLE = 3475;
    public static final int THE_ILLUSIVE = 3476;
    public static final int A_DOUBT = 3477;
    public static final int THE_ILLUSIVE_3478 = 3478;
    public static final int MORGAN = 3479;
    public static final int DR_HARLOW = 3480;
    public static final int COUNT_DRAYNOR = 3481;
    public static final int COUNT_DRAYNOR_3482 = 3482;
    public static final int WILL_O_THE_WISP_3483 = 3483;
    public static final int MONK_OF_ZAMORAK_3484 = 3484;
    public static final int MONK_OF_ZAMORAK_3485 = 3485;
    public static final int MONK_OF_ZAMORAK_3486 = 3486;
    public static final int JORRAL = 3490;
    public static final int MELINA = 3491;
    public static final int MELINA_3492 = 3492;
    public static final int DROALAK = 3493;
    public static final int DROALAK_3494 = 3494;
    public static final int DRON = 3495;
    public static final int BLANIN = 3496;
    public static final int GERTRUDES_CAT_3497 = 3497;
    public static final int KITTEN = 3498;
    public static final int CRATE = 3499;
    public static final int SHILOP = 3501;
    public static final int PHILOP = 3502;
    public static final int WILOUGH = 3503;
    public static final int KANEL = 3504;
    public static final int CIVILIAN = 3505;
    public static final int CIVILIAN_3506 = 3506;
    public static final int CIVILIAN_3507 = 3507;
    public static final int BOUNCER_3508 = 3508;
    public static final int BOUNCER_3509 = 3509;
    public static final int GENERAL_KHAZARD_3510 = 3510;
    public static final int SCOUT = 3511;
    public static final int SCOUT_3512 = 3512;
    public static final int SCOUT_3513 = 3513;
    public static final int SCOUT_3514 = 3514;
    public static final int SIN_SEER = 3515;
    public static final int GHOST_3516 = 3516;
    public static final int RENEGADE_KNIGHT = 3517;
    public static final int THRANTAX_THE_MIGHTY = 3518;
    public static final int SIR_LANCELOT = 3519;
    public static final int SIR_GAWAIN = 3520;
    public static final int SIR_KAY = 3521;
    public static final int SIR_BEDIVERE = 3522;
    public static final int SIR_TRISTRAM = 3523;
    public static final int SIR_PELLEAS = 3524;
    public static final int SIR_LUCAN = 3525;
    public static final int SIR_PALOMEDES = 3526;
    public static final int SIR_MORDRED = 3527;
    public static final int MORGAN_LE_FAYE = 3528;
    public static final int MERLIN = 3529;
    public static final int THE_LADY_OF_THE_LAKE = 3530;
    public static final int KING_ARTHUR = 3531;
    public static final int BEGGAR = 3532;
    public static final int ALI_MORRISANE = 3533;
    public static final int DRUNKEN_ALI = 3534;
    public static final int ALI_THE_BARMAN = 3535;
    public static final int ALI_THE_KEBAB_SELLER = 3536;
    public static final int MARKET_SELLER = 3537;
    public static final int ALI_THE_CAMEL_MAN = 3538;
    public static final int STREET_URCHIN = 3539;
    public static final int ALI_THE_MAYOR = 3540;
    public static final int ALI_THE_HAG = 3541;
    public static final int ALI_THE_SNAKE_CHARMER = 3542;
    public static final int ALI_THE_CAMEL = 3543;
    public static final int DESERT_SNAKE = 3544;
    public static final int SNAKE_3545 = 3545;
    public static final int BLACKJACK_SELLER = 3546;
    public static final int MENAPHITE_LEADER = 3547;
    public static final int ALI_THE_OPERATOR = 3548;
    public static final int MENAPHITE_THUG = 3549;
    public static final int MENAPHITE_THUG_3550 = 3550;
    public static final int TOUGH_GUY = 3551;
    public static final int VILLAGER = 3552;
    public static final int VILLAGER_3553 = 3553;
    public static final int VILLAGER_3554 = 3554;
    public static final int VILLAGER_3555 = 3555;
    public static final int VILLAGER_3556 = 3556;
    public static final int VILLAGER_3557 = 3557;
    public static final int VILLAGER_3558 = 3558;
    public static final int VILLAGER_3559 = 3559;
    public static final int VILLAGER_3560 = 3560;
    public static final int VERONICA = 3561;
    public static final int PROFESSOR_ODDENSTEIN = 3562;
    public static final int ERNEST = 3563;
    public static final int LIL_DESTRUCTOR = 3564;
    public static final int SKELETON_3565 = 3565;
    public static final int LIL_CREATOR_3566 = 3566;
    public static final int PENTYN = 3568;
    public static final int ARISTARCHUS = 3569;
    public static final int BONEGUARD = 3570;
    public static final int PILE_OF_BONES = 3571;
    public static final int DESERT_SPIRIT = 3572;
    public static final int CRUST_OF_ICE = 3573;
    public static final int FURNACE_GRATE = 3574;
    public static final int ENAKHRA = 3575;
    public static final int ENAKHRA_3576 = 3576;
    public static final int BONEGUARD_3577 = 3577;
    public static final int AKTHANAKOS = 3578;
    public static final int AKTHANAKOS_3579 = 3579;
    public static final int LAZIM = 3580;
    public static final int ENAKHRA_3581 = 3581;
    public static final int AKTHANAKOS_3582 = 3582;
    public static final int KNIGHT = 3583;
    public static final int SKELETON_3584 = 3584;
    public static final int EFFIGY = 3585;
    public static final int EFFIGY_3586 = 3586;
    public static final int BONAFIDO = 3587;
    public static final int HOMUNCULUS = 3588;
    public static final int HOMUNCULUS_3589 = 3589;
    public static final int HOMUNCULUS_3590 = 3590;
    public static final int CAGE = 3591;
    public static final int TRANSMUTE_THE_ALCHEMIST = 3592;
    public static final int TRANSMUTE_THE_ALCHEMIST_3593 = 3593;
    public static final int CURRENCY_THE_ALCHEMIST = 3594;
    public static final int CURRENCY_THE_ALCHEMIST_3595 = 3595;
    public static final int BLACKEYE = 3596;
    public static final int NO_FINGERS = 3597;
    public static final int GUMMY = 3598;
    public static final int THE_GUNS = 3599;
    public static final int FROGEEL = 3600;
    public static final int UNICOW = 3601;
    public static final int SPIDINE = 3602;
    public static final int SWORDCHICK = 3603;
    public static final int JUBSTER = 3604;
    public static final int NEWTROOST = 3605;
    public static final int BROTHER_KOJO = 3606;
    public static final int DUNGEON_RAT_3607 = 3607;
    public static final int DUNGEON_RAT_3608 = 3608;
    public static final int DUNGEON_RAT_3609 = 3609;
    public static final int MINE_CART = 3610;
    public static final int ZEALOT = 3611;
    public static final int POSSESSED_PICKAXE = 3612;
    public static final int IRON_PICKAXE = 3613;
    public static final int CORPSE = 3614;
    public static final int SKELETAL_MINER = 3615;
    public static final int TREUS_DAYTH = 3616;
    public static final int GHOST_3617 = 3617;
    public static final int LOADING_CRANE = 3618;
    public static final int INNOCENTLOOKING_KEY = 3619;
    public static final int MINE_CART_3620 = 3620;
    public static final int MINE_CART_3621 = 3621;
    public static final int MINE_CART_3622 = 3622;
    public static final int MINE_CART_3623 = 3623;
    public static final int MINE_CART_3624 = 3624;
    public static final int GHOST_3625 = 3625;
    public static final int HAZE = 3626;
    public static final int MISCHIEVOUS_GHOST = 3627;
    public static final int DIGSITE_WORKMAN = 3628;
    public static final int DOUG_DEEPING = 3629;
    public static final int DIGSITE_WORKMAN_3630 = 3630;
    public static final int DIGSITE_WORKMAN_3631 = 3631;
    public static final int STUDENT = 3632;
    public static final int STUDENT_3633 = 3633;
    public static final int STUDENT_3634 = 3634;
    public static final int EXAMINER = 3635;
    public static final int EXAMINER_3636 = 3636;
    public static final int EXAMINER_3637 = 3637;
    public static final int RESEARCHER = 3638;
    public static final int ARCHAEOLOGICAL_EXPERT = 3639;
    public static final int PANNING_GUIDE = 3640;
    public static final int NICK = 3641;
    public static final int NISHA = 3642;
    public static final int REDBEARD_FRANK = 3643;
    public static final int CAPTAIN_TOBIAS = 3644;
    public static final int SEAMAN_LORRIS = 3645;
    public static final int SEAMAN_THRESNOR = 3646;
    public static final int LUTHAS = 3647;
    public static final int CUSTOMS_OFFICER = 3648;
    public static final int TOY_SOLDIER = 3649;
    public static final int GARDENER_3651 = 3651;
    public static final int MAN_3652 = 3652;
    public static final int LUMBERJACK_LEIF = 3653;
    public static final int MINER_MAGNUS = 3654;
    public static final int FISHERMAN_FRODI = 3655;
    public static final int GARDENER_GUNNHILD = 3656;
    public static final int FISHING_SPOT_3657 = 3657;
    public static final int CARPENTER_KJALLAK = 3658;
    public static final int FARMER_FROMUND = 3659;
    public static final int REACHER_3660 = 3660;
    public static final int CHICKEN_3661 = 3661;
    public static final int CHICKEN_3662 = 3662;
    public static final int ROOSTER_3663 = 3663;
    public static final int RABBIT_3664 = 3664;
    public static final int RABBIT_3665 = 3665;
    public static final int PRINCE_BRAND = 3666;
    public static final int PRINCESS_ASTRID = 3667;
    public static final int KING_VARGAS = 3668;
    public static final int GUARD_3669 = 3669;
    public static final int DERRIK = 3671;
    public static final int FARMER_3672 = 3672;
    public static final int FLOWER_GIRL = 3673;
    public static final int RAGNAR_3674 = 3674;
    public static final int EINAR = 3675;
    public static final int ALRIK = 3676;
    public static final int THORHILD = 3677;
    public static final int HALLA_3678 = 3678;
    public static final int YRSA = 3679;
    public static final int SAILOR = 3680;
    public static final int RANNVEIG = 3681;
    public static final int THORA_3682 = 3682;
    public static final int VALGERD = 3683;
    public static final int SKRAELING_3684 = 3684;
    public static final int BRODDI = 3685;
    public static final int SKRAELING_3686 = 3686;
    public static final int RAGNVALD = 3687;
    public static final int FISHMONGER_3688 = 3688;
    public static final int GREENGROCER_3689 = 3689;
    public static final int VAMPYRE_JUVINATE = 3690;
    public static final int VAMPYRE_JUVINATE_3691 = 3691;
    public static final int VAMPYRE_JUVENILE = 3692;
    public static final int VAMPYRE_JUVENILE_3693 = 3693;
    public static final int VAMPYRE_JUVINATE_3694 = 3694;
    public static final int VAMPYRE_JUVINATE_3695 = 3695;
    public static final int VAMPYRE_JUVENILE_3696 = 3696;
    public static final int VAMPYRE_JUVENILE_3697 = 3697;
    public static final int VAMPYRE_JUVINATE_3698 = 3698;
    public static final int VAMPYRE_JUVINATE_3699 = 3699;
    public static final int VAMPYRE_JUVINATE_3700 = 3700;
    public static final int FORMER_VAMPYRE = 3701;
    public static final int FORMER_VAMPYRE_3702 = 3702;
    public static final int FORMER_VAMPYRE_3703 = 3703;
    public static final int FORMER_VAMPYRE_3704 = 3704;
    public static final int FORMER_VAMPYRE_3705 = 3705;
    public static final int FORMER_VAMPYRE_3706 = 3706;
    public static final int FERAL_VAMPYRE_3707 = 3707;
    public static final int FERAL_VAMPYRE_3708 = 3708;
    public static final int VYREWATCH = 3709;
    public static final int VYREWATCH_3710 = 3710;
    public static final int VYREWATCH_3711 = 3711;
    public static final int VYREWATCH_3712 = 3712;
    public static final int VYREWATCH_3713 = 3713;
    public static final int VYREWATCH_3714 = 3714;
    public static final int VYREWATCH_3715 = 3715;
    public static final int VYREWATCH_3716 = 3716;
    public static final int VYREWATCH_3717 = 3717;
    public static final int VYREWATCH_3718 = 3718;
    public static final int VYREWATCH_3719 = 3719;
    public static final int VYREWATCH_3720 = 3720;
    public static final int VYREWATCH_3721 = 3721;
    public static final int VYREWATCH_3722 = 3722;
    public static final int VYREWATCH_3723 = 3723;
    public static final int VYREWATCH_3724 = 3724;
    public static final int VYREWATCH_3725 = 3725;
    public static final int VYREWATCH_3726 = 3726;
    public static final int VYREWATCH_3727 = 3727;
    public static final int VYREWATCH_3728 = 3728;
    public static final int VYREWATCH_3729 = 3729;
    public static final int VYREWATCH_3730 = 3730;
    public static final int VYREWATCH_3731 = 3731;
    public static final int VYREWATCH_3732 = 3732;
    public static final int VANSTROM_KLAUSE = 3733;
    public static final int VANSTROM_KLAUSE_3734 = 3734;
    public static final int VANSTROM_KLAUSE_3735 = 3735;
    public static final int VANSTROM_KLAUSE_3736 = 3736;
    public static final int VANSTROM_KLAUSE_3737 = 3737;
    public static final int VANSTROM_KLAUSE_3738 = 3738;
    public static final int VANSTROM_KLAUSE_3739 = 3739;
    public static final int VANESCULA_DRAKAN = 3740;
    public static final int VANESCULA_DRAKAN_3741 = 3741;
    public static final int VANESCULA_DRAKAN_3742 = 3742;
    public static final int VANESCULA_DRAKAN_3743 = 3743;
    public static final int RANIS_DRAKAN = 3744;
    public static final int RANIS_DRAKAN_3745 = 3745;
    public static final int RANIS_DRAKAN_3746 = 3746;
    public static final int RANIS_DRAKAN_3747 = 3747;
    public static final int VYREWATCH_3748 = 3748;
    public static final int VYREWATCH_3749 = 3749;
    public static final int VYREWATCH_3750 = 3750;
    public static final int VYREWATCH_3751 = 3751;
    public static final int VYREWATCH_3752 = 3752;
    public static final int VYREWATCH_3753 = 3753;
    public static final int VYREWATCH_3754 = 3754;
    public static final int VYREWATCH_3755 = 3755;
    public static final int VYREWATCH_3756 = 3756;
    public static final int VYREWATCH_3757 = 3757;
    public static final int VYREWATCH_3758 = 3758;
    public static final int VYREWATCH_3759 = 3759;
    public static final int VYREWATCH_3760 = 3760;
    public static final int VYREWATCH_3761 = 3761;
    public static final int VYREWATCH_3762 = 3762;
    public static final int VYREWATCH_3763 = 3763;
    public static final int FLYING_FEMALE_VAMPIRE = 3764;
    public static final int FLYING_FEMALE_VAMPIRE_3765 = 3765;
    public static final int FLYING_FEMALE_VAMPIRE_3766 = 3766;
    public static final int FLYING_FEMALE_VAMPIRE_3767 = 3767;
    public static final int VYREWATCH_3768 = 3768;
    public static final int VYREWATCH_3769 = 3769;
    public static final int VYREWATCH_3770 = 3770;
    public static final int VYREWATCH_3771 = 3771;
    public static final int OLD_MAN_RAL = 3772;
    public static final int AEONISIG_RAISPHER = 3774;
    public static final int SAFALAAN_HALLOW = 3775;
    public static final int SARIUS_GUILE = 3776;
    public static final int ELEMENTAL_BALANCE_3777 = 3777;
    public static final int SARIUS_GUILE_3778 = 3778;
    public static final int TRADER_SVEN = 3779;
    public static final int MEIYERDITCH_CITIZEN = 3780;
    public static final int MEIYERDITCH_CITIZEN_3781 = 3781;
    public static final int MEIYERDITCH_CITIZEN_3782 = 3782;
    public static final int MEIYERDITCH_CITIZEN_3783 = 3783;
    public static final int MEIYERDITCH_CITIZEN_3784 = 3784;
    public static final int MEIYERDITCH_CITIZEN_3785 = 3785;
    public static final int MEIYERDITCH_CITIZEN_3786 = 3786;
    public static final int MEIYERDITCH_CITIZEN_3787 = 3787;
    public static final int MEIYERDITCH_CITIZEN_3788 = 3788;
    public static final int MEIYERDITCH_CITIZEN_3789 = 3789;
    public static final int MEIYERDITCH_CITIZEN_3790 = 3790;
    public static final int MEIYERDITCH_CITIZEN_3791 = 3791;
    public static final int MEIYERDITCH_CITIZEN_3792 = 3792;
    public static final int MEIYERDITCH_CITIZEN_3793 = 3793;
    public static final int MEIYERDITCH_CITIZEN_3794 = 3794;
    public static final int MEIYERDITCH_CITIZEN_3795 = 3795;
    public static final int MEIYERDITCH_CITIZEN_3796 = 3796;
    public static final int MEIYERDITCH_CITIZEN_3797 = 3797;
    public static final int MEIYERDITCH_CITIZEN_3798 = 3798;
    public static final int MEIYERDITCH_CITIZEN_3799 = 3799;
    public static final int MEIYERDITCH_CITIZEN_3800 = 3800;
    public static final int MEIYERDITCH_CITIZEN_3801 = 3801;
    public static final int MEIYERDITCH_CITIZEN_3802 = 3802;
    public static final int MEIYERDITCH_CITIZEN_3803 = 3803;
    public static final int MEIYERDITCH_CITIZEN_3804 = 3804;
    public static final int MEIYERDITCH_CITIZEN_3805 = 3805;
    public static final int MEIYERDITCH_CITIZEN_3806 = 3806;
    public static final int MEIYERDITCH_CITIZEN_3807 = 3807;
    public static final int MEIYERDITCH_CITIZEN_3808 = 3808;
    public static final int CHILD_3809 = 3809;
    public static final int CHILD_3810 = 3810;
    public static final int CHILD_3811 = 3811;
    public static final int CHILD_3812 = 3812;
    public static final int CHILD_3813 = 3813;
    public static final int CHILD_3814 = 3814;
    public static final int CHILD_3815 = 3815;
    public static final int CHILD_3816 = 3816;
    public static final int CHILD_3817 = 3817;
    public static final int CHILD_3818 = 3818;
    public static final int MEIYERDITCH_MINER = 3819;
    public static final int MEIYERDITCH_MINER_3820 = 3820;
    public static final int MEIYERDITCH_MINER_3821 = 3821;
    public static final int MEIYERDITCH_MINER_3822 = 3822;
    public static final int MEIYERDITCH_MINER_3823 = 3823;
    public static final int MEIYERDITCH_MINER_3824 = 3824;
    public static final int SHADOWY_FIGURE = 3825;
    public static final int SHADOWY_FIGURE_3826 = 3826;
    public static final int SHADOWY_FIGURE_3827 = 3827;
    public static final int STRAY_DOG_3829 = 3829;
    public static final int STRAY_DOG_3830 = 3830;
    public static final int CAT_3831 = 3831;
    public static final int CAT_3832 = 3832;
    public static final int BOAT = 3833;
    public static final int BOAT_3834 = 3834;
    public static final int ONEIROMANCER = 3835;
    public static final int HOUSE = 3836;
    public static final int BABA_YAGA = 3837;
    public static final int PAULINE_POLARIS = 3838;
    public static final int METEORA = 3839;
    public static final int MELANA_MOONLANDER = 3840;
    public static final int SELENE = 3841;
    public static final int RIMAE_SIRSALIS = 3842;
    public static final int SIRSAL_BANKER = 3843;
    public static final int CLAN_GUARD = 3844;
    public static final int ENCHANTED_BROOM = 3845;
    public static final int ENCHANTED_BROOM_3846 = 3846;
    public static final int ENCHANTED_BROOM_3847 = 3847;
    public static final int ENCHANTED_BUCKET = 3848;
    public static final int ENCHANTED_BUCKET_3849 = 3849;
    public static final int BOUQUET_MAC_HYACINTH = 3850;
    public static final int MOSS_GIANT_3851 = 3851;
    public static final int MOSS_GIANT_3852 = 3852;
    public static final int PARROT_3853 = 3853;
    public static final int LOKAR_SEARUNNER = 3855;
    public static final int CABIN_BOY = 3856;
    public static final int BEEFY_BURNS = 3858;
    public static final int EAGLEEYE_SHULTZ = 3859;
    public static final int FIRST_MATE_DAVEYBOY = 3860;
    public static final int BIRDSEYE_JACK_3861 = 3861;
    public static final int PICARRON_PETE = 3862;
    public static final int JAKE = 3863;
    public static final int BEDREAD_THE_BOLD = 3864;
    public static final int WILSON = 3865;
    public static final int TOMMY_2TIMES = 3866;
    public static final int MURKY_PAT = 3867;
    public static final int JACK_SAILS = 3868;
    public static final int PALMER = 3869;
    public static final int BETTY_BBOPPIN = 3870;
    public static final int BEEDYEYE_JONES = 3871;
    public static final int JENNY_BLADE = 3872;
    public static final int LECHEROUS_LEE = 3873;
    public static final int STICKY_SANDERS = 3874;
    public static final int JEX = 3875;
    public static final int MAISA = 3876;
    public static final int OSMAN_3877 = 3877;
    public static final int OSMAN_3878 = 3878;
    public static final int OSMAN_3879 = 3879;
    public static final int OSMAN_3880 = 3880;
    public static final int SOPHANEM_GUARD = 3881;
    public static final int SOPHANEM_GUARD_3882 = 3882;
    public static final int SOPHANEM_GUARD_3883 = 3883;
    public static final int SOPHANEM_GUARD_3884 = 3884;
    public static final int BANKER_3887 = 3887;
    public static final int BANKER_3888 = 3888;
    public static final int STONEMASON_3889 = 3889;
    public static final int NATHIFA = 3890;
    public static final int URBI = 3891;
    public static final int JAMILA = 3892;
    public static final int DORIC = 3893;
    public static final int SIGMUND_THE_MERCHANT = 3894;
    public static final int PEER_THE_SEER = 3895;
    public static final int THORVALD_THE_WARRIOR = 3896;
    public static final int KOSCHEI_THE_DEATHLESS = 3897;
    public static final int KOSCHEI_THE_DEATHLESS_3898 = 3898;
    public static final int KOSCHEI_THE_DEATHLESS_3899 = 3899;
    public static final int KOSCHEI_THE_DEATHLESS_3900 = 3900;
    public static final int FOX = 3901;
    public static final int BUNNY = 3902;
    public static final int BUNNY_3903 = 3903;
    public static final int GULL_3904 = 3904;
    public static final int GULL_3905 = 3905;
    public static final int GULL_3906 = 3906;
    public static final int GULL_3907 = 3907;
    public static final int BEAR_CUB = 3908;
    public static final int BEAR_CUB_3909 = 3909;
    public static final int UNICORN_FOAL = 3910;
    public static final int BLACK_UNICORN_FOAL = 3911;
    public static final int WOLF_3912 = 3912;
    public static final int FISHING_SPOT_3913 = 3913;
    public static final int FISHING_SPOT_3914 = 3914;
    public static final int FISHING_SPOT_3915 = 3915;
    public static final int BJORN = 3916;
    public static final int ELDGRIM = 3917;
    public static final int PRINCE_BRAND_3918 = 3918;
    public static final int PRINCESS_ASTRID_3919 = 3919;
    public static final int MANNI_THE_REVELLER = 3920;
    public static final int COUNCIL_WORKMAN = 3921;
    public static final int THE_DRAUGEN = 3922;
    public static final int BUTTERFLY_3923 = 3923;
    public static final int SIGLI_THE_HUNTSMAN = 3924;
    public static final int SWENSEN_THE_NAVIGATOR = 3925;
    public static final int GUARD_3928 = 3928;
    public static final int GUARD_3929 = 3929;
    public static final int TOWN_GUARD = 3930;
    public static final int TOWN_GUARD_3931 = 3931;
    public static final int THORA_THE_BARKEEP = 3932;
    public static final int YRSA_3933 = 3933;
    public static final int FISHERMAN = 3934;
    public static final int SKULGRIMEN = 3935;
    public static final int SAILOR_3936 = 3936;
    public static final int AGNAR = 3937;
    public static final int FREIDIR = 3938;
    public static final int BORROKAR = 3939;
    public static final int LANZIG = 3940;
    public static final int PONTAK = 3941;
    public static final int FREYGERD_3942 = 3942;
    public static final int LENSA_3943 = 3943;
    public static final int JENNELLA = 3944;
    public static final int SASSILIK_3945 = 3945;
    public static final int INGA = 3946;
    public static final int FISH_MONGER = 3947;
    public static final int FUR_TRADER_3948 = 3948;
    public static final int MARKET_GUARD_3949 = 3949;
    public static final int WARRIOR_3950 = 3950;
    public static final int LEGENDS_GUARD = 3951;
    public static final int LEGENDS_GUARD_3952 = 3952;
    public static final int RADIMUS_ERKLE = 3953;
    public static final int JUNGLE_FORESTER = 3954;
    public static final int JUNGLE_FORESTER_3955 = 3955;
    public static final int GUJUO = 3956;
    public static final int UNGADULU = 3957;
    public static final int UNGADULU_3958 = 3958;
    public static final int JUNGLE_SAVAGE = 3959;
    public static final int FIONELLA = 3960;
    public static final int SIEGFRIED_ERKLE = 3961;
    public static final int NEZIKCHENED = 3962;
    public static final int VIYELDI = 3963;
    public static final int SAN_TOJALON = 3964;
    public static final int IRVIG_SENAY = 3965;
    public static final int RANALPH_DEVERE = 3966;
    public static final int BOULDER_3967 = 3967;
    public static final int ECHNED_ZEKIN = 3968;
    public static final int ZOMBIE_RAT = 3969;
    public static final int ZOMBIE_RAT_3970 = 3970;
    public static final int ZOMBIE_RAT_3971 = 3971;
    public static final int SKELETON_3972 = 3972;
    public static final int SKELETON_3973 = 3973;
    public static final int SKELETON_3974 = 3974;
    public static final int GHOST_3975 = 3975;
    public static final int GHOST_3976 = 3976;
    public static final int GHOST_3977 = 3977;
    public static final int GHOST_3978 = 3978;
    public static final int GHOST_3979 = 3979;
    public static final int ZOMBIE_3980 = 3980;
    public static final int ZOMBIE_3981 = 3981;
    public static final int LESSER_DEMON_3982 = 3982;
    public static final int DOCTOR_ORBON = 3984;
    public static final int FARMER_BRUMTY = 3985;
    public static final int RED_SHEEP = 3986;
    public static final int GREEN_SHEEP = 3987;
    public static final int BLUE_SHEEP = 3988;
    public static final int YELLOW_SHEEP = 3989;
    public static final int BOY = 3994;
    public static final int NORA_T_HAGG = 3995;
    public static final int WITCHS_EXPERIMENT = 3996;
    public static final int WITCHS_EXPERIMENT_SECOND_FORM = 3997;
    public static final int WITCHS_EXPERIMENT_THIRD_FORM = 3998;
    public static final int WITCHS_EXPERIMENT_FOURTH_FORM = 3999;
    public static final int MOUSE = 4000;
    public static final int CHOMPY_CHICK = 4001;
    public static final int CHOMPY_CHICK_4002 = 4002;
    public static final int SHADOW_4004 = 4004;
    public static final int DARK_BEAST = 4005;
    public static final int THORGEL = 4010;
    public static final int BILL_TEACH = 4011;
    public static final int BILL_TEACH_4012 = 4012;
    public static final int BILL_TEACH_4014 = 4014;
    public static final int BILL_TEACH_4015 = 4015;
    public static final int BILL_TEACH_4016 = 4016;
    public static final int CHARLEY = 4017;
    public static final int SMITH = 4018;
    public static final int JOE_4019 = 4019;
    public static final int MAMA = 4020;
    public static final int MAMA_4021 = 4021;
    public static final int MIKE = 4022;
    public static final int PIRATE_4023 = 4023;
    public static final int PIRATE_4024 = 4024;
    public static final int PIRATE_4025 = 4025;
    public static final int PIRATE_4026 = 4026;
    public static final int PIRATE_4027 = 4027;
    public static final int PIRATE_4028 = 4028;
    public static final int PIRATE_4029 = 4029;
    public static final int PIRATE_4030 = 4030;
    public static final int PIRATE_4031 = 4031;
    public static final int PIRATE_4032 = 4032;
    public static final int PIRATE_4033 = 4033;
    public static final int PIRATE_4034 = 4034;
    public static final int PIRATE_4035 = 4035;
    public static final int PIRATE_4036 = 4036;
    public static final int PIRATE_4037 = 4037;
    public static final int PIRATE_4038 = 4038;
    public static final int PIRATE_4039 = 4039;
    public static final int PIRATE_4040 = 4040;
    public static final int PIRATE_4041 = 4041;
    public static final int PIRATE_4042 = 4042;
    public static final int PIRATE_4043 = 4043;
    public static final int PIRATE_4044 = 4044;
    public static final int PIRATE_4045 = 4045;
    public static final int PIRATE_4046 = 4046;
    public static final int PIRATE_4047 = 4047;
    public static final int PIRATE_4048 = 4048;
    public static final int PIRATE_4049 = 4049;
    public static final int PIRATE_4050 = 4050;
    public static final int PIRATE_4051 = 4051;
    public static final int PIRATE_4052 = 4052;
    public static final int GULL_4053 = 4053;
    public static final int BANKER_4054 = 4054;
    public static final int BANKER_4055 = 4055;
    public static final int GRAIL_MAIDEN = 4056;
    public static final int SIR_PERCIVAL = 4057;
    public static final int KING_PERCIVAL = 4058;
    public static final int MERLIN_4059 = 4059;
    public static final int PEASANT = 4060;
    public static final int PEASANT_4061 = 4061;
    public static final int HIGH_PRIEST = 4062;
    public static final int CRONE = 4063;
    public static final int GALAHAD = 4064;
    public static final int FISHERMAN_4065 = 4065;
    public static final int THE_FISHER_KING = 4066;
    public static final int BLACK_KNIGHT_TITAN = 4067;
    public static final int MONK_4068 = 4068;
    public static final int BONZO = 4069;
    public static final int SINISTER_STRANGER = 4070;
    public static final int SINISTER_STRANGER_4071 = 4071;
    public static final int MORRIS = 4072;
    public static final int BIG_DAVE = 4073;
    public static final int JOSHUA = 4074;
    public static final int GRANDPA_JACK = 4075;
    public static final int FORESTER_4076 = 4076;
    public static final int AUSTRI = 4077;
    public static final int VESTRI = 4078;
    public static final int FISHING_SPOT_4079 = 4079;
    public static final int FISHING_SPOT_4080 = 4080;
    public static final int FISHING_SPOT_4081 = 4081;
    public static final int FISHING_SPOT_4082 = 4082;
    public static final int DENULTH = 4083;
    public static final int SERGEANT = 4084;
    public static final int SERGEANT_4085 = 4085;
    public static final int SOLDIER = 4086;
    public static final int SOLDIER_4087 = 4087;
    public static final int SOLDIER_4088 = 4088;
    public static final int SOLDIER_4089 = 4089;
    public static final int SOLDIER_4090 = 4090;
    public static final int SOLDIER_4091 = 4091;
    public static final int SOLDIER_4092 = 4092;
    public static final int SABA = 4093;
    public static final int TENZING = 4094;
    public static final int EADBURG = 4095;
    public static final int ARCHER_4096 = 4096;
    public static final int ARCHER_4097 = 4097;
    public static final int ARCHER_4098 = 4098;
    public static final int GUARD_4099 = 4099;
    public static final int GUARD_4100 = 4100;
    public static final int HAROLD = 4101;
    public static final int TOSTIG = 4102;
    public static final int EOHRIC = 4103;
    public static final int SERVANT_4104 = 4104;
    public static final int DUNSTAN = 4105;
    public static final int WISTAN = 4106;
    public static final int BREOCA = 4107;
    public static final int OCGA = 4108;
    public static final int PENDA = 4109;
    public static final int HYGD = 4110;
    public static final int CEOLBURG = 4111;
    public static final int HILD_4112 = 4112;
    public static final int WHITE_KNIGHT_4114 = 4114;
    public static final int FAREED_HARD = 4115;
    public static final int BILLY = 4116;
    public static final int MOUNTAIN_GOAT_4117 = 4117;
    public static final int EADGAR = 4118;
    public static final int GODRIC = 4119;
    public static final int TROLL_GENERAL = 4120;
    public static final int TROLL_GENERAL_4121 = 4121;
    public static final int TROLL_GENERAL_4122 = 4122;
    public static final int TROLL_SPECTATOR = 4123;
    public static final int TROLL_SPECTATOR_4124 = 4124;
    public static final int TROLL_SPECTATOR_4125 = 4125;
    public static final int TROLL_SPECTATOR_4126 = 4126;
    public static final int TROLL_SPECTATOR_4127 = 4127;
    public static final int TROLL_SPECTATOR_4128 = 4128;
    public static final int TROLL_SPECTATOR_4129 = 4129;
    public static final int DAD = 4130;
    public static final int TWIG_4131 = 4131;
    public static final int BERRY = 4132;
    public static final int TWIG_4133 = 4133;
    public static final int BERRY_4134 = 4134;
    public static final int THROWER_TROLL_4135 = 4135;
    public static final int THROWER_TROLL_4136 = 4136;
    public static final int THROWER_TROLL_4137 = 4137;
    public static final int THROWER_TROLL_4138 = 4138;
    public static final int THROWER_TROLL_4139 = 4139;
    public static final int COOK_4140 = 4140;
    public static final int COOK_4141 = 4141;
    public static final int COOK_4142 = 4142;
    public static final int MOUNTAIN_TROLL_4143 = 4143;
    public static final int MUSHROOM = 4144;
    public static final int MOUNTAIN_GOAT_4145 = 4145;
    public static final int MOUNTAIN_GOAT_4146 = 4146;
    public static final int MOUNTAIN_GOAT_4147 = 4147;
    public static final int GUARD_4148 = 4148;
    public static final int GUARD_4149 = 4149;
    public static final int GUARD_4150 = 4150;
    public static final int GUARD_4151 = 4151;
    public static final int GUARD_4152 = 4152;
    public static final int GUARD_4153 = 4153;
    public static final int GUARD_4154 = 4154;
    public static final int GUARD_4155 = 4155;
    public static final int GUARD_4156 = 4156;
    public static final int BURNTMEAT = 4157;
    public static final int RAT_BURGISS = 4158;
    public static final int SUROK_MAGIS = 4159;
    public static final int SUROK_MAGIS_4160 = 4160;
    public static final int ZAFF_4161 = 4161;
    public static final int ANNA_JONES = 4162;
    public static final int KING_ROALD_4163 = 4163;
    public static final int MISHKALUN_DORN = 4164;
    public static final int DAKHTHOULAN_AEGIS = 4165;
    public static final int SILAS_DAHCSNU = 4166;
    public static final int OUTLAW = 4167;
    public static final int OUTLAW_4168 = 4168;
    public static final int OUTLAW_4169 = 4169;
    public static final int OUTLAW_4170 = 4170;
    public static final int OUTLAW_4171 = 4171;
    public static final int OUTLAW_4172 = 4172;
    public static final int OUTLAW_4173 = 4173;
    public static final int OUTLAW_4174 = 4174;
    public static final int OUTLAW_4175 = 4175;
    public static final int OUTLAW_4176 = 4176;
    public static final int MONKEY_4177 = 4177;
    public static final int BENCH = 4178;
    public static final int HADLEY = 4179;
    public static final int GERALD = 4180;
    public static final int ALMERA = 4181;
    public static final int HUDON = 4182;
    public static final int GOLRIE_4183 = 4183;
    public static final int CROCODILE = 4184;
    public static final int JACKAL = 4185;
    public static final int ODDSKULL = 4187;
    public static final int SCARAB_SWARM_4192 = 4192;
    public static final int WANDERER = 4193;
    public static final int WANDERER_4194 = 4194;
    public static final int APPARITION = 4195;
    public static final int APPARITION_4196 = 4196;
    public static final int APPARITION_4197 = 4197;
    public static final int APPARITION_4198 = 4198;
    public static final int ICTHLARIN = 4199;
    public static final int EMBALMER = 4202;
    public static final int CARPENTER = 4203;
    public static final int SIAMUN = 4205;
    public static final int HIGH_PRIEST_4206 = 4206;
    public static final int PRIEST_4207 = 4207;
    public static final int PRIEST_4208 = 4208;
    public static final int SPHINX_4209 = 4209;
    public static final int POSSESSED_PRIEST = 4210;
    public static final int DONOVAN_THE_FAMILY_HANDYMAN = 4212;
    public static final int PIERRE = 4213;
    public static final int HOBBES = 4214;
    public static final int LOUISA = 4215;
    public static final int MARY = 4216;
    public static final int STANFORD = 4217;
    public static final int GUARD_4218 = 4218;
    public static final int GOSSIP = 4219;
    public static final int ANNA_4220 = 4220;
    public static final int BOB_4221 = 4221;
    public static final int CAROL = 4222;
    public static final int DAVID_4223 = 4223;
    public static final int ELIZABETH = 4224;
    public static final int FRANK = 4225;
    public static final int SINCLAIR = 4226;
    public static final int POISON_SALESMAN = 4227;
    public static final int SINCLAIR_GUARD_DOG = 4228;
    public static final int LOVE_CATS_4229 = 4229;
    public static final int NEITE_4230 = 4230;
    public static final int BOB_4231 = 4231;
    public static final int BEITE = 4232;
    public static final int GNOME = 4233;
    public static final int GNOME_4234 = 4234;
    public static final int ODYSSEUS_4235 = 4235;
    public static final int NEITE_4236 = 4236;
    public static final int UNFERTH = 4237;
    public static final int UNFERTH_4238 = 4238;
    public static final int UNFERTH_4239 = 4239;
    public static final int UNFERTH_4240 = 4240;
    public static final int UNFERTH_4241 = 4241;
    public static final int RELDO = 4242;
    public static final int RELDO_4243 = 4243;
    public static final int BROTHER_OMAD = 4244;
    public static final int BROTHER_CEDRIC = 4245;
    public static final int MONK_4246 = 4246;
    public static final int THIEF_4247 = 4247;
    public static final int HEAD_THIEF = 4248;
    public static final int ALRENA = 4249;
    public static final int ALRENA_4250 = 4250;
    public static final int ALRENA_4251 = 4251;
    public static final int BRAVEK = 4252;
    public static final int BRAVEK_4253 = 4253;
    public static final int CLERK = 4255;
    public static final int EDMOND_4256 = 4256;
    public static final int ELENA_4257 = 4257;
    public static final int TED_REHNISON = 4263;
    public static final int MARTHA_REHNISON = 4264;
    public static final int BILLY_REHNISON = 4265;
    public static final int MILLI_REHNISON = 4266;
    public static final int MAN_4268 = 4268;
    public static final int MAN_4269 = 4269;
    public static final int MAN_4270 = 4270;
    public static final int MAN_4271 = 4271;
    public static final int MAN_4272 = 4272;
    public static final int LEELA = 4274;
    public static final int JAIL_GUARD = 4276;
    public static final int JAIL_GUARD_4277 = 4277;
    public static final int JAIL_GUARD_4278 = 4278;
    public static final int JAIL_GUARD_4279 = 4279;
    public static final int NED = 4280;
    public static final int AGGIE = 4284;
    public static final int CHANCELLOR_HASSAN = 4285;
    public static final int OSMAN_4286 = 4286;
    public static final int BORDER_GUARD = 4287;
    public static final int BORDER_GUARD_4288 = 4288;
    public static final int GULL_4289 = 4289;
    public static final int GULL_4290 = 4290;
    public static final int HERMAN_CARANOS = 4291;
    public static final int FRANKLIN_CARANOS = 4292;
    public static final int ARNOLD_LYDSPOR = 4293;
    public static final int DEVIN_MENDELBERG = 4294;
    public static final int GEORGE_LAXMEISTER = 4295;
    public static final int RAMARA_DU_CROISSANT = 4296;
    public static final int KATHY_CORKAT = 4298;
    public static final int KATHY_CORKAT_4299 = 4299;
    public static final int ELEMENTAL_BALANCE_4301 = 4301;
    public static final int ELEMENTAL_BALANCE_4302 = 4302;
    public static final int KALPHITE_QUEEN_4303 = 4303;
    public static final int KALPHITE_QUEEN_4304 = 4304;
    public static final int DRUNKEN_DWARF_4305 = 4305;
    public static final int WISE_OLD_MAN_4306 = 4306;
    public static final int WISE_OLD_MAN_4307 = 4307;
    public static final int SEA_TROLL = 4308;
    public static final int SEA_TROLL_4309 = 4309;
    public static final int SEA_TROLL_4310 = 4310;
    public static final int SEA_TROLL_4311 = 4311;
    public static final int SKELETON_MAGE_4312 = 4312;
    public static final int SEA_TROLL_4313 = 4313;
    public static final int SEA_TROLL_GENERAL = 4314;
    public static final int SEA_TROLL_QUEEN = 4315;
    public static final int FISHING_SPOT_4316 = 4316;
    public static final int CALEB = 4317;
    public static final int SKELETON_MAGE_4318 = 4318;
    public static final int SKELETON_MAGE_4319 = 4319;
    public static final int MORGAN_LE_FAYE_4320 = 4320;
    public static final int RENEGADE_KNIGHT_4321 = 4321;
    public static final int ZANIK_4322 = 4322;
    public static final int ZANIK_4323 = 4323;
    public static final int ZANIK_4324 = 4324;
    public static final int LIGHT_CREATURE = 4325;
    public static final int ZANIK_4326 = 4326;
    public static final int HAM_MEMBER_4327 = 4327;
    public static final int SIGMUND_4328 = 4328;
    public static final int HAM_DEACON_4329 = 4329;
    public static final int JOHANHUS_ULSBRECHT_4330 = 4330;
    public static final int BLACK_KNIGHT_4331 = 4331;
    public static final int GUARD_4332 = 4332;
    public static final int COURT_JUDGE = 4333;
    public static final int JURY = 4334;
    public static final int GUARD_4335 = 4335;
    public static final int GUARD_4336 = 4336;
    public static final int PROSECUTOR = 4337;
    public static final int ARTHUR = 4340;
    public static final int SIR_LUCAN_4342 = 4342;
    public static final int SIR_PALOMEDES_4343 = 4343;
    public static final int SIR_LANCELOT_4344 = 4344;
    public static final int SIR_BEDIVERE_4345 = 4345;
    public static final int SIR_TRISTRAM_4346 = 4346;
    public static final int SIR_PELLEAS_4347 = 4347;
    public static final int SIR_GAWAIN_4348 = 4348;
    public static final int SIR_KAY_4349 = 4349;
    public static final int SIR_PELLEAS_4350 = 4350;
    public static final int SIR_GAWAIN_4351 = 4351;
    public static final int SIR_KAY_4352 = 4352;
    public static final int SQUIRE_4353 = 4353;
    public static final int SIR_LANCELOT_4354 = 4354;
    public static final int SIR_KAY_4355 = 4355;
    public static final int SIR_GAWAIN_4356 = 4356;
    public static final int SIR_LUCAN_4357 = 4357;
    public static final int SIR_PALOMEDES_4358 = 4358;
    public static final int SIR_TRISTRAM_4359 = 4359;
    public static final int SIR_PELLEAS_4360 = 4360;
    public static final int SIR_BEDIVERE_4361 = 4361;
    public static final int OGRE_CHIEFTAIN = 4362;
    public static final int OGRE_CHIEFTAIN_4363 = 4363;
    public static final int OG = 4364;
    public static final int GREW = 4365;
    public static final int TOBAN = 4366;
    public static final int GORAD = 4367;
    public static final int OGRE_GUARD_4368 = 4368;
    public static final int OGRE_GUARD_4369 = 4369;
    public static final int OGRE_GUARD_4370 = 4370;
    public static final int OGRE_GUARD_4371 = 4371;
    public static final int OGRE_GUARD_4372 = 4372;
    public static final int CITY_GUARD = 4373;
    public static final int SCARED_SKAVID = 4374;
    public static final int MAD_SKAVID = 4375;
    public static final int SKAVID_4376 = 4376;
    public static final int SKAVID_4377 = 4377;
    public static final int SKAVID_4378 = 4378;
    public static final int SKAVID_4379 = 4379;
    public static final int SKAVID_4380 = 4380;
    public static final int ENCLAVE_GUARD = 4381;
    public static final int OGRE_SHAMAN_4382 = 4382;
    public static final int OGRE_SHAMAN_4383 = 4383;
    public static final int OGRE_SHAMAN_4384 = 4384;
    public static final int BLUE_DRAGON_4385 = 4385;
    public static final int OGRE_SHAMAN_4386 = 4386;
    public static final int OGRE_SHAMAN_4387 = 4387;
    public static final int OGRE_SHAMAN_4388 = 4388;
    public static final int OGRE_SHAMAN_4389 = 4389;
    public static final int OGRE_SHAMAN_4390 = 4390;
    public static final int OGRE_SHAMAN_4391 = 4391;
    public static final int OGRE_SHAMAN_4392 = 4392;
    public static final int OGRE_SHAMAN_4393 = 4393;
    public static final int OGRE_SHAMAN_4394 = 4394;
    public static final int OGRE_SHAMAN_4395 = 4395;
    public static final int OGRE_SHAMAN_4396 = 4396;
    public static final int WATCHTOWER_WIZARD = 4397;
    public static final int WIZARD_4398 = 4398;
    public static final int WIZARD_4399 = 4399;
    public static final int WIZARD_4400 = 4400;
    public static final int OGRE_TRADER = 4401;
    public static final int OGRE_MERCHANT = 4402;
    public static final int OGRE_TRADER_4403 = 4403;
    public static final int OGRE_TRADER_4404 = 4404;
    public static final int TOWER_GUARD = 4405;
    public static final int COLONEL_RADICK = 4406;
    public static final int AVA = 4407;
    public static final int WITCH_4409 = 4409;
    public static final int ALICES_HUSBAND = 4411;
    public static final int ALICES_HUSBAND_4412 = 4412;
    public static final int ALICES_HUSBAND_4413 = 4413;
    public static final int ALICES_HUSBAND_4414 = 4414;
    public static final int TREE_4416 = 4416;
    public static final int UNDEAD_TREE = 4417;
    public static final int _SNEAKY_UNDEAD_FOWL = 4419;
    public static final int COW31337KILLER = 4420;
    public static final int UNDEAD_COW_4421 = 4421;
    public static final int ALICE_4422 = 4422;
    public static final int JOSSIK = 4423;
    public static final int JOSSIK_4424 = 4424;
    public static final int LARRISSA = 4425;
    public static final int LARRISSA_4426 = 4426;
    public static final int VAMPYRE_JUVINATE_4427 = 4427;
    public static final int VAMPYRE_JUVINATE_4428 = 4428;
    public static final int VAMPYRE_JUVINATE_4429 = 4429;
    public static final int VAMPYRE_JUVINATE_4430 = 4430;
    public static final int FERAL_VAMPYRE_4431 = 4431;
    public static final int VAMPYRE_JUVINATE_4432 = 4432;
    public static final int VAMPYRE_JUVINATE_4433 = 4433;
    public static final int VAMPYRE_JUVINATE_4434 = 4434;
    public static final int MIST_4435 = 4435;
    public static final int VAMPYRE_JUVENILE_4436 = 4436;
    public static final int VAMPYRE_JUVENILE_4437 = 4437;
    public static final int VAMPYRE_JUVENILE_4438 = 4438;
    public static final int VAMPYRE_JUVENILE_4439 = 4439;
    public static final int IVAN_STROM = 4440;
    public static final int IVAN_STROM_4441 = 4441;
    public static final int VAMPYRE_JUVINATE_4442 = 4442;
    public static final int VAMPYRE_JUVINATE_4443 = 4443;
    public static final int ELISABETA = 4444;
    public static final int AUREL = 4445;
    public static final int SORIN = 4446;
    public static final int LUSCION = 4447;
    public static final int SERGIU = 4448;
    public static final int RADU = 4449;
    public static final int GRIGORE = 4450;
    public static final int ILEANA = 4451;
    public static final int VALERIA = 4452;
    public static final int EMILIA = 4453;
    public static final int FLORIN = 4454;
    public static final int CATALINA = 4455;
    public static final int IVAN = 4456;
    public static final int VICTOR = 4457;
    public static final int HELENA = 4458;
    public static final int TEODOR = 4459;
    public static final int MARIUS = 4460;
    public static final int GABRIELA = 4461;
    public static final int VLADIMIR = 4462;
    public static final int CALIN = 4463;
    public static final int MIHAIL = 4464;
    public static final int NICOLETA = 4465;
    public static final int SIMONA = 4466;
    public static final int VASILE = 4467;
    public static final int RAZVAN = 4468;
    public static final int LUMINATA = 4469;
    public static final int CORNELIUS = 4470;
    public static final int CORNELIUS_4471 = 4471;
    public static final int BENJAMIN_4472 = 4472;
    public static final int LIAM_4473 = 4473;
    public static final int MIALA_4474 = 4474;
    public static final int VERAK_4475 = 4475;
    public static final int FISHING_SPOT_4476 = 4476;
    public static final int FISHING_SPOT_4477 = 4477;
    public static final int SORIN_4478 = 4478;
    public static final int WISKIT = 4480;
    public static final int VAMPYRE_JUVINATE_4481 = 4481;
    public static final int VAMPYRE_JUVINATE_4482 = 4482;
    public static final int GADDERANKS = 4483;
    public static final int GADDERANKS_4484 = 4484;
    public static final int GADDERANKS_4485 = 4485;
    public static final int VAMPYRE_JUVINATE_4486 = 4486;
    public static final int VAMPYRE_JUVINATE_4487 = 4487;
    public static final int OLAF_HRADSON = 4488;
    public static final int VOLF_OLAFSON = 4489;
    public static final int INGRID_HRADSON = 4490;
    public static final int SKELETON_FREMENNIK = 4491;
    public static final int SKELETON_FREMENNIK_4492 = 4492;
    public static final int SKELETON_FREMENNIK_4493 = 4493;
    public static final int SKELETON_FREMENNIK_4494 = 4494;
    public static final int SKELETON_FREMENNIK_4495 = 4495;
    public static final int SKELETON_FREMENNIK_4496 = 4496;
    public static final int SKELETON_FREMENNIK_4497 = 4497;
    public static final int SKELETON_FREMENNIK_4498 = 4498;
    public static final int SKELETON_FREMENNIK_4499 = 4499;
    public static final int ULFRIC = 4500;
    public static final int BRINE_RAT = 4501;
    public static final int BOULDER_4502 = 4502;
    public static final int BOULDER_4503 = 4503;
    public static final int GIANT_BAT_4504 = 4504;
    public static final int ULFRIC_4505 = 4505;
    public static final int ZANIK_4506 = 4506;
    public static final int ZANIK_4508 = 4508;
    public static final int ZANIK_4509 = 4509;
    public static final int ZANIK_4510 = 4510;
    public static final int ZANIK_4511 = 4511;
    public static final int DWARF_4512 = 4512;
    public static final int HAM_MEMBER_4513 = 4513;
    public static final int HAM_MEMBER_4514 = 4514;
    public static final int GUARD_4516 = 4516;
    public static final int GUARD_4517 = 4517;
    public static final int GUARD_4518 = 4518;
    public static final int GUARD_4519 = 4519;
    public static final int GUARD_4520 = 4520;
    public static final int GUARD_4521 = 4521;
    public static final int GUARD_4522 = 4522;
    public static final int GUARD_4523 = 4523;
    public static final int GUARD_4524 = 4524;
    public static final int GUARD_4525 = 4525;
    public static final int GUARD_4526 = 4526;
    public static final int CHADWELL = 4527;
    public static final int BLESSED_SPIDER = 4533;
    public static final int BLESSED_GIANT_RAT = 4534;
    public static final int BLESSED_GIANT_RAT_4535 = 4535;
    public static final int BOULDER_4543 = 4543;
    public static final int NILOOF = 4551;
    public static final int KLANK = 4552;
    public static final int KAMEN = 4553;
    public static final int SPIDER_4561 = 4561;
    public static final int GIANT_BAT_4562 = 4562;
    public static final int MONK_4563 = 4563;
    public static final int DEAD_MONK = 4564;
    public static final int HIGH_PRIEST_4565 = 4565;
    public static final int MONK_4566 = 4566;
    public static final int MONK_4567 = 4567;
    public static final int ASSASSIN = 4568;
    public static final int RED_AXE_HENCHMAN_4569 = 4569;
    public static final int RED_AXE_HENCHMAN_4570 = 4570;
    public static final int OGRE_SHAMAN_4571 = 4571;
    public static final int THE_BEAST = 4572;
    public static final int BELLEMORDE = 4573;
    public static final int POX = 4574;
    public static final int POX_4575 = 4575;
    public static final int BONES_4576 = 4576;
    public static final int CERIL_CARNILLEAN_4577 = 4577;
    public static final int COUNCILLOR_HALGRIVE = 4578;
    public static final int SPICE_SELLER_4579 = 4579;
    public static final int FUR_TRADER_4580 = 4580;
    public static final int GEM_MERCHANT_4581 = 4581;
    public static final int SILVER_MERCHANT = 4582;
    public static final int SILK_MERCHANT_4583 = 4583;
    public static final int ZENESHA = 4584;
    public static final int ALI_MORRISANE_4585 = 4585;
    public static final int GRIMESQUIT = 4586;
    public static final int PHINGSPET = 4587;
    public static final int HOOKNOSED_JACK = 4588;
    public static final int JIMMY_DAZZLER = 4589;
    public static final int THE_FACE = 4590;
    public static final int FELKRASH = 4591;
    public static final int SMOKIN_JOE = 4592;
    public static final int RAT_4593 = 4593;
    public static final int RAT_4594 = 4594;
    public static final int KING_RAT = 4595;
    public static final int TURBOGROOMER = 4596;
    public static final int PUSSKINS = 4597;
    public static final int LOKI = 4598;
    public static final int CAPTAIN_TOM = 4599;
    public static final int TREACLE = 4600;
    public static final int MITTENS = 4601;
    public static final int CLAUDE = 4602;
    public static final int TOPSY = 4603;
    public static final int RAUBORN = 4604;
    public static final int VAERINGK = 4605;
    public static final int OXI = 4606;
    public static final int FIOR = 4607;
    public static final int SAGIRA = 4608;
    public static final int ANLEIF = 4609;
    public static final int RAT_4610 = 4610;
    public static final int RAT_4611 = 4611;
    public static final int RAT_4612 = 4612;
    public static final int RAT_4613 = 4613;
    public static final int RAT_4614 = 4614;
    public static final int RAT_4615 = 4615;
    public static final int RAT_4616 = 4616;
    public static final int RAT_4617 = 4617;
    public static final int RAT_4618 = 4618;
    public static final int HETTY = 4619;
    public static final int ELEMENTAL_BALANCE_4620 = 4620;
    public static final int ELEMENTAL_BALANCE_4621 = 4621;
    public static final int ELEMENTAL_BALANCE_4622 = 4622;
    public static final int ELEMENTAL_BALANCE_4623 = 4623;
    public static final int ELEMENTAL_BALANCE_4624 = 4624;
    public static final int TRUFITUS = 4625;
    public static final int COOK_4626 = 4626;
    public static final int MILLIE_MILLER = 4627;
    public static final int GILLIE_GROATS = 4628;
    public static final int ANA = 4629;
    public static final int ANA_4630 = 4630;
    public static final int FEMALE_SLAVE = 4631;
    public static final int MALE_SLAVE = 4632;
    public static final int ESCAPING_SLAVE = 4633;
    public static final int ROWDY_SLAVE = 4634;
    public static final int MERCENARY_CAPTAIN = 4635;
    public static final int CAPTAIN_SIAD = 4636;
    public static final int AL_SHABIM = 4637;
    public static final int BEDABIN_NOMAD = 4638;
    public static final int BEDABIN_NOMAD_GUARD = 4639;
    public static final int IRENA = 4640;
    public static final int IRENA_4641 = 4641;
    public static final int SHANTAY = 4642;
    public static final int SHANTAY_GUARD = 4643;
    public static final int OAKNOCK_THE_ENGINEER = 4644;
    public static final int GLOUPHRIE_THE_UNTRUSTED = 4645;
    public static final int KING_HEALTHORG = 4646;
    public static final int HAZELMERE_4647 = 4647;
    public static final int SHANTAY_GUARD_4648 = 4648;
    public static final int DESERT_WOLF = 4649;
    public static final int DESERT_WOLF_4650 = 4650;
    public static final int DESERT_WOLF_4651 = 4651;
    public static final int UGTHANKI = 4652;
    public static final int MINE_CART_DRIVER = 4653;
    public static final int ROWDY_GUARD = 4654;
    public static final int BEDABIN_NOMAD_FIGHTER = 4655;
    public static final int MERCENARY = 4656;
    public static final int MERCENARY_4657 = 4657;
    public static final int MERCENARY_4658 = 4658;
    public static final int MERCENARY_4659 = 4659;
    public static final int GUARD_4660 = 4660;
    public static final int GUARD_4661 = 4661;
    public static final int GUARD_4662 = 4662;
    public static final int GUARD_4663 = 4663;
    public static final int GUARD_4664 = 4664;
    public static final int GUARD_4665 = 4665;
    public static final int GUARD_4666 = 4666;
    public static final int GUARD_4667 = 4667;
    public static final int GUARD_4668 = 4668;
    public static final int GUARD_4669 = 4669;
    public static final int MALE_SLAVE_4670 = 4670;
    public static final int MALE_SLAVE_4671 = 4671;
    public static final int FEMALE_SLAVE_4672 = 4672;
    public static final int FEMALE_SLAVE_4673 = 4673;
    public static final int CART_CAMEL = 4674;
    public static final int MINE_CART_4675 = 4675;
    public static final int MINE_CART_4676 = 4676;
    public static final int ANA_4677 = 4677;
    public static final int MERCENARY_4678 = 4678;
    public static final int SIR_SPISHYUS = 4679;
    public static final int LADY_TABLE = 4680;
    public static final int SIR_KUAM_FERENTSE = 4681;
    public static final int SIR_LEYE = 4682;
    public static final int SIR_TINLEY = 4683;
    public static final int SIR_REN_ITCHOOD = 4684;
    public static final int MISS_CHEEVERS = 4685;
    public static final int MS_HYNN_TERPRETT = 4686;
    public static final int SIR_TIFFY_CASHIEN = 4687;
    public static final int ANGRY_UNICORN_4688 = 4688;
    public static final int ANGRY_GIANT_RAT_4689 = 4689;
    public static final int ANGRY_GIANT_RAT_4690 = 4690;
    public static final int ANGRY_GOBLIN_4691 = 4691;
    public static final int ANGRY_BEAR_4692 = 4692;
    public static final int FEAR_REAPER_4693 = 4693;
    public static final int CONFUSION_BEAST_4694 = 4694;
    public static final int HOPELESS_CREATURE_4695 = 4695;
    public static final int HOPELESS_BEAST = 4696;
    public static final int HOPELESS_BEAST_4697 = 4697;
    public static final int TIMFRAKU = 4698;
    public static final int TIADECHE = 4699;
    public static final int TIADECHE_4700 = 4700;
    public static final int TINSAY = 4701;
    public static final int TINSAY_4702 = 4702;
    public static final int TAMAYU = 4703;
    public static final int TAMAYU_4704 = 4704;
    public static final int TAMAYU_4705 = 4705;
    public static final int TAMAYU_4706 = 4706;
    public static final int LUBUFU = 4707;
    public static final int THE_SHAIKAHAN = 4708;
    public static final int THE_SHAIKAHAN_4709 = 4709;
    public static final int FISHING_SPOT_4710 = 4710;
    public static final int FISHING_SPOT_4711 = 4711;
    public static final int FISHING_SPOT_4712 = 4712;
    public static final int FISHING_SPOT_4713 = 4713;
    public static final int FISHING_SPOT_4714 = 4714;
    public static final int AUGUSTE = 4715;
    public static final int AUGUSTE_4716 = 4716;
    public static final int AUGUSTE_4717 = 4717;
    public static final int AUGUSTE_4718 = 4718;
    public static final int ASSISTANT_SERF = 4719;
    public static final int ASSISTANT_BROCK = 4720;
    public static final int ASSISTANT_MARROW = 4721;
    public static final int ASSISTANT_LE_SMITH = 4722;
    public static final int ASSISTANT_STAN = 4723;
    public static final int BOB_4724 = 4724;
    public static final int CURLY = 4725;
    public static final int MOE = 4726;
    public static final int LARRY_4727 = 4727;
    public static final int THURGO = 4733;
    public static final int GULL_4734 = 4734;
    public static final int GULL_4735 = 4735;
    public static final int SIR_VYVIN = 4736;
    public static final int SQUIRE_4737 = 4737;
    public static final int GENIE_4738 = 4738;
    public static final int NIRRIE = 4739;
    public static final int TIRRIE = 4740;
    public static final int HALLAK = 4741;
    public static final int BLACK_GOLEM = 4742;
    public static final int WHITE_GOLEM = 4743;
    public static final int GREY_GOLEM = 4744;
    public static final int GHASLOR_THE_ELDER = 4745;
    public static final int ALI_THE_CARTER = 4746;
    public static final int USI = 4747;
    public static final int NKUKU = 4748;
    public static final int GARAI = 4749;
    public static final int HABIBAH = 4750;
    public static final int MESKHENET = 4751;
    public static final int ZAHRA = 4752;
    public static final int ZAHUR = 4753;
    public static final int SEDDU = 4754;
    public static final int KAZEMDE = 4755;
    public static final int AWUSAH_THE_MAYOR = 4756;
    public static final int TARIK_4757 = 4757;
    public static final int POLTENIP = 4758;
    public static final int RADAT = 4759;
    public static final int SHIRATTI_THE_CUSTODIAN = 4760;
    public static final int ROKUH = 4761;
    public static final int NARDAH_BANKER = 4762;
    public static final int TARGET = 4763;
    public static final int TARGET_4764 = 4764;
    public static final int TEGID = 4766;
    public static final int THISTLE = 4767;
    public static final int PARROTS = 4768;
    public static final int PARROTY_PETE = 4769;
    public static final int FAKE_MAN = 4770;
    public static final int SIR_AMIK_VARZE_4771 = 4771;
    public static final int FORTRESS_GUARD = 4772;
    public static final int FORTRESS_GUARD_4773 = 4773;
    public static final int FORTRESS_GUARD_4774 = 4774;
    public static final int FORTRESS_GUARD_4775 = 4775;
    public static final int FORTRESS_GUARD_4776 = 4776;
    public static final int BLACK_KNIGHT_CAPTAIN = 4777;
    public static final int WITCH_4778 = 4778;
    public static final int GRELDO = 4779;
    public static final int BLACK_CAT = 4780;
    public static final int COL_ONIALL = 4781;
    public static final int COL_ONIALL_4782 = 4782;
    public static final int MAYOR_HOBB = 4783;
    public static final int MAYOR_HOBB_4784 = 4784;
    public static final int BROTHER_MALEDICT = 4787;
    public static final int BROTHER_MALEDICT_4788 = 4788;
    public static final int EZEKIAL_LOVECRAFT = 4789;
    public static final int WITCHAVEN_VILLAGER = 4790;
    public static final int WITCHAVEN_VILLAGER_4791 = 4791;
    public static final int WITCHAVEN_VILLAGER_4792 = 4792;
    public static final int WITCHAVEN_VILLAGER_4793 = 4793;
    public static final int WITCHAVEN_VILLAGER_4794 = 4794;
    public static final int WITCHAVEN_VILLAGER_4795 = 4795;
    public static final int MOTHER_MALLUM = 4796;
    public static final int SLUG_PRINCE = 4797;
    public static final int SLUG_PRINCE_4798 = 4798;
    public static final int GIANT_LOBSTER_4799 = 4799;
    public static final int GIANT_LOBSTER_4800 = 4800;
    public static final int SEA_SLUG = 4801;
    public static final int JEB = 4802;
    public static final int JEB_4803 = 4803;
    public static final int SIR_TINLEY_4804 = 4804;
    public static final int HOBGOBLIN_4805 = 4805;
    public static final int EVIL_DAVE_4806 = 4806;
    public static final int EVIL_DAVE_4807 = 4807;
    public static final int DORIS = 4808;
    public static final int HELLRAT = 4809;
    public static final int AN_OLD_DWARF = 4810;
    public static final int ROHAK = 4811;
    public static final int ROHAK_4812 = 4812;
    public static final int ICEFIEND_4813 = 4813;
    public static final int PIRATE_PETE_4814 = 4814;
    public static final int PIRATE_PETE_4816 = 4816;
    public static final int MOGRE_GUARD = 4817;
    public static final int NUNG = 4818;
    public static final int CRAB_4819 = 4819;
    public static final int MUDSKIPPER = 4820;
    public static final int MUDSKIPPER_4821 = 4821;
    public static final int CRAB_4822 = 4822;
    public static final int FISH = 4823;
    public static final int FISH_4824 = 4824;
    public static final int FISH_4825 = 4825;
    public static final int FISH_4826 = 4826;
    public static final int FISH_4827 = 4827;
    public static final int FISH_4828 = 4828;
    public static final int FISH_4829 = 4829;
    public static final int FISH_4830 = 4830;
    public static final int FISH_4831 = 4831;
    public static final int FISH_4832 = 4832;
    public static final int FISH_4833 = 4833;
    public static final int FISH_4834 = 4834;
    public static final int FISH_4835 = 4835;
    public static final int FISH_4836 = 4836;
    public static final int FISH_4837 = 4837;
    public static final int FISH_4838 = 4838;
    public static final int FISH_4839 = 4839;
    public static final int FISH_4840 = 4840;
    public static final int FISH_4841 = 4841;
    public static final int FISH_4842 = 4842;
    public static final int FISH_4843 = 4843;
    public static final int FISH_4844 = 4844;
    public static final int FISH_4845 = 4845;
    public static final int FISH_4846 = 4846;
    public static final int GYPSY = 4847;
    public static final int GYPSY_4848 = 4848;
    public static final int CULINAROMANCER_4849 = 4849;
    public static final int GOBLIN_COOK = 4850;
    public static final int GOBLIN_COOK_4851 = 4851;
    public static final int GOBLIN_COOK_4852 = 4852;
    public static final int SKRACH_UGLOGWEE_4853 = 4853;
    public static final int SKRACH_UGLOGWEE_4854 = 4854;
    public static final int RANTZ_4855 = 4855;
    public static final int RANTZ_4856 = 4856;
    public static final int RANTZ_4857 = 4857;
    public static final int OGRE_BOAT = 4858;
    public static final int OGRE_BOAT_4859 = 4859;
    public static final int BALLOON_TOAD = 4860;
    public static final int BALLOON_TOAD_4861 = 4861;
    public static final int BALLOON_TOAD_4862 = 4862;
    public static final int JUBBLY_BIRD = 4863;
    public static final int JUBBLY_BIRD_4864 = 4864;
    public static final int ELEMENTAL_BALANCE_4865 = 4865;
    public static final int ELEMENTAL_BALANCE_4866 = 4866;
    public static final int ELEMENTAL_BALANCE_4867 = 4867;
    public static final int ELEMENTAL_BALANCE_4868 = 4868;
    public static final int ELEMENTAL_BALANCE_4869 = 4869;
    public static final int ELEMENTAL_BALANCE_4870 = 4870;
    public static final int ELEMENTAL_BALANCE_4871 = 4871;
    public static final int CULINAROMANCER_4872 = 4872;
    public static final int CULINAROMANCER_4873 = 4873;
    public static final int CULINAROMANCER_4874 = 4874;
    public static final int CULINAROMANCER_4875 = 4875;
    public static final int CULINAROMANCER_4876 = 4876;
    public static final int CULINAROMANCER_4877 = 4877;
    public static final int CULINAROMANCER_4878 = 4878;
    public static final int CULINAROMANCER_4879 = 4879;
    public static final int AGRITHNANA = 4880;
    public static final int FLAMBEED = 4881;
    public static final int KARAMEL = 4882;
    public static final int DESSOURT = 4883;
    public static final int GELATINNOTH_MOTHER = 4884;
    public static final int GELATINNOTH_MOTHER_4885 = 4885;
    public static final int GELATINNOTH_MOTHER_4886 = 4886;
    public static final int GELATINNOTH_MOTHER_4887 = 4887;
    public static final int GELATINNOTH_MOTHER_4888 = 4888;
    public static final int GELATINNOTH_MOTHER_4889 = 4889;
    public static final int DONDAKAN_THE_DWARF = 4890;
    public static final int DONDAKAN_THE_DWARF_4891 = 4891;
    public static final int DONDAKAN_THE_DWARF_4892 = 4892;
    public static final int DWARVEN_ENGINEER = 4893;
    public static final int ROLAD = 4894;
    public static final int KHORVAK_A_DWARVEN_ENGINEER = 4895;
    public static final int DWARVEN_FERRYMAN = 4896;
    public static final int DWARVEN_FERRYMAN_4897 = 4897;
    public static final int DWARVEN_BOATMAN_4898 = 4898;
    public static final int MIODVETNIR = 4899;
    public static final int DERNU = 4900;
    public static final int DERNI = 4901;
    public static final int GOBLIN_4902 = 4902;
    public static final int GOBLIN_4903 = 4903;
    public static final int GOBLIN_4904 = 4904;
    public static final int GOBLIN_4905 = 4905;
    public static final int GOBLIN_4906 = 4906;
    public static final int GNOME_SOLDIER = 4907;
    public static final int GNOME_SOLDIER_4908 = 4908;
    public static final int GNOME_SOLDIER_4909 = 4909;
    public static final int GNOME_SOLDIER_4910 = 4910;
    public static final int GNOME_SOLDIER_4911 = 4911;
    public static final int HEALTHORG_AND_TORTOISE = 4912;
    public static final int BRIMSTAIL = 4914;
    public static final int GARV = 4915;
    public static final int GRUBOR = 4916;
    public static final int TROBERT = 4917;
    public static final int SETH = 4918;
    public static final int GRIP = 4919;
    public static final int ALFONSE_THE_WAITER = 4920;
    public static final int CHARLIE_THE_COOK = 4921;
    public static final int ICE_QUEEN = 4922;
    public static final int ACHIETTIES = 4923;
    public static final int HELEMOS = 4924;
    public static final int VELRAK_THE_EXPLORER = 4925;
    public static final int PIRATE_GUARD = 4926;
    public static final int ENTRANA_FIREBIRD = 4927;
    public static final int FISHING_SPOT_4928 = 4928;
    public static final int LORD_DAQUARIUS = 4929;
    public static final int SOLUS_DELLAGAR = 4930;
    public static final int SAVANT = 4931;
    public static final int LORD_DAQUARIUS_4932 = 4932;
    public static final int SOLUS_DELLAGAR_4933 = 4933;
    public static final int BLACK_KNIGHT_4934 = 4934;
    public static final int LORD_DAQUARIUS_4935 = 4935;
    public static final int MAGE_OF_ZAMORAK_4936 = 4936;
    public static final int MAGE_OF_ZAMORAK_4937 = 4937;
    public static final int MAGE_OF_ZAMORAK_4938 = 4938;
    public static final int WOMAN_4958 = 4958;
    public static final int BLACK_KNIGHT_4959 = 4959;
    public static final int BLACK_KNIGHT_4960 = 4960;
    public static final int RANGER = 4961;
    public static final int SOLUS_DELLAGAR_4962 = 4962;
    public static final int KING_BOLREN = 4963;
    public static final int COMMANDER_MONTAI = 4964;
    public static final int BOLKOY = 4965;
    public static final int REMSAI = 4966;
    public static final int ELKOY = 4967;
    public static final int ELKOY_4968 = 4968;
    public static final int KHAZARD_TROOPER = 4969;
    public static final int KHAZARD_TROOPER_4970 = 4970;
    public static final int KHAZARD_COMMANDER = 4972;
    public static final int GNOME_TROOP = 4973;
    public static final int GNOME_TROOP_4974 = 4974;
    public static final int TRACKER_GNOME_1 = 4975;
    public static final int TRACKER_GNOME_2 = 4976;
    public static final int TRACKER_GNOME_3 = 4977;
    public static final int LOCAL_GNOME = 4978;
    public static final int LOCAL_GNOME_4979 = 4979;
    public static final int KALRON = 4980;
    public static final int SPIRIT_TREE = 4981;
    public static final int SPIRIT_TREE_4982 = 4982;
    public static final int DIMINTHEIS = 4984;
    public static final int BOOT = 4985;
    public static final int CHRONOZON = 4987;
    public static final int ELEMENTAL_BALANCE_4989 = 4989;
    public static final int ELEMENTAL_BALANCE_4990 = 4990;
    public static final int ELEMENTAL_BALANCE_4991 = 4991;
    public static final int ELEMENTAL_BALANCE_4992 = 4992;
    public static final int ELEMENTAL_BALANCE_4993 = 4993;
    public static final int ELEMENTAL_BALANCE_4994 = 4994;
    public static final int ELEMENTAL_BALANCE_4995 = 4995;
    public static final int ELEMENTAL_BALANCE_4996 = 4996;
    public static final int ELEMENTAL_BALANCE_4997 = 4997;
    public static final int ELEMENTAL_BALANCE_4998 = 4998;
    public static final int ELEMENTAL_BALANCE_4999 = 4999;
    public static final int ELEMENTAL_BALANCE_5000 = 5000;
    public static final int ELEMENTAL_BALANCE_5001 = 5001;
    public static final int ELEMENTAL_BALANCE_5002 = 5002;
    public static final int ELEMENTAL_BALANCE_5003 = 5003;
    public static final int ELEMENTAL_BALANCE_5004 = 5004;
    public static final int WIZARD_GRAYZAG = 5006;
    public static final int IMP_5007 = 5007;
    public static final int LIL_DESTRUCTOR_5008 = 5008;
    public static final int ELEMENTAL_BALANCE_5009 = 5009;
    public static final int ELEMENTAL_BALANCE_5010 = 5010;
    public static final int ELEMENTAL_BALANCE_5011 = 5011;
    public static final int ELEMENTAL_BALANCE_5012 = 5012;
    public static final int ELEMENTAL_BALANCE_5013 = 5013;
    public static final int ELEMENTAL_BALANCE_5014 = 5014;
    public static final int ELEMENTAL_BALANCE_5015 = 5015;
    public static final int ELEMENTAL_BALANCE_5016 = 5016;
    public static final int ELEMENTAL_BALANCE_5017 = 5017;
    public static final int ELEMENTAL_BALANCE_5018 = 5018;
    public static final int ELEMENTAL_BALANCE_5019 = 5019;
    public static final int ELEMENTAL_BALANCE_5020 = 5020;
    public static final int ELEMENTAL_BALANCE_5021 = 5021;
    public static final int COMBAT_STONE_5022 = 5022;
    public static final int COMBAT_STONE_5023 = 5023;
    public static final int COMBAT_STONE_5024 = 5024;
    public static final int COMBAT_STONE_5025 = 5025;
    public static final int COMBAT_STONE_5026 = 5026;
    public static final int COMBAT_STONE_5027 = 5027;
    public static final int COMBAT_STONE_5028 = 5028;
    public static final int COMBAT_STONE_5029 = 5029;
    public static final int COMBAT_STONE_5030 = 5030;
    public static final int COMBAT_STONE_5031 = 5031;
    public static final int COMBAT_STONE_5032 = 5032;
    public static final int COMBAT_STONE_5033 = 5033;
    public static final int JULIET = 5035;
    public static final int APOTHECARY = 5036;
    public static final int ROMEO = 5037;
    public static final int FATHER_LAWRENCE = 5038;
    public static final int DRAUL_LEPTOC = 5039;
    public static final int PHILLIPA = 5040;
    public static final int MARTINA_SCORSBY = 5041;
    public static final int JEREMY_CLERKSIN = 5042;
    public static final int SUIT_OF_ARMOUR = 5043;
    public static final int SANFEW = 5044;
    public static final int KAQEMEEX = 5045;
    public static final int CYREG_PADDLEHORN = 5046;
    public static final int VELIAF_HURTZ_5048 = 5048;
    public static final int RADIGAD_PONFIT = 5051;
    public static final int POLMAFI_FERDYGRIS = 5052;
    public static final int IVAN_STROM_5053 = 5053;
    public static final int SKELETON_HELLHOUND = 5054;
    public static final int STRANGER_5055 = 5055;
    public static final int VANSTROM_KLAUSE_5056 = 5056;
    public static final int MIST_5057 = 5057;
    public static final int SEA_SLUG_5061 = 5061;
    public static final int KENNITH = 5062;
    public static final int KENNITH_5063 = 5063;
    public static final int BAILEY = 5066;
    public static final int CAROLINE = 5067;
    public static final int HOLGART = 5068;
    public static final int HOLGART_5069 = 5069;
    public static final int HOLGART_5070 = 5070;
    public static final int HOLGART_5072 = 5072;
    public static final int HOLGART_5073 = 5073;
    public static final int KENT = 5074;
    public static final int FISHERMAN_5075 = 5075;
    public static final int FISHERMAN_5076 = 5076;
    public static final int FISHERMAN_5077 = 5077;
    public static final int FISHERMAN_5078 = 5078;
    public static final int DELRITH = 5079;
    public static final int WEAKENED_DELRITH = 5080;
    public static final int WIZARD_TRAIBORN = 5081;
    public static final int GYPSY_ARIS = 5082;
    public static final int SIR_PRYSIN = 5083;
    public static final int SIR_PRYSIN_5084 = 5084;
    public static final int CAPTAIN_ROVIN = 5085;
    public static final int DARK_WIZARD_5086 = 5086;
    public static final int DARK_WIZARD_5087 = 5087;
    public static final int DARK_WIZARD_5088 = 5088;
    public static final int DARK_WIZARD_5089 = 5089;
    public static final int DENATH_5090 = 5090;
    public static final int DENATH_5091 = 5091;
    public static final int WALLY = 5092;
    public static final int COMBAT_STONE_5093 = 5093;
    public static final int COMBAT_STONE_5094 = 5094;
    public static final int COMBAT_STONE_5095 = 5095;
    public static final int COMBAT_STONE_5096 = 5096;
    public static final int COMBAT_STONE_5097 = 5097;
    public static final int COMBAT_STONE_5098 = 5098;
    public static final int COMBAT_STONE_5099 = 5099;
    public static final int COMBAT_STONE_5100 = 5100;
    public static final int COMBAT_STONE_5101 = 5101;
    public static final int COMBAT_STONE_5102 = 5102;
    public static final int COMBAT_STONE_5103 = 5103;
    public static final int COMBAT_STONE_5104 = 5104;
    public static final int COMBAT_STONE_5105 = 5105;
    public static final int COMBAT_STONE_5106 = 5106;
    public static final int COMBAT_STONE_5107 = 5107;
    public static final int COMBAT_STONE_5108 = 5108;
    public static final int COMBAT_STONE_5109 = 5109;
    public static final int COMBAT_STONE_5110 = 5110;
    public static final int COMBAT_STONE_5111 = 5111;
    public static final int COMBAT_STONE_5112 = 5112;
    public static final int COMBAT_STONE_5113 = 5113;
    public static final int COMBAT_STONE_5114 = 5114;
    public static final int COMBAT_STONE_5115 = 5115;
    public static final int COMBAT_STONE_5116 = 5116;
    public static final int COMBAT_STONE_5117 = 5117;
    public static final int SYLAS = 5118;
    public static final int GRIMGNASH = 5119;
    public static final int RUPERT_THE_BEARD = 5120;
    public static final int RUPERT_THE_BEARD_5121 = 5121;
    public static final int DRAIN_PIPE = 5122;
    public static final int RUPERT_THE_BEARD_5123 = 5123;
    public static final int RUPERT_THE_BEARD_5124 = 5124;
    public static final int MIAZRQA = 5125;
    public static final int EXPERIMENT_NO2 = 5126;
    public static final int MOUSE_5127 = 5127;
    public static final int MOUSE_5128 = 5128;
    public static final int GLOD = 5129;
    public static final int GNOME_5130 = 5130;
    public static final int WINKIN = 5131;
    public static final int GNOME_5132 = 5132;
    public static final int CAGE_5133 = 5133;
    public static final int BROKEN_CLAY_GOLEM = 5134;
    public static final int DAMAGED_CLAY_GOLEM = 5135;
    public static final int CLAY_GOLEM_5136 = 5136;
    public static final int DESERT_PHOENIX = 5137;
    public static final int ELISSA = 5138;
    public static final int SIGMUND_5139 = 5139;
    public static final int ZANIK_5140 = 5140;
    public static final int GUARD_5141 = 5141;
    public static final int SIGMUND_5142 = 5142;
    public static final int SIGMUND_5143 = 5143;
    public static final int SIGMUND_5144 = 5144;
    public static final int SIGMUND_5145 = 5145;
    public static final int SIGMUND_5146 = 5146;
    public static final int ZANIK_5147 = 5147;
    public static final int ZANIK_5148 = 5148;
    public static final int GENERAL_BENTNOZE_5149 = 5149;
    public static final int GENERAL_WARTFACE_5150 = 5150;
    public static final int GRUBFOOT_5151 = 5151;
    public static final int GOBLIN_5152 = 5152;
    public static final int GOBLIN_5153 = 5153;
    public static final int GOBLIN_5154 = 5154;
    public static final int ZANIK_5155 = 5155;
    public static final int URTAG_5156 = 5156;
    public static final int HAM_ARCHER = 5157;
    public static final int HAM_MAGE = 5158;
    public static final int ZANIK_5159 = 5159;
    public static final int SIGMUND_AND_ZANIK = 5160;
    public static final int SERGEANT_MOSSFISTS = 5161;
    public static final int SERGEANT_SLIMETOES = 5162;
    public static final int CAVE_GOBLIN_5163 = 5163;
    public static final int CAVE_GOBLIN_5164 = 5164;
    public static final int CAVE_GOBLIN_5165 = 5165;
    public static final int CAVE_GOBLIN_5166 = 5166;
    public static final int CAVE_GOBLIN_5167 = 5167;
    public static final int CAVE_GOBLIN_5168 = 5168;
    public static final int TICKET_GOBLIN = 5169;
    public static final int DWARF_5170 = 5170;
    public static final int DWARF_5171 = 5171;
    public static final int DWARF_5172 = 5172;
    public static final int DWARF_5173 = 5173;
    public static final int DWARF_5174 = 5174;
    public static final int DWARF_5175 = 5175;
    public static final int TICKET_DWARF = 5176;
    public static final int AMBASSADOR_ALVIJAR = 5177;
    public static final int BUILDER = 5178;
    public static final int BUILDER_5179 = 5179;
    public static final int BUILDER_5180 = 5180;
    public static final int BUILDER_5181 = 5181;
    public static final int TEGDAK = 5182;
    public static final int ZANIK_5184 = 5184;
    public static final int GUARD_5185 = 5185;
    public static final int GUARD_5186 = 5186;
    public static final int GUARD_5187 = 5187;
    public static final int GUARD_5188 = 5188;
    public static final int GUARD_5189 = 5189;
    public static final int LOLLK = 5190;
    public static final int CAPTAIN_LAWGOF = 5191;
    public static final int GOBLIN_5192 = 5192;
    public static final int GOBLIN_5193 = 5193;
    public static final int BABY_GREEN_DRAGON = 5194;
    public static final int GOBLIN_5195 = 5195;
    public static final int GOBLIN_5196 = 5196;
    public static final int GOBLIN_5197 = 5197;
    public static final int GOBLIN_5198 = 5198;
    public static final int GOBLIN_5199 = 5199;
    public static final int GOBLIN_5200 = 5200;
    public static final int GOBLIN_5201 = 5201;
    public static final int GOBLIN_5202 = 5202;
    public static final int GOBLIN_5203 = 5203;
    public static final int GOBLIN_5204 = 5204;
    public static final int GOBLIN_5205 = 5205;
    public static final int GOBLIN_5206 = 5206;
    public static final int GOBLIN_5207 = 5207;
    public static final int GOBLIN_5208 = 5208;
    public static final int CHARLIE_THE_TRAMP = 5209;
    public static final int KATRINE = 5210;
    public static final int WEAPONSMASTER = 5211;
    public static final int STRAVEN = 5212;
    public static final int JONNY_THE_BEARD = 5213;
    public static final int CURATOR_HAIG_HALEN = 5214;
    public static final int KING_ROALD_5215 = 5215;
    public static final int BENNY = 5216;
    public static final int THIEF_5217 = 5217;
    public static final int THIEF_5218 = 5218;
    public static final int THIEF_5219 = 5219;
    public static final int THIEF_5220 = 5220;
    public static final int JIG_CART = 5221;
    public static final int JIG_CART_5222 = 5222;
    public static final int JIG_CART_5223 = 5223;
    public static final int JIG_CART_5224 = 5224;
    public static final int JIG_CART_5225 = 5225;
    public static final int JIG_CART_5226 = 5226;
    public static final int KHARID_SCORPION = 5228;
    public static final int KHARID_SCORPION_5229 = 5229;
    public static final int KHARID_SCORPION_5230 = 5230;
    public static final int SEER = 5231;
    public static final int THORMAC = 5232;
    public static final int FISHING_SPOT_5233 = 5233;
    public static final int FISHING_SPOT_5234 = 5234;
    public static final int MONKEY_MINDER = 5235;
    public static final int SKELETON_5237 = 5237;
    public static final int SPIDER_5238 = 5238;
    public static final int SPIDER_5239 = 5239;
    public static final int BIRD = 5240;
    public static final int BIRD_5241 = 5241;
    public static final int SCORPION_5242 = 5242;
    public static final int JUNGLE_SPIDER_5243 = 5243;
    public static final int SNAKE_5244 = 5244;
    public static final int DUGOPUL = 5245;
    public static final int SALENAB = 5246;
    public static final int TREFAJI = 5247;
    public static final int ABERAB = 5248;
    public static final int SOLIHIB = 5249;
    public static final int DAGA = 5250;
    public static final int TUTAB = 5251;
    public static final int IFABA = 5252;
    public static final int HAMAB = 5253;
    public static final int HAFUBA = 5254;
    public static final int DENADU = 5255;
    public static final int LOFU = 5256;
    public static final int KRUK = 5257;
    public static final int DUKE = 5258;
    public static final int OIPUIS = 5259;
    public static final int UYORO = 5260;
    public static final int OUHAI = 5261;
    public static final int UODAI = 5262;
    public static final int PADULAH = 5263;
    public static final int AWOWOGEI_5264 = 5264;
    public static final int UWOGO = 5265;
    public static final int MURUWOI = 5266;
    public static final int SLEEPING_MONKEY = 5267;
    public static final int MONKEY_CHILD = 5268;
    public static final int THE_MONKEYS_UNCLE = 5269;
    public static final int THE_MONKEYS_AUNT = 5270;
    public static final int MONKEY_GUARD = 5271;
    public static final int MONKEY_ARCHER = 5272;
    public static final int MONKEY_ARCHER_5273 = 5273;
    public static final int MONKEY_ARCHER_5274 = 5274;
    public static final int MONKEY_GUARD_5275 = 5275;
    public static final int MONKEY_GUARD_5276 = 5276;
    public static final int ELDER_GUARD = 5277;
    public static final int ELDER_GUARD_5278 = 5278;
    public static final int MONKEY_5279 = 5279;
    public static final int MONKEY_5280 = 5280;
    public static final int MONKEY_ZOMBIE = 5281;
    public static final int MONKEY_ZOMBIE_5282 = 5282;
    public static final int MONKEY_ZOMBIE_5283 = 5283;
    public static final int BONZARA = 5284;
    public static final int ELF_WARRIOR = 5293;
    public static final int ELF_WARRIOR_5294 = 5294;
    public static final int ELF_ARCHER = 5295;
    public static final int ELF_ARCHER_5296 = 5296;
    public static final int GOREU = 5297;
    public static final int ARVEL = 5299;
    public static final int MAWRTH = 5300;
    public static final int EOIN = 5302;
    public static final int IONA = 5303;
    public static final int ELUNED = 5304;
    public static final int RED_SHEEP_5305 = 5305;
    public static final int GREEN_SHEEP_5306 = 5306;
    public static final int BLUE_SHEEP_5307 = 5307;
    public static final int YELLOW_SHEEP_5308 = 5308;
    public static final int GNOME_5309 = 5309;
    public static final int GPDT_EMPLOYEE = 5313;
    public static final int HORACIO = 5315;
    public static final int KANGAI_MAU = 5316;
    public static final int EAGLE_5317 = 5317;
    public static final int EAGLE_5318 = 5318;
    public static final int EAGLE_5319 = 5319;
    public static final int EAGLE_5320 = 5320;
    public static final int COMBAT_STONE_5321 = 5321;
    public static final int SIGMUND_5322 = 5322;
    public static final int SIGMUND_5323 = 5323;
    public static final int URTAG_5326 = 5326;
    public static final int DUKE_HORACIO_5327 = 5327;
    public static final int MISTAG = 5328;
    public static final int SIGMUND_5329 = 5329;
    public static final int CAVE_GOBLIN_MINER = 5330;
    public static final int CAVE_GOBLIN_MINER_5331 = 5331;
    public static final int CAVE_GOBLIN_MINER_5332 = 5332;
    public static final int CAVE_GOBLIN_MINER_5333 = 5333;
    public static final int CAVE_GOBLIN_GUARD = 5334;
    public static final int CAVE_GOBLIN_GUARD_5335 = 5335;
    public static final int CAVE_GOBLIN_MINER_5336 = 5336;
    public static final int CAVE_GOBLIN_MINER_5337 = 5337;
    public static final int CAVE_GOBLIN_MINER_5338 = 5338;
    public static final int CAVE_GOBLIN_MINER_5339 = 5339;
    public static final int MOSOL_REI = 5340;
    public static final int SPIRIT_OF_ZADIMUS = 5341;
    public static final int UNDEAD_ONE = 5342;
    public static final int UNDEAD_ONE_5343 = 5343;
    public static final int UNDEAD_ONE_5344 = 5344;
    public static final int UNDEAD_ONE_5345 = 5345;
    public static final int UNDEAD_ONE_5346 = 5346;
    public static final int UNDEAD_ONE_5347 = 5347;
    public static final int UNDEAD_ONE_5348 = 5348;
    public static final int UNDEAD_ONE_5349 = 5349;
    public static final int UNDEAD_ONE_5350 = 5350;
    public static final int UNDEAD_ONE_5351 = 5351;
    public static final int RASHILIYIA = 5352;
    public static final int NAZASTAROOL = 5353;
    public static final int NAZASTAROOL_5354 = 5354;
    public static final int NAZASTAROOL_5355 = 5355;
    public static final int HAJEDY = 5356;
    public static final int VIGROY = 5357;
    public static final int KALEB_PARAMAYA = 5358;
    public static final int YOHNUS = 5359;
    public static final int SERAVEL = 5360;
    public static final int YANNI_SALIKA = 5361;
    public static final int OBLI = 5362;
    public static final int FERNAHEI = 5363;
    public static final int CAPTAIN_SHANKS = 5364;
    public static final int OBSERVATORY_ASSISTANT = 5365;
    public static final int OBSERVATORY_PROFESSOR = 5366;
    public static final int OBSERVATORY_PROFESSOR_5367 = 5367;
    public static final int SLEEPING_GUARD = 5368;
    public static final int GOBLIN_GUARD = 5369;
    public static final int GHOST_5370 = 5370;
    public static final int SPIRIT_OF_SCORPIUS = 5371;
    public static final int GRAVE_SCORPION = 5372;
    public static final int POISON_SPIDER_5373 = 5373;
    public static final int NAGHEAD = 5374;
    public static final int WAGCHIN = 5375;
    public static final int GOBLIN_5376 = 5376;
    public static final int GOBLIN_5377 = 5377;
    public static final int GREASYCHEEKS = 5378;
    public static final int SMELLYTOES = 5379;
    public static final int CREAKYKNEES = 5380;
    public static final int CLOTHEARS = 5381;
    public static final int GUARD_CAPTAIN = 5383;
    public static final int SANDY = 5384;
    public static final int SANDY_5385 = 5385;
    public static final int MAZION = 5386;
    public static final int BLAEC = 5387;
    public static final int REESO = 5388;
    public static final int COMBAT_STONE_5389 = 5389;
    public static final int COMBAT_STONE_5390 = 5390;
    public static final int COMBAT_STONE_5391 = 5391;
    public static final int COMBAT_STONE_5392 = 5392;
    public static final int COMBAT_STONE_5393 = 5393;
    public static final int COMBAT_STONE_5394 = 5394;
    public static final int COMBAT_STONE_5395 = 5395;
    public static final int COMBAT_STONE_5396 = 5396;
    public static final int COMBAT_STONE_5397 = 5397;
    public static final int COMBAT_STONE_5398 = 5398;
    public static final int COMBAT_STONE_5399 = 5399;
    public static final int COMBAT_STONE_5400 = 5400;
    public static final int COMBAT_STONE_5401 = 5401;
    public static final int COMBAT_STONE_5402 = 5402;
    public static final int COMBAT_STONE_5403 = 5403;
    public static final int COMBAT_STONE_5404 = 5404;
    public static final int COMBAT_STONE_5405 = 5405;
    public static final int COMBAT_STONE_5406 = 5406;
    public static final int COMBAT_STONE_5407 = 5407;
    public static final int COMBAT_STONE_5408 = 5408;
    public static final int COMBAT_STONE_5409 = 5409;
    public static final int COMBAT_STONE_5410 = 5410;
    public static final int COMBAT_STONE_5412 = 5412;
    public static final int COMBAT_STONE_5413 = 5413;
    public static final int COMBAT_STONE_5414 = 5414;
    public static final int COMBAT_STONE_5415 = 5415;
    public static final int COMBAT_STONE_5416 = 5416;
    public static final int PRIEST_5417 = 5417;
    public static final int GUARD_5418 = 5418;
    public static final int DOOR_MAN = 5419;
    public static final int WATCHMAN = 5420;
    public static final int SOLDIER_5421 = 5421;
    public static final int WYSON_THE_GARDENER = 5422;
    public static final int SIGBERT_THE_ADVENTURER = 5423;
    public static final int CAPT_ARNAV = 5426;
    public static final int FLIPPA = 5427;
    public static final int TILT = 5428;
    public static final int FROG_5429 = 5429;
    public static final int FROG_5430 = 5430;
    public static final int FROG_5431 = 5431;
    public static final int FROG_5432 = 5432;
    public static final int CALEB_5433 = 5433;
    public static final int FROG_PRINCE = 5434;
    public static final int FROG_PRINCESS = 5435;
    public static final int NILES = 5436;
    public static final int MILES = 5437;
    public static final int GILES = 5438;
    public static final int NILES_5439 = 5439;
    public static final int MILES_5440 = 5440;
    public static final int GILES_5441 = 5441;
    public static final int SECURITY_GUARD = 5442;
    public static final int JOHNATHON = 5443;
    public static final int CAPN_HAND = 5444;
    public static final int JOHNATHON_5445 = 5445;
    public static final int ADVISOR_GHRIM = 5447;
    public static final int ADVISOR_GHRIM_5448 = 5448;
    public static final int BOB_BARTER_HERBS = 5449;
    public static final int MURKY_MATT_RUNES = 5450;
    public static final int RELOBO_BLINYO_LOGS = 5451;
    public static final int HOFUTHAND_WEAPONS_AND_ARMOUR = 5452;
    public static final int RESHI = 5453;
    public static final int THUMPY = 5454;
    public static final int THOMDRIL = 5455;
    public static final int KENDALL = 5456;
    public static final int SHIPYARD_WORKER_5457 = 5457;
    public static final int SUSPECT_5458 = 5458;
    public static final int SUSPECT_5459 = 5459;
    public static final int SUSPECT_5460 = 5460;
    public static final int SUSPECT_5461 = 5461;
    public static final int SUSPECT_5462 = 5462;
    public static final int SUSPECT_5463 = 5463;
    public static final int MOLLY_5464 = 5464;
    public static final int SUSPECT_5465 = 5465;
    public static final int SUSPECT_5466 = 5466;
    public static final int MOLLY_5467 = 5467;
    public static final int SUSPECT_5468 = 5468;
    public static final int SUSPECT_5469 = 5469;
    public static final int SUSPECT_5470 = 5470;
    public static final int MOLLY_5471 = 5471;
    public static final int SUSPECT_5472 = 5472;
    public static final int SUSPECT_5473 = 5473;
    public static final int MOLLY_5474 = 5474;
    public static final int SUSPECT_5475 = 5475;
    public static final int MOLLY_5476 = 5476;
    public static final int SUSPECT_5477 = 5477;
    public static final int MOLLY_5478 = 5478;
    public static final int SUSPECT_5479 = 5479;
    public static final int MOLLY_5480 = 5480;
    public static final int SUSPECT_5481 = 5481;
    public static final int SUSPECT_5482 = 5482;
    public static final int SUSPECT_5483 = 5483;
    public static final int SUSPECT_5484 = 5484;
    public static final int MOLLY_5485 = 5485;
    public static final int MOLLY_5486 = 5486;
    public static final int MOLLY_5487 = 5487;
    public static final int BALLOON_ANIMAL_5488 = 5488;
    public static final int BALLOON_ANIMAL_5489 = 5489;
    public static final int BALLOON_ANIMAL_5491 = 5491;
    public static final int BALLOON_ANIMAL_5492 = 5492;
    public static final int BALLOON_ANIMAL_5493 = 5493;
    public static final int PHEASANT_5500 = 5500;
    public static final int PHEASANT_5502 = 5502;
    public static final int DUNCE = 5503;
    public static final int MR_MORDAUT = 5504;
    public static final int GIANT = 5505;
    public static final int MUMMY_5506 = 5506;
    public static final int ZOMBIE_5507 = 5507;
    public static final int GOBLIN_5508 = 5508;
    public static final int GOBLIN_5509 = 5509;
    public static final int SANDWICH_LADY = 5510;
    public static final int GUARD_RECRUITER = 5511;
    public static final int GARDENER_5512 = 5512;
    public static final int ELITE_VOID_KNIGHT = 5513;
    public static final int LESSER_FANATIC = 5514;
    public static final int LUNA = 5515;
    public static final int THE_WEDGE = 5517;
    public static final int ELDER_GNOME_CHILD = 5518;
    public static final int TWOPINTS = 5519;
    public static final int JARR = 5520;
    public static final int LESABR = 5521;
    public static final int FLAX_KEEPER = 5522;
    public static final int HATIUS_COSAINTUS = 5523;
    public static final int SIR_REBRAL = 5524;
    public static final int TOBY = 5525;
    public static final int THORODIN_5526 = 5526;
    public static final int TWIGGY_OKORN = 5527;
    public static final int SQUIRREL_5528 = 5528;
    public static final int HUNTING_EXPERT_5529 = 5529;
    public static final int SPOTTED_KEBBIT = 5531;
    public static final int DARK_KEBBIT = 5532;
    public static final int DASHING_KEBBIT = 5533;
    public static final int WHIRLPOOL_5534 = 5534;
    public static final int ENORMOUS_TENTACLE = 5535;
    public static final int VETION_JR = 5536;
    public static final int VETION_JR_5537 = 5537;
    public static final int EGG = 5538;
    public static final int EGG_5539 = 5539;
    public static final int EGG_5540 = 5540;
    public static final int EGG_5541 = 5541;
    public static final int EGG_5542 = 5542;
    public static final int EGG_5543 = 5543;
    public static final int JACK = 5544;
    public static final int JILL = 5545;
    public static final int JEFF_5546 = 5546;
    public static final int SCORPIAS_OFFSPRING = 5547;
    public static final int TROPICAL_WAGTAIL = 5548;
    public static final int CRIMSON_SWIFT = 5549;
    public static final int CERULEAN_TWITCH = 5550;
    public static final int GOLDEN_WARBLER = 5551;
    public static final int COPPER_LONGTAIL = 5552;
    public static final int BLACK_WARLOCK = 5553;
    public static final int SNOWY_KNIGHT = 5554;
    public static final int SAPPHIRE_GLACIALIS = 5555;
    public static final int RUBY_HARVEST = 5556;
    public static final int VENENATIS_SPIDERLING_5557 = 5557;
    public static final int CALLISTO_CUB_5558 = 5558;
    public static final int VETION_JR_5559 = 5559;
    public static final int VETION_JR_5560 = 5560;
    public static final int SCORPIAS_OFFSPRING_5561 = 5561;
    public static final int MERCY = 5562;
    public static final int ANGRY_BARBARIAN_SPIRIT = 5563;
    public static final int ENRAGED_BARBARIAN_SPIRIT = 5564;
    public static final int BERSERK_BARBARIAN_SPIRIT = 5565;
    public static final int FEROCIOUS_BARBARIAN_SPIRIT = 5566;
    public static final int DEATH = 5567;
    public static final int ZOMBIE_5568 = 5568;
    public static final int MOST_OF_A_ZOMBIE = 5569;
    public static final int MOST_OF_A_ZOMBIE_5570 = 5570;
    public static final int ZOMBIE_5571 = 5571;
    public static final int MOST_OF_A_ZOMBIE_5572 = 5572;
    public static final int ZOMBIE_HEAD = 5573;
    public static final int ZOMBIE_5574 = 5574;
    public static final int HALFZOMBIE = 5575;
    public static final int OTHER_HALFZOMBIE = 5576;
    public static final int CHILD_5577 = 5577;
    public static final int CHILD_5578 = 5578;
    public static final int CHILD_5579 = 5579;
    public static final int CHILD_5580 = 5580;
    public static final int CHILD_5581 = 5581;
    public static final int CHILD_5582 = 5582;
    public static final int ZOMBIE_5583 = 5583;
    public static final int WILY_CAT = 5584;
    public static final int WILY_CAT_5585 = 5585;
    public static final int WILY_CAT_5586 = 5586;
    public static final int WILY_CAT_5587 = 5587;
    public static final int WILY_CAT_5588 = 5588;
    public static final int WILY_CAT_5589 = 5589;
    public static final int WILY_HELLCAT = 5590;
    public static final int KITTEN_5591 = 5591;
    public static final int KITTEN_5592 = 5592;
    public static final int KITTEN_5593 = 5593;
    public static final int KITTEN_5594 = 5594;
    public static final int KITTEN_5595 = 5595;
    public static final int KITTEN_5596 = 5596;
    public static final int HELLKITTEN = 5597;
    public static final int OVERGROWN_CAT = 5598;
    public static final int OVERGROWN_CAT_5599 = 5599;
    public static final int OVERGROWN_CAT_5600 = 5600;
    public static final int OVERGROWN_CAT_5601 = 5601;
    public static final int OVERGROWN_CAT_5602 = 5602;
    public static final int OVERGROWN_CAT_5603 = 5603;
    public static final int OVERGROWN_HELLCAT = 5604;
    public static final int PEACEFUL_BARBARIAN_SPIRIT = 5605;
    public static final int MINER_5606 = 5606;
    public static final int MURPHY = 5607;
    public static final int MURPHY_5608 = 5608;
    public static final int MURPHY_5609 = 5609;
    public static final int MURPHY_5610 = 5610;
    public static final int SHARK_5611 = 5611;
    public static final int SHARK_5612 = 5612;
    public static final int SAM_5613 = 5613;
    public static final int RACHAEL = 5614;
    public static final int SWAMP_SNAKE = 5615;
    public static final int SWAMP_SNAKE_5616 = 5616;
    public static final int SWAMP_SNAKE_5617 = 5617;
    public static final int SWAMP_SNAKE_5618 = 5618;
    public static final int DEAD_SWAMP_SNAKE = 5619;
    public static final int DEAD_SWAMP_SNAKE_5620 = 5620;
    public static final int DEAD_SWAMP_SNAKE_5621 = 5621;
    public static final int GHAST_5622 = 5622;
    public static final int GHAST_5623 = 5623;
    public static final int GHAST_5624 = 5624;
    public static final int GHAST_5625 = 5625;
    public static final int GHAST_5626 = 5626;
    public static final int GHAST_5627 = 5627;
    public static final int GIANT_SNAIL = 5628;
    public static final int GIANT_SNAIL_5629 = 5629;
    public static final int GIANT_SNAIL_5630 = 5630;
    public static final int RIYL_SHADOW_5631 = 5631;
    public static final int ASYN_SHADOW_5632 = 5632;
    public static final int SHADE = 5633;
    public static final int VAMPYRE_JUVINATE_5634 = 5634;
    public static final int VAMPYRE_JUVINATE_5635 = 5635;
    public static final int VAMPYRE_JUVINATE_5636 = 5636;
    public static final int VAMPYRE_JUVINATE_5637 = 5637;
    public static final int VAMPYRE_JUVINATE_5638 = 5638;
    public static final int VAMPYRE_JUVINATE_5639 = 5639;
    public static final int FERAL_VAMPYRE_5640 = 5640;
    public static final int FERAL_VAMPYRE_5641 = 5641;
    public static final int FERAL_VAMPYRE_5642 = 5642;
    public static final int TENTACLE_5643 = 5643;
    public static final int HEAD = 5644;
    public static final int HEAD_5645 = 5645;
    public static final int TENTACLE_5646 = 5646;
    public static final int ZOMBIE_5647 = 5647;
    public static final int UNDEAD_LUMBERJACK = 5648;
    public static final int UNDEAD_LUMBERJACK_5649 = 5649;
    public static final int UNDEAD_LUMBERJACK_5650 = 5650;
    public static final int UNDEAD_LUMBERJACK_5651 = 5651;
    public static final int UNDEAD_LUMBERJACK_5652 = 5652;
    public static final int UNDEAD_LUMBERJACK_5653 = 5653;
    public static final int UNDEAD_LUMBERJACK_5654 = 5654;
    public static final int UNDEAD_LUMBERJACK_5655 = 5655;
    public static final int UNDEAD_LUMBERJACK_5656 = 5656;
    public static final int UNDEAD_LUMBERJACK_5657 = 5657;
    public static final int UNDEAD_LUMBERJACK_5658 = 5658;
    public static final int UNDEAD_LUMBERJACK_5659 = 5659;
    public static final int UNDEAD_LUMBERJACK_5660 = 5660;
    public static final int UNDEAD_LUMBERJACK_5661 = 5661;
    public static final int UNDEAD_LUMBERJACK_5662 = 5662;
    public static final int UNDEAD_LUMBERJACK_5663 = 5663;
    public static final int UNDEAD_LUMBERJACK_5665 = 5665;
    public static final int UNDEAD_LUMBERJACK_5666 = 5666;
    public static final int UNDEAD_LUMBERJACK_5667 = 5667;
    public static final int UNDEAD_LUMBERJACK_5668 = 5668;
    public static final int UNDEAD_LUMBERJACK_5669 = 5669;
    public static final int UNDEAD_LUMBERJACK_5670 = 5670;
    public static final int UNDEAD_LUMBERJACK_5671 = 5671;
    public static final int UNDEAD_LUMBERJACK_5672 = 5672;
    public static final int UNDEAD_LUMBERJACK_5673 = 5673;
    public static final int UNDEAD_LUMBERJACK_5674 = 5674;
    public static final int UNDEAD_LUMBERJACK_5675 = 5675;
    public static final int UNDEAD_LUMBERJACK_5676 = 5676;
    public static final int UNDEAD_LUMBERJACK_5677 = 5677;
    public static final int UNDEAD_LUMBERJACK_5678 = 5678;
    public static final int UNDEAD_LUMBERJACK_5679 = 5679;
    public static final int UNDEAD_LUMBERJACK_5680 = 5680;
    public static final int UNDEAD_LUMBERJACK_5681 = 5681;
    public static final int UNDEAD_LUMBERJACK_5682 = 5682;
    public static final int UNDEAD_LUMBERJACK_5683 = 5683;
    public static final int UNDEAD_LUMBERJACK_5684 = 5684;
    public static final int UNDEAD_LUMBERJACK_5685 = 5685;
    public static final int UNDEAD_LUMBERJACK_5686 = 5686;
    public static final int UNDEAD_LUMBERJACK_5687 = 5687;
    public static final int UNDEAD_LUMBERJACK_5688 = 5688;
    public static final int UNDEAD_LUMBERJACK_5689 = 5689;
    public static final int UNDEAD_LUMBERJACK_5690 = 5690;
    public static final int UNDEAD_LUMBERJACK_5691 = 5691;
    public static final int UNDEAD_LUMBERJACK_5692 = 5692;
    public static final int UNDEAD_LUMBERJACK_5693 = 5693;
    public static final int UNDEAD_LUMBERJACK_5694 = 5694;
    public static final int UNDEAD_LUMBERJACK_5695 = 5695;
    public static final int UNDEAD_LUMBERJACK_5696 = 5696;
    public static final int UNDEAD_LUMBERJACK_5697 = 5697;
    public static final int UNDEAD_LUMBERJACK_5698 = 5698;
    public static final int UNDEAD_LUMBERJACK_5699 = 5699;
    public static final int UNDEAD_LUMBERJACK_5700 = 5700;
    public static final int UNDEAD_LUMBERJACK_5701 = 5701;
    public static final int UNDEAD_LUMBERJACK_5702 = 5702;
    public static final int UNDEAD_LUMBERJACK_5703 = 5703;
    public static final int UNDEAD_LUMBERJACK_5704 = 5704;
    public static final int UNDEAD_LUMBERJACK_5705 = 5705;
    public static final int UNDEAD_LUMBERJACK_5706 = 5706;
    public static final int UNDEAD_LUMBERJACK_5707 = 5707;
    public static final int UNDEAD_LUMBERJACK_5708 = 5708;
    public static final int UNDEAD_LUMBERJACK_5709 = 5709;
    public static final int UNDEAD_LUMBERJACK_5710 = 5710;
    public static final int UNDEAD_LUMBERJACK_5711 = 5711;
    public static final int UNDEAD_LUMBERJACK_5712 = 5712;
    public static final int UNDEAD_LUMBERJACK_5713 = 5713;
    public static final int UNDEAD_LUMBERJACK_5714 = 5714;
    public static final int UNDEAD_LUMBERJACK_5715 = 5715;
    public static final int UNDEAD_LUMBERJACK_5716 = 5716;
    public static final int UNDEAD_LUMBERJACK_5717 = 5717;
    public static final int UNDEAD_LUMBERJACK_5718 = 5718;
    public static final int UNDEAD_LUMBERJACK_5719 = 5719;
    public static final int UNDEAD_LUMBERJACK_5720 = 5720;
    public static final int BARRICADE = 5722;
    public static final int BARRICADE_5723 = 5723;
    public static final int BARRICADE_5724 = 5724;
    public static final int BARRICADE_5725 = 5725;
    public static final int SHEEP_5726 = 5726;
    public static final int RABBIT_5727 = 5727;
    public static final int IMP_5728 = 5728;
    public static final int SHIPYARD_WORKER_5729 = 5729;
    public static final int MASTER_FARMER = 5730;
    public static final int MASTER_FARMER_5731 = 5731;
    public static final int MARKET_GUARD_5732 = 5732;
    public static final int ELNOCK_INQUISITOR = 5734;
    public static final int IMMENIZZ = 5735;
    public static final int FAIRY_AERYKA = 5736;
    public static final int WANDERING_IMPLING = 5737;
    public static final int IMP_DEFENDER = 5738;
    public static final int PENANCE_FIGHTER_5739 = 5739;
    public static final int PENANCE_FIGHTER_5740 = 5740;
    public static final int PENANCE_FIGHTER_5741 = 5741;
    public static final int PENANCE_FIGHTER_5742 = 5742;
    public static final int PENANCE_FIGHTER_5743 = 5743;
    public static final int PENANCE_FIGHTER_5744 = 5744;
    public static final int PENANCE_FIGHTER_5745 = 5745;
    public static final int PENANCE_FIGHTER_5746 = 5746;
    public static final int PENANCE_FIGHTER_5747 = 5747;
    public static final int PENANCE_RUNNER_5748 = 5748;
    public static final int PENANCE_RUNNER_5749 = 5749;
    public static final int PENANCE_RUNNER_5750 = 5750;
    public static final int PENANCE_RUNNER_5751 = 5751;
    public static final int PENANCE_RUNNER_5752 = 5752;
    public static final int PENANCE_RUNNER_5753 = 5753;
    public static final int PENANCE_RUNNER_5754 = 5754;
    public static final int PENANCE_RUNNER_5755 = 5755;
    public static final int PENANCE_RUNNER_5756 = 5756;
    public static final int PENANCE_RANGER_5757 = 5757;
    public static final int PENANCE_RANGER_5758 = 5758;
    public static final int PENANCE_RANGER_5759 = 5759;
    public static final int PENANCE_RANGER_5760 = 5760;
    public static final int PENANCE_RANGER_5761 = 5761;
    public static final int PENANCE_RANGER_5762 = 5762;
    public static final int PENANCE_RANGER_5763 = 5763;
    public static final int PENANCE_RANGER_5764 = 5764;
    public static final int PENANCE_RANGER_5765 = 5765;
    public static final int PENANCE_HEALER_5766 = 5766;
    public static final int PENANCE_HEALER_5767 = 5767;
    public static final int PENANCE_HEALER_5768 = 5768;
    public static final int PENANCE_HEALER_5769 = 5769;
    public static final int PENANCE_HEALER_5770 = 5770;
    public static final int PENANCE_HEALER_5771 = 5771;
    public static final int PENANCE_HEALER_5772 = 5772;
    public static final int PENANCE_HEALER_5773 = 5773;
    public static final int PENANCE_HEALER_5774 = 5774;
    public static final int PENANCE_QUEEN = 5775;
    public static final int QUEEN_SPAWN = 5776;
    public static final int EGG_LAUNCHER_5777 = 5777;
    public static final int EGG_LAUNCHER_5778 = 5778;
    public static final int GIANT_MOLE = 5779;
    public static final int BABY_MOLE = 5780;
    public static final int BABY_MOLE_5781 = 5781;
    public static final int BABY_MOLE_5782 = 5782;
    public static final int LIGHT_CREATURE_5783 = 5783;
    public static final int LIGHT_CREATURE_5784 = 5784;
    public static final int JUNA = 5785;
    public static final int SIMON_TEMPLETON = 5786;
    public static final int PYRAMID_BLOCK = 5787;
    public static final int PYRAMID_BLOCK_5788 = 5788;
    public static final int CAPN_IZZY_NOBEARD = 5789;
    public static final int RAULYN = 5790;
    public static final int GIANT_BAT_5791 = 5791;
    public static final int PARTY_PETE = 5792;
    public static final int KNIGHT_5793 = 5793;
    public static final int MEGAN = 5794;
    public static final int LUCY = 5795;
    public static final int WINTER_ELEMENTAL = 5796;
    public static final int WINTER_ELEMENTAL_5797 = 5797;
    public static final int WINTER_ELEMENTAL_5798 = 5798;
    public static final int WINTER_ELEMENTAL_5799 = 5799;
    public static final int WINTER_ELEMENTAL_5800 = 5800;
    public static final int WINTER_ELEMENTAL_5801 = 5801;
    public static final int AUTUMN_ELEMENTAL = 5802;
    public static final int AUTUMN_ELEMENTAL_5803 = 5803;
    public static final int AUTUMN_ELEMENTAL_5804 = 5804;
    public static final int AUTUMN_ELEMENTAL_5805 = 5805;
    public static final int AUTUMN_ELEMENTAL_5806 = 5806;
    public static final int AUTUMN_ELEMENTAL_5807 = 5807;
    public static final int REACHER_5808 = 5808;
    public static final int TANNER = 5809;
    public static final int MASTER_CRAFTER = 5810;
    public static final int MASTER_CRAFTER_5811 = 5811;
    public static final int MASTER_CRAFTER_5812 = 5812;
    public static final int MINER_5813 = 5813;
    public static final int MINER_5814 = 5814;
    public static final int BERT = 5815;
    public static final int YAK = 5816;
    public static final int ICEBERG = 5817;
    public static final int ICEBERG_5818 = 5818;
    public static final int BERT_5819 = 5819;
    public static final int FISHING_SPOT_5820 = 5820;
    public static final int FISHING_SPOT_5821 = 5821;
    public static final int ICE_TROLL_KING = 5822;
    public static final int ICE_TROLL_RUNT_5823 = 5823;
    public static final int ICE_TROLL_MALE_5824 = 5824;
    public static final int ICE_TROLL_FEMALE_5825 = 5825;
    public static final int ICE_TROLL_GRUNT_5826 = 5826;
    public static final int BORK_SIGMUNDSON = 5827;
    public static final int ICE_TROLL_RUNT_5828 = 5828;
    public static final int ICE_TROLL_MALE_5829 = 5829;
    public static final int ICE_TROLL_FEMALE_5830 = 5830;
    public static final int ICE_TROLL_GRUNT_5831 = 5831;
    public static final int MARTIN_THE_MASTER_GARDENER = 5832;
    public static final int FROG_5833 = 5833;
    public static final int STORM_CLOUD_5834 = 5834;
    public static final int COORDINATOR = 5835;
    public static final int FAIRY_NUFF_5836 = 5836;
    public static final int FAIRY_GODFATHER_5837 = 5837;
    public static final int SLIM_LOUIE = 5838;
    public static final int FAT_ROCCO = 5839;
    public static final int GATEKEEPER = 5840;
    public static final int ZANDAR_HORFYRE = 5841;
    public static final int COW_5842 = 5842;
    public static final int SHEEP_5843 = 5843;
    public static final int SHEEP_5844 = 5844;
    public static final int SHEEP_5845 = 5845;
    public static final int SHEEP_5846 = 5846;
    public static final int ZANARIS_CHOIR = 5847;
    public static final int TANGLEFOOT = 5848;
    public static final int BABY_TANGLEFOOT = 5853;
    public static final int BABY_TANGLEFOOT_5854 = 5854;
    public static final int GATEKEEPER_5855 = 5855;
    public static final int FAIRY_CHEF = 5856;
    public static final int CERBERUS = 5862;
    public static final int CERBERUS_5863 = 5863;
    public static final int CAPTAIN_NED_5864 = 5864;
    public static final int CERBERUS_5866 = 5866;
    public static final int SUMMONED_SOUL = 5867;
    public static final int SUMMONED_SOUL_5868 = 5868;
    public static final int SUMMONED_SOUL_5869 = 5869;
    public static final int KEY_MASTER = 5870;
    public static final int KING_ARTHUR_5871 = 5871;
    public static final int BABY_GREEN_DRAGON_5872 = 5872;
    public static final int BABY_GREEN_DRAGON_5873 = 5873;
    public static final int BLACK_DEMON_5874 = 5874;
    public static final int BLACK_DEMON_5875 = 5875;
    public static final int BLACK_DEMON_5876 = 5876;
    public static final int BLACK_DEMON_5877 = 5877;
    public static final int BLUE_DRAGON_5878 = 5878;
    public static final int BLUE_DRAGON_5879 = 5879;
    public static final int BLUE_DRAGON_5880 = 5880;
    public static final int BLUE_DRAGON_5881 = 5881;
    public static final int BLUE_DRAGON_5882 = 5882;
    public static final int ABYSSAL_ORPHAN = 5883;
    public static final int ABYSSAL_ORPHAN_5884 = 5884;
    public static final int ABYSSAL_SIRE = 5886;
    public static final int ABYSSAL_SIRE_5887 = 5887;
    public static final int ABYSSAL_SIRE_5888 = 5888;
    public static final int ABYSSAL_SIRE_5889 = 5889;
    public static final int ABYSSAL_SIRE_5890 = 5890;
    public static final int ABYSSAL_SIRE_5891 = 5891;
    public static final int TZREKJAD = 5892;
    public static final int TZREKJAD_5893 = 5893;
    public static final int BAST = 5894;
    public static final int DROGO_DWARF = 5895;
    public static final int FLYNN = 5896;
    public static final int WAYNE = 5897;
    public static final int DWARF_5904 = 5904;
    public static final int BETTY_5905 = 5905;
    public static final int PROBITA = 5906;
    public static final int CHAOS_ELEMENTAL_JR_5907 = 5907;
    public static final int ABYSSAL_SIRE_5908 = 5908;
    public static final int TENTACLE_5909 = 5909;
    public static final int TENTACLE_5910 = 5910;
    public static final int TENTACLE_5911 = 5911;
    public static final int TENTACLE_5912 = 5912;
    public static final int TENTACLE_5913 = 5913;
    public static final int RESPIRATORY_SYSTEM = 5914;
    public static final int VENT = 5915;
    public static final int SPAWN = 5916;
    public static final int SPAWN_5917 = 5917;
    public static final int SCION = 5918;
    public static final int GRACE = 5919;
    public static final int MARK = 5920;
    public static final int BIGREDJAPAN = 5921;
    public static final int SKULLBALL = 5922;
    public static final int SKULLBALL_BOSS = 5923;
    public static final int AGILITY_BOSS = 5924;
    public static final int SKULLBALL_TRAINER = 5925;
    public static final int AGILITY_TRAINER = 5926;
    public static final int AGILITY_TRAINER_5927 = 5927;
    public static final int WEREWOLF_5928 = 5928;
    public static final int KNIGHT_5929 = 5929;
    public static final int EGG_5932 = 5932;
    public static final int EGG_5933 = 5933;
    public static final int EGG_5934 = 5934;
    public static final int SAND_CRAB = 5935;
    public static final int SANDY_ROCKS = 5936;
    public static final int JARVALD = 5937;
    public static final int WALLASALKI = 5938;
    public static final int WALLASALKI_5939 = 5939;
    public static final int GIANT_ROCK_CRAB_5940 = 5940;
    public static final int BOULDER_5941 = 5941;
    public static final int DAGANNOTH_5942 = 5942;
    public static final int DAGANNOTH_5943 = 5943;
    public static final int ROCK_LOBSTER = 5944;
    public static final int ROCK_5945 = 5945;
    public static final int SUSPICIOUS_WATER = 5946;
    public static final int SPINOLYP = 5947;
    public static final int SUSPICIOUS_WATER_5948 = 5948;
    public static final int AL_THE_CAMEL = 5949;
    public static final int ELLY_THE_CAMEL = 5950;
    public static final int OLLIE_THE_CAMEL = 5951;
    public static final int CAM_THE_CAMEL = 5952;
    public static final int ALICE_THE_CAMEL = 5954;
    public static final int NEFERTI_THE_CAMEL = 5955;
    public static final int ALI_THE_LEAFLET_DROPPER = 5956;
    public static final int ALI_THE_SMITH = 5957;
    public static final int ALI_THE_FARMER = 5958;
    public static final int ALI_THE_TAILOR = 5959;
    public static final int ALI_THE_GUARD = 5960;
    public static final int SPINOLYP_5961 = 5961;
    public static final int SUSPICIOUS_WATER_5962 = 5962;
    public static final int SPINOLYP_5963 = 5963;
    public static final int KHAZARD_TROOPER_5964 = 5964;
    public static final int KHAZARD_TROOPER_5965 = 5965;
    public static final int GNOME_TROOP_5966 = 5966;
    public static final int GNOME_TROOP_5967 = 5967;
    public static final int GNOME_5968 = 5968;
    public static final int GNOME_5969 = 5969;
    public static final int GNOME_5970 = 5970;
    public static final int MOUNTED_TERRORBIRD_GNOME_5971 = 5971;
    public static final int MOUNTED_TERRORBIRD_GNOME_5972 = 5972;
    public static final int MOUNTED_TERRORBIRD_GNOME_5973 = 5973;
    public static final int SWEEPER = 5974;
    public static final int FLYING_BOOK = 5975;
    public static final int FLYING_BOOK_5976 = 5976;
    public static final int JUSTICIAR_ZACHARIAH = 5977;
    public static final int ENTRANCE_GUARDIAN = 5978;
    public static final int TELEKINETIC_GUARDIAN = 5979;
    public static final int ALCHEMY_GUARDIAN = 5980;
    public static final int ENCHANTMENT_GUARDIAN = 5981;
    public static final int GRAVEYARD_GUARDIAN = 5982;
    public static final int PET_ROCK = 5983;
    public static final int REWARDS_GUARDIAN = 5984;
    public static final int CHARMED_WARRIOR = 5985;
    public static final int CHARMED_WARRIOR_5986 = 5986;
    public static final int CHARMED_WARRIOR_5987 = 5987;
    public static final int CHARMED_WARRIOR_5988 = 5988;
    public static final int SECRETARY = 5989;
    public static final int PURPLE_PEWTER_SECRETARY = 5990;
    public static final int YELLOW_FORTUNE_SECRETARY = 5991;
    public static final int BLUE_OPAL_SECRETARY = 5992;
    public static final int GREEN_GEMSTONE_SECRETARY = 5993;
    public static final int WHITE_CHISEL_SECRETARY = 5994;
    public static final int SILVER_COG_SECRETARY = 5995;
    public static final int BROWN_ENGINE_SECRETARY = 5996;
    public static final int RED_AXE_SECRETARY = 5997;
    public static final int PURPLE_PEWTER_DIRECTOR_5998 = 5998;
    public static final int BLUE_OPAL_DIRECTOR_5999 = 5999;
    public static final int YELLOW_FORTUNE_DIRECTOR_6000 = 6000;
    public static final int ORLANDO_SMITH = 6001;
    public static final int NATURAL_HISTORIAN = 6002;
    public static final int NATURAL_HISTORIAN_6003 = 6003;
    public static final int NATURAL_HISTORIAN_6004 = 6004;
    public static final int NATURAL_HISTORIAN_6005 = 6005;
    public static final int NATURAL_HISTORIAN_6006 = 6006;
    public static final int LEECH_DISPLAY = 6007;
    public static final int SEA_SLUGS_DISPLAY = 6008;
    public static final int SNAIL_DISPLAY = 6009;
    public static final int MONKEY_DISPLAY = 6010;
    public static final int LIZARD_DISPLAY = 6011;
    public static final int PENGUIN_DISPLAY = 6012;
    public static final int CAMEL_DISPLAY = 6013;
    public static final int TERRORBIRD_DISPLAY = 6014;
    public static final int DRAGON_DISPLAY = 6015;
    public static final int WYVERN_DISPLAY = 6016;
    public static final int BATTLE_TORTOISE_DISPLAY = 6017;
    public static final int MOLE_DISPLAY = 6018;
    public static final int TORRCS = 6019;
    public static final int MARFET = 6020;
    public static final int GREEN_GEMSTONE_DIRECTOR_6021 = 6021;
    public static final int WHITE_CHISEL_DIRECTOR_6022 = 6022;
    public static final int SILVER_COG_DIRECTOR_6023 = 6023;
    public static final int BROWN_ENGINE_DIRECTOR_6024 = 6024;
    public static final int RED_AXE_DIRECTOR_6025 = 6025;
    public static final int RED_AXE_CAT_6026 = 6026;
    public static final int TRADER = 6027;
    public static final int TRADER_6028 = 6028;
    public static final int TRADER_6029 = 6029;
    public static final int TRADER_6030 = 6030;
    public static final int TRADER_6031 = 6031;
    public static final int TRADER_6032 = 6032;
    public static final int TRADER_6033 = 6033;
    public static final int TRADER_6034 = 6034;
    public static final int TRADER_6035 = 6035;
    public static final int TRADER_6036 = 6036;
    public static final int TRADER_6037 = 6037;
    public static final int TRADER_6038 = 6038;
    public static final int TRADER_6039 = 6039;
    public static final int TRADER_6040 = 6040;
    public static final int TRADER_6041 = 6041;
    public static final int TRADER_6042 = 6042;
    public static final int TRADE_REFEREE = 6043;
    public static final int SUPREME_COMMANDER = 6044;
    public static final int COMMANDER_VELDABAN_6045 = 6045;
    public static final int BLACK_GUARD_6046 = 6046;
    public static final int BLACK_GUARD_6047 = 6047;
    public static final int BLACK_GUARD_6048 = 6048;
    public static final int BLACK_GUARD_6049 = 6049;
    public static final int BLACK_GUARD_BERSERKER_6050 = 6050;
    public static final int BLACK_GUARD_BERSERKER_6051 = 6051;
    public static final int BLACK_GUARD_BERSERKER_6052 = 6052;
    public static final int GNOME_EMISSARY_6053 = 6053;
    public static final int GNOME_TRAVELLER = 6054;
    public static final int GNOME_TRAVELLER_6055 = 6055;
    public static final int GUARD_6056 = 6056;
    public static final int RANGING_GUILD_DOORMAN = 6057;
    public static final int LEATHERWORKER = 6058;
    public static final int ARMOUR_SALESMAN = 6059;
    public static final int BOW_AND_ARROW_SALESMAN = 6060;
    public static final int TOWER_ADVISOR = 6061;
    public static final int TOWER_ADVISOR_6062 = 6062;
    public static final int TOWER_ADVISOR_6063 = 6063;
    public static final int TOWER_ADVISOR_6064 = 6064;
    public static final int TOWER_ARCHER = 6065;
    public static final int TOWER_ARCHER_6066 = 6066;
    public static final int TOWER_ARCHER_6067 = 6067;
    public static final int TOWER_ARCHER_6068 = 6068;
    public static final int TRIBAL_WEAPON_SALESMAN = 6069;
    public static final int COMPETITION_JUDGE = 6070;
    public static final int TICKET_MERCHANT = 6071;
    public static final int JIMMY = 6072;
    public static final int REF = 6073;
    public static final int REF_6074 = 6074;
    public static final int TORTOISE = 6075;
    public static final int TORTOISE_6076 = 6076;
    public static final int GNOME_CHILD = 6077;
    public static final int GNOME_CHILD_6078 = 6078;
    public static final int GNOME_CHILD_6079 = 6079;
    public static final int GNOME_TRAINER = 6080;
    public static final int GNOME_GUARD_6081 = 6081;
    public static final int GNOME_GUARD_6082 = 6082;
    public static final int GNOME_SHOP_KEEPER_6083 = 6083;
    public static final int GNOME_BANKER = 6084;
    public static final int GNOME_BALLER_6085 = 6085;
    public static final int GNOME_WOMAN = 6086;
    public static final int GNOME_WOMAN_6087 = 6087;
    public static final int CAPTAIN_ERRDO = 6088;
    public static final int GNOME_6094 = 6094;
    public static final int GNOME_6095 = 6095;
    public static final int GNOME_6096 = 6096;
    public static final int GNOME_ARCHER = 6097;
    public static final int GNOME_DRIVER = 6098;
    public static final int GNOME_MAGE = 6099;
    public static final int LIEUTENANT_SCHEPBUR = 6100;
    public static final int TRAINER_NACKLEPEN = 6101;
    public static final int BUSH_SNAKE = 6102;
    public static final int BUSH_SNAKE_6103 = 6103;
    public static final int ELVARG_HARD = 6118;
    public static final int THE_INADEQUACY_HARD = 6119;
    public static final int THE_EVERLASTING_HARD = 6120;
    public static final int THE_UNTOUCHABLE_HARD = 6121;
    public static final int URIUM_SHADOW = 6143;
    public static final int SCION_6177 = 6177;
    public static final int JUNGLE_SPIDER_6267 = 6267;
    public static final int JUNGLE_SPIDER_6271 = 6271;
    public static final int LARGE_MOSQUITO = 6272;
    public static final int MOSQUITO_SWARM = 6273;
    public static final int TANGLEFOOT_HARD = 6291;
    public static final int CHRONOZON_HARD = 6292;
    public static final int BOUNCER_HARD = 6293;
    public static final int ICE_TROLL_KING_HARD = 6294;
    public static final int BLACK_DEMON_HARD = 6295;
    public static final int BLOODHOUND = 6296;
    public static final int GLOD_HARD = 6297;
    public static final int TREUS_DAYTH_HARD = 6298;
    public static final int BLACK_KNIGHT_TITAN_HARD = 6299;
    public static final int DAGANNOTH_MOTHER_HARD = 6300;
    public static final int DAGANNOTH_MOTHER_HARD_6301 = 6301;
    public static final int DAGANNOTH_MOTHER_HARD_6302 = 6302;
    public static final int DAGANNOTH_MOTHER_HARD_6303 = 6303;
    public static final int DAGANNOTH_MOTHER_HARD_6304 = 6304;
    public static final int DAGANNOTH_MOTHER_HARD_6305 = 6305;
    public static final int EVIL_CHICKEN_HARD = 6306;
    public static final int CULINAROMANCER_HARD = 6307;
    public static final int AGRITHNANA_HARD = 6308;
    public static final int FLAMBEED_HARD = 6309;
    public static final int KARAMEL_HARD = 6310;
    public static final int DESSOURT_HARD = 6311;
    public static final int GELATINNOTH_MOTHER_HARD = 6312;
    public static final int GELATINNOTH_MOTHER_HARD_6313 = 6313;
    public static final int GELATINNOTH_MOTHER_HARD_6314 = 6314;
    public static final int GELATINNOTH_MOTHER_HARD_6315 = 6315;
    public static final int GELATINNOTH_MOTHER_HARD_6316 = 6316;
    public static final int GELATINNOTH_MOTHER_HARD_6317 = 6317;
    public static final int NEZIKCHENED_HARD = 6318;
    public static final int TREE_SPIRIT_HARD = 6319;
    public static final int ME_HARD = 6320;
    public static final int JUNGLE_DEMON_HARD = 6321;
    public static final int THE_KENDAL_HARD = 6322;
    public static final int GIANT_ROC_HARD = 6323;
    public static final int SLAGILITH_HARD = 6324;
    public static final int MOSS_GUARDIAN_HARD = 6325;
    public static final int SKELETON_HELLHOUND_HARD = 6326;
    public static final int AGRITH_NAAR_HARD = 6327;
    public static final int KING_ROALD_HARD = 6328;
    public static final int KHAZARD_WARLORD_HARD = 6329;
    public static final int DAD_HARD = 6330;
    public static final int ARRG_HARD = 6331;
    public static final int COUNT_DRAYNOR_HARD = 6332;
    public static final int WITCHS_EXPERIMENT_HARD = 6333;
    public static final int WITCHS_EXPERIMENT_SECOND_FORM_HARD = 6334;
    public static final int WITCHS_EXPERIMENT_THIRD_FORM_HARD = 6335;
    public static final int WITCHS_EXPERIMENT_FOURTH_FORM_HARD = 6336;
    public static final int NAZASTAROOL_HARD = 6337;
    public static final int NAZASTAROOL_HARD_6338 = 6338;
    public static final int NAZASTAROOL_HARD_6339 = 6339;
    public static final int COW_HARD = 6340;
    public static final int ZAPPER = 6341;
    public static final int BARRELCHEST_6342 = 6342;
    public static final int GIANT_SCARAB_6343 = 6343;
    public static final int DESSOUS_6344 = 6344;
    public static final int KAMIL_6345 = 6345;
    public static final int DAMIS_6346 = 6346;
    public static final int DAMIS_6347 = 6347;
    public static final int FAREED_6348 = 6348;
    public static final int ELVARG_6349 = 6349;
    public static final int THE_INADEQUACY_6350 = 6350;
    public static final int THE_EVERLASTING_6351 = 6351;
    public static final int THE_UNTOUCHABLE_6352 = 6352;
    public static final int TANGLEFOOT_6353 = 6353;
    public static final int CHRONOZON_6354 = 6354;
    public static final int BOUNCER_6355 = 6355;
    public static final int ICE_TROLL_KING_6356 = 6356;
    public static final int BLACK_DEMON_6357 = 6357;
    public static final int GLOD_6358 = 6358;
    public static final int TREUS_DAYTH_6359 = 6359;
    public static final int BLACK_KNIGHT_TITAN_6360 = 6360;
    public static final int DAGANNOTH_MOTHER_6361 = 6361;
    public static final int DAGANNOTH_MOTHER_6362 = 6362;
    public static final int DAGANNOTH_MOTHER_6363 = 6363;
    public static final int DAGANNOTH_MOTHER_6364 = 6364;
    public static final int DAGANNOTH_MOTHER_6365 = 6365;
    public static final int DAGANNOTH_MOTHER_6366 = 6366;
    public static final int EVIL_CHICKEN_6367 = 6367;
    public static final int CULINAROMANCER_6368 = 6368;
    public static final int AGRITHNANA_6369 = 6369;
    public static final int FLAMBEED_6370 = 6370;
    public static final int KARAMEL_6371 = 6371;
    public static final int DESSOURT_6372 = 6372;
    public static final int GELATINNOTH_MOTHER_6373 = 6373;
    public static final int GELATINNOTH_MOTHER_6374 = 6374;
    public static final int GELATINNOTH_MOTHER_6375 = 6375;
    public static final int GELATINNOTH_MOTHER_6376 = 6376;
    public static final int GELATINNOTH_MOTHER_6377 = 6377;
    public static final int GELATINNOTH_MOTHER_6378 = 6378;
    public static final int NEZIKCHENED_6379 = 6379;
    public static final int TREE_SPIRIT_6380 = 6380;
    public static final int ME_6381 = 6381;
    public static final int JUNGLE_DEMON_6382 = 6382;
    public static final int THE_KENDAL_6383 = 6383;
    public static final int GIANT_ROC_6384 = 6384;
    public static final int SLAGILITH_6385 = 6385;
    public static final int MOSS_GUARDIAN_6386 = 6386;
    public static final int SKELETON_HELLHOUND_6387 = 6387;
    public static final int AGRITH_NAAR_6388 = 6388;
    public static final int KING_ROALD_6389 = 6389;
    public static final int KHAZARD_WARLORD = 6390;
    public static final int DAD_6391 = 6391;
    public static final int ARRG_6392 = 6392;
    public static final int COUNT_DRAYNOR_6393 = 6393;
    public static final int WITCHS_EXPERIMENT_6394 = 6394;
    public static final int WITCHS_EXPERIMENT_SECOND_FORM_6395 = 6395;
    public static final int WITCHS_EXPERIMENT_THIRD_FORM_6396 = 6396;
    public static final int WITCHS_EXPERIMENT_FOURTH_FORM_6397 = 6397;
    public static final int NAZASTAROOL_6398 = 6398;
    public static final int NAZASTAROOL_6399 = 6399;
    public static final int NAZASTAROOL_6400 = 6400;
    public static final int COW_6401 = 6401;
    public static final int MOSQUITO_SWARM_6402 = 6402;
    public static final int TRIBESMAN_6406 = 6406;
    public static final int TRIBESMAN_6407 = 6407;
    public static final int BROODOO_VICTIM = 6408;
    public static final int BROODOO_VICTIM_6409 = 6409;
    public static final int BROODOO_VICTIM_6410 = 6410;
    public static final int BROODOO_VICTIM_6411 = 6411;
    public static final int BROODOO_VICTIM_6412 = 6412;
    public static final int BROODOO_VICTIM_6413 = 6413;
    public static final int SHARIMIKA = 6414;
    public static final int SHARIMIKA_6415 = 6415;
    public static final int MAMMA_BUFETTA = 6416;
    public static final int MAMMA_BUFETTA_6417 = 6417;
    public static final int LAYLEEN = 6418;
    public static final int LAYLEEN_6419 = 6419;
    public static final int KARADAY = 6420;
    public static final int KARADAY_6421 = 6421;
    public static final int SAFTA_DOC = 6422;
    public static final int SAFTA_DOC_6423 = 6423;
    public static final int GABOOTY = 6424;
    public static final int GABOOTY_6425 = 6425;
    public static final int FANELLAMAN = 6426;
    public static final int FANELLAMAN_6427 = 6427;
    public static final int JAGBAKOBA_6428 = 6428;
    public static final int JAGBAKOBA_6429 = 6429;
    public static final int MURCAILY_6430 = 6430;
    public static final int MURCAILY_6431 = 6431;
    public static final int RIONASTA = 6432;
    public static final int RIONASTA_6433 = 6433;
    public static final int CAVE_GOBLIN_6434 = 6434;
    public static final int CAVE_GOBLIN_6435 = 6435;
    public static final int CAVE_GOBLIN_6436 = 6436;
    public static final int CAVE_GOBLIN_6437 = 6437;
    public static final int ANIMATED_STEEL_ARMOUR_6438 = 6438;
    public static final int AMY = 6439;
    public static final int GIANT_SKELETON_6440 = 6440;
    public static final int SKELETON_6441 = 6441;
    public static final int SKELETON_6442 = 6442;
    public static final int SKELETON_6443 = 6443;
    public static final int SKELETON_6444 = 6444;
    public static final int SKELETON_6445 = 6445;
    public static final int SKELETON_6446 = 6446;
    public static final int SKELETON_6447 = 6447;
    public static final int SKELETON_6448 = 6448;
    public static final int ZOMBIE_6449 = 6449;
    public static final int ZOMBIE_6450 = 6450;
    public static final int ZOMBIE_6451 = 6451;
    public static final int ZOMBIE_6452 = 6452;
    public static final int ZOMBIE_6453 = 6453;
    public static final int ZOMBIE_6454 = 6454;
    public static final int ZOMBIE_6455 = 6455;
    public static final int ZOMBIE_6456 = 6456;
    public static final int ZOMBIE_6457 = 6457;
    public static final int ZOMBIE_6458 = 6458;
    public static final int ZOMBIE_6459 = 6459;
    public static final int ZOMBIE_6460 = 6460;
    public static final int ZOMBIE_6461 = 6461;
    public static final int ZOMBIE_6462 = 6462;
    public static final int ZOMBIE_6463 = 6463;
    public static final int ZOMBIE_6464 = 6464;
    public static final int ZOMBIE_6465 = 6465;
    public static final int ZOMBIE_6466 = 6466;
    public static final int SKELETON_6467 = 6467;
    public static final int SKELETON_6468 = 6468;
    public static final int POSSESSED_PICKAXE_6469 = 6469;
    public static final int ANIMATED_SPADE = 6470;
    public static final int TERROR_DOG_STATUE = 6471;
    public static final int TERROR_DOG_STATUE_6472 = 6472;
    public static final int TERROR_DOG = 6473;
    public static final int TERROR_DOG_6474 = 6474;
    public static final int TARN = 6475;
    public static final int TARN_6476 = 6476;
    public static final int MUTANT_TARN = 6477;
    public static final int RUFUS_6478 = 6478;
    public static final int EYE = 6479;
    public static final int EYE_6480 = 6480;
    public static final int MAC = 6481;
    public static final int BOULDER_6482 = 6482;
    public static final int MONKEY_6483 = 6483;
    public static final int GELIN = 6484;
    public static final int FINANCIAL_SEER = 6485;
    public static final int FISHING_SPOT_6488 = 6488;
    public static final int KREEARRA_6492 = 6492;
    public static final int COMMANDER_ZILYANA_6493 = 6493;
    public static final int GENERAL_GRAARDOR_6494 = 6494;
    public static final int KRIL_TSUTSAROTH_6495 = 6495;
    public static final int DAGANNOTH_SUPREME_6496 = 6496;
    public static final int DAGANNOTH_PRIME_6497 = 6497;
    public static final int DAGANNOTH_REX_6498 = 6498;
    public static final int GIANT_MOLE_6499 = 6499;
    public static final int KALPHITE_QUEEN_6500 = 6500;
    public static final int KALPHITE_QUEEN_6501 = 6501;
    public static final int KING_BLACK_DRAGON_6502 = 6502;
    public static final int CALLISTO = 6503;
    public static final int VENENATIS = 6504;
    public static final int CHAOS_ELEMENTAL_6505 = 6505;
    public static final int TZTOKJAD_6506 = 6506;
    public static final int SQUIRE_6509 = 6509;
    public static final int FINANCIAL_WIZARD = 6510;
    public static final int MAGNUS_GRAM = 6512;
    public static final int MAGNUS_GRAM_6513 = 6513;
    public static final int FINANCIAL_WIZARD_6514 = 6514;
    public static final int FINANCIAL_WIZARD_6515 = 6515;
    public static final int FINANCIAL_WIZARD_6516 = 6516;
    public static final int FINANCIAL_WIZARD_6517 = 6517;
    public static final int FINANCIAL_WIZARD_6518 = 6518;
    public static final int FINANCIAL_WIZARD_6519 = 6519;
    public static final int BARKER = 6524;
    public static final int FIDELIO = 6525;
    public static final int SBOTT = 6526;
    public static final int ROAVAR = 6527;
    public static final int HERQUIN = 6529;
    public static final int ROMMIK = 6530;
    public static final int BLURBERRY = 6531;
    public static final int BARMAN_6532 = 6532;
    public static final int ROMILY_WEAKLAX = 6533;
    public static final int GUARD_6561 = 6561;
    public static final int PROSPECTOR_PERCY = 6562;
    public static final int PAYDIRT = 6564;
    public static final int MINER_6565 = 6565;
    public static final int RUNITE_MINOR = 6566;
    public static final int MINER_6567 = 6567;
    public static final int MINER_6568 = 6568;
    public static final int MINER_6569 = 6569;
    public static final int MINER_6570 = 6570;
    public static final int MINER_6571 = 6571;
    public static final int MINER_6572 = 6572;
    public static final int GNOME_GUARD_6574 = 6574;
    public static final int GUARD_6575 = 6575;
    public static final int GUARD_6576 = 6576;
    public static final int GUARD_6579 = 6579;
    public static final int GUARD_6580 = 6580;
    public static final int GUARD_6581 = 6581;
    public static final int GUARD_6582 = 6582;
    public static final int GUARD_6583 = 6583;
    public static final int URI_6584 = 6584;
    public static final int SHERLOCK = 6586;
    public static final int ARMADYLEAN_GUARD = 6587;
    public static final int BANDOSIAN_GUARD = 6588;
    public static final int DR_FORD = 6589;
    public static final int SISTER_SCAROPHIA = 6590;
    public static final int LAVA_DRAGON = 6593;
    public static final int ENT = 6594;
    public static final int PRIFDDINAS_GUARD = 6595;
    public static final int ZOMBIE_6596 = 6596;
    public static final int ZOMBIE_6597 = 6597;
    public static final int ZOMBIE_6598 = 6598;
    public static final int MANDRITH = 6599;
    public static final int RUNITE_GOLEM = 6600;
    public static final int ROCKS_6601 = 6601;
    public static final int NUMPTY = 6602;
    public static final int ROGUE_6603 = 6603;
    public static final int MAMMOTH = 6604;
    public static final int BANDIT_6605 = 6605;
    public static final int DARK_WARRIOR_6606 = 6606;
    public static final int ELDER_CHAOS_DRUID = 6607;
    public static final int ANKOU_6608 = 6608;
    public static final int CALLISTO_6609 = 6609;
    public static final int VENENATIS_6610 = 6610;
    public static final int VETION = 6611;
    public static final int VETION_REBORN = 6612;
    public static final int SKELETON_HELLHOUND_6613 = 6613;
    public static final int GREATER_SKELETON_HELLHOUND = 6614;
    public static final int SCORPIA = 6615;
    public static final int SCORPIAS_OFFSPRING_6616 = 6616;
    public static final int SCORPIAS_GUARDIAN = 6617;
    public static final int CRAZY_ARCHAEOLOGIST = 6618;
    public static final int CHAOS_FANATIC = 6619;
    public static final int MINIATURE_CHAOTIC_CLOUDS = 6620;
    public static final int BOULDER_6621 = 6621;
    public static final int ENERGY_SPRITE = 6624;
    public static final int REACHER_6625 = 6625;
    public static final int DAGANNOTH_SUPREME_JR = 6626;
    public static final int DAGANNOTH_PRIME_JR = 6627;
    public static final int DAGANNOTH_SUPREME_JR_6628 = 6628;
    public static final int DAGANNOTH_PRIME_JR_6629 = 6629;
    public static final int DAGANNOTH_REX_JR = 6630;
    public static final int KREEARRA_JR = 6631;
    public static final int GENERAL_GRAARDOR_JR = 6632;
    public static final int ZILYANA_JR = 6633;
    public static final int KRIL_TSUTSAROTH_JR = 6634;
    public static final int BABY_MOLE_6635 = 6635;
    public static final int PRINCE_BLACK_DRAGON = 6636;
    public static final int KALPHITE_PRINCESS = 6637;
    public static final int KALPHITE_PRINCESS_6638 = 6638;
    public static final int SMOKE_DEVIL_6639 = 6639;
    public static final int KRAKEN_6640 = 6640;
    public static final int DAGANNOTH_REX_JR_6641 = 6641;
    public static final int PENANCE_PET = 6642;
    public static final int KREEARRA_JR_6643 = 6643;
    public static final int GENERAL_GRAARDOR_JR_6644 = 6644;
    public static final int MINER_6645 = 6645;
    public static final int ZILYANA_JR_6646 = 6646;
    public static final int KRIL_TSUTSAROTH_JR_6647 = 6647;
    public static final int LOKAR_SEARUNNER_6648 = 6648;
    public static final int CAPTAIN_BENTLEY = 6649;
    public static final int CAPTAIN_BENTLEY_6650 = 6650;
    public static final int BABY_MOLE_6651 = 6651;
    public static final int PRINCE_BLACK_DRAGON_6652 = 6652;
    public static final int KALPHITE_PRINCESS_6653 = 6653;
    public static final int KALPHITE_PRINCESS_6654 = 6654;
    public static final int SMOKE_DEVIL_6655 = 6655;
    public static final int KRAKEN_6656 = 6656;
    public static final int PET_ROCK_6657 = 6657;
    public static final int FISHBOWL = 6658;
    public static final int FISHBOWL_6659 = 6659;
    public static final int FISHBOWL_6660 = 6660;
    public static final int CLOCKWORK_CAT_6661 = 6661;
    public static final int CAT_6662 = 6662;
    public static final int CAT_6663 = 6663;
    public static final int CAT_6664 = 6664;
    public static final int CAT_6665 = 6665;
    public static final int CAT_6666 = 6666;
    public static final int CAT_6667 = 6667;
    public static final int HELLCAT_6668 = 6668;
    public static final int JOHN = 6669;
    public static final int REACHER_6670 = 6670;
    public static final int JOHN_6671 = 6671;
    public static final int JOHN_6672 = 6672;
    public static final int REACHER_6673 = 6673;
    public static final int PENANCE_PET_6674 = 6674;
    public static final int WAYDAR_6675 = 6675;
    public static final int OVERGROWN_CAT_6676 = 6676;
    public static final int OVERGROWN_CAT_6677 = 6677;
    public static final int OVERGROWN_CAT_6678 = 6678;
    public static final int OVERGROWN_CAT_6679 = 6679;
    public static final int OVERGROWN_CAT_6680 = 6680;
    public static final int OVERGROWN_CAT_6681 = 6681;
    public static final int OVERGROWN_HELLCAT_6682 = 6682;
    public static final int LAZY_CAT_6683 = 6683;
    public static final int LAZY_CAT_6684 = 6684;
    public static final int LAZY_CAT_6685 = 6685;
    public static final int LAZY_CAT_6686 = 6686;
    public static final int LAZY_CAT_6687 = 6687;
    public static final int LAZY_CAT_6688 = 6688;
    public static final int LAZY_HELLCAT_6689 = 6689;
    public static final int WILY_CAT_6690 = 6690;
    public static final int WILY_CAT_6691 = 6691;
    public static final int WILY_CAT_6692 = 6692;
    public static final int WILY_CAT_6693 = 6693;
    public static final int WILY_CAT_6694 = 6694;
    public static final int WILY_CAT_6695 = 6695;
    public static final int WILY_HELLCAT_6696 = 6696;
    public static final int GHOST_GUARD_6698 = 6698;
    public static final int GUARD_6699 = 6699;
    public static final int GUARD_6700 = 6700;
    public static final int GUARD_6701 = 6701;
    public static final int GUARD_6702 = 6702;
    public static final int FINANCIAL_WIZARD_6703 = 6703;
    public static final int HERON = 6715;
    public static final int CHAOTIC_DEATH_SPAWN = 6716;
    public static final int BEAVER = 6717;
    public static final int BABY_CHINCHOMPA = 6718;
    public static final int BABY_CHINCHOMPA_6719 = 6719;
    public static final int BABY_CHINCHOMPA_6720 = 6720;
    public static final int BABY_CHINCHOMPA_6721 = 6721;
    public static final int HERON_6722 = 6722;
    public static final int CHAOTIC_DEATH_SPAWN_6723 = 6723;
    public static final int BEAVER_6724 = 6724;
    public static final int ROCK_GOLEM_6725 = 6725;
    public static final int ROCK_GOLEM_6726 = 6726;
    public static final int ROCK_GOLEM_6727 = 6727;
    public static final int ROCK_GOLEM_6728 = 6728;
    public static final int ROCK_GOLEM_6729 = 6729;
    public static final int ROCK_GOLEM_6730 = 6730;
    public static final int FISHING_SPOT_6731 = 6731;
    public static final int RIVER_TROLL = 6732;
    public static final int RIVER_TROLL_6733 = 6733;
    public static final int RIVER_TROLL_6734 = 6734;
    public static final int RIVER_TROLL_6735 = 6735;
    public static final int RIVER_TROLL_6736 = 6736;
    public static final int RIVER_TROLL_6737 = 6737;
    public static final int POSTIE_PETE_6738 = 6738;
    public static final int EVIL_CHICKEN_6739 = 6739;
    public static final int SHADE_6740 = 6740;
    public static final int ZOMBIE_6741 = 6741;
    public static final int MYSTERIOUS_OLD_MAN_6742 = 6742;
    public static final int SERGEANT_DAMIEN_6743 = 6743;
    public static final int FLIPPA_6744 = 6744;
    public static final int LEO = 6745;
    public static final int LEO_6746 = 6746;
    public static final int BEE_KEEPER_6747 = 6747;
    public static final int FREAKY_FORESTER_6748 = 6748;
    public static final int DUNCE_6749 = 6749;
    public static final int MYSTERIOUS_OLD_MAN_6750 = 6750;
    public static final int MYSTERIOUS_OLD_MAN_6751 = 6751;
    public static final int MYSTERIOUS_OLD_MAN_6752 = 6752;
    public static final int MYSTERIOUS_OLD_MAN_6753 = 6753;
    public static final int EVIL_BOB_6754 = 6754;
    public static final int QUIZ_MASTER_6755 = 6755;
    public static final int BABY_CHINCHOMPA_6756 = 6756;
    public static final int BABY_CHINCHOMPA_6757 = 6757;
    public static final int BABY_CHINCHOMPA_6758 = 6758;
    public static final int BABY_CHINCHOMPA_6759 = 6759;
    public static final int PYRELORD = 6762;
    public static final int LIZARDMAN_SHAMAN = 6766;
    public static final int LIZARDMAN_SHAMAN_6767 = 6767;
    public static final int SPAWN_6768 = 6768;
    public static final int OSTEN = 6769;
    public static final int ARCIS = 6770;
    public static final int DREW = 6771;
    public static final int LOVADA = 6772;
    public static final int DOOMSAYER = 6773;
    public static final int DOOMSAYER_6774 = 6774;
    public static final int GALLOW = 6775;
    public static final int MAN_6776 = 6776;
    public static final int MAZE_GUARDIAN = 6777;
    public static final int MAZE_GUARDIAN_6779 = 6779;
    public static final int PILIAR = 6780;
    public static final int SHAYDA = 6781;
    public static final int FISHING_SPOT_6784 = 6784;
    public static final int HOSA = 6785;
    public static final int HELLRAT_BEHEMOTH = 6793;
    public static final int MONKEY_ARCHER_6794 = 6794;
    public static final int PYRELORD_6795 = 6795;
    public static final int NIEVE = 6797;
    public static final int STEVE = 6798;
    public static final int STEVE_6799 = 6799;
    public static final int PIEVE = 6801;
    public static final int COMBAT_TEST = 6802;
    public static final int MANIACAL_MONKEY = 6803;
    public static final int KRUK_6804 = 6804;
    public static final int KRUK_6805 = 6805;
    public static final int ASSISTANT_LE_SMITH_6806 = 6806;
    public static final int MONKEY_GUARD_6811 = 6811;
    public static final int AWOWOGEI_6812 = 6812;
    public static final int MONKEY_ARCHER_6813 = 6813;
    public static final int LAMMY_LANGLE = 6814;
    public static final int MAN_6815 = 6815;
    public static final int GEE = 6816;
    public static final int GREAT_BLUE_HERON = 6817;
    public static final int MAN_6818 = 6818;
    public static final int TOWN_CRIER_6823 = 6823;
    public static final int GIANT_BAT_6824 = 6824;
    public static final int ROD_FISHING_SPOT_6825 = 6825;
    public static final int WOUNDED_SOLDIER = 6826;
    public static final int WOUNDED_SOLDIER_6827 = 6827;
    public static final int WOUNDED_SOLDIER_6828 = 6828;
    public static final int WOUNDED_SOLDIER_6829 = 6829;
    public static final int WOUNDED_SOLDIER_6830 = 6830;
    public static final int WOUNDED_SOLDIER_6831 = 6831;
    public static final int WOUNDED_SOLDIER_6832 = 6832;
    public static final int WOUNDED_SOLDIER_6833 = 6833;
    public static final int WOUNDED_SOLDIER_6834 = 6834;
    public static final int WOUNDED_SOLDIER_6835 = 6835;
    public static final int WOUNDED_SOLDIER_6836 = 6836;
    public static final int WOUNDED_SOLDIER_6837 = 6837;
    public static final int WOUNDED_SOLDIER_6838 = 6838;
    public static final int WOUNDED_SOLDIER_6839 = 6839;
    public static final int WOUNDED_SOLDIER_6840 = 6840;
    public static final int WOUNDED_SOLDIER_6841 = 6841;
    public static final int WOUNDED_SOLDIER_6842 = 6842;
    public static final int WOUNDED_SOLDIER_6843 = 6843;
    public static final int WOUNDED_SOLDIER_6844 = 6844;
    public static final int WOUNDED_SOLDIER_6845 = 6845;
    public static final int WOUNDED_SOLDIER_6846 = 6846;
    public static final int WOUNDED_SOLDIER_6847 = 6847;
    public static final int WOUNDED_SOLDIER_6848 = 6848;
    public static final int WOUNDED_SOLDIER_6849 = 6849;
    public static final int WOUNDED_SOLDIER_6850 = 6850;
    public static final int WOUNDED_SOLDIER_6851 = 6851;
    public static final int WOUNDED_SOLDIER_6852 = 6852;
    public static final int WOUNDED_SOLDIER_6853 = 6853;
    public static final int WOUNDED_SOLDIER_6854 = 6854;
    public static final int WOUNDED_SOLDIER_6855 = 6855;
    public static final int WOUNDED_SOLDIER_6856 = 6856;
    public static final int WOUNDED_SOLDIER_6857 = 6857;
    public static final int LOOKOUT = 6858;
    public static final int BANKER_6859 = 6859;
    public static final int BANKER_6860 = 6860;
    public static final int BANKER_6861 = 6861;
    public static final int BANKER_6862 = 6862;
    public static final int BANKER_6863 = 6863;
    public static final int BANKER_6864 = 6864;
    public static final int GENERAL_SALARA = 6865;
    public static final int GENERAL_BABACUS = 6866;
    public static final int GENERAL_KILIAN = 6867;
    public static final int SOLDIER_6868 = 6868;
    public static final int SOLDIER_6869 = 6869;
    public static final int SOLDIER_6870 = 6870;
    public static final int SOLDIER_6871 = 6871;
    public static final int NEW_RECRUIT_TONY = 6872;
    public static final int NURSE_WOONED = 6873;
    public static final int NURSE_INNJUREE = 6874;
    public static final int NURSE_BOUBOU = 6875;
    public static final int CAPTAIN_RACHELLE = 6876;
    public static final int SOLDIER_6877 = 6877;
    public static final int SOLDIER_6878 = 6878;
    public static final int SOLDIER_6879 = 6879;
    public static final int SOLDIER_6880 = 6880;
    public static final int RANGER_6881 = 6881;
    public static final int DRILL_SERGEANT = 6882;
    public static final int SOLDIER_6883 = 6883;
    public static final int SOLDIER_6884 = 6884;
    public static final int SOLDIER_6885 = 6885;
    public static final int SOLDIER_6886 = 6886;
    public static final int SOLDIER_6887 = 6887;
    public static final int SOLDIER_6888 = 6888;
    public static final int SOLDIER_6889 = 6889;
    public static final int SOLDIER_6890 = 6890;
    public static final int SOLDIER_6891 = 6891;
    public static final int SOLDIER_6892 = 6892;
    public static final int SOLDIER_6893 = 6893;
    public static final int SOLDIER_6894 = 6894;
    public static final int CAPTAIN_GINEA = 6895;
    public static final int GANGSTER = 6896;
    public static final int GANGSTER_6897 = 6897;
    public static final int GANGSTER_6898 = 6898;
    public static final int GANGSTER_6899 = 6899;
    public static final int GANG_BOSS = 6900;
    public static final int GANG_BOSS_6901 = 6901;
    public static final int GANG_BOSS_6902 = 6902;
    public static final int GANG_BOSS_6903 = 6903;
    public static final int SOLDIER_TIER_1 = 6904;
    public static final int SOLDIER_TIER_1_6905 = 6905;
    public static final int SOLDIER_TIER_2 = 6906;
    public static final int SOLDIER_TIER_2_6907 = 6907;
    public static final int SOLDIER_TIER_3 = 6908;
    public static final int SOLDIER_TIER_3_6909 = 6909;
    public static final int SOLDIER_TIER_4 = 6910;
    public static final int SOLDIER_TIER_4_6911 = 6911;
    public static final int SOLDIER_TIER_5 = 6912;
    public static final int SOLDIER_TIER_5_6913 = 6913;
    public static final int LIZARDMAN = 6914;
    public static final int LIZARDMAN_6915 = 6915;
    public static final int LIZARDMAN_6916 = 6916;
    public static final int LIZARDMAN_6917 = 6917;
    public static final int LIZARDMAN_BRUTE = 6918;
    public static final int LIZARDMAN_BRUTE_6919 = 6919;
    public static final int FARMER_GRICOLLER = 6920;
    public static final int MARISI = 6921;
    public static final int KONOO = 6922;
    public static final int CLERK_6923 = 6923;
    public static final int PLOUGH = 6924;
    public static final int PLOUGH_6925 = 6925;
    public static final int EWESEY = 6926;
    public static final int SERVERY_ASSISTANT = 6927;
    public static final int SERVERY_ASSISTANT_6928 = 6928;
    public static final int SOLDIER_6929 = 6929;
    public static final int SOLDIER_6930 = 6930;
    public static final int SOLDIER_6931 = 6931;
    public static final int SOLDIER_6932 = 6932;
    public static final int SOLDIER_6933 = 6933;
    public static final int SOLDIER_6934 = 6934;
    public static final int SOLDIER_6935 = 6935;
    public static final int SOLDIER_6936 = 6936;
    public static final int RAMOCEAN = 6937;
    public static final int TALIA = 6938;
    public static final int BANKER_6939 = 6939;
    public static final int BANKER_6940 = 6940;
    public static final int BANKER_6941 = 6941;
    public static final int BANKER_6942 = 6942;
    public static final int HORACE = 6943;
    public static final int VANNAH = 6944;
    public static final int LOGAVA = 6945;
    public static final int FARMER_6947 = 6947;
    public static final int FARMER_6948 = 6948;
    public static final int FARMER_6949 = 6949;
    public static final int FARMER_6950 = 6950;
    public static final int FARMER_6951 = 6951;
    public static final int FARMER_6952 = 6952;
    public static final int GOLOVA = 6953;
    public static final int RICHARD_6954 = 6954;
    public static final int FATHER_JEAN = 6955;
    public static final int MONK_6956 = 6956;
    public static final int CHIEF_FARMER = 6957;
    public static final int FARMERS_WIFE = 6958;
    public static final int FARMER_6959 = 6959;
    public static final int FARMER_6960 = 6960;
    public static final int FARMER_6961 = 6961;
    public static final int FARMER_HAYFIELD = 6962;
    public static final int FRANKIE = 6963;
    public static final int TYNAN = 6964;
    public static final int NICHOLAS = 6965;
    public static final int DOCKMASTER = 6966;
    public static final int DOCK_WORKER = 6967;
    public static final int DOCK_WORKER_6968 = 6968;
    public static final int BANKER_6969 = 6969;
    public static final int BANKER_6970 = 6970;
    public static final int CAPTAIN_KHALED = 6971;
    public static final int CAPTAIN_KHALED_6972 = 6972;
    public static final int PATROLMAN = 6973;
    public static final int PATROLMAN_6974 = 6974;
    public static final int PATROLMAN_6975 = 6975;
    public static final int PATROLWOMAN = 6976;
    public static final int PATROLMAN_6977 = 6977;
    public static final int PATROLWOMAN_6978 = 6978;
    public static final int PATROLMAN_6979 = 6979;
    public static final int PATROLMAN_6980 = 6980;
    public static final int FISHERMAN_6981 = 6981;
    public static final int POOR_LOOKING_MAN = 6982;
    public static final int POOR_LOOKING_MAN_6983 = 6983;
    public static final int POOR_LOOKING_WOMAN = 6984;
    public static final int POOR_LOOKING_WOMAN_6985 = 6985;
    public static final int LEENZ = 6986;
    public static final int MAN_6987 = 6987;
    public static final int MAN_6988 = 6988;
    public static final int MAN_6989 = 6989;
    public static final int WOMAN_6990 = 6990;
    public static final int WOMAN_6991 = 6991;
    public static final int WOMAN_6992 = 6992;
    public static final int PIRATE_6993 = 6993;
    public static final int PIRATE_6994 = 6994;
    public static final int PIRATE_6995 = 6995;
    public static final int MUGGER_6996 = 6996;
    public static final int DARK_WIZARD_6997 = 6997;
    public static final int PIRATE_6998 = 6998;
    public static final int PORT_OFFICIAL = 6999;
    public static final int CAPTAIN_JANAWAY = 7000;
    public static final int PORT_WORKER = 7001;
    public static final int MARK_7002 = 7002;
    public static final int CHERYL = 7003;
    public static final int CHARLES = 7004;
    public static final int SARAH_7005 = 7005;
    public static final int DARREN_7006 = 7006;
    public static final int MELVIN = 7007;
    public static final int SIMON_7008 = 7008;
    public static final int ANDREA = 7009;
    public static final int ELIZABETH_7010 = 7010;
    public static final int LORRAINE = 7011;
    public static final int ROSS_AND_BEN = 7012;
    public static final int DOBWINKLE = 7013;
    public static final int ALEXANDER = 7014;
    public static final int CHARLIE_BROWN = 7015;
    public static final int REANIMATED_GOBLIN = 7018;
    public static final int REANIMATED_MONKEY = 7019;
    public static final int REANIMATED_IMP = 7020;
    public static final int REANIMATED_MINOTAUR = 7021;
    public static final int REANIMATED_SCORPION = 7022;
    public static final int REANIMATED_BEAR = 7023;
    public static final int REANIMATED_UNICORN = 7024;
    public static final int REANIMATED_DOG = 7025;
    public static final int REANIMATED_CHAOS_DRUID = 7026;
    public static final int REANIMATED_GIANT = 7027;
    public static final int REANIMATED_OGRE = 7028;
    public static final int REANIMATED_ELF = 7029;
    public static final int REANIMATED_TROLL = 7030;
    public static final int REANIMATED_HORROR = 7031;
    public static final int REANIMATED_KALPHITE = 7032;
    public static final int REANIMATED_DAGANNOTH = 7033;
    public static final int REANIMATED_BLOODVELD = 7034;
    public static final int REANIMATED_TZHAAR = 7035;
    public static final int REANIMATED_DEMON = 7036;
    public static final int REANIMATED_AVIANSIE = 7037;
    public static final int REANIMATED_ABYSSAL = 7038;
    public static final int REANIMATED_DRAGON = 7039;
    public static final int CLERRIS = 7040;
    public static final int ENOCH = 7041;
    public static final int ARETHA = 7042;
    public static final int SISTER_SOUL_JAR = 7043;
    public static final int LOGOSIA = 7044;
    public static final int BIBLIA = 7045;
    public static final int HORPHIS = 7046;
    public static final int VILLIA = 7047;
    public static final int PROFESSOR_GRACKLEBONE = 7048;
    public static final int SAM_7049 = 7049;
    public static final int TYSS = 7050;
    public static final int TROSSA = 7051;
    public static final int TOWER_MAGE = 7052;
    public static final int RASSAIN = 7053;
    public static final int MOFINA = 7054;
    public static final int NOVICE = 7055;
    public static final int REGATH = 7056;
    public static final int BANKER_7057 = 7057;
    public static final int BANKER_7058 = 7058;
    public static final int BANKER_7059 = 7059;
    public static final int BANKER_7060 = 7060;
    public static final int BATT_MELLAMY = 7061;
    public static final int FREALD = 7062;
    public static final int COB = 7063;
    public static final int DARK_WIZARD_7064 = 7064;
    public static final int DARK_WIZARD_7065 = 7065;
    public static final int WIZARD_7066 = 7066;
    public static final int WIZARD_7067 = 7067;
    public static final int OUDITOR = 7068;
    public static final int SMOGGY = 7069;
    public static final int TOOTHY = 7071;
    public static final int OPERATOR = 7072;
    public static final int OPERATOR_7073 = 7073;
    public static final int BLASTED_ORE = 7074;
    public static final int MINE_SUPERVISOR = 7075;
    public static final int MINE_SUPERVISOR_7076 = 7076;
    public static final int BANKER_7077 = 7077;
    public static final int BANKER_7078 = 7078;
    public static final int BANKER_7079 = 7079;
    public static final int BANKER_7080 = 7080;
    public static final int BANKER_7081 = 7081;
    public static final int BANKER_7082 = 7082;
    public static final int ARMOURER_TIER_1 = 7083;
    public static final int ARMOURER_TIER_2 = 7084;
    public static final int ARMOURER_TIER_3 = 7085;
    public static final int ARMOURER_TIER_4 = 7086;
    public static final int ARMOURER_TIER_5 = 7087;
    public static final int MUNTY = 7088;
    public static final int MOGGY = 7089;
    public static final int FUGGY = 7090;
    public static final int MINER_7091 = 7091;
    public static final int MINER_7092 = 7092;
    public static final int MINER_7093 = 7093;
    public static final int MINER_7094 = 7094;
    public static final int TORTURED_GORILLA = 7095;
    public static final int TORTURED_GORILLA_7096 = 7096;
    public static final int TORTURED_GORILLA_7097 = 7097;
    public static final int STUNTED_DEMONIC_GORILLA = 7098;
    public static final int KRUK_7099 = 7099;
    public static final int GLOUGH_7100 = 7100;
    public static final int GLOUGH_7101 = 7101;
    public static final int GLOUGH_7102 = 7102;
    public static final int GLOUGH_7103 = 7103;
    public static final int KEEF = 7104;
    public static final int KEEF_7105 = 7105;
    public static final int KOB = 7106;
    public static final int KOB_7107 = 7107;
    public static final int NIEVE_7108 = 7108;
    public static final int NIEVE_7109 = 7109;
    public static final int NIEVE_7110 = 7110;
    public static final int GARKOR_7111 = 7111;
    public static final int LUMO_7112 = 7112;
    public static final int ZOOKNOCK_7113 = 7113;
    public static final int CARADO_7114 = 7114;
    public static final int GLOUGH_7115 = 7115;
    public static final int LOOMING_SHADOW = 7116;
    public static final int KINEER = 7117;
    public static final int MANIACAL_MONKEY_7118 = 7118;
    public static final int MANIACAL_MONKEY_ARCHER = 7119;
    public static final int OOBAPOHK = 7120;
    public static final int JUMAANE = 7121;
    public static final int MONKEY_GUARD_7122 = 7122;
    public static final int MONKEY_GUARD_7123 = 7123;
    public static final int MONKEY_GUARD_7124 = 7124;
    public static final int MONKEY_GUARD_7125 = 7125;
    public static final int MONKEY_GUARD_7126 = 7126;
    public static final int MONKEY_GUARD_7127 = 7127;
    public static final int MONKEY_GUARD_7128 = 7128;
    public static final int MONKEY_GUARD_7129 = 7129;
    public static final int MONKEY_GUARD_7130 = 7130;
    public static final int MONKEY_GUARD_7131 = 7131;
    public static final int MONKEY_GUARD_7132 = 7132;
    public static final int MONKEY_GUARD_7133 = 7133;
    public static final int MONKEY_GUARD_7134 = 7134;
    public static final int MONKEY_GUARD_7135 = 7135;
    public static final int MONKEY_GUARD_7136 = 7136;
    public static final int MONKEY_GUARD_7137 = 7137;
    public static final int MONKEY_GUARD_7138 = 7138;
    public static final int MONKEY_GUARD_7139 = 7139;
    public static final int MONKEY_GUARD_7140 = 7140;
    public static final int MONKEY_GUARD_7141 = 7141;
    public static final int MONKEY_GUARD_7142 = 7142;
    public static final int MONKEY_GUARD_7143 = 7143;
    public static final int DEMONIC_GORILLA = 7144;
    public static final int DEMONIC_GORILLA_7145 = 7145;
    public static final int DEMONIC_GORILLA_7146 = 7146;
    public static final int DEMONIC_GORILLA_7147 = 7147;
    public static final int DEMONIC_GORILLA_7148 = 7148;
    public static final int DEMONIC_GORILLA_7149 = 7149;
    public static final int TORTURED_GORILLA_7150 = 7150;
    public static final int TORTURED_GORILLA_7151 = 7151;
    public static final int DEMONIC_GORILLA_7152 = 7152;
    public static final int TORTURED_GORILLA_7153 = 7153;
    public static final int ASSISTANT_LORI = 7154;
    public static final int FISHING_SPOT_7155 = 7155;
    public static final int ANITA = 7156;
    public static final int ANITA_7157 = 7157;
    public static final int GARKOR_7158 = 7158;
    public static final int GARKOR_7159 = 7159;
    public static final int LUMO_7160 = 7160;
    public static final int LUMO_7161 = 7161;
    public static final int BUNKDO_7162 = 7162;
    public static final int BUNKDO_7163 = 7163;
    public static final int CARADO_7164 = 7164;
    public static final int CARADO_7165 = 7165;
    public static final int BUNKWICKET_7166 = 7166;
    public static final int BUNKWICKET_7167 = 7167;
    public static final int WAYMOTTIN_7168 = 7168;
    public static final int WAYMOTTIN_7169 = 7169;
    public static final int ZOOKNOCK_7170 = 7170;
    public static final int ZOOKNOCK_7171 = 7171;
    public static final int KARAM_7172 = 7172;
    public static final int KARAM_7173 = 7173;
    public static final int KARAM_7174 = 7174;
    public static final int DUKE_7176 = 7176;
    public static final int CAPTAIN_SHORACKS = 7178;
    public static final int FISHING_SPOT_7199 = 7199;
    public static final int FISHING_SPOT_7200 = 7200;
    public static final int BRIGET = 7201;
    public static final int KENELME = 7202;
    public static final int THYRIA = 7203;
    public static final int FILAMINA = 7204;
    public static final int JARVALD_7205 = 7205;
    public static final int SAND_CRAB_7206 = 7206;
    public static final int SANDY_ROCKS_7207 = 7207;
    public static final int GUARD_DOG_7209 = 7209;
    public static final int MANIACAL_MONKEY_7212 = 7212;
    public static final int MANIACAL_MONKEY_7213 = 7213;
    public static final int MANIACAL_MONKEY_7214 = 7214;
    public static final int MANIACAL_MONKEY_7215 = 7215;
    public static final int MANIACAL_MONKEY_7216 = 7216;
    public static final int MIRIAM = 7217;
    public static final int MIRIAM_7218 = 7218;
    public static final int TRAXI = 7219;
    public static final int TRAXI_7220 = 7220;
    public static final int RAELI = 7221;
    public static final int RAELI_7222 = 7222;
    public static final int MOGRIM = 7223;
    public static final int MOGRIM_7224 = 7224;
    public static final int LOINUR = 7225;
    public static final int LOINUR_7226 = 7226;
    public static final int BLOODHOUND_7232 = 7232;
    public static final int LUCKY_IMPLING = 7233;
    public static final int ENT_7234 = 7234;
    public static final int BERRY_7235 = 7235;
    public static final int GUILDMASTER_LARS = 7236;
    public static final int MURFET = 7237;
    public static final int FORESTER_7238 = 7238;
    public static final int KAI = 7239;
    public static final int PERRY = 7240;
    public static final int ABYSSAL_DEMON_7241 = 7241;
    public static final int BLACK_DEMON_7242 = 7242;
    public static final int BLACK_DEMON_7243 = 7243;
    public static final int GREATER_DEMON_7244 = 7244;
    public static final int GREATER_DEMON_7245 = 7245;
    public static final int GREATER_DEMON_7246 = 7246;
    public static final int LESSER_DEMON_7247 = 7247;
    public static final int LESSER_DEMON_7248 = 7248;
    public static final int DUST_DEVIL_7249 = 7249;
    public static final int DARK_BEAST_7250 = 7250;
    public static final int FIRE_GIANT_7251 = 7251;
    public static final int FIRE_GIANT_7252 = 7252;
    public static final int BRONZE_DRAGON_7253 = 7253;
    public static final int IRON_DRAGON_7254 = 7254;
    public static final int STEEL_DRAGON_7255 = 7255;
    public static final int HELLHOUND_7256 = 7256;
    public static final int ANKOU_7257 = 7257;
    public static final int SHADE_7258 = 7258;
    public static final int DAGANNOTH_7259 = 7259;
    public static final int DAGANNOTH_7260 = 7260;
    public static final int HILL_GIANT_7261 = 7261;
    public static final int MOSS_GIANT_7262 = 7262;
    public static final int GHOST_7263 = 7263;
    public static final int GHOST_7264 = 7264;
    public static final int SKELETON_7265 = 7265;
    public static final int KING_SAND_CRAB = 7266;
    public static final int SANDY_BOULDER = 7267;
    public static final int POSSESSED_PICKAXE_7268 = 7268;
    public static final int MAGIC_AXE_7269 = 7269;
    public static final int CYCLOPS_7270 = 7270;
    public static final int CYCLOPS_7271 = 7271;
    public static final int TWISTED_BANSHEE = 7272;
    public static final int BRUTAL_BLUE_DRAGON = 7273;
    public static final int BRUTAL_RED_DRAGON = 7274;
    public static final int BRUTAL_BLACK_DRAGON = 7275;
    public static final int MUTATED_BLOODVELD = 7276;
    public static final int WARPED_JELLY = 7277;
    public static final int GREATER_NECHRYAEL = 7278;
    public static final int DEVIANT_SPECTRE = 7279;
    public static final int KINEER_7280 = 7280;
    public static final int MAN_7281 = 7281;
    public static final int PIRATE_7282 = 7282;
    public static final int LILLIA = 7283;
    public static final int GERTRUDE = 7284;
    public static final int BARBARIAN_GUARD_7285 = 7285;
    public static final int SKOTIZO = 7286;
    public static final int REANIMATED_DEMON_SPAWN = 7287;
    public static final int AWAKENED_ALTAR = 7288;
    public static final int ALTAR = 7289;
    public static final int AWAKENED_ALTAR_7290 = 7290;
    public static final int ALTAR_7291 = 7291;
    public static final int AWAKENED_ALTAR_7292 = 7292;
    public static final int ALTAR_7293 = 7293;
    public static final int AWAKENED_ALTAR_7294 = 7294;
    public static final int ALTAR_7295 = 7295;
    public static final int DARK_ANKOU = 7296;
    public static final int MISTAG_7297 = 7297;
    public static final int MISTAG_7298 = 7298;
    public static final int MISTAG_7299 = 7299;
    public static final int KAZGAR = 7300;
    public static final int KAZGAR_7301 = 7301;
    public static final int LUCKY_IMPLING_7302 = 7302;
    public static final int WATSON = 7303;
    public static final int WATSON_7304 = 7304;
    public static final int GRUFF_MCSCRUFF = 7305;
    public static final int FALO_THE_BARD = 7306;
    public static final int ANCIENT_WIZARD = 7307;
    public static final int ANCIENT_WIZARD_7308 = 7308;
    public static final int ANCIENT_WIZARD_7309 = 7309;
    public static final int BRASSICAN_MAGE = 7310;
    public static final int URI_7311 = 7311;
    public static final int DOUBLE_AGENT_7312 = 7312;
    public static final int LISA = 7316;
    public static final int LISA_7317 = 7317;
    public static final int NESTY = 7321;
    public static final int FISHING_SPOT_7323 = 7323;
    public static final int HOLGART_7324 = 7324;
    public static final int RICK_7327 = 7327;
    public static final int MAID_7328 = 7328;
    public static final int COOK_7329 = 7329;
    public static final int BUTLER_7330 = 7330;
    public static final int DEMON_BUTLER_7331 = 7331;
    public static final int FAIRY_FIXIT = 7332;
    public static final int FAIRY_FIXIT_7333 = 7333;
    public static final int GIANT_SQUIRREL = 7334;
    public static final int TANGLEROOT = 7335;
    public static final int ROCKY = 7336;
    public static final int RIFT_GUARDIAN = 7337;
    public static final int RIFT_GUARDIAN_7338 = 7338;
    public static final int RIFT_GUARDIAN_7339 = 7339;
    public static final int RIFT_GUARDIAN_7340 = 7340;
    public static final int RIFT_GUARDIAN_7341 = 7341;
    public static final int RIFT_GUARDIAN_7342 = 7342;
    public static final int RIFT_GUARDIAN_7343 = 7343;
    public static final int RIFT_GUARDIAN_7344 = 7344;
    public static final int RIFT_GUARDIAN_7345 = 7345;
    public static final int RIFT_GUARDIAN_7346 = 7346;
    public static final int RIFT_GUARDIAN_7347 = 7347;
    public static final int RIFT_GUARDIAN_7348 = 7348;
    public static final int RIFT_GUARDIAN_7349 = 7349;
    public static final int RIFT_GUARDIAN_7350 = 7350;
    public static final int GIANT_SQUIRREL_7351 = 7351;
    public static final int TANGLEROOT_7352 = 7352;
    public static final int ROCKY_7353 = 7353;
    public static final int RIFT_GUARDIAN_7354 = 7354;
    public static final int RIFT_GUARDIAN_7355 = 7355;
    public static final int RIFT_GUARDIAN_7356 = 7356;
    public static final int RIFT_GUARDIAN_7357 = 7357;
    public static final int RIFT_GUARDIAN_7358 = 7358;
    public static final int RIFT_GUARDIAN_7359 = 7359;
    public static final int RIFT_GUARDIAN_7360 = 7360;
    public static final int RIFT_GUARDIAN_7361 = 7361;
    public static final int RIFT_GUARDIAN_7362 = 7362;
    public static final int RIFT_GUARDIAN_7363 = 7363;
    public static final int RIFT_GUARDIAN_7364 = 7364;
    public static final int RIFT_GUARDIAN_7365 = 7365;
    public static final int RIFT_GUARDIAN_7366 = 7366;
    public static final int RIFT_GUARDIAN_7367 = 7367;
    public static final int PHOENIX_7368 = 7368;
    public static final int WESLEY = 7369;
    public static final int PHOENIX_7370 = 7370;
    public static final int PYROMANCER = 7371;
    public static final int INCAPACITATED_PYROMANCER = 7372;
    public static final int IGNISIA = 7374;
    public static final int ESTHER = 7376;
    public static final int CAPTAIN_KALT = 7377;
    public static final int ISH_THE_NAVIGATOR = 7378;
    public static final int WINTER_SOLDIER = 7379;
    public static final int CAT_7380 = 7380;
    public static final int WINTERTOAD = 7381;
    public static final int SNOW = 7383;
    public static final int STUMPY = 7384;
    public static final int PUMPY = 7385;
    public static final int DUMPY = 7386;
    public static final int DUMPY_7387 = 7387;
    public static final int CRUSHING_HAND = 7388;
    public static final int CHASM_CRAWLER = 7389;
    public static final int SCREAMING_BANSHEE = 7390;
    public static final int SCREAMING_TWISTED_BANSHEE = 7391;
    public static final int GIANT_ROCKSLUG = 7392;
    public static final int COCKATHRICE = 7393;
    public static final int FLAMING_PYRELORD = 7394;
    public static final int MONSTROUS_BASILISK = 7395;
    public static final int MALEVOLENT_MAGE = 7396;
    public static final int INSATIABLE_BLOODVELD = 7397;
    public static final int INSATIABLE_MUTATED_BLOODVELD = 7398;
    public static final int VITREOUS_JELLY = 7399;
    public static final int VITREOUS_WARPED_JELLY = 7400;
    public static final int CAVE_ABOMINATION = 7401;
    public static final int ABHORRENT_SPECTRE = 7402;
    public static final int REPUGNANT_SPECTRE = 7403;
    public static final int CHOKE_DEVIL = 7404;
    public static final int KING_KURASK = 7405;
    public static final int NUCLEAR_SMOKE_DEVIL = 7406;
    public static final int MARBLE_GARGOYLE = 7407;
    public static final int MARBLE_GARGOYLE_7408 = 7408;
    public static final int NIGHT_BEAST = 7409;
    public static final int GREATER_ABYSSAL_DEMON = 7410;
    public static final int NECHRYARCH = 7411;
    public static final int LIEVE_MCCRACKEN = 7412;
    public static final int UNDEAD_COMBAT_DUMMY = 7413;
    public static final int COUNT_CHECK = 7414;
    public static final int BOLOGA = 7415;
    public static final int OBOR = 7416;
    public static final int AMY_7417 = 7417;
    public static final int ZAMORAK_WARRIOR = 7418;
    public static final int ZAMORAK_WARRIOR_7419 = 7419;
    public static final int ZAMORAK_RANGER = 7420;
    public static final int ZAMORAK_RANGER_7421 = 7421;
    public static final int ZAMORAK_MAGE = 7422;
    public static final int ZAMORAK_MAGE_7423 = 7423;
    public static final int CAVE_LIZARD = 7424;
    public static final int MAGE_OF_ZAMORAK_7425 = 7425;
    public static final int ZAMORAK_CRAFTER = 7426;
    public static final int ZAMORAK_CRAFTER_7427 = 7427;
    public static final int GUARD_7437 = 7437;
    public static final int GUARD_7438 = 7438;
    public static final int ROCK_GOLEM_7439 = 7439;
    public static final int ROCK_GOLEM_7440 = 7440;
    public static final int ROCK_GOLEM_7441 = 7441;
    public static final int ROCK_GOLEM_7442 = 7442;
    public static final int ROCK_GOLEM_7443 = 7443;
    public static final int ROCK_GOLEM_7444 = 7444;
    public static final int ROCK_GOLEM_7445 = 7445;
    public static final int ROCK_GOLEM_7446 = 7446;
    public static final int ROCK_GOLEM_7447 = 7447;
    public static final int ROCK_GOLEM_7448 = 7448;
    public static final int ROCK_GOLEM_7449 = 7449;
    public static final int ROCK_GOLEM_7450 = 7450;
    public static final int ROCK_GOLEM_7451 = 7451;
    public static final int ROCK_GOLEM_7452 = 7452;
    public static final int ROCK_GOLEM_7453 = 7453;
    public static final int ROCK_GOLEM_7454 = 7454;
    public static final int ROCK_GOLEM_7455 = 7455;
    public static final int PERDU = 7456;
    public static final int FISHING_SPOT_7459 = 7459;
    public static final int FISHING_SPOT_7460 = 7460;
    public static final int FISHING_SPOT_7461 = 7461;
    public static final int FISHING_SPOT_7462 = 7462;
    public static final int ROD_FISHING_SPOT_7463 = 7463;
    public static final int ROD_FISHING_SPOT_7464 = 7464;
    public static final int FISHING_SPOT_7465 = 7465;
    public static final int FISHING_SPOT_7466 = 7466;
    public static final int FISHING_SPOT_7467 = 7467;
    public static final int ROD_FISHING_SPOT_7468 = 7468;
    public static final int FISHING_SPOT_7469 = 7469;
    public static final int FISHING_SPOT_7470 = 7470;
    public static final int CAPTAIN_MAGORO = 7471;
    public static final int RANGER_7472 = 7472;
    public static final int DUCK_7473 = 7473;
    public static final int REGINALD = 7474;
    public static final int ROY_JR = 7475;
    public static final int ROBERT_BOSS = 7476;
    public static final int KNIGHT_OF_VARLAMORE = 7477;
    public static final int HUGOR = 7478;
    public static final int LAN_THE_BUTCHER = 7479;
    public static final int RAKKAR = 7480;
    public static final int HOPLEEZ = 7481;
    public static final int CAPTAIN_SDIAR = 7482;
    public static final int SANDICRAHB = 7483;
    public static final int SANDICRAHB_7484 = 7484;
    public static final int ZOMBIE_7485 = 7485;
    public static final int ZOMBIE_7486 = 7486;
    public static final int ZOMBIE_7487 = 7487;
    public static final int ZOMBIE_7488 = 7488;
    public static final int JARDRIC = 7509;
    public static final int ANNETTE = 7510;
    public static final int SHIELD_MASTER = 7511;
    public static final int PIZAZZ_HAT = 7512;
    public static final int DERWEN = 7513;
    public static final int ENERGY_BALL = 7514;
    public static final int PORAZDIR = 7515;
    public static final int GNORMADIUM_AVLAFRIM = 7516;
    public static final int GNORMADIUM_AVLAFRIM_7517 = 7517;
    public static final int JELLY_7518 = 7518;
    public static final int OLMLET = 7519;
    public static final int OLMLET_7520 = 7520;
    public static final int SOLDIER_7521 = 7521;
    public static final int SOLDIER_7522 = 7522;
    public static final int SOLDIER_7523 = 7523;
    public static final int SOLDIER_7524 = 7524;
    public static final int VANGUARD = 7525;
    public static final int VANGUARD_7526 = 7526;
    public static final int VANGUARD_7527 = 7527;
    public static final int VANGUARD_7528 = 7528;
    public static final int VANGUARD_7529 = 7529;
    public static final int VESPULA = 7530;
    public static final int VESPULA_7531 = 7531;
    public static final int VESPULA_7532 = 7532;
    public static final int ABYSSAL_PORTAL = 7533;
    public static final int LUX_GRUB = 7534;
    public static final int LUX_GRUB_7535 = 7535;
    public static final int LUX_GRUB_7536 = 7536;
    public static final int LUX_GRUB_7537 = 7537;
    public static final int VESPINE_SOLDIER = 7538;
    public static final int VESPINE_SOLDIER_7539 = 7539;
    public static final int TEKTON = 7540;
    public static final int TEKTON_7541 = 7541;
    public static final int TEKTON_7542 = 7542;
    public static final int TEKTON_ENRAGED = 7543;
    public static final int TEKTON_ENRAGED_7544 = 7544;
    public static final int TEKTON_7545 = 7545;
    public static final int BARTENDER_7546 = 7546;
    public static final int SCAVENGER_BEAST = 7548;
    public static final int SCAVENGER_BEAST_7549 = 7549;
    public static final int GREAT_OLM_RIGHT_CLAW = 7550;
    public static final int GREAT_OLM = 7551;
    public static final int GREAT_OLM_LEFT_CLAW = 7552;
    public static final int GREAT_OLM_RIGHT_CLAW_7553 = 7553;
    public static final int GREAT_OLM_7554 = 7554;
    public static final int GREAT_OLM_LEFT_CLAW_7555 = 7555;
    public static final int FIRE = 7558;
    public static final int DEATHLY_RANGER = 7559;
    public static final int DEATHLY_MAGE = 7560;
    public static final int MUTTADILE = 7561;
    public static final int MUTTADILE_7562 = 7562;
    public static final int MUTTADILE_7563 = 7563;
    public static final int MEAT_TREE = 7564;
    public static final int ROCKS_7565 = 7565;
    public static final int VASA_NISTIRIO = 7566;
    public static final int VASA_NISTIRIO_7567 = 7567;
    public static final int GLOWING_CRYSTAL = 7568;
    public static final int GUARDIAN = 7569;
    public static final int GUARDIAN_7570 = 7570;
    public static final int GUARDIAN_7571 = 7571;
    public static final int GUARDIAN_7572 = 7572;
    public static final int LIZARDMAN_SHAMAN_7573 = 7573;
    public static final int LIZARDMAN_SHAMAN_7574 = 7574;
    public static final int SPAWN_7575 = 7575;
    public static final int JEWELLED_CRAB = 7576;
    public static final int JEWELLED_CRAB_RED = 7577;
    public static final int JEWELLED_CRAB_GREEN = 7578;
    public static final int JEWELLED_CRAB_BLUE = 7579;
    public static final int ENERGY_FOCUS_WHITE = 7580;
    public static final int ENERGY_FOCUS_RED = 7581;
    public static final int ENERGY_FOCUS_GREEN = 7582;
    public static final int ENERGY_FOCUS_BLUE = 7583;
    public static final int ICE_DEMON = 7584;
    public static final int ICE_DEMON_7585 = 7585;
    public static final int ICEFIEND_7586 = 7586;
    public static final int GUANIC_BAT = 7587;
    public static final int PRAEL_BAT = 7588;
    public static final int GIRAL_BAT = 7589;
    public static final int PHLUXIA_BAT = 7590;
    public static final int KRYKET_BAT = 7591;
    public static final int MURNG_BAT = 7592;
    public static final int PSYKK_BAT = 7593;
    public static final int CAVE_SNAKE = 7594;
    public static final int CAPTAIN_RIMOR = 7595;
    public static final int LIZARD_7597 = 7597;
    public static final int STRANGE_DEVICE = 7598;
    public static final int MOUNTAIN_GUIDE = 7599;
    public static final int MOUNTAIN_GUIDE_7600 = 7600;
    public static final int SWAMP_PRIEST = 7601;
    public static final int CORRUPTED_SCAVENGER = 7602;
    public static final int CORRUPTED_SCAVENGER_7603 = 7603;
    public static final int SKELETAL_MYSTIC = 7604;
    public static final int SKELETAL_MYSTIC_7605 = 7605;
    public static final int SKELETAL_MYSTIC_7606 = 7606;
    public static final int IMEROMINIA = 7607;
    public static final int PAGIDA = 7608;
    public static final int LOGIOS = 7610;
    public static final int MELETI = 7611;
    public static final int KRATO = 7612;
    public static final int EKTHEME = 7613;
    public static final int ARCHEIO = 7614;
    public static final int STULIETTE = 7615;
    public static final int STULIETTE_7616 = 7616;
    public static final int TEMPLE_GUARDIAN = 7620;
    public static final int KHAZARD_WARLORD_7621 = 7621;
    public static final int KHAZARD_WARLORD_7622 = 7622;
    public static final int ABIGALE = 7623;
    public static final int HEWEY = 7624;
    public static final int SID = 7625;
    public static final int SID_7626 = 7626;
    public static final int TAYTEN = 7627;
    public static final int LACEY = 7628;
    public static final int MANDY = 7629;
    public static final int MANDY_7630 = 7630;
    public static final int MANDY_7631 = 7631;
    public static final int KILLER = 7632;
    public static final int ABIGALE_7633 = 7633;
    public static final int ABIGALE_7635 = 7635;
    public static final int KILLER_7636 = 7636;
    public static final int HEWEY_7637 = 7637;
    public static final int SHADY_FIGURE = 7638;
    public static final int MIRROR = 7640;
    public static final int MIRROR_7641 = 7641;
    public static final int ROCK_GOLEM_7642 = 7642;
    public static final int ROCK_GOLEM_7643 = 7643;
    public static final int ROCK_GOLEM_7644 = 7644;
    public static final int ROCK_GOLEM_7645 = 7645;
    public static final int ROCK_GOLEM_7646 = 7646;
    public static final int ROCK_GOLEM_7647 = 7647;
    public static final int ROCK_GOLEM_7648 = 7648;
    public static final int CHAOTIC_DEATH_SPAWN_7649 = 7649;
    public static final int PIRATE_JACKIE_THE_FRUIT = 7650;
    public static final int PIRATE_JACKIE_THE_FRUIT_7651 = 7651;
    public static final int TZHAARMEJ_7652 = 7652;
    public static final int SLIEVE = 7653;
    public static final int BREIVE = 7654;
    public static final int LESSER_DEMON_7656 = 7656;
    public static final int LESSER_DEMON_7657 = 7657;
    public static final int MUMMY_7658 = 7658;
    public static final int MUMMY_7659 = 7659;
    public static final int MUMMY_7660 = 7660;
    public static final int MUMMY_7661 = 7661;
    public static final int MUMMY_7662 = 7662;
    public static final int KRYSTILIA = 7663;
    public static final int LESSER_DEMON_7664 = 7664;
    public static final int BANISOCH = 7665;
    public static final int BANISOCH_7666 = 7666;
    public static final int HIEVE = 7667;
    public static final int VOICE_OF_YAMA = 7668;
    public static final int DISCIPLE_OF_YAMA = 7669;
    public static final int DISCIPLE_OF_YAMA_7670 = 7670;
    public static final int SKOTOS_7671 = 7671;
    public static final int EVE = 7672;
    public static final int SOLZTUN = 7673;
    public static final int JALNIBREK = 7674;
    public static final int JALNIBREK_7675 = 7675;
    public static final int ROD_FISHING_SPOT_7676 = 7676;
    public static final int TZHAARKETZUH = 7677;
    public static final int TZHAARKETYIL = 7678;
    public static final int TZHAARKET_7679 = 7679;
    public static final int TZHAARMEJDIR = 7680;
    public static final int TZHAARMEJBAL = 7681;
    public static final int TZHAARHUR_7682 = 7682;
    public static final int TZHAARHUR_7683 = 7683;
    public static final int TZHAARHUR_7684 = 7684;
    public static final int TZHAARHUR_7685 = 7685;
    public static final int TZHAARHUR_7686 = 7686;
    public static final int TZHAARHUR_7687 = 7687;
    public static final int TZHAARHURZAL = 7688;
    public static final int TZHAARHURRIN = 7689;
    public static final int JALNIB = 7691;
    public static final int JALMEJRAH = 7692;
    public static final int JALAK = 7693;
    public static final int JALAKREKMEJ = 7694;
    public static final int JALAKREKXIL = 7695;
    public static final int JALAKREKKET = 7696;
    public static final int JALIMKOT = 7697;
    public static final int JALXIL = 7698;
    public static final int JALZEK = 7699;
    public static final int JALTOKJAD = 7700;
    public static final int YTHURKOT_7701 = 7701;
    public static final int JALXIL_7702 = 7702;
    public static final int JALZEK_7703 = 7703;
    public static final int JALTOKJAD_7704 = 7704;
    public static final int YTHURKOT_7705 = 7705;
    public static final int TZKALZUK = 7706;
    public static final int ANCESTRAL_GLYPH = 7707;
    public static final int JALMEJJAK = 7708;
    public static final int ROCKY_SUPPORT = 7709;
    public static final int ROCKY_SUPPORT_7710 = 7710;
    public static final int ROCK_GOLEM_7711 = 7711;
    public static final int DWARF_7712 = 7712;
    public static final int DWARF_7713 = 7713;
    public static final int DWARF_7714 = 7714;
    public static final int DWARF_7715 = 7715;
    public static final int GADRIN = 7716;
    public static final int HENDOR = 7717;
    public static final int YARSUL = 7718;
    public static final int BELONA = 7719;
    public static final int UTREC = 7720;
    public static final int DWARF_7721 = 7721;
    public static final int ROCKS_7722 = 7722;
    public static final int GERTRUDE_7723 = 7723;
    public static final int BARBARIAN_GUARD_7724 = 7724;
    public static final int DWARVEN_BOATMAN_7725 = 7725;
    public static final int DWARVEN_BOATMAN_7726 = 7726;
    public static final int KYLIE_MINNOW = 7727;
    public static final int KYLIE_MINNOW_7728 = 7728;
    public static final int FISHING_SPOT_7730 = 7730;
    public static final int FISHING_SPOT_7731 = 7731;
    public static final int FISHING_SPOT_7732 = 7732;
    public static final int FISHING_SPOT_7733 = 7733;
    public static final int RECRUITER = 7734;
    public static final int ROCK_GOLEM_7736 = 7736;
    public static final int ROCK_GOLEM_7737 = 7737;
    public static final int ROCK_GOLEM_7738 = 7738;
    public static final int ROCK_GOLEM_7739 = 7739;
    public static final int ROCK_GOLEM_7740 = 7740;
    public static final int ROCK_GOLEM_7741 = 7741;
    public static final int CAPTAIN_CLEIVE = 7742;
    public static final int SOLDIER_7743 = 7743;
    public static final int LIZARDMAN_SHAMAN_7744 = 7744;
    public static final int LIZARDMAN_SHAMAN_7745 = 7745;
    public static final int WIZARD_MIZGOG = 7746;
    public static final int WIZARD_MIZGOG_7747 = 7747;
    public static final int FAIRY_7748 = 7748;
    public static final int ARMOURED_FOE = 7750;
    public static final int WELLARMED_FOE = 7751;
    public static final int DARK_MAGE_7752 = 7752;
    public static final int DARK_MAGE_7753 = 7753;
    public static final int SQUIRREL_7754 = 7754;
    public static final int SQUIRREL_7755 = 7755;
    public static final int SQUIRREL_7756 = 7756;
    public static final int TOOL_LEPRECHAUN_7757 = 7757;
    public static final int MERNIA = 7758;
    public static final int HERBI = 7759;
    public static final int HERBI_7760 = 7760;
    public static final int LEAD_NAVIGATOR = 7761;
    public static final int LEAD_NAVIGATOR_7762 = 7762;
    public static final int JUNIOR_NAVIGATOR = 7763;
    public static final int JUNIOR_NAVIGATOR_7764 = 7764;
    public static final int JOHN_7765 = 7765;
    public static final int DAVID_7766 = 7766;
    public static final int BARGE_GUARD = 7768;
    public static final int SHOP_KEEPER_7769 = 7769;
    public static final int FOSSIL_COLLECTOR = 7770;
    public static final int DOG_7771 = 7771;
    public static final int PETER = 7772;
    public static final int CHARLES_7773 = 7773;
    public static final int JOHN_7774 = 7774;
    public static final int DUNCE_7775 = 7775;
    public static final int PETRIFIED_PETE = 7776;
    public static final int WEVE = 7777;
    public static final int IRENE = 7778;
    public static final int BOBBING_FOSSIL = 7779;
    public static final int ISLWYN = 7780;
    public static final int PUFFER_FISH = 7781;
    public static final int FISH_SHOAL = 7782;
    public static final int CETO = 7783;
    public static final int MAIRIN = 7784;
    public static final int HERBIBOAR = 7785;
    public static final int HERBIBOAR_7786 = 7786;
    public static final int MATTIMEO = 7787;
    public static final int CHARLES_CHARLINGTON = 7788;
    public static final int HOLGART_7789 = 7789;
    public static final int JOHN_7790 = 7790;
    public static final int DAVID_7791 = 7791;
    public static final int LONGTAILED_WYVERN = 7792;
    public static final int TALONED_WYVERN = 7793;
    public static final int SPITTING_WYVERN = 7794;
    public static final int ANCIENT_WYVERN = 7795;
    public static final int LOBSTROSITY = 7796;
    public static final int ANCIENT_ZYGOMITE = 7797;
    public static final int GAIUS = 7798;
    public static final int AMMONITE_CRAB = 7799;
    public static final int FOSSIL_ROCK = 7800;
    public static final int TAR_BUBBLES = 7801;
    public static final int HOOP_SNAKE = 7802;
    public static final int STUNNED_HOOP_SNAKE = 7803;
    public static final int TAR_MONSTER = 7804;
    public static final int PASSIVE_TAR_MONSTER = 7805;
    public static final int DERANGED_ARCHAEOLOGIST = 7806;
    public static final int GIANT_BOULDER = 7807;
    public static final int GIANT_BOULDER_7808 = 7808;
    public static final int GIANT_BOULDER_7809 = 7809;
    public static final int LARGE_BOULDER = 7810;
    public static final int LARGE_BOULDER_7811 = 7811;
    public static final int MEDIUM_BOULDER = 7812;
    public static final int MEDIUM_BOULDER_7813 = 7813;
    public static final int SMALL_BOULDER = 7814;
    public static final int SMALL_BOULDER_7815 = 7815;
    public static final int SMALL_BOULDER_7816 = 7816;
    public static final int LAVA_BEAST = 7817;
    public static final int DAVID_7818 = 7818;
    public static final int DUSK = 7849;
    public static final int DAWN = 7850;
    public static final int DUSK_7851 = 7851;
    public static final int DAWN_7852 = 7852;
    public static final int DAWN_7853 = 7853;
    public static final int DUSK_7854 = 7854;
    public static final int DUSK_7855 = 7855;
    public static final int JUSTICIAR_ZACHARIAH_7858 = 7858;
    public static final int DERWEN_7859 = 7859;
    public static final int PORAZDIR_7860 = 7860;
    public static final int BLACK_DRAGON_7861 = 7861;
    public static final int BLACK_DRAGON_7862 = 7862;
    public static final int BLACK_DRAGON_7863 = 7863;
    public static final int ANKOU_7864 = 7864;
    public static final int LESSER_DEMON_7865 = 7865;
    public static final int LESSER_DEMON_7866 = 7866;
    public static final int LESSER_DEMON_7867 = 7867;
    public static final int GREEN_DRAGON_7868 = 7868;
    public static final int GREEN_DRAGON_7869 = 7869;
    public static final int GREEN_DRAGON_7870 = 7870;
    public static final int GREATER_DEMON_7871 = 7871;
    public static final int GREATER_DEMON_7872 = 7872;
    public static final int GREATER_DEMON_7873 = 7873;
    public static final int BLACK_DEMON_7874 = 7874;
    public static final int BLACK_DEMON_7875 = 7875;
    public static final int BLACK_DEMON_7876 = 7876;
    public static final int HELLHOUND_7877 = 7877;
    public static final int ICE_GIANT_7878 = 7878;
    public static final int ICE_GIANT_7879 = 7879;
    public static final int ICE_GIANT_7880 = 7880;
    public static final int REVENANT_IMP = 7881;
    public static final int DUSK_7882 = 7882;
    public static final int DUSK_7883 = 7883;
    public static final int DAWN_7884 = 7884;
    public static final int DAWN_7885 = 7885;
    public static final int DUSK_7886 = 7886;
    public static final int DUSK_7887 = 7887;
    public static final int DUSK_7888 = 7888;
    public static final int DUSK_7889 = 7889;
    public static final int MIDNIGHT = 7890;
    public static final int NOON = 7891;
    public static final int NOON_7892 = 7892;
    public static final int MIDNIGHT_7893 = 7893;
    public static final int SAND_SNAKE_HARD = 7894;
    public static final int SAND_SNAKE = 7895;
    public static final int ARTUR_HOSIDIUS = 7898;
    public static final int ARTUR_HOSIDIUS_7899 = 7899;
    public static final int BUTLER_JARVIS = 7900;
    public static final int CHEF_OLIVIA = 7901;
    public static final int GALANA = 7902;
    public static final int SAND_SNAKE_7903 = 7903;
    public static final int TOMAS_LAWRY = 7904;
    public static final int ROBERT_OREILLY = 7905;
    public static final int DEVAN_RUTTER = 7906;
    public static final int CONRAD_KING = 7907;
    public static final int THE_QUEEN_OF_THIEVES = 7908;
    public static final int LADY_SHAUNA_PISCARILIUS = 7909;
    public static final int SOPHIA_HUGHES = 7910;
    public static final int BARTENDER_7911 = 7911;
    public static final int FISH_MONGER_7912 = 7912;
    public static final int SHOP_KEEPER_7913 = 7913;
    public static final int THIEF_7914 = 7914;
    public static final int THIEF_7915 = 7915;
    public static final int THIEF_7916 = 7916;
    public static final int PIRATE_7917 = 7917;
    public static final int PIRATE_7918 = 7918;
    public static final int MAN_7919 = 7919;
    public static final int MAN_7920 = 7920;
    public static final int WOMAN_7921 = 7921;
    public static final int WOMAN_7922 = 7922;
    public static final int POOR_LOOKING_WOMAN_7923 = 7923;
    public static final int REVENANT_GOBLIN = 7931;
    public static final int REVENANT_PYREFIEND = 7932;
    public static final int REVENANT_HOBGOBLIN = 7933;
    public static final int REVENANT_CYCLOPS = 7934;
    public static final int REVENANT_HELLHOUND = 7935;
    public static final int REVENANT_DEMON = 7936;
    public static final int REVENANT_ORK = 7937;
    public static final int REVENANT_DARK_BEAST = 7938;
    public static final int REVENANT_KNIGHT = 7939;
    public static final int REVENANT_DRAGON = 7940;
    public static final int IRON_MAN_TUTOR_7941 = 7941;
    public static final int EMBLEM_TRADER_7943 = 7943;
    public static final int FISHING_SPOT_7946 = 7946;
    public static final int FISHING_SPOT_7947 = 7947;
    public static final int CORSAIR_TRAITOR_HARD = 7948;
    public static final int CORSAIR_TRAITOR = 7949;
    public static final int ALEC_KINCADE = 7950;
    public static final int PONTS_THE_BRIDGEMASTER = 7951;
    public static final int ERDAN = 7952;
    public static final int PRIMULA = 7953;
    public static final int MYSTERIOUS_ADVENTURER = 7954;
    public static final int BABY_BLACK_DRAGON_7955 = 7955;
    public static final int CAPTAIN_TOCK = 7956;
    public static final int CAPTAIN_TOCK_7957 = 7957;
    public static final int CAPTAIN_TOCK_7958 = 7958;
    public static final int ITHOI_THE_NAVIGATOR = 7961;
    public static final int ITHOI_THE_NAVIGATOR_7963 = 7963;
    public static final int ITHOI_THE_NAVIGATOR_7964 = 7964;
    public static final int CABIN_BOY_COLIN = 7965;
    public static final int CABIN_BOY_COLIN_7966 = 7966;
    public static final int CABIN_BOY_COLIN_7967 = 7967;
    public static final int BUGS_7969 = 7969;
    public static final int GNOCCI_THE_COOK = 7970;
    public static final int GNOCCI_THE_COOK_7971 = 7971;
    public static final int GNOCCI_THE_COOK_7972 = 7972;
    public static final int DOLL = 7975;
    public static final int ARSEN_THE_THIEF = 7976;
    public static final int THE_MIMIC = 7979;
    public static final int YUSUF = 7981;
    public static final int YUSUF_7982 = 7982;
    public static final int FRANCOIS = 7983;
    public static final int MADAME_CALDARIUM = 7984;
    public static final int HARIS = 7985;
    public static final int ALTARKIZ = 7986;
    public static final int LORD_MARSHAL_BROGAN = 7987;
    public static final int CHIEF_TESS = 7988;
    public static final int OGRESS_WARRIOR = 7989;
    public static final int OGRESS_WARRIOR_7990 = 7990;
    public static final int OGRESS_SHAMAN = 7991;
    public static final int OGRESS_SHAMAN_7992 = 7992;
    public static final int ELDER_CHAOS_DRUID_7995 = 7995;
    public static final int CORRUPT_LIZARDMAN_HARD = 7996;
    public static final int CORRUPT_LIZARDMAN = 7997;
    public static final int PHILEAS_RIMOR = 7999;
    public static final int CORRUPT_LIZARDMAN_8000 = 8000;
    public static final int CRUNCHY = 8001;
    public static final int TIM = 8002;
    public static final int DAVE = 8003;
    public static final int GENE = 8004;
    public static final int ART = 8005;
    public static final int GNOSI = 8006;
    public static final int HISTORIAN_DUFFY = 8007;
    public static final int CORPOREAL_CRITTER = 8008;
    public static final int TZREKZUK = 8009;
    public static final int CORPOREAL_CRITTER_8010 = 8010;
    public static final int TZREKZUK_8011 = 8011;
    public static final int BARGE_GUARD_8012 = 8012;
    public static final int BARGE_GUARD_8013 = 8013;
    public static final int KING_NARNODE_SHAREEN = 8019;
    public static final int KING_NARNODE_SHAREEN_8020 = 8020;
    public static final int NATURAL_HISTORIAN_8021 = 8021;
    public static final int MYSTERIOUS_VOICE = 8022;
    public static final int GNOSI_8023 = 8023;
    public static final int RIFT_GUARDIAN_8024 = 8024;
    public static final int VORKI = 8025;
    public static final int VORKATH = 8026;
    public static final int RUNE_DRAGON = 8027;
    public static final int RIFT_GUARDIAN_8028 = 8028;
    public static final int VORKI_8029 = 8029;
    public static final int ADAMANT_DRAGON = 8030;
    public static final int RUNE_DRAGON_8031 = 8031;
    public static final int ELVARG_8033 = 8033;
    public static final int BOB_8034 = 8034;
    public static final int NEITE_8035 = 8035;
    public static final int DIANA = 8036;
    public static final int JACK_8037 = 8037;
    public static final int ELLEN = 8038;
    public static final int FREJA = 8039;
    public static final int LUTWIDGE = 8040;
    public static final int DOG_8041 = 8041;
    public static final int KING_ROALD_8042 = 8042;
    public static final int AEONISIG_RAISPHER_8043 = 8043;
    public static final int SIR_AMIK_VARZE_8044 = 8044;
    public static final int SIR_TIFFY_CASHIEN_8045 = 8045;
    public static final int KING_LATHAS = 8046;
    public static final int KING_ARTHUR_8047 = 8047;
    public static final int BRUNDT_THE_CHIEFTAIN = 8048;
    public static final int ONEIROMANCER_8049 = 8049;
    public static final int DENULTH_8050 = 8050;
    public static final int DUKE_HORACIO_8051 = 8051;
    public static final int WISE_OLD_MAN_8052 = 8052;
    public static final int JARDRIC_8053 = 8053;
    public static final int ACHIETTIES_8054 = 8054;
    public static final int BOB_8055 = 8055;
    public static final int SPAWN_8056 = 8056;
    public static final int ROBERT_THE_STRONG_8057 = 8057;
    public static final int VORKATH_8058 = 8058;
    public static final int VORKATH_8059 = 8059;
    public static final int VORKATH_8060 = 8060;
    public static final int VORKATH_8061 = 8061;
    public static final int ZOMBIFIED_SPAWN = 8062;
    public static final int ZOMBIFIED_SPAWN_8063 = 8063;
    public static final int STONE_GUARDIAN = 8064;
    public static final int STONE_GUARDIAN_8065 = 8065;
    public static final int STONE_GUARDIAN_8066 = 8066;
    public static final int ZOMBIE_8067 = 8067;
    public static final int ZOMBIE_8068 = 8068;
    public static final int ZOMBIE_8069 = 8069;
    public static final int SKELETON_8070 = 8070;
    public static final int SKELETON_8071 = 8071;
    public static final int SKELETON_8072 = 8072;
    public static final int GREEN_DRAGON_8073 = 8073;
    public static final int BLUE_DRAGON_8074 = 8074;
    public static final int RED_DRAGON_8075 = 8075;
    public static final int GREEN_DRAGON_8076 = 8076;
    public static final int BLUE_DRAGON_8077 = 8077;
    public static final int RED_DRAGON_8078 = 8078;
    public static final int RED_DRAGON_8079 = 8079;
    public static final int IRON_DRAGON_8080 = 8080;
    public static final int BRUTAL_GREEN_DRAGON_8081 = 8081;
    public static final int GREEN_DRAGON_8082 = 8082;
    public static final int BLUE_DRAGON_8083 = 8083;
    public static final int BLACK_DRAGON_8084 = 8084;
    public static final int BLACK_DRAGON_8085 = 8085;
    public static final int STEEL_DRAGON_8086 = 8086;
    public static final int BRUTAL_RED_DRAGON_8087 = 8087;
    public static final int MITHRIL_DRAGON_8088 = 8088;
    public static final int MITHRIL_DRAGON_8089 = 8089;
    public static final int ADAMANT_DRAGON_8090 = 8090;
    public static final int RUNE_DRAGON_8091 = 8091;
    public static final int BRUTAL_BLACK_DRAGON_8092 = 8092;
    public static final int BRUTAL_BLACK_DRAGON_8093 = 8093;
    public static final int GALVEK = 8094;
    public static final int GALVEK_8095 = 8095;
    public static final int GALVEK_8096 = 8096;
    public static final int GALVEK_8097 = 8097;
    public static final int GALVEK_8098 = 8098;
    public static final int TSUNAMI = 8099;
    public static final int DALLAS_JONES = 8100;
    public static final int DALLAS_JONES_8101 = 8101;
    public static final int DALLAS_JONES_8102 = 8102;
    public static final int DALLAS_JONES_8103 = 8103;
    public static final int DALLAS_JONES_8104 = 8104;
    public static final int JARDRIC_8105 = 8105;
    public static final int JARDRIC_8106 = 8106;
    public static final int JARDRIC_8107 = 8107;
    public static final int JARDRIC_8108 = 8108;
    public static final int BOB_8111 = 8111;
    public static final int BOB_8112 = 8112;
    public static final int BOB_8113 = 8113;
    public static final int BOB_8114 = 8114;
    public static final int BOB_8115 = 8115;
    public static final int NOT_BOB = 8116;
    public static final int NOT_BOB_8117 = 8117;
    public static final int NEITE_8118 = 8118;
    public static final int UNFERTH_8119 = 8119;
    public static final int DRAGONKIN_8120 = 8120;
    public static final int CAMORRA = 8121;
    public static final int TRISTAN = 8122;
    public static final int AIVAS = 8123;
    public static final int ROBERT_THE_STRONG_8124 = 8124;
    public static final int DRAGONKIN_8125 = 8125;
    public static final int CAMORRA_8126 = 8126;
    public static final int TRISTAN_8127 = 8127;
    public static final int AIVAS_8128 = 8128;
    public static final int ROBERT_THE_STRONG_8129 = 8129;
    public static final int ODYSSEUS_8130 = 8130;
    public static final int TORFINN = 8131;
    public static final int ENIOLA = 8132;
    public static final int ODOVACAR = 8133;
    public static final int SARAH_8134 = 8134;
    public static final int DRAGONKIN_8135 = 8135;
    public static final int ZORGOTH = 8136;
    public static final int SPIDER_8137 = 8137;
    public static final int SPIDER_8138 = 8138;
    public static final int SKELETON_8139 = 8139;
    public static final int SKELETON_8140 = 8140;
    public static final int STRANGE_EGG = 8142;
    public static final int DRAGON_HEAD = 8143;
    public static final int DRAGON_HEAD_8144 = 8144;
    public static final int BRUNDT_THE_CHIEFTAIN_8145 = 8145;
    public static final int THORVALD_THE_WARRIOR_8146 = 8146;
    public static final int PEER_THE_SEER_8147 = 8147;
    public static final int SWENSEN_THE_NAVIGATOR_8148 = 8148;
    public static final int WHITE_KNIGHT_8149 = 8149;
    public static final int PALADIN_8150 = 8150;
    public static final int KOSCHEI_THE_DEATHLESS_8151 = 8151;
    public static final int KOSCHEI_THE_DEATHLESS_8152 = 8152;
    public static final int BRUNDT_THE_CHIEFTAIN_8153 = 8153;
    public static final int WISE_OLD_MAN_8154 = 8154;
    public static final int JARDRIC_8155 = 8155;
    public static final int ACHIETTIES_8156 = 8156;
    public static final int SIR_TIFFY_CASHIEN_8157 = 8157;
    public static final int ONEIROMANCER_8158 = 8158;
    public static final int BOB_8159 = 8159;
    public static final int SOLDIER_8160 = 8160;
    public static final int BRUNDT_THE_CHIEFTAIN_8161 = 8161;
    public static final int WISE_OLD_MAN_8162 = 8162;
    public static final int HISTORIAN_DUFFY_8163 = 8163;
    public static final int ACHIETTIES_8164 = 8164;
    public static final int SIR_TIFFY_CASHIEN_8165 = 8165;
    public static final int ONEIROMANCER_8166 = 8166;
    public static final int ZORGOTH_8167 = 8167;
    public static final int SOLDIER_8168 = 8168;
    public static final int BRUNDT_THE_CHIEFTAIN_8169 = 8169;
    public static final int WISE_OLD_MAN_8170 = 8170;
    public static final int JARDRIC_8171 = 8171;
    public static final int ACHIETTIES_8172 = 8172;
    public static final int SIR_TIFFY_CASHIEN_8173 = 8173;
    public static final int ONEIROMANCER_8174 = 8174;
    public static final int ZORGOTH_8175 = 8175;
    public static final int ZORGOTH_8176 = 8176;
    public static final int GALVEK_8177 = 8177;
    public static final int GALVEK_8178 = 8178;
    public static final int GALVEK_8179 = 8179;
    public static final int AMELIA = 8180;
    public static final int JONATHAN = 8181;
    public static final int NATURAL_HISTORIAN_8182 = 8182;
    public static final int LITTLE_PARASITE = 8183;
    public static final int BOULDER_8188 = 8188;
    public static final int GRAVE_DIGGER = 8189;
    public static final int JAMES = 8193;
    public static final int GROWTHLING = 8194;
    public static final int BRYOPHYTA = 8195;
    public static final int PUPPADILE = 8196;
    public static final int TEKTINY = 8197;
    public static final int VANGUARD_8198 = 8198;
    public static final int VASA_MINIRIO = 8199;
    public static final int VESPINA = 8200;
    public static final int PUPPADILE_8201 = 8201;
    public static final int TEKTINY_8202 = 8202;
    public static final int VANGUARD_8203 = 8203;
    public static final int VASA_MINIRIO_8204 = 8204;
    public static final int VESPINA_8205 = 8205;
    public static final int GARTH_8206 = 8206;
    public static final int GARTH_8207 = 8207;
    public static final int MYSTERIOUS_STRANGER = 8208;
    public static final int VYRELORD = 8209;
    public static final int VYRELADY = 8210;
    public static final int MEIYERDITCH_CITIZEN_8211 = 8211;
    public static final int HARPERT = 8212;
    public static final int MERCENARY_8213 = 8213;
    public static final int MERCENARY_8214 = 8214;
    public static final int MERCENARY_8215 = 8215;
    public static final int SAFALAAN_HALLOW_8216 = 8216;
    public static final int SAFALAAN_HALLOW_8217 = 8217;
    public static final int SAFALAAN_HALLOW_8218 = 8218;
    public static final int SAFALAAN_HALLOW_8219 = 8219;
    public static final int VERTIDA_SEFALATIS = 8220;
    public static final int VERTIDA_SEFALATIS_8221 = 8221;
    public static final int VERTIDA_SEFALATIS_8222 = 8222;
    public static final int VERTIDA_SEFALATIS_8223 = 8223;
    public static final int FLAYGIAN_SCREWTE = 8224;
    public static final int FLAYGIAN_SCREWTE_8225 = 8225;
    public static final int MEKRITUS_AHARA = 8226;
    public static final int MEKRITUS_AHARA_8227 = 8227;
    public static final int ANDIESS_JUIP = 8228;
    public static final int ANDIESS_JUIP_8229 = 8229;
    public static final int KAEL_FORSHAW = 8230;
    public static final int KAEL_FORSHAW_8231 = 8231;
    public static final int KAEL_FORSHAW_8232 = 8232;
    public static final int KAEL_FORSHAW_8233 = 8233;
    public static final int MEIYERDITCH_CITIZEN_8235 = 8235;
    public static final int MEIYERDITCH_CITIZEN_8236 = 8236;
    public static final int MEIYERDITCH_CITIZEN_8237 = 8237;
    public static final int VANSTROM_KLAUSE_8238 = 8238;
    public static final int VANSTROM_KLAUSE_8239 = 8239;
    public static final int VANSTROM_KLAUSE_8240 = 8240;
    public static final int RANIS_DRAKAN_8241 = 8241;
    public static final int RANIS_DRAKAN_8242 = 8242;
    public static final int RANIS_DRAKAN_8243 = 8243;
    public static final int RANIS_DRAKAN_8244 = 8244;
    public static final int RANIS_DRAKAN_8245 = 8245;
    public static final int RANIS_DRAKAN_8246 = 8246;
    public static final int RANIS_DRAKAN_8247 = 8247;
    public static final int RANIS_DRAKAN_8248 = 8248;
    public static final int VANESCULA_DRAKAN_8249 = 8249;
    public static final int VERZIK_VITUR = 8250;
    public static final int VYREWATCH_8251 = 8251;
    public static final int VYREWATCH_8252 = 8252;
    public static final int VYREWATCH_8253 = 8253;
    public static final int VYREWATCH_8254 = 8254;
    public static final int VYREWATCH_8255 = 8255;
    public static final int VYREWATCH_8256 = 8256;
    public static final int VYREWATCH_8257 = 8257;
    public static final int VYREWATCH_8258 = 8258;
    public static final int VYREWATCH_8259 = 8259;
    public static final int ABOMINATION = 8260;
    public static final int ABOMINATION_8261 = 8261;
    public static final int ABOMINATION_8262 = 8262;
    public static final int NYLOCAS_ISCHYROS = 8263;
    public static final int NYLOCAS_TOXOBOLOS = 8264;
    public static final int ANDRAS = 8267;
    public static final int ANDRAS_8268 = 8268;
    public static final int YENRAB = 8269;
    public static final int LAHSRAM = 8270;
    public static final int ERODOEHT = 8271;
    public static final int LECTOR_GURA = 8273;
    public static final int SISTER_SEVI = 8274;
    public static final int SISTER_TOEN = 8275;
    public static final int SISTER_YRAM = 8276;
    public static final int LADY_CROMBWICK = 8287;
    public static final int NEMISHKA = 8288;
    public static final int SWAMP_CRAB = 8297;
    public static final int SWAMP_CRAB_8298 = 8298;
    public static final int SWAMPY_LOG = 8299;
    public static final int VYREWATCH_8300 = 8300;
    public static final int VYREWATCH_8301 = 8301;
    public static final int VYREWATCH_8302 = 8302;
    public static final int VYREWATCH_8303 = 8303;
    public static final int VYREWATCH_8304 = 8304;
    public static final int VYREWATCH_8305 = 8305;
    public static final int VYREWATCH_8306 = 8306;
    public static final int VYREWATCH_8307 = 8307;
    public static final int MEIYERDITCH_CITIZEN_8320 = 8320;
    public static final int BANKER_8321 = 8321;
    public static final int BANKER_8322 = 8322;
    public static final int VYRE_ORATOR = 8323;
    public static final int VYRE_ORATOR_8324 = 8324;
    public static final int VAMPYRE_JUVENILE_8326 = 8326;
    public static final int VAMPYRE_JUVENILE_8327 = 8327;
    public static final int MEIYERDITCH_CITIZEN_8328 = 8328;
    public static final int MEIYERDITCH_CITIZEN_8329 = 8329;
    public static final int MEIYERDITCH_CITIZEN_8330 = 8330;
    public static final int MEIYERDITCH_CITIZEN_8331 = 8331;
    public static final int VYRELORD_8332 = 8332;
    public static final int VYRELADY_8333 = 8333;
    public static final int VYRELORD_8334 = 8334;
    public static final int VYRELADY_8335 = 8335;
    public static final int LIL_ZIK = 8336;
    public static final int LIL_ZIK_8337 = 8337;
    public static final int XARPUS = 8338;
    public static final int XARPUS_8339 = 8339;
    public static final int XARPUS_8340 = 8340;
    public static final int XARPUS_8341 = 8341;
    public static final int NYLOCAS_ISCHYROS_8342 = 8342;
    public static final int NYLOCAS_TOXOBOLOS_8343 = 8343;
    public static final int NYLOCAS_HAGIOS = 8344;
    public static final int NYLOCAS_ISCHYROS_8345 = 8345;
    public static final int NYLOCAS_TOXOBOLOS_8346 = 8346;
    public static final int NYLOCAS_HAGIOS_8347 = 8347;
    public static final int NYLOCAS_ISCHYROS_8348 = 8348;
    public static final int NYLOCAS_TOXOBOLOS_8349 = 8349;
    public static final int NYLOCAS_HAGIOS_8350 = 8350;
    public static final int NYLOCAS_ISCHYROS_8351 = 8351;
    public static final int NYLOCAS_TOXOBOLOS_8352 = 8352;
    public static final int NYLOCAS_HAGIOS_8353 = 8353;
    public static final int NYLOCAS_VASILIAS = 8354;
    public static final int NYLOCAS_VASILIAS_8355 = 8355;
    public static final int NYLOCAS_VASILIAS_8356 = 8356;
    public static final int NYLOCAS_VASILIAS_8357 = 8357;
    public static final int PESTILENT_BLOAT = 8359;
    public static final int THE_MAIDEN_OF_SUGADINTI = 8360;
    public static final int THE_MAIDEN_OF_SUGADINTI_8361 = 8361;
    public static final int THE_MAIDEN_OF_SUGADINTI_8362 = 8362;
    public static final int THE_MAIDEN_OF_SUGADINTI_8363 = 8363;
    public static final int THE_MAIDEN_OF_SUGADINTI_8364 = 8364;
    public static final int THE_MAIDEN_OF_SUGADINTI_8365 = 8365;
    public static final int NYLOCAS_MATOMENOS = 8366;
    public static final int BLOOD_SPAWN = 8367;
    public static final int ABIGAILA = 8368;
    public static final int VERZIK_VITUR_8369 = 8369;
    public static final int VERZIK_VITUR_8370 = 8370;
    public static final int VERZIK_VITUR_8371 = 8371;
    public static final int VERZIK_VITUR_8372 = 8372;
    public static final int VERZIK_VITUR_8373 = 8373;
    public static final int VERZIK_VITUR_8374 = 8374;
    public static final int VERZIK_VITUR_8375 = 8375;
    public static final int WEB = 8376;
    public static final int COLLAPSING_PILLAR = 8377;
    public static final int COLLAPSING_PILLAR_8378 = 8378;
    public static final int SUPPORTING_PILLAR = 8379;
    public static final int NYLOCAS_ISCHYROS_8381 = 8381;
    public static final int NYLOCAS_TOXOBOLOS_8382 = 8382;
    public static final int NYLOCAS_HAGIOS_8383 = 8383;
    public static final int NYLOCAS_ATHANATOS = 8384;
    public static final int NYLOCAS_MATOMENOS_8385 = 8385;
    public static final int SOTETSEG = 8387;
    public static final int SOTETSEG_8388 = 8388;
    public static final int NIGEL = 8390;
    public static final int NIGEL_8391 = 8391;
    public static final int MONK_OF_ZAMORAK_8400 = 8400;
    public static final int MONK_OF_ZAMORAK_8401 = 8401;
    public static final int ASKELADDEN = 8402;
    public static final int ASKELADDEN_8403 = 8403;
    public static final int ASKELADDEN_8404 = 8404;
    public static final int ASKELADDEN_8405 = 8405;
    public static final int WISE_OLD_MAN_8407 = 8407;
    public static final int WISE_OLD_MAN_8409 = 8409;
    public static final int WISE_YOUNG_MAN = 8410;
    public static final int MY_ARM_8411 = 8411;
    public static final int DRUNKEN_DWARFS_LEG_8419 = 8419;
    public static final int SQUIRREL_8422 = 8422;
    public static final int WOLFBONE = 8424;
    public static final int MOTHER = 8425;
    public static final int MOTHER_8426 = 8426;
    public static final int MOTHER_8428 = 8428;
    public static final int MOTHER_8429 = 8429;
    public static final int MOTHER_8430 = 8430;
    public static final int SNOWFLAKE = 8431;
    public static final int ODD_MUSHROOM = 8434;
    public static final int ODD_MUSHROOM_8435 = 8435;
    public static final int DONT_KNOW_WHAT = 8438;
    public static final int DONT_KNOW_WHAT_8439 = 8439;
    public static final int DONT_KNOW_WHAT_8440 = 8440;
    public static final int BOULDER_8442 = 8442;
    public static final int ROOT = 8444;
    public static final int ROOT_8445 = 8445;
    public static final int ICICLE = 8446;
    public static final int ICICLE_8447 = 8447;
    public static final int DRIFTWOOD = 8448;
    public static final int DRIFTWOOD_8449 = 8449;
    public static final int PEBBLE = 8450;
    public static final int PEBBLE_8451 = 8451;
    public static final int GOAT_POO = 8452;
    public static final int GOAT_POO_8453 = 8453;
    public static final int YELLOW_SNOW = 8454;
    public static final int YELLOW_SNOW_8455 = 8455;
    public static final int BUTTERFLY_8456 = 8456;
    public static final int BUTTERFLY_8459 = 8459;
    public static final int ODD_STONE = 8460;
    public static final int ODD_STONE_8463 = 8463;
    public static final int SQUIRREL_8464 = 8464;
    public static final int SQUIRREL_8467 = 8467;
    public static final int TROLL_8470 = 8470;
    public static final int TROLL_8471 = 8471;
    public static final int TROLL_8472 = 8472;
    public static final int TROLL_8473 = 8473;
    public static final int BLACK_GUARD_8474 = 8474;
    public static final int BLACK_GUARD_8475 = 8475;
    public static final int BLACK_GUARD_8476 = 8476;
    public static final int COMBAT_TEST_MAXHIT = 8479;
    public static final int WIZARD_CROMPERTY = 8480;
    public static final int WIZARD_CROMPERTY_8481 = 8481;
    public static final int SMOKE_DEVIL_8482 = 8482;
    public static final int SMOKE_DEVIL_8483 = 8483;
    public static final int VEOS_8484 = 8484;
    public static final int EEK = 8485;
    public static final int MAKEOVER_MAGE_8487 = 8487;
    public static final int THE_COLLECTOR = 8491;
    public static final int IKKLE_HYDRA = 8492;
    public static final int IKKLE_HYDRA_8493 = 8493;
    public static final int IKKLE_HYDRA_8494 = 8494;
    public static final int IKKLE_HYDRA_8495 = 8495;
    public static final int DWARF_8496 = 8496;
    public static final int OLD_DWARF = 8500;
    public static final int MORI = 8501;
    public static final int MORI_8502 = 8502;
    public static final int SURVIVAL_EXPERT = 8503;
    public static final int LORD_TROBIN_ARCEUUS = 8504;
    public static final int LORD_TROBIN_ARCEUUS_8505 = 8505;
    public static final int TOWER_MAGE_8507 = 8507;
    public static final int TOWER_MAGE_8508 = 8508;
    public static final int TOWER_MAGE_8509 = 8509;
    public static final int TOWER_MAGE_8510 = 8510;
    public static final int TOWER_MAGE_8511 = 8511;
    public static final int TORMENTED_SOUL = 8512;
    public static final int TORMENTED_SOUL_8513 = 8513;
    public static final int TRAPPED_SOUL = 8514;
    public static final int ALYSSA = 8515;
    public static final int IKKLE_HYDRA_8517 = 8517;
    public static final int IKKLE_HYDRA_8518 = 8518;
    public static final int IKKLE_HYDRA_8519 = 8519;
    public static final int IKKLE_HYDRA_8520 = 8520;
    public static final int ALRY_THE_ANGLER = 8521;
    public static final int CORMORANT_8522 = 8522;
    public static final int FISHING_SPOT_8523 = 8523;
    public static final int ROD_FISHING_SPOT_8524 = 8524;
    public static final int FISHING_SPOT_8525 = 8525;
    public static final int FISHING_SPOT_8526 = 8526;
    public static final int FISHING_SPOT_8527 = 8527;
    public static final int TRAPPED_SOUL_8528 = 8528;
    public static final int TRAPPED_SOUL_HARD = 8529;
    public static final int AMELIA_8530 = 8530;
    public static final int ALLANNA = 8531;
    public static final int JATIX = 8532;
    public static final int NIKKIE = 8533;
    public static final int ROSIE = 8534;
    public static final int ALAN = 8535;
    public static final int ALEXANDRA = 8536;
    public static final int LATLINK_FASTBELL = 8537;
    public static final int ELISE = 8538;
    public static final int ARC_TEST_01 = 8539;
    public static final int ARC_TEST_02 = 8540;
    public static final int LITTLE_PARASITE_8541 = 8541;
    public static final int ROYAL_GUARD = 8542;
    public static final int ROYAL_GUARD_8543 = 8543;
    public static final int UNDOR = 8544;
    public static final int UNDOR_8545 = 8545;
    public static final int THIRUS = 8546;
    public static final int THIRUS_8547 = 8547;
    public static final int DARCOR_QUO_NARGA = 8548;
    public static final int UURRAK_QUO_NARGA = 8549;
    public static final int MARMOR_QUO_NARGA = 8550;
    public static final int LORNOR_QUO_NARGA = 8551;
    public static final int GORHAK_QUO_NARGA = 8552;
    public static final int FORNEK_QUO_MATEN = 8553;
    public static final int VORNAS_QUO_MATEN = 8554;
    public static final int XORRAH_QUO_SIHAR = 8555;
    public static final int CORKAT_QUO_SIHAR = 8556;
    public static final int LOKRAA_QUO_SIHAR = 8557;
    public static final int WENGRA_QUO_SIHAR = 8558;
    public static final int HALDOR_QUO_KERAN = 8559;
    public static final int VORTAS_QUO_KERAN = 8560;
    public static final int MALLAK_QUO_KERAN = 8561;
    public static final int RICHARD_FLINTMAUL = 8562;
    public static final int LIZARDMAN_8563 = 8563;
    public static final int LIZARDMAN_BRUTE_8564 = 8564;
    public static final int LIZARDMAN_SHAMAN_8565 = 8565;
    public static final int RANGER_8566 = 8566;
    public static final int SOLDIER_8567 = 8567;
    public static final int SOLDIER_8568 = 8568;
    public static final int DOYEN = 8569;
    public static final int DOYEN_8570 = 8570;
    public static final int NURSE_EMMA_GENTSY = 8571;
    public static final int WOUNDED_SOLDIER_8572 = 8572;
    public static final int WOUNDED_SOLDIER_8573 = 8573;
    public static final int WOUNDED_SOLDIER_8574 = 8574;
    public static final int SOLDIER_8575 = 8575;
    public static final int LOVAKENGJ_ENGINEER = 8576;
    public static final int GENERAL_VIR = 8577;
    public static final int SWAMP_FROG = 8578;
    public static final int SHAYZIEN_INFILTRATOR = 8579;
    public static final int KETSAL_KUK = 8581;
    public static final int EKANS_CHAN = 8582;
    public static final int HESPORI = 8583;
    public static final int FLOWER = 8584;
    public static final int FLOWER_8585 = 8585;
    public static final int GUILDMASTER_JANE = 8586;
    public static final int GUILDMASTER_JANE_8587 = 8587;
    public static final int ARNO = 8588;
    public static final int BANKER_8589 = 8589;
    public static final int BANKER_8590 = 8590;
    public static final int RABBIT_8591 = 8591;
    public static final int RABBIT_8592 = 8592;
    public static final int RABBIT_8593 = 8593;
    public static final int CAT_8594 = 8594;
    public static final int FELFIZ_YARYUS = 8595;
    public static final int KEITH = 8596;
    public static final int ORNATE_COMBAT_DUMMY = 8598;
    public static final int SOLDIER_8599 = 8599;
    public static final int SERGEANT_8600 = 8600;
    public static final int RANGER_8601 = 8601;
    public static final int KAALKETJOR = 8602;
    public static final int KAALMEJSAN = 8603;
    public static final int KAALXILDAR = 8604;
    public static final int TARFOL_QUO_MATEN = 8605;
    public static final int KORMAR_QUO_MATEN = 8606;
    public static final int GARBEK_QUO_MATEN = 8607;
    public static final int ORRVOR_QUO_MATEN = 8608;
    public static final int HYDRA = 8609;
    public static final int WYRM = 8610;
    public static final int WYRM_8611 = 8611;
    public static final int DRAKE_8612 = 8612;
    public static final int DRAKE_8613 = 8613;
    public static final int SULPHUR_LIZARD = 8614;
    public static final int ALCHEMICAL_HYDRA = 8615;
    public static final int ALCHEMICAL_HYDRA_8616 = 8616;
    public static final int ALCHEMICAL_HYDRA_8617 = 8617;
    public static final int ALCHEMICAL_HYDRA_8618 = 8618;
    public static final int ALCHEMICAL_HYDRA_8619 = 8619;
    public static final int ALCHEMICAL_HYDRA_8620 = 8620;
    public static final int ALCHEMICAL_HYDRA_8621 = 8621;
    public static final int ALCHEMICAL_HYDRA_8622 = 8622;
    public static final int KONAR_QUO_MATEN = 8623;
    public static final int TAYLOR = 8629;
    public static final int VEOS_8630 = 8630;
    public static final int SEAMAN_MORRIS = 8631;
    public static final int THE_MIMIC_8633 = 8633;
    public static final int ALCHEMICAL_HYDRA_8634 = 8634;
    public static final int THIRD_AGE_WARRIOR = 8635;
    public static final int THIRD_AGE_RANGER = 8636;
    public static final int THIRD_AGE_MAGE = 8637;
    public static final int URI_8638 = 8638;
    public static final int FIRE_8643 = 8643;
    public static final int SARADOMINIST_RECRUITER = 8644;
    public static final int ARTHUR_THE_CLUE_HUNTER = 8665;
    public static final int BANKER_8666 = 8666;
    public static final int PUFFER_FISH_8667 = 8667;
    public static final int ISLWYN_8675 = 8675;
    public static final int ILFEEN = 8676;
    public static final int FERAL_VAMPYRE_8678 = 8678;
    public static final int ABIDOR_CRANK_8679 = 8679;
    public static final int DAVON = 8680;
    public static final int ZENESHA_8681 = 8681;
    public static final int AEMAD = 8682;
    public static final int KORTAN = 8683;
    public static final int ROACHEY = 8684;
    public static final int FRENITA = 8685;
    public static final int NURMOF = 8686;
    public static final int TEA_SELLER = 8687;
    public static final int FAT_TONY = 8688;
    public static final int ANCIENT_FUNGI = 8690;
    public static final int ANCIENT_FUNGI_8691 = 8691;
    public static final int NOTERAZZO = 8692;
    public static final int DIANGO = 8693;
    public static final int BRIAN_8694 = 8694;
    public static final int MOSOL_REI_8696 = 8696;
    public static final int LEKE_QUO_KERAN = 8697;
    public static final int MONK_OF_ZAMORAK_8698 = 8698;
    public static final int LARRAN = 8699;
    public static final int GIANT_FROG_8700 = 8700;
    public static final int BIG_FROG_8701 = 8701;
    public static final int FROG_8702 = 8702;
    public static final int TEMPLE_SPIDER = 8703;
    public static final int BROTHER_AIMERI = 8704;
    public static final int BROTHER_AIMERI_8705 = 8705;
    public static final int OLBERTUS = 8706;
    public static final int OLBERTUS_8707 = 8707;
    public static final int OLBERTUS_8708 = 8708;
    public static final int SHAEDED_BEAST = 8709;
    public static final int SHAEDED_BEAST_8710 = 8710;
    public static final int EODAN = 8711;
    public static final int KNIGHT_OF_VARLAMORE_8712 = 8712;
    public static final int SARACHNIS = 8713;
    public static final int SPAWN_OF_SARACHNIS = 8714;
    public static final int SPAWN_OF_SARACHNIS_8715 = 8715;
    public static final int JUSTINE = 8721;
    public static final int SILVER_MERCHANT_8722 = 8722;
    public static final int GEM_MERCHANT_8723 = 8723;
    public static final int BAKER_8724 = 8724;
    public static final int BAKER_8725 = 8725;
    public static final int SPICE_SELLER_8726 = 8726;
    public static final int FUR_TRADER_8727 = 8727;
    public static final int SILK_MERCHANT_8728 = 8728;
    public static final int YOUNGLLEF = 8729;
    public static final int CORRUPTED_YOUNGLLEF = 8730;
    public static final int SMOLCANO = 8731;
    public static final int MUGGER_8732 = 8732;
    public static final int CRAB_8733 = 8733;
    public static final int MOSS_GIANT_8736 = 8736;
    public static final int YOUNGLLEF_8737 = 8737;
    public static final int CORRUPTED_YOUNGLLEF_8738 = 8738;
    public static final int SMOLCANO_8739 = 8739;
    public static final int CRYSTAL_IMPLING = 8741;
    public static final int CRYSTAL_IMPLING_8742 = 8742;
    public static final int CRYSTAL_IMPLING_8743 = 8743;
    public static final int CRYSTAL_IMPLING_8744 = 8744;
    public static final int CRYSTAL_IMPLING_8745 = 8745;
    public static final int CRYSTAL_IMPLING_8746 = 8746;
    public static final int CRYSTAL_IMPLING_8747 = 8747;
    public static final int CRYSTAL_IMPLING_8748 = 8748;
    public static final int CRYSTAL_IMPLING_8749 = 8749;
    public static final int CRYSTAL_IMPLING_8750 = 8750;
    public static final int CRYSTAL_IMPLING_8751 = 8751;
    public static final int CRYSTAL_IMPLING_8752 = 8752;
    public static final int CRYSTAL_IMPLING_8753 = 8753;
    public static final int CRYSTAL_IMPLING_8754 = 8754;
    public static final int CRYSTAL_IMPLING_8755 = 8755;
    public static final int CRYSTAL_IMPLING_8756 = 8756;
    public static final int CRYSTAL_IMPLING_8757 = 8757;
    public static final int LORD_IORWERTH = 8758;
    public static final int IORWERTH_WARRIOR_8759 = 8759;
    public static final int IORWERTH_ARCHER_8760 = 8760;
    public static final int ELF_TRACKER = 8761;
    public static final int TYRAS_GUARD_8762 = 8762;
    public static final int CAPTAIN_BARNABY_8763 = 8763;
    public static final int CAPTAIN_BARNABY_8764 = 8764;
    public static final int COUNCILLOR_HALGRIVE_8765 = 8765;
    public static final int ELUNED_8766 = 8766;
    public static final int ELUNED_8767 = 8767;
    public static final int ELVEN_SCOUT = 8768;
    public static final int ILFEEN_8769 = 8769;
    public static final int ELVEN_SCOUT_8770 = 8770;
    public static final int MOURNER = 8771;
    public static final int SLAVE = 8772;
    public static final int SLAVE_8773 = 8773;
    public static final int SLAVE_8774 = 8774;
    public static final int SEREN = 8775;
    public static final int MEMORY_OF_SEREN = 8776;
    public static final int MEMORY_OF_SEREN_8777 = 8777;
    public static final int MEMORY_OF_SEREN_8778 = 8778;
    public static final int MEMORY_OF_SEREN_8779 = 8779;
    public static final int MEMORY_OF_SEREN_8780 = 8780;
    public static final int MEMORY_OF_SEREN_8781 = 8781;
    public static final int MEMORY_OF_SEREN_8782 = 8782;
    public static final int MEMORY_OF_SEREN_8783 = 8783;
    public static final int MEMORY_OF_SEREN_8784 = 8784;
    public static final int ELENA_8791 = 8791;
    public static final int ELENA_8792 = 8792;
    public static final int ELENA_8793 = 8793;
    public static final int ELENA_8794 = 8794;
    public static final int ELENA_8795 = 8795;
    public static final int ELENA_8797 = 8797;
    public static final int ELENA_8798 = 8798;
    public static final int KNIGHT_OF_ARDOUGNE_8799 = 8799;
    public static final int KNIGHT_OF_ARDOUGNE_8800 = 8800;
    public static final int KNIGHT_OF_ARDOUGNE_8801 = 8801;
    public static final int COUNCILLOR_HALGRIVE_8802 = 8802;
    public static final int COUNCILLOR_HALGRIVE_8803 = 8803;
    public static final int OMART = 8804;
    public static final int KILRON = 8805;
    public static final int JETHICK = 8806;
    public static final int CARLA = 8807;
    public static final int BAXTORIAN = 8808;
    public static final int BAXTORIAN_8809 = 8809;
    public static final int BAXTORIAN_8810 = 8810;
    public static final int BAXTORIAN_8811 = 8811;
    public static final int BAXTORIAN_8812 = 8812;
    public static final int BAXTORIAN_8813 = 8813;
    public static final int YSGAWYN = 8814;
    public static final int YSGAWYN_8815 = 8815;
    public static final int YSGAWYN_8816 = 8816;
    public static final int ISLWYN_8818 = 8818;
    public static final int ISLWYN_8819 = 8819;
    public static final int ISLWYN_8821 = 8821;
    public static final int ISLWYN_8822 = 8822;
    public static final int ISLWYN_8823 = 8823;
    public static final int ILFEEN_8825 = 8825;
    public static final int ILFEEN_8827 = 8827;
    public static final int ELUNED_8829 = 8829;
    public static final int ELUNED_8830 = 8830;
    public static final int RESISTANCE_FIGHTER = 8831;
    public static final int RESISTANCE_FIGHTER_8832 = 8832;
    public static final int RESISTANCE_FIGHTER_8833 = 8833;
    public static final int RESISTANCE_FIGHTER_8834 = 8834;
    public static final int RESISTANCE_FIGHTER_8835 = 8835;
    public static final int OMART_8836 = 8836;
    public static final int KILRON_8837 = 8837;
    public static final int ELENA_8838 = 8838;
    public static final int JETHICK_8839 = 8839;
    public static final int OMART_8840 = 8840;
    public static final int KILRON_8841 = 8841;
    public static final int KING_LATHAS_8842 = 8842;
    public static final int LATHAS = 8843;
    public static final int MOURNER_8844 = 8844;
    public static final int MOURNER_8845 = 8845;
    public static final int MOURNER_8846 = 8846;
    public static final int ESSYLLT_8847 = 8847;
    public static final int GNOME_8848 = 8848;
    public static final int PALADIN_8849 = 8849;
    public static final int PALADIN_8850 = 8850;
    public static final int KNIGHT_OF_ARDOUGNE_8851 = 8851;
    public static final int KNIGHT_OF_ARDOUGNE_8852 = 8852;
    public static final int PALADIN_8853 = 8853;
    public static final int KNIGHT_OF_ARDOUGNE_8854 = 8854;
    public static final int KNIGHT_OF_ARDOUGNE_8855 = 8855;
    public static final int EXECUTIONER = 8856;
    public static final int HOODED_FIGURE = 8857;
    public static final int MAN_8858 = 8858;
    public static final int MAN_8859 = 8859;
    public static final int MAN_8860 = 8860;
    public static final int MAN_8861 = 8861;
    public static final int MAN_8862 = 8862;
    public static final int WOMAN_8863 = 8863;
    public static final int WOMAN_8864 = 8864;
    public static final int ARIANWYN_8865 = 8865;
    public static final int ARIANWYN_8866 = 8866;
    public static final int ARIANWYN_8867 = 8867;
    public static final int ARIANWYN_8868 = 8868;
    public static final int MORVRAN_8869 = 8869;
    public static final int MORVRAN_8870 = 8870;
    public static final int ESSYLLT_8871 = 8871;
    public static final int ESSYLLT_8872 = 8872;
    public static final int LORD_IORWERTH_8873 = 8873;
    public static final int LORD_IORWERTH_8874 = 8874;
    public static final int LORD_IORWERTH_8875 = 8875;
    public static final int LORD_IORWERTH_8876 = 8876;
    public static final int IORWERTH_WARRIOR_8877 = 8877;
    public static final int IORWERTH_ARCHER_8878 = 8878;
    public static final int IORWERTH_WARRIOR_8879 = 8879;
    public static final int IORWERTH_ARCHER_8880 = 8880;
    public static final int IORWERTH_WARRIOR_8881 = 8881;
    public static final int IORWERTH_WARRIOR_8882 = 8882;
    public static final int IORWERTH_WARRIOR_8883 = 8883;
    public static final int IORWERTH_WARRIOR_8884 = 8884;
    public static final int IORWERTH_ARCHER_8885 = 8885;
    public static final int IORWERTH_ARCHER_8886 = 8886;
    public static final int REBEL_ARCHER = 8887;
    public static final int REBEL_ARCHER_8888 = 8888;
    public static final int REBEL_ARCHER_8889 = 8889;
    public static final int REBEL_WARRIOR = 8890;
    public static final int REBEL_WARRIOR_8891 = 8891;
    public static final int LADY_TANGWEN_TRAHAEARN = 8892;
    public static final int ELDERLY_ELF = 8893;
    public static final int ELDERLY_ELF_8894 = 8894;
    public static final int LADY_TANGWEN_TRAHAEARN_8895 = 8895;
    public static final int MYSTERIOUS_FIGURE = 8897;
    public static final int LORD_IEUAN_AMLODD = 8898;
    public static final int LORD_IEUAN_AMLODD_8899 = 8899;
    public static final int TREE_8901 = 8901;
    public static final int TREE_8902 = 8902;
    public static final int LORD_PIQUAN_CRWYS = 8903;
    public static final int LORD_PIQUAN_CRWYS_8904 = 8904;
    public static final int ELF_HERMIT = 8906;
    public static final int LADY_CARYS_HEFIN = 8907;
    public static final int LADY_CARYS_HEFIN_8908 = 8908;
    public static final int LADY_FFION_MEILYR = 8910;
    public static final int LADY_FFION_MEILYR_8911 = 8911;
    public static final int LADY_KELYN_ITHELL = 8913;
    public static final int KELYN = 8914;
    public static final int LADY_KELYN_ITHELL_8915 = 8915;
    public static final int FRAGMENT_OF_SEREN = 8917;
    public static final int FRAGMENT_OF_SEREN_8918 = 8918;
    public static final int FRAGMENT_OF_SEREN_8919 = 8919;
    public static final int FRAGMENT_OF_SEREN_8920 = 8920;
    public static final int CRYSTAL_WHIRLWIND = 8921;
    public static final int IORWERTH_WARRIOR_8922 = 8922;
    public static final int IORWERTH_ARCHER_8923 = 8923;
    public static final int IESTIN = 8924;
    public static final int IESTIN_8925 = 8925;
    public static final int IESTIN_8926 = 8926;
    public static final int MAWRTH_8927 = 8927;
    public static final int MAWRTH_8928 = 8928;
    public static final int IONA_8929 = 8929;
    public static final int EOIN_8930 = 8930;
    public static final int EOIN_8931 = 8931;
    public static final int REBEL_ARCHER_8932 = 8932;
    public static final int REBEL_ARCHER_8933 = 8933;
    public static final int REBEL_WARRIOR_8934 = 8934;
    public static final int REBEL_WARRIOR_8935 = 8935;
    public static final int IORWERTH_ARCHER_8936 = 8936;
    public static final int IORWERTH_ARCHER_8937 = 8937;
    public static final int IORWERTH_WARRIOR_8938 = 8938;
    public static final int IORWERTH_WARRIOR_8939 = 8939;
    public static final int KLANK_8940 = 8940;
    public static final int NILOOF_8941 = 8941;
    public static final int KAMEN_8942 = 8942;
    public static final int THORGEL_8943 = 8943;
    public static final int GENERAL_HINING_8944 = 8944;
    public static final int TYRAS_GUARD_8945 = 8945;
    public static final int REBEL_ARCHER_8946 = 8946;
    public static final int REBEL_ARCHER_8947 = 8947;
    public static final int REBEL_WARRIOR_8948 = 8948;
    public static final int REBEL_WARRIOR_8949 = 8949;
    public static final int ESSYLLT_8950 = 8950;
    public static final int BARRIER = 8951;
    public static final int BARRIER_8952 = 8952;
    public static final int IORWERTH_ARCHER_8953 = 8953;
    public static final int IORWERTH_ARCHER_8954 = 8954;
    public static final int IORWERTH_WARRIOR_8955 = 8955;
    public static final int IORWERTH_WARRIOR_8956 = 8956;
    public static final int REBEL_ARCHER_8957 = 8957;
    public static final int REBEL_ARCHER_8958 = 8958;
    public static final int REBEL_WARRIOR_8959 = 8959;
    public static final int REBEL_WARRIOR_8960 = 8960;
    public static final int TYRAS_GUARD_8961 = 8961;
    public static final int TYRAS_GUARD_8962 = 8962;
    public static final int TYRAS_GUARD_8963 = 8963;
    public static final int TYRAS_GUARD_8964 = 8964;
    public static final int REBEL_SCOUT = 8965;
    public static final int REBEL_SCOUT_8966 = 8966;
    public static final int CARLA_8967 = 8967;
    public static final int CLERK_8968 = 8968;
    public static final int HEAD_MOURNER = 8969;
    public static final int MOURNER_8970 = 8970;
    public static final int MOURNER_8971 = 8971;
    public static final int MOURNER_8972 = 8972;
    public static final int RECRUITER_8973 = 8973;
    public static final int JETHICK_8974 = 8974;
    public static final int MOURNER_8975 = 8975;
    public static final int KOFTIK_8976 = 8976;
    public static final int BLESSED_SPIDER_8978 = 8978;
    public static final int SLAVE_8979 = 8979;
    public static final int SLAVE_8980 = 8980;
    public static final int SLAVE_8981 = 8981;
    public static final int SLAVE_8982 = 8982;
    public static final int SLAVE_8983 = 8983;
    public static final int SLAVE_8984 = 8984;
    public static final int SLAVE_8985 = 8985;
    public static final int UNICORN_8986 = 8986;
    public static final int SIR_JERRO = 8987;
    public static final int SIR_CARL = 8988;
    public static final int SIR_HARRY = 8989;
    public static final int HALFSOULLESS = 8990;
    public static final int KARDIA = 8991;
    public static final int WITCHS_CAT = 8992;
    public static final int KALRAG = 8993;
    public static final int OTHAINIAN = 8994;
    public static final int DOOMION = 8995;
    public static final int HOLTHION = 8996;
    public static final int DISCIPLE_OF_IBAN = 8997;
    public static final int IBAN = 8998;
    public static final int MOURNER_8999 = 8999;
    public static final int MOURNER_9000 = 9000;
    public static final int KILRON_9001 = 9001;
    public static final int OMART_9002 = 9002;
    public static final int MOURNER_9003 = 9003;
    public static final int MOURNER_9004 = 9004;
    public static final int KING_LATHAS_9005 = 9005;
    public static final int KING_THOROS = 9006;
    public static final int MOURNER_9007 = 9007;
    public static final int MOURNER_9008 = 9008;
    public static final int MOURNER_9009 = 9009;
    public static final int MOURNER_9010 = 9010;
    public static final int KING_THOROS_9011 = 9011;
    public static final int MOURNER_9013 = 9013;
    public static final int ARIANWYN_9014 = 9014;
    public static final int KELYN_9015 = 9015;
    public static final int ESSYLLT_9016 = 9016;
    public static final int MOURNER_9017 = 9017;
    public static final int MOURNER_9018 = 9018;
    public static final int ED = 9019;
    public static final int BRYN = 9020;
    public static final int CRYSTALLINE_HUNLLEF = 9021;
    public static final int CRYSTALLINE_HUNLLEF_9022 = 9022;
    public static final int CRYSTALLINE_HUNLLEF_9023 = 9023;
    public static final int CRYSTALLINE_HUNLLEF_9024 = 9024;
    public static final int CRYSTALLINE_RAT = 9026;
    public static final int CRYSTALLINE_SPIDER = 9027;
    public static final int CRYSTALLINE_BAT = 9028;
    public static final int CRYSTALLINE_UNICORN = 9029;
    public static final int CRYSTALLINE_SCORPION = 9030;
    public static final int CRYSTALLINE_WOLF = 9031;
    public static final int CRYSTALLINE_BEAR = 9032;
    public static final int CRYSTALLINE_DRAGON = 9033;
    public static final int CRYSTALLINE_DARK_BEAST = 9034;
    public static final int CORRUPTED_HUNLLEF = 9035;
    public static final int CORRUPTED_HUNLLEF_9036 = 9036;
    public static final int CORRUPTED_HUNLLEF_9037 = 9037;
    public static final int CORRUPTED_HUNLLEF_9038 = 9038;
    public static final int CORRUPTED_RAT = 9040;
    public static final int CORRUPTED_SPIDER = 9041;
    public static final int CORRUPTED_BAT = 9042;
    public static final int CORRUPTED_UNICORN = 9043;
    public static final int CORRUPTED_SCORPION = 9044;
    public static final int CORRUPTED_WOLF = 9045;
    public static final int CORRUPTED_BEAR = 9046;
    public static final int CORRUPTED_DRAGON = 9047;
    public static final int CORRUPTED_DARK_BEAST = 9048;
    public static final int ZALCANO = 9049;
    public static final int ZALCANO_9050 = 9050;
    public static final int GOLEM_9051 = 9051;
    public static final int RHIANNON = 9052;
    public static final int AMROD = 9053;
    public static final int MIRIEL = 9054;
    public static final int CURUFIN = 9055;
    public static final int ENERDHIL = 9056;
    public static final int TATIE = 9057;
    public static final int FINDUILAS = 9058;
    public static final int GELMIR = 9059;
    public static final int MITHRELLAS = 9060;
    public static final int ERESTOR = 9061;
    public static final int LINDIR = 9062;
    public static final int IDRIL = 9063;
    public static final int INGWION = 9064;
    public static final int THINGOL = 9065;
    public static final int ELENWE = 9066;
    public static final int OROPHIN = 9067;
    public static final int VAIRE = 9068;
    public static final int ELLADAN = 9069;
    public static final int GUILIN = 9070;
    public static final int INGWE = 9071;
    public static final int CIRDAN = 9072;
    public static final int GLORFINDEL = 9073;
    public static final int AREDHEL = 9074;
    public static final int CELEGORM = 9075;
    public static final int ANAIRE = 9076;
    public static final int MAEGLIN = 9077;
    public static final int EDRAHIL = 9078;
    public static final int FINGON = 9079;
    public static final int SALGANT = 9080;
    public static final int CELEBRIAN = 9081;
    public static final int IMIN = 9082;
    public static final int OROPHER = 9083;
    public static final int FINGOLFIN = 9084;
    public static final int MAHTAN = 9085;
    public static final int INDIS = 9086;
    public static final int IMINYE = 9087;
    public static final int FEANOR = 9088;
    public static final int SAEROS = 9089;
    public static final int NELLAS = 9090;
    public static final int RHYFEL = 9091;
    public static final int GWYL = 9092;
    public static final int ENILLY = 9093;
    public static final int FFONI = 9094;
    public static final int YMLADD = 9095;
    public static final int SADWRN = 9096;
    public static final int DIOL = 9097;
    public static final int YSBEID = 9098;
    public static final int CLEDDYF = 9099;
    public static final int SAETH = 9100;
    public static final int NIMRODEL = 9101;
    public static final int MAEDHROS = 9102;
    public static final int FINARFIN = 9103;
    public static final int GWINDOR = 9104;
    public static final int ELDALOTE = 9105;
    public static final int ENELYE = 9106;
    public static final int NERDANEL = 9107;
    public static final int NIMLOTH = 9108;
    public static final int FINDIS = 9109;
    public static final int EARWEN = 9110;
    public static final int CARANTHIR = 9111;
    public static final int ENEL = 9112;
    public static final int HENDOR_9113 = 9113;
    public static final int GALATHIL = 9114;
    public static final int TURGON = 9115;
    public static final int LENWE = 9116;
    public static final int ARANWE = 9117;
    public static final int RABBIT_9118 = 9118;
    public static final int LORD_IEUAN_AMLODD_9119 = 9119;
    public static final int LORD_BAXTORIAN_CADARN = 9120;
    public static final int LADY_KELYN_ITHELL_9121 = 9121;
    public static final int LADY_TANGWEN_TRAHAEARN_9122 = 9122;
    public static final int LORD_PIQUAN_CRWYS_9123 = 9123;
    public static final int LADY_FFION_MEILYR_9124 = 9124;
    public static final int LADY_CARYS_HEFIN_9125 = 9125;
    public static final int LORD_IESTIN_IORWERTH = 9126;
    public static final int BANKER_9127 = 9127;
    public static final int BANKER_9128 = 9128;
    public static final int BANKER_9129 = 9129;
    public static final int BANKER_9130 = 9130;
    public static final int BANKER_9131 = 9131;
    public static final int BANKER_9132 = 9132;
    public static final int REESE = 9133;
    public static final int REESE_9134 = 9134;
    public static final int CONWENNA = 9135;
    public static final int CONWENNA_9136 = 9136;
    public static final int ALWYN = 9137;
    public static final int OSWALLT = 9138;
    public static final int EIRA = 9139;
    public static final int SAWMILL_OPERATOR_9140 = 9140;
    public static final int PENNANT = 9141;
    public static final int PENNANT_9142 = 9142;
    public static final int GLADIATOR = 9143;
    public static final int GLADIATOR_9144 = 9144;
    public static final int ELUNED_9145 = 9145;
    public static final int ISLWYN_9146 = 9146;
    public static final int ELENA_9148 = 9148;
    public static final int MORVRAN_9149 = 9149;
    public static final int LILIFANG = 9150;
    public static final int CREFYDD = 9151;
    public static final int YSTWYTH = 9152;
    public static final int IWAN = 9153;
    public static final int DERWEN_9154 = 9154;
    public static final int ELGAN = 9155;
    public static final int CELYN = 9156;
    public static final int ANEIRIN = 9157;
    public static final int GWALLTER = 9158;
    public static final int GWYN = 9159;
    public static final int OSIAN = 9160;
    public static final int CAERWYN = 9161;
    public static final int ANWEN = 9162;
    public static final int GLENDA = 9163;
    public static final int GUINEVERE = 9164;
    public static final int NIA = 9165;
    public static final int SIAN = 9166;
    public static final int BRANWEN = 9167;
    public static final int LLIO = 9168;
    public static final int EFA = 9169;
    public static final int LLIANN = 9170;
    public static final int FISHING_SPOT_9171 = 9171;
    public static final int FISHING_SPOT_9172 = 9172;
    public static final int FISHING_SPOT_9173 = 9173;
    public static final int FISHING_SPOT_9174 = 9174;
    public static final int RHODDWR_TAN = 9175;
    public static final int MEDDWL_YMLAEN_LLAW = 9176;
    public static final int LLEIDR_GOLAU = 9177;
    public static final int GWERIN_HAPUS = 9178;
    public static final int PRESWYLWYR_DALL = 9179;
    public static final int MARWOLAETH_DAWNSIO = 9180;
    public static final int DIRE_WOLF_9181 = 9181;
    public static final int GUARD_9182 = 9182;
    public static final int GUARD_9183 = 9183;
    public static final int GUARD_9184 = 9184;
    public static final int GUARD_9185 = 9185;
    public static final int GUARD_9186 = 9186;
    public static final int GUARD_9187 = 9187;
    public static final int GUARD_9188 = 9188;
    public static final int GUARD_9189 = 9189;
    public static final int GUARD_9190 = 9190;
    public static final int GUARD_9191 = 9191;
    public static final int RAVEN = 9192;
    public static final int LENNY = 9193;
    public static final int GHOST_9194 = 9194;
    public static final int FLOKI = 9195;
    public static final int SURMA = 9196;
    public static final int SLIPPERS = 9197;
    public static final int RED_PANDA = 9198;
    public static final int BEAR_CUB_9199 = 9199;
    public static final int ED_9200 = 9200;
    public static final int CRAB_9201 = 9201;
    public static final int TIDE = 9202;
    public static final int ADVENTURER_JON_9244 = 9244;
    public static final int ARIANWYN_HARD = 9246;
    public static final int ESSYLLT_HARD = 9247;
    public static final int ARIANWYN_9248 = 9248;
    public static final int ESSYLLT_9249 = 9249;
    public static final int CAPTAIN_BARNABY_9250 = 9250;
    public static final int TOY_SOLDIER_9251 = 9251;
    public static final int TOY_DOLL = 9252;
    public static final int TOY_DOLL_9253 = 9253;
    public static final int TOY_MOUSE = 9254;
    public static final int TOY_MOUSE_9255 = 9255;
    public static final int PENGUIN_SUIT_9257 = 9257;
    public static final int BASILISK_SENTINEL = 9258;
    public static final int OSPAK_9259 = 9259;
    public static final int STYRMIR_9260 = 9260;
    public static final int TORBRUND_9261 = 9261;
    public static final int FRIDGEIR_9262 = 9262;
    public static final int BRUNDT_THE_CHIEFTAIN_9263 = 9263;
    public static final int THORA_THE_BARKEEP_9264 = 9264;
    public static final int BRUNDT_THE_CHIEFTAIN_9265 = 9265;
    public static final int BRUNDT_THE_CHIEFTAIN_9266 = 9266;
    public static final int BRUNDT_THE_CHIEFTAIN_9267 = 9267;
    public static final int BRUNDT_THE_CHIEFTAIN_9268 = 9268;
    public static final int HASKELL = 9270;
    public static final int HASKELL_9271 = 9271;
    public static final int HASKELL_9272 = 9272;
    public static final int AGNAR_9273 = 9273;
    public static final int OLAF_THE_BARD_9274 = 9274;
    public static final int MANNI_THE_REVELLER_9275 = 9275;
    public static final int FREMENNIK_WARRIOR = 9276;
    public static final int FREMENNIK_WARRIOR_9277 = 9277;
    public static final int BRUNDT_THE_CHIEFTAIN_9278 = 9278;
    public static final int BRUNDT_THE_CHIEFTAIN_9279 = 9279;
    public static final int THORVALD_THE_WARRIOR_9280 = 9280;
    public static final int KOSCHEI_THE_DEATHLESS_9281 = 9281;
    public static final int BASILISK_YOUNGLING = 9282;
    public static final int BASILISK_9283 = 9283;
    public static final int BASILISK_9284 = 9284;
    public static final int BASILISK_9285 = 9285;
    public static final int BASILISK_9286 = 9286;
    public static final int MONSTROUS_BASILISK_9287 = 9287;
    public static final int MONSTROUS_BASILISK_9288 = 9288;
    public static final int THE_JORMUNGAND = 9289;
    public static final int THE_JORMUNGAND_9290 = 9290;
    public static final int THE_JORMUNGAND_9291 = 9291;
    public static final int THE_JORMUNGAND_9292 = 9292;
    public static final int BASILISK_KNIGHT = 9293;
    public static final int BAKUNA = 9294;
    public static final int TYPHOR = 9295;
    public static final int TYPHOR_9296 = 9296;
    public static final int VRITRA = 9297;
    public static final int MAZ = 9298;
    public static final int TRADER_STAN = 9299;
    public static final int TRADER_STAN_9300 = 9300;
    public static final int TRADER_STAN_9301 = 9301;
    public static final int TRADER_STAN_9302 = 9302;
    public static final int TRADER_STAN_9303 = 9303;
    public static final int TRADER_STAN_9304 = 9304;
    public static final int TRADER_STAN_9305 = 9305;
    public static final int LOKAR_SEARUNNER_9306 = 9306;
    public static final int TRADER_STAN_9307 = 9307;
    public static final int TRADER_STAN_9308 = 9308;
    public static final int TRADER_STAN_9309 = 9309;
    public static final int TRADER_STAN_9310 = 9310;
    public static final int TRADER_STAN_9311 = 9311;
    public static final int TRADER_CREWMEMBER = 9312;
    public static final int TRADER_CREWMEMBER_9313 = 9313;
    public static final int TRADER_CREWMEMBER_9314 = 9314;
    public static final int TRADER_CREWMEMBER_9315 = 9315;
    public static final int TRADER_CREWMEMBER_9316 = 9316;
    public static final int TRADER_CREWMEMBER_9317 = 9317;
    public static final int TRADER_CREWMEMBER_9318 = 9318;
    public static final int TRADER_CREWMEMBER_9319 = 9319;
    public static final int TRADER_CREWMEMBER_9320 = 9320;
    public static final int TRADER_CREWMEMBER_9321 = 9321;
    public static final int TRADER_CREWMEMBER_9322 = 9322;
    public static final int TRADER_CREWMEMBER_9323 = 9323;
    public static final int TRADER_CREWMEMBER_9324 = 9324;
    public static final int TRADER_CREWMEMBER_9325 = 9325;
    public static final int TRADER_CREWMEMBER_9326 = 9326;
    public static final int TRADER_CREWMEMBER_9327 = 9327;
    public static final int TRADER_CREWMEMBER_9328 = 9328;
    public static final int TRADER_CREWMEMBER_9329 = 9329;
    public static final int TRADER_CREWMEMBER_9330 = 9330;
    public static final int TRADER_CREWMEMBER_9331 = 9331;
    public static final int TRADER_CREWMEMBER_9332 = 9332;
    public static final int TRADER_CREWMEMBER_9333 = 9333;
    public static final int TRADER_CREWMEMBER_9334 = 9334;
    public static final int TRADER_CREWMEMBER_9335 = 9335;
    public static final int TRADER_CREWMEMBER_9336 = 9336;
    public static final int TRADER_CREWMEMBER_9337 = 9337;
    public static final int TRADER_CREWMEMBER_9338 = 9338;
    public static final int TRADER_CREWMEMBER_9339 = 9339;
    public static final int TRADER_CREWMEMBER_9340 = 9340;
    public static final int TRADER_CREWMEMBER_9341 = 9341;
    public static final int TRADER_CREWMEMBER_9342 = 9342;
    public static final int TRADER_CREWMEMBER_9343 = 9343;
    public static final int TRADER_CREWMEMBER_9344 = 9344;
    public static final int TRADER_CREWMEMBER_9345 = 9345;
    public static final int TRADER_CREWMEMBER_9346 = 9346;
    public static final int TRADER_CREWMEMBER_9347 = 9347;
    public static final int TRADER_CREWMEMBER_9348 = 9348;
    public static final int TRADER_CREWMEMBER_9349 = 9349;
    public static final int TRADER_CREWMEMBER_9350 = 9350;
    public static final int TRADER_CREWMEMBER_9351 = 9351;
    public static final int TRADER_CREWMEMBER_9352 = 9352;
    public static final int TRADER_CREWMEMBER_9353 = 9353;
    public static final int TRADER_CREWMEMBER_9354 = 9354;
    public static final int TRADER_CREWMEMBER_9355 = 9355;
    public static final int TRADER_CREWMEMBER_9356 = 9356;
    public static final int TRADER_CREWMEMBER_9357 = 9357;
    public static final int TRADER_CREWMEMBER_9358 = 9358;
    public static final int TRADER_CREWMEMBER_9359 = 9359;
    public static final int TRADER_CREWMEMBER_9360 = 9360;
    public static final int TRADER_CREWMEMBER_9361 = 9361;
    public static final int TRADER_CREWMEMBER_9362 = 9362;
    public static final int TRADER_CREWMEMBER_9363 = 9363;
    public static final int TRADER_CREWMEMBER_9364 = 9364;
    public static final int TRADER_CREWMEMBER_9365 = 9365;
    public static final int TRADER_CREWMEMBER_9366 = 9366;
    public static final int TRADER_CREWMEMBER_9367 = 9367;
    public static final int TRADER_CREWMEMBER_9368 = 9368;
    public static final int TRADER_CREWMEMBER_9369 = 9369;
    public static final int TRADER_CREWMEMBER_9370 = 9370;
    public static final int TRADER_CREWMEMBER_9371 = 9371;
    public static final int TRADER_CREWMEMBER_9372 = 9372;
    public static final int TRADER_CREWMEMBER_9373 = 9373;
    public static final int TRADER_CREWMEMBER_9374 = 9374;
    public static final int TRADER_CREWMEMBER_9375 = 9375;
    public static final int TRADER_CREWMEMBER_9376 = 9376;
    public static final int TRADER_CREWMEMBER_9377 = 9377;
    public static final int TRADER_CREWMEMBER_9378 = 9378;
    public static final int TRADER_CREWMEMBER_9379 = 9379;
    public static final int TRADER_CREWMEMBER_9380 = 9380;
    public static final int TRADER_CREWMEMBER_9381 = 9381;
    public static final int TRADER_CREWMEMBER_9382 = 9382;
    public static final int TRADER_CREWMEMBER_9383 = 9383;
    public static final int LITTLE_NIGHTMARE = 9398;
    public static final int LITTLE_NIGHTMARE_9399 = 9399;
    public static final int OATH_LORD_DROWS = 9400;
    public static final int OATHBREAKER_MALS = 9401;
    public static final int OATHBREAKER_EPIWS = 9402;
    public static final int OATHBREAKER_BATS = 9403;
    public static final int LITHIL = 9404;
    public static final int SISTER_ASERET = 9405;
    public static final int SISTER_NAOJ = 9406;
    public static final int SISTER_SALOHCIN = 9407;
    public static final int LUMIERE = 9408;
    public static final int DAER_KRAND = 9409;
    public static final int STRONG_RONNY = 9410;
    public static final int SHURA = 9413;
    public static final int SHURA_9414 = 9414;
    public static final int ACORN = 9415;
    public static final int PHOSANIS_NIGHTMARE_9416 = 9416;
    public static final int PHOSANIS_NIGHTMARE_9417 = 9417;
    public static final int PHOSANIS_NIGHTMARE_9418 = 9418;
    public static final int PHOSANIS_NIGHTMARE_9419 = 9419;
    public static final int PHOSANIS_NIGHTMARE_9420 = 9420;
    public static final int PHOSANIS_NIGHTMARE_9421 = 9421;
    public static final int PHOSANIS_NIGHTMARE_9422 = 9422;
    public static final int PHOSANIS_NIGHTMARE_9423 = 9423;
    public static final int PHOSANIS_NIGHTMARE_9424 = 9424;
    public static final int THE_NIGHTMARE_9425 = 9425;
    public static final int THE_NIGHTMARE_9426 = 9426;
    public static final int THE_NIGHTMARE_9427 = 9427;
    public static final int THE_NIGHTMARE_9428 = 9428;
    public static final int THE_NIGHTMARE_9429 = 9429;
    public static final int THE_NIGHTMARE_9430 = 9430;
    public static final int THE_NIGHTMARE_9431 = 9431;
    public static final int THE_NIGHTMARE_9432 = 9432;
    public static final int THE_NIGHTMARE_9433 = 9433;
    public static final int TOTEM = 9434;
    public static final int TOTEM_9435 = 9435;
    public static final int TOTEM_9436 = 9436;
    public static final int TOTEM_9437 = 9437;
    public static final int TOTEM_9438 = 9438;
    public static final int TOTEM_9439 = 9439;
    public static final int TOTEM_9440 = 9440;
    public static final int TOTEM_9441 = 9441;
    public static final int TOTEM_9442 = 9442;
    public static final int TOTEM_9443 = 9443;
    public static final int TOTEM_9444 = 9444;
    public static final int TOTEM_9445 = 9445;
    public static final int SLEEPWALKER_9446 = 9446;
    public static final int SLEEPWALKER_9447 = 9447;
    public static final int SLEEPWALKER_9448 = 9448;
    public static final int SLEEPWALKER_9449 = 9449;
    public static final int SLEEPWALKER_9450 = 9450;
    public static final int SLEEPWALKER_9451 = 9451;
    public static final int PARASITE = 9452;
    public static final int PARASITE_9453 = 9453;
    public static final int HUSK = 9454;
    public static final int HUSK_9455 = 9455;
    public static final int THE_NIGHTMARE_9460 = 9460;
    public static final int THE_NIGHTMARE_9461 = 9461;
    public static final int THE_NIGHTMARE_9462 = 9462;
    public static final int THE_NIGHTMARE_9463 = 9463;
    public static final int THE_NIGHTMARE_9464 = 9464;
    public static final int INFERNAL_PYRELORD = 9465;
    public static final int HUSK_9466 = 9466;
    public static final int HUSK_9467 = 9467;
    public static final int PARASITE_9468 = 9468;
    public static final int PARASITE_9469 = 9469;
    public static final int SLEEPWALKER_9470 = 9470;
    public static final int SISTER_SENGA = 9471;
    public static final int SISTER_SENGA_9472 = 9472;
    public static final int ENT_TRUNK = 9474;
    public static final int GIELINOR_GUIDE_9476 = 9476;
    public static final int SURVIVAL_EXPERT_9477 = 9477;
    public static final int FISHING_SPOT_9478 = 9478;
    public static final int MASTER_NAVIGATOR = 9479;
    public static final int QUEST_GUIDE_9480 = 9480;
    public static final int MINING_INSTRUCTOR_9481 = 9481;
    public static final int COMBAT_INSTRUCTOR_9482 = 9482;
    public static final int GIANT_RAT_9483 = 9483;
    public static final int BANKER_9484 = 9484;
    public static final int BROTHER_BRACE_9485 = 9485;
    public static final int IRON_MAN_TUTOR_9486 = 9486;
    public static final int MAGIC_INSTRUCTOR_9487 = 9487;
    public static final int CHICKEN_9488 = 9488;
    public static final int VELIAF_HURTZ_9489 = 9489;
    public static final int HAMELN_THE_JESTER = 9490;
    public static final int HANCHEN_THE_HOUND = 9491;
    public static final int TANGLEROOT_9492 = 9492;
    public static final int TANGLEROOT_9493 = 9493;
    public static final int TANGLEROOT_9494 = 9494;
    public static final int TANGLEROOT_9495 = 9495;
    public static final int TANGLEROOT_9496 = 9496;
    public static final int TANGLEROOT_9497 = 9497;
    public static final int TANGLEROOT_9498 = 9498;
    public static final int TANGLEROOT_9499 = 9499;
    public static final int TANGLEROOT_9500 = 9500;
    public static final int TANGLEROOT_9501 = 9501;
    public static final int IORWERTH_WARRIOR_9502 = 9502;
    public static final int IORWERTH_WARRIOR_9503 = 9503;
    public static final int ACCOUNT_SECURITY_TUTOR = 9504;
    public static final int HAMELN_THE_JESTER_9505 = 9505;
    public static final int HANCHEN_THE_HOUND_9506 = 9506;
    public static final int ENRAGED_TEKTINY = 9511;
    public static final int FLYING_VESPINA = 9512;
    public static final int ENRAGED_TEKTINY_9513 = 9513;
    public static final int FLYING_VESPINA_9514 = 9514;
    public static final int VELIAF_HURTZ_9521 = 9521;
    public static final int VELIAF_HURTZ_9522 = 9522;
    public static final int VELIAF_HURTZ_9523 = 9523;
    public static final int VELIAF_HURTZ_9524 = 9524;
    public static final int VELIAF_HURTZ_9525 = 9525;
    public static final int VELIAF_HURTZ_9526 = 9526;
    public static final int VELIAF_HURTZ_9527 = 9527;
    public static final int VELIAF_HURTZ_9528 = 9528;
    public static final int VELIAF_HURTZ_9529 = 9529;
    public static final int IVAN_STROM_9530 = 9530;
    public static final int IVAN_STROM_9531 = 9531;
    public static final int IVAN_STROM_9532 = 9532;
    public static final int IVAN_STROM_9533 = 9533;
    public static final int IVAN_STROM_9534 = 9534;
    public static final int IVAN_STROM_9535 = 9535;
    public static final int IVAN_STROM_9536 = 9536;
    public static final int SAFALAAN_HALLOW_9537 = 9537;
    public static final int SAFALAAN_HALLOW_9538 = 9538;
    public static final int SAFALAAN_HALLOW_9539 = 9539;
    public static final int SAFALAAN_HALLOW_9540 = 9540;
    public static final int SAFALAAN_HALLOW_9541 = 9541;
    public static final int SAFALAAN_HALLOW_9542 = 9542;
    public static final int KAEL_FORSHAW_9543 = 9543;
    public static final int KAEL_FORSHAW_9544 = 9544;
    public static final int KAEL_FORSHAW_9545 = 9545;
    public static final int KAEL_FORSHAW_9546 = 9546;
    public static final int VERTIDA_SEFALATIS_9547 = 9547;
    public static final int VERTIDA_SEFALATIS_9548 = 9548;
    public static final int VERTIDA_SEFALATIS_9549 = 9549;
    public static final int VERTIDA_SEFALATIS_9550 = 9550;
    public static final int RADIGAD_PONFIT_9551 = 9551;
    public static final int RADIGAD_PONFIT_9552 = 9552;
    public static final int RADIGAD_PONFIT_9553 = 9553;
    public static final int POLMAFI_FERDYGRIS_9554 = 9554;
    public static final int POLMAFI_FERDYGRIS_9555 = 9555;
    public static final int POLMAFI_FERDYGRIS_9556 = 9556;
    public static final int CARL = 9557;
    public static final int CARL_9558 = 9558;
    public static final int KROY = 9559;
    public static final int KROY_9560 = 9560;
    public static final int DAMIEN_LEUCURTE = 9561;
    public static final int DAMIEN_LEUCURTE_9562 = 9562;
    public static final int DAMIEN_LEUCURTE_9563 = 9563;
    public static final int DAMIEN_LEUCURTE_9564 = 9564;
    public static final int LORD_CROMBWICK = 9565;
    public static final int VANSTROM_KLAUSE_9566 = 9566;
    public static final int VANSTROM_KLAUSE_9567 = 9567;
    public static final int VANSTROM_KLAUSE_9568 = 9568;
    public static final int VANSTROM_KLAUSE_9569 = 9569;
    public static final int VANSTROM_KLAUSE_9570 = 9570;
    public static final int VANSTROM_KLAUSE_9571 = 9571;
    public static final int MIST_9572 = 9572;
    public static final int ACIDIC_BLOODVELD = 9573;
    public static final int VANESCULA_DRAKAN_9574 = 9574;
    public static final int VANESCULA_DRAKAN_9575 = 9575;
    public static final int VANESCULA_DRAKAN_9576 = 9576;
    public static final int VANESCULA_DRAKAN_9577 = 9577;
    public static final int LORD_ALEXEI_JOVKAI = 9578;
    public static final int LOWERNIEL_DRAKAN = 9579;
    public static final int WEREWOLF_9580 = 9580;
    public static final int WEREWOLF_9581 = 9581;
    public static final int WEREWOLF_9582 = 9582;
    public static final int PRISONER = 9583;
    public static final int PRISONER_9584 = 9584;
    public static final int PRISONER_9585 = 9585;
    public static final int VAMPYRE_JUVINATE_9586 = 9586;
    public static final int VAMPYRE_JUVINATE_9587 = 9587;
    public static final int DESMODUS_LASIURUS = 9588;
    public static final int MORDAN_NIKAZSI = 9589;
    public static final int VYREWATCH_9590 = 9590;
    public static final int VYREWATCH_9591 = 9591;
    public static final int PRISONER_9592 = 9592;
    public static final int PRISONER_9593 = 9593;
    public static final int MARIA_GADDERANKS = 9594;
    public static final int MARIA_GADDERANKS_9595 = 9595;
    public static final int RON_GADDERANKS = 9596;
    public static final int RON_GADDERANKS_9597 = 9597;
    public static final int SARIUS_GUILE_9598 = 9598;
    public static final int VYREWATCH_SENTINEL = 9599;
    public static final int VYREWATCH_SENTINEL_9600 = 9600;
    public static final int VYREWATCH_SENTINEL_9601 = 9601;
    public static final int VYREWATCH_SENTINEL_9602 = 9602;
    public static final int VYREWATCH_SENTINEL_9603 = 9603;
    public static final int VYREWATCH_SENTINEL_9604 = 9604;
    public static final int VYREWATCH_9605 = 9605;
    public static final int VYREWATCH_9606 = 9606;
    public static final int VYREWATCH_9607 = 9607;
    public static final int VYREWATCH_9608 = 9608;
    public static final int SEAGULL_9609 = 9609;
    public static final int MUTATED_BLOODVELD_9610 = 9610;
    public static final int MUTATED_BLOODVELD_9611 = 9611;
    public static final int NAIL_BEAST_9612 = 9612;
    public static final int NAIL_BEAST_9613 = 9613;
    public static final int VAMPYRE_JUVINATE_9614 = 9614;
    public static final int VAMPYRE_JUVINATE_9615 = 9615;
    public static final int VAMPYRE_JUVINATE_9616 = 9616;
    public static final int VAMPYRE_JUVINATE_9617 = 9617;
    public static final int CURPILE_FYOD = 9619;
    public static final int VELIAF_HURTZ_9621 = 9621;
    public static final int SANI_PILIU = 9622;
    public static final int SANI_PILIU_9623 = 9623;
    public static final int HAROLD_EVANS = 9624;
    public static final int HAROLD_EVANS_9625 = 9625;
    public static final int RADIGAD_PONFIT_9627 = 9627;
    public static final int POLMAFI_FERDYGRIS_9629 = 9629;
    public static final int IVAN_STROM_9631 = 9631;
    public static final int VANSTROM_KLAUSE_9632 = 9632;
    public static final int VELIAF_HURTZ_9633 = 9633;
    public static final int WISKIT_9634 = 9634;
    public static final int GADDERANKS_9635 = 9635;
    public static final int DREZEL = 9636;
    public static final int DARK_SQUIRREL = 9637;
    public static final int DARK_SQUIRREL_9638 = 9638;
    public static final int MYSTERIOUS_STRANGER_9639 = 9639;
    public static final int MYSTERIOUS_STRANGER_9640 = 9640;
    public static final int BAT_9641 = 9641;
    public static final int BAT_9642 = 9642;
    public static final int SPIDER_9643 = 9643;
    public static final int SPIDER_9644 = 9644;
    public static final int FISH_9645 = 9645;
    public static final int FISH_9646 = 9646;
    public static final int KNIGHT_OF_THE_OWL = 9648;
    public static final int KNIGHT_OF_THE_UNICORN = 9650;
    public static final int KNIGHT_OF_THE_WOLF = 9652;
    public static final int KNIGHT_OF_THE_LION = 9654;
    public static final int ARCHPRIEST_OF_THE_UNICORN = 9656;
    public static final int DARKMEYER_SLAVE = 9657;
    public static final int DARKMEYER_SLAVE_9658 = 9658;
    public static final int MAD_MELVIN96 = 9659;
    public static final int R2T2PNSH0TY = 9660;
    public static final int JYN = 9661;
    public static final int C4SSI4N = 9662;
    public static final int FISHRUNNER82 = 9663;
    public static final int WEAST_SIDE49 = 9664;
    public static final int C0LECT0R890 = 9665;
    public static final int GIANT_SQUIRREL_9666 = 9666;
    public static final int OWL = 9667;
    public static final int OWL_9668 = 9668;
    public static final int NORANNA_TYTANIN = 9675;
    public static final int NORANNA_TYTANIN_9676 = 9676;
    public static final int SLAVE_9677 = 9677;
    public static final int SLAVE_9678 = 9678;
    public static final int SLAVE_9679 = 9679;
    public static final int SLAVE_9680 = 9680;
    public static final int SLAVE_9681 = 9681;
    public static final int SLAVE_9682 = 9682;
    public static final int VAMPYRE_JUVINATE_9683 = 9683;
    public static final int VAMPYRE_JUVINATE_9684 = 9684;
    public static final int VALENTIN_RASPUTIN = 9685;
    public static final int VON_VAN_VON = 9686;
    public static final int VLAD_BECHSTEIN = 9687;
    public static final int DRACONIS_SANGUINE = 9688;
    public static final int MORT_NIGHTSHADE = 9689;
    public static final int VAMPYRUS_DIAEMUS = 9690;
    public static final int CARNIVUS_BELAMORTA = 9691;
    public static final int VORMAR_VAKAN = 9692;
    public static final int MISDRIEVUS_SHADUM = 9693;
    public static final int VLAD_DIAEMUS = 9694;
    public static final int NOCTILLION_LUGOSI = 9695;
    public static final int ALEK_CONSTANTINE = 9696;
    public static final int GRIGOR_RASPUTIN = 9697;
    public static final int HAEMAS_LAMESCUS = 9698;
    public static final int REMUS_KANINUS = 9699;
    public static final int VALLESSIA_DRACYULA = 9700;
    public static final int VIOLETTA_SANGUINE = 9701;
    public static final int DIPHYLLA_BECHSTEIN = 9702;
    public static final int EPISCULA_HELSING = 9703;
    public static final int VAMPYRESSA_VAN_VON = 9704;
    public static final int VALLESSIA_VON_PITT = 9705;
    public static final int VONNETTA_VARNIS = 9706;
    public static final int NATALIDAE_SHADUM = 9707;
    public static final int MORTINA_DAUBENTON = 9708;
    public static final int LASENNA_RASPUTIN = 9709;
    public static final int CANINELLE_DRAYNAR = 9710;
    public static final int VALENTINA_DIAEMUS = 9711;
    public static final int NAKASA_JOVKAI = 9712;
    public static final int CRIMSONETTE_VAN_MARR = 9713;
    public static final int PIPISTRELLE_DRAYNAR = 9714;
    public static final int LADY_NADEZHDA_SHADUM = 9715;
    public static final int LORD_MISCHA_MYRMEL = 9716;
    public static final int LORD_ALEXEI_JOVKAI_9717 = 9717;
    public static final int BANKER_9718 = 9718;
    public static final int BANKER_9719 = 9719;
    public static final int GRINKA_KRAST = 9720;
    public static final int DRASDAN_RANOR = 9721;
    public static final int DESPOINA_CALLIDRA = 9722;
    public static final int LENYIG_KARNA = 9723;
    public static final int VARRIAN_SOBAK = 9724;
    public static final int SLAVE_9725 = 9725;
    public static final int SLAVE_9726 = 9726;
    public static final int VAMPYRE_JUVINATE_9727 = 9727;
    public static final int VAMPYRE_JUVINATE_9728 = 9728;
    public static final int VAMPYRE_JUVINATE_9729 = 9729;
    public static final int VAMPYRE_JUVINATE_9730 = 9730;
    public static final int VAMPYRE_JUVENILE_9731 = 9731;
    public static final int VAMPYRE_JUVENILE_9732 = 9732;
    public static final int VAMPYRE_JUVENILE_9733 = 9733;
    public static final int VAMPYRE_JUVENILE_9734 = 9734;
    public static final int VYREWATCH_9735 = 9735;
    public static final int VYREWATCH_9736 = 9736;
    public static final int VYREWATCH_9737 = 9737;
    public static final int VYREWATCH_9738 = 9738;
    public static final int VYREWATCH_9739 = 9739;
    public static final int VYREWATCH_9740 = 9740;
    public static final int VYREWATCH_9741 = 9741;
    public static final int VYREWATCH_9742 = 9742;
    public static final int WEREWOLF_9743 = 9743;
    public static final int WEREWOLF_9744 = 9744;
    public static final int WEREWOLF_9745 = 9745;
    public static final int AUCTIONEER = 9746;
    public static final int FRANK_9749 = 9749;
    public static final int SPECTATOR = 9750;
    public static final int SPECTATOR_9751 = 9751;
    public static final int SPECTATOR_9752 = 9752;
    public static final int SPECTATOR_9753 = 9753;
    public static final int SPECTATOR_9754 = 9754;
    public static final int SPECTATOR_9755 = 9755;
    public static final int VYREWATCH_SENTINEL_9756 = 9756;
    public static final int VYREWATCH_SENTINEL_9757 = 9757;
    public static final int VYREWATCH_SENTINEL_9758 = 9758;
    public static final int VYREWATCH_SENTINEL_9759 = 9759;
    public static final int VYREWATCH_SENTINEL_9760 = 9760;
    public static final int VYREWATCH_SENTINEL_9761 = 9761;
    public static final int VYREWATCH_SENTINEL_9762 = 9762;
    public static final int VYREWATCH_SENTINEL_9763 = 9763;
    public static final int YENRAB_9764 = 9764;
    public static final int LAHSRAM_9765 = 9765;
    public static final int ERODOEHT_9766 = 9766;
    public static final int CARL_9767 = 9767;
    public static final int ROY = 9768;
    public static final int LECTOR_GURA_9769 = 9769;
    public static final int SISTER_SEVI_9770 = 9770;
    public static final int SISTER_TOEN_9771 = 9771;
    public static final int SISTER_YRAM_9772 = 9772;
    public static final int KROY_9773 = 9773;
    public static final int DAMIEN_LEUCURTE_9774 = 9774;
    public static final int LORD_CROMBWICK_9775 = 9775;
    public static final int PAINTED_ONE = 9776;
    public static final int PAINTED_ONE_9777 = 9777;
    public static final int KURT = 9778;
    public static final int DON = 9779;
    public static final int DEBRA = 9780;
    public static final int TANYA_9781 = 9781;
    public static final int KURT_9782 = 9782;
    public static final int DON_9783 = 9783;
    public static final int DEBRA_9784 = 9784;
    public static final int TANYA_9785 = 9785;
    public static final int CHILD_9786 = 9786;
    public static final int CHILD_9787 = 9787;
    public static final int CHILD_9788 = 9788;
    public static final int CHILD_9789 = 9789;
    public static final int CHILD_9790 = 9790;
    public static final int CHILD_9791 = 9791;
    public static final int CHILD_9792 = 9792;
    public static final int CHILD_9793 = 9793;
    public static final int CHILD_9794 = 9794;
    public static final int CHILD_9795 = 9795;
    public static final int CHILD_9796 = 9796;
    public static final int CHILD_9797 = 9797;
    public static final int CHILD_9798 = 9798;
    public static final int CHILD_9799 = 9799;
    public static final int SLEEPWALKER_9801 = 9801;
    public static final int SLEEPWALKER_9802 = 9802;
    public static final int RED = 9850;
    public static final int ZIGGY = 9851;
    public static final int RED_9852 = 9852;
    public static final int ZIGGY_9853 = 9853;
    public static final int DEATH_9855 = 9855;
    public static final int GRAVE = 9856;
    public static final int GRAVE_9857 = 9857;
    public static final int SQUIRE_10368 = 10368;
    public static final int BOBAWU = 10369;
    public static final int MARTEN = 10370;
    public static final int WIZARD_10371 = 10371;
    public static final int WIZARD_10372 = 10372;
    public static final int WIZARD_10373 = 10373;
    public static final int HILL_GIANT_10374 = 10374;
    public static final int HILL_GIANT_10375 = 10375;
    public static final int HILL_GIANT_10376 = 10376;
    public static final int FEROX = 10377;
    public static final int SIGISMUND = 10378;
    public static final int ZAMORAKIAN_ACOLYTE = 10379;
    public static final int ZAMORAKIAN_ACOLYTE_10380 = 10380;
    public static final int ZAMORAKIAN_ACOLYTE_10381 = 10381;
    public static final int SKULLY = 10382;
    public static final int REFUGEE = 10383;
    public static final int REFUGEE_10384 = 10384;
    public static final int REFUGEE_10385 = 10385;
    public static final int PHABELLE_BILE = 10386;
    public static final int DERSE_VENATOR = 10387;
    public static final int ANDROS_MAI = 10388;
    public static final int BANKER_10389 = 10389;
    public static final int MERCENARY_10390 = 10390;
    public static final int FINANCIAL_WIZARD_10391 = 10391;
    public static final int CAMARST = 10392;
    public static final int WARRIOR_OF_MURAHS = 10393;
    public static final int WARRIOR_OF_MURAHS_10394 = 10394;
    public static final int WARRIOR_OF_MURAHS_10395 = 10395;
    public static final int SPIKED_TUROTH = 10397;
    public static final int SHADOW_WYRM = 10398;
    public static final int SHADOW_WYRM_10399 = 10399;
    public static final int GUARDIAN_DRAKE = 10400;
    public static final int GUARDIAN_DRAKE_10401 = 10401;
    public static final int COLOSSAL_HYDRA = 10402;
    public static final int TORFINN_10403 = 10403;
    public static final int TORFINN_10404 = 10404;
    public static final int TORFINN_10405 = 10405;
    public static final int TORFINN_10406 = 10406;
    public static final int JARVALD_10407 = 10407;
    public static final int MARLO = 10408;
    public static final int MARLO_10409 = 10409;
    public static final int ELLIE = 10410;
    public static final int ELLIE_10411 = 10411;
    public static final int ANGELO = 10412;
    public static final int ANGELO_10413 = 10413;
    public static final int BOB_10414 = 10414;
    public static final int JEFF_10415 = 10415;
    public static final int SARAH_10416 = 10416;
    public static final int TAU = 10417;
    public static final int LARRY_10418 = 10418;
    public static final int NOELLA = 10419;
    public static final int ROSS = 10420;
    public static final int JESS = 10421;
    public static final int MARIAH = 10422;
    public static final int LEELA_10423 = 10423;
    public static final int BARBARA = 10424;
    public static final int OLD_MAN_YARLO = 10425;
    public static final int OLD_MAN_YARLO_10426 = 10426;
    public static final int OLD_MAN_YARLO_10427 = 10427;
    public static final int SPRIA = 10432;
    public static final int SPRIA_10433 = 10433;
    public static final int SPRIA_10434 = 10434;
    public static final int SOURHOG = 10435;
    public static final int SOURHOG_10436 = 10436;
    public static final int PIG_THING = 10437;
    public static final int ROSIE_10438 = 10438;
    public static final int SHEEPDOG_10439 = 10439;
    public static final int SPIDER_10442 = 10442;
    public static final int SPIDER_10443 = 10443;
    public static final int BEES = 10444;
    public static final int GNORMADIUM_AVLAFRIM_10445 = 10445;
    public static final int GNORMADIUM_AVLAFRIM_10446 = 10446;
    public static final int GNORMADIUM_AVLAFRIM_10447 = 10447;
    public static final int GNORMADIUM_AVLAFRIM_10448 = 10448;
    public static final int EVE_10449 = 10449;
    public static final int GNORMADIUM_AVLAFRIM_10450 = 10450;
    public static final int GNORMADIUM_AVLAFRIM_10451 = 10451;
    public static final int CAPTAIN_DALBUR = 10452;
    public static final int CAPTAIN_DALBUR_10453 = 10453;
    public static final int CAPTAIN_DALBUR_10454 = 10454;
    public static final int CAPTAIN_DALBUR_10455 = 10455;
    public static final int CAPTAIN_DALBUR_10456 = 10456;
    public static final int CAPTAIN_DALBUR_10457 = 10457;
    public static final int CAPTAIN_DALBUR_10458 = 10458;
    public static final int CAPTAIN_BLEEMADGE = 10459;
    public static final int JACKOLANTERN = 10460;
    public static final int CAPTAIN_BLEEMADGE_10461 = 10461;
    public static final int CAPTAIN_BLEEMADGE_10462 = 10462;
    public static final int CAPTAIN_BLEEMADGE_10463 = 10463;
    public static final int CAPTAIN_BLEEMADGE_10464 = 10464;
    public static final int CAPTAIN_BLEEMADGE_10465 = 10465;
    public static final int CAPTAIN_BLEEMADGE_10466 = 10466;
    public static final int CAPTAIN_ERRDO_10467 = 10467;
    public static final int CAPTAIN_ERRDO_10468 = 10468;
    public static final int CAPTAIN_ERRDO_10469 = 10469;
    public static final int CAPTAIN_ERRDO_10470 = 10470;
    public static final int CAPTAIN_ERRDO_10471 = 10471;
    public static final int CAPTAIN_ERRDO_10472 = 10472;
    public static final int CAPTAIN_ERRDO_10473 = 10473;
    public static final int LEAGUES_ASSISTANT = 10476;
    public static final int THESSALIA = 10477;
    public static final int THESSALIA_10478 = 10478;
    public static final int CAPTAIN_KLEMFOODLE = 10479;
    public static final int CAPTAIN_KLEMFOODLE_10480 = 10480;
    public static final int CAPTAIN_KLEMFOODLE_10481 = 10481;
    public static final int CAPTAIN_KLEMFOODLE_10482 = 10482;
    public static final int CAPTAIN_KLEMFOODLE_10483 = 10483;
    public static final int CAPTAIN_KLEMFOODLE_10484 = 10484;
    public static final int CAPTAIN_KLEMFOODLE_10485 = 10485;
    public static final int CAPTAIN_SHORACKS_10486 = 10486;
    public static final int CAPTAIN_SHORACKS_10487 = 10487;
    public static final int CAPTAIN_SHORACKS_10488 = 10488;
    public static final int CAPTAIN_SHORACKS_10489 = 10489;
    public static final int CAPTAIN_SHORACKS_10490 = 10490;
    public static final int CAPTAIN_SHORACKS_10491 = 10491;
    public static final int HEADLESS_BEAST_HARD = 10492;
    public static final int HEADLESS_BEAST = 10493;
    public static final int CHICKEN_10494 = 10494;
    public static final int CHICKEN_10495 = 10495;
    public static final int CHICKEN_10496 = 10496;
    public static final int CHICKEN_10497 = 10497;
    public static final int CHICKEN_10498 = 10498;
    public static final int CHICKEN_10499 = 10499;
    public static final int GORDON = 10500;
    public static final int GORDON_10501 = 10501;
    public static final int MARY_10502 = 10502;
    public static final int MARY_10503 = 10503;
    public static final int MARY_10504 = 10504;
    public static final int SERGEANT_10505 = 10505;
    public static final int HEADLESS_BEAST_10506 = 10506;
    public static final int ORNATE_UNDEAD_COMBAT_DUMMY = 10507;
    public static final int ORNATE_WILDERNESS_COMBAT_DUMMY = 10508;
    public static final int ORNATE_KALPHITE_COMBAT_DUMMY = 10509;
    public static final int ORNATE_KURASK_COMBAT_DUMMY = 10510;
    public static final int ORNATE_UNDEAD_COMBAT_DUMMY_10511 = 10511;
    public static final int ORNATE_UNDEAD_COMBAT_DUMMY_10512 = 10512;
    public static final int FISHING_SPOT_10513 = 10513;
    public static final int FISHING_SPOT_10514 = 10514;
    public static final int FISHING_SPOT_10515 = 10515;
    public static final int NOMAD = 10516;
    public static final int ZIMBERFIZZ = 10517;
    public static final int ZIMBERFIZZ_10518 = 10518;
    public static final int ZIMBERFIZZ_10519 = 10519;
    public static final int AVATAR_OF_CREATION = 10520;
    public static final int AVATAR_OF_DESTRUCTION = 10521;
    public static final int WOLF_10522 = 10522;
    public static final int FORGOTTEN_SOUL = 10523;
    public static final int FORGOTTEN_SOUL_10524 = 10524;
    public static final int FORGOTTEN_SOUL_10525 = 10525;
    public static final int FORGOTTEN_SOUL_10526 = 10526;
    public static final int NOMAD_10528 = 10528;
    public static final int NOMAD_10529 = 10529;
    public static final int ZIMBERFIZZ_10530 = 10530;
    public static final int AVATAR_OF_CREATION_10531 = 10531;
    public static final int AVATAR_OF_DESTRUCTION_10532 = 10532;
    public static final int WOLF_10533 = 10533;
    public static final int FORGOTTEN_SOUL_10534 = 10534;
    public static final int FORGOTTEN_SOUL_10535 = 10535;
    public static final int FORGOTTEN_SOUL_10536 = 10536;
    public static final int FORGOTTEN_SOUL_10537 = 10537;
    public static final int GHOST_10538 = 10538;
    public static final int BARRICADE_10539 = 10539;
    public static final int BARRICADE_10540 = 10540;
    public static final int BIRD_10541 = 10541;
    public static final int FORGOTTEN_SOUL_10544 = 10544;
    public static final int FORGOTTEN_SOUL_10545 = 10545;
    public static final int DUCK_10546 = 10546;
    public static final int DUCK_10547 = 10547;
    public static final int CHICKEN_10556 = 10556;
    public static final int ANCIENT_GHOST = 10557;
    public static final int ANCIENT_GHOST_10558 = 10558;
    public static final int SCRUBFOOT = 10559;
    public static final int GARL = 10560;
    public static final int RED_FIREFLIES = 10561;
    public static final int TINY_TEMPOR = 10562;
    public static final int CRAB_10563 = 10563;
    public static final int GREEN_FIREFLIES = 10564;
    public static final int FISHING_SPOT_10565 = 10565;
    public static final int GOBLIN_10566 = 10566;
    public static final int GOBLIN_10567 = 10567;
    public static final int FISHING_SPOT_10568 = 10568;
    public static final int FISHING_SPOT_10569 = 10569;
    public static final int INACTIVE_SPIRIT_POOL = 10570;
    public static final int SPIRIT_POOL = 10571;
    public static final int TEMPOROSS = 10572;
    public static final int TEMPOROSS_10574 = 10574;
    public static final int TEMPOROSS_10575 = 10575;
    public static final int AMMUNITION_CRATE = 10576;
    public static final int AMMUNITION_CRATE_10577 = 10577;
    public static final int AMMUNITION_CRATE_10578 = 10578;
    public static final int AMMUNITION_CRATE_10579 = 10579;
    public static final int LIGHTNING_CLOUD = 10580;
    public static final int CAPTAIN_PUDI = 10583;
    public static final int CAPTAIN_DUDI = 10584;
    public static final int CAPTAIN_PUDI_10585 = 10585;
    public static final int CAPTAIN_PUDI_10586 = 10586;
    public static final int CAPTAIN_DUDI_10587 = 10587;
    public static final int CAPTAIN_DUDI_10588 = 10588;
    public static final int URIUM_SHADE = 10589;
    public static final int DAMPE = 10590;
    public static final int UNDEAD_ZEALOT = 10591;
    public static final int UNDEAD_ZEALOT_10592 = 10592;
    public static final int FIRST_MATE_DERI = 10593;
    public static final int SHEEP_10594 = 10594;
    public static final int FIRST_MATE_DERI_10595 = 10595;
    public static final int FIRST_MATE_PERI = 10596;
    public static final int FIRST_MATE_PERI_10597 = 10597;
    public static final int COW_10598 = 10598;
    public static final int CANNONEER = 10599;
    public static final int CANNONEER_10600 = 10600;
    public static final int CANNONEER_10601 = 10601;
    public static final int CANNONEER_10602 = 10602;
    public static final int MONKEY_ON_UNICORN = 10603;
    public static final int SAILOR_10604 = 10604;
    public static final int SPIRIT_ANGLER = 10605;
    public static final int SPIRIT_ANGLER_10606 = 10606;
    public static final int SPIRIT_ANGLER_10607 = 10607;
    public static final int SPIRIT_ANGLER_10608 = 10608;
    public static final int SPIRIT_ANGLER_10609 = 10609;
    public static final int FERRYMAN_SATHWOOD = 10610;
    public static final int FERRYMAN_NATHWOOD = 10611;
    public static final int RETIRED_SAILOR = 10612;
    public static final int GITA_PRYMES = 10613;
    public static final int TAIMAN = 10614;
    public static final int KOANEE = 10615;
    public static final int TIMALLUS = 10616;
    public static final int LAURETTA = 10617;
    public static final int ISHMAEL = 10618;
    public static final int BOB_10619 = 10619;
    public static final int JALREKJAD = 10620;
    public static final int TZHAARKETRAK = 10621;
    public static final int TZHAARKETRAK_10622 = 10622;
    public static final int JALTOKJAD_10623 = 10623;
    public static final int YTHURKOT_10624 = 10624;
    public static final int JALREKJAD_10625 = 10625;
    public static final int SHADOW_10628 = 10628;
    public static final int DUSURI = 10630;
    public static final int DUSURI_10631 = 10631;
    public static final int STAR_SPRITE = 10632;
    public static final int SHANTAY_GUARD_10634 = 10634;
    public static final int FISHING_SPOT_10635 = 10635;
    public static final int GREAT_BLUE_HERON_10636 = 10636;
    public static final int TINY_TEMPOR_10637 = 10637;
    public static final int BABY_MOLERAT = 10650;
    public static final int BABY_MOLERAT_10651 = 10651;
    public static final int SPIDER_10652 = 10652;
    public static final int FISHING_SPOT_10653 = 10653;
    public static final int ANCIENT_GUARDIAN = 10654;
    public static final int WILLOW = 10655;
    public static final int MARLEY = 10656;
    public static final int CHECKAL = 10657;
    public static final int ATLAS = 10658;
    public static final int BURNTOF = 10659;
    public static final int BURNTOF_10660 = 10660;
    public static final int WILLOW_10661 = 10661;
    public static final int MARLEY_10662 = 10662;
    public static final int CHECKAL_10663 = 10663;
    public static final int BURNTOF_10664 = 10664;
    public static final int ANCIENT_GUARDIAN_10665 = 10665;
    public static final int RUBBLE = 10666;
    public static final int FUSE = 10667;
    public static final int TINA_10668 = 10668;
    public static final int ATLAS_10669 = 10669;
    public static final int RAMARNO = 10670;
    public static final int TRAMP_10671 = 10671;
    public static final int MAN_10672 = 10672;
    public static final int MAN_10673 = 10673;
    public static final int WOMAN_10674 = 10674;
    public static final int STRAY_DOG_10675 = 10675;
    public static final int BARBARIAN_10676 = 10676;
    public static final int BARBARIAN_10677 = 10677;
    public static final int BARBARIAN_10678 = 10678;
    public static final int BARBARIAN_10679 = 10679;
    public static final int GUARD_10680 = 10680;
    public static final int AUBURY = 10681;
    public static final int RAT_10682 = 10682;
    public static final int BARAEK_10683 = 10683;
    public static final int RAMARNO_10684 = 10684;
    public static final int RAMARNO_10685 = 10685;
    public static final int FISHING_SPOT_10686 = 10686;
    public static final int FISHING_SPOT_10687 = 10687;
    public static final int FISHING_SPOT_10688 = 10688;
    public static final int CHAOS_GOLEM = 10689;
    public static final int RUBBLE_10690 = 10690;
    public static final int BODY_GOLEM = 10691;
    public static final int RUBBLE_10692 = 10692;
    public static final int MIND_GOLEM = 10693;
    public static final int RUBBLE_10694 = 10694;
    public static final int FLAWED_GOLEM = 10695;
    public static final int RUBBLE_10696 = 10696;
    public static final int GHOST_10697 = 10697;
    public static final int GHOST_10698 = 10698;
    public static final int GHOST_10699 = 10699;
    public static final int FROG_10700 = 10700;
    public static final int MURPHY_10707 = 10707;
    public static final int ENORMOUS_TENTACLE_10708 = 10708;
    public static final int ENORMOUS_TENTACLE_10709 = 10709;
    public static final int SKELETON_10717 = 10717;
    public static final int SKELETON_10718 = 10718;
    public static final int SKELETON_10719 = 10719;
    public static final int SKELETON_10720 = 10720;
    public static final int SKELETON_10721 = 10721;
    public static final int ICE_SPIDER_10722 = 10722;
    public static final int VEOS_10723 = 10723;
    public static final int VEOS_10724 = 10724;
    public static final int SERGEANT_10725 = 10725;
    public static final int VEOS_10726 = 10726;
    public static final int VEOS_10727 = 10727;
    public static final int WOMAN_10728 = 10728;
    public static final int RANGER_10731 = 10731;
    public static final int ZAMORAKIAN_RECRUITER = 10732;
    public static final int MEREDITH = 10733;
    public static final int BANKER_10734 = 10734;
    public static final int BANKER_10735 = 10735;
    public static final int BANKER_10736 = 10736;
    public static final int BANKER_10737 = 10737;
    public static final int RAQUEEL = 10739;
    public static final int RAQUEEL_10740 = 10740;
    public static final int RAQUEEL_10741 = 10741;
    public static final int RAQUEEL_10742 = 10742;
    public static final int RAQUEEL_10743 = 10743;
    public static final int RAQUEEL_10744 = 10744;
    public static final int RAQUEEL_10745 = 10745;
    public static final int RAQUEEL_10746 = 10746;
    public static final int GEM = 10748;
    public static final int GEM_10749 = 10749;
    public static final int GEM_10750 = 10750;
    public static final int GEM_10751 = 10751;
    public static final int GEM_10752 = 10752;
    public static final int GEM_10753 = 10753;
    public static final int GEM_10754 = 10754;
    public static final int GEM_10755 = 10755;
    public static final int GARDENER_JAY_JR = 10756;
    public static final int GARDENER_JAY_JR_10757 = 10757;
    public static final int GARDENER_JAY_JR_10758 = 10758;
    public static final int CLERK_10759 = 10759;
    public static final int STRAY_DOG_10760 = 10760;
    public static final int LIL_MAIDEN = 10761;
    public static final int LIL_BLOAT = 10762;
    public static final int LIL_NYLO = 10763;
    public static final int LIL_SOT = 10764;
    public static final int LIL_XARP = 10765;
    public static final int XARPUS_10766 = 10766;
    public static final int XARPUS_10767 = 10767;
    public static final int XARPUS_10768 = 10768;
    public static final int XARPUS_10769 = 10769;
    public static final int XARPUS_10770 = 10770;
    public static final int XARPUS_10771 = 10771;
    public static final int XARPUS_10772 = 10772;
    public static final int XARPUS_10773 = 10773;
    public static final int NYLOCAS_ISCHYROS_10774 = 10774;
    public static final int NYLOCAS_TOXOBOLOS_10775 = 10775;
    public static final int NYLOCAS_HAGIOS_10776 = 10776;
    public static final int NYLOCAS_ISCHYROS_10777 = 10777;
    public static final int NYLOCAS_TOXOBOLOS_10778 = 10778;
    public static final int NYLOCAS_HAGIOS_10779 = 10779;
    public static final int NYLOCAS_ISCHYROS_10780 = 10780;
    public static final int NYLOCAS_TOXOBOLOS_10781 = 10781;
    public static final int NYLOCAS_HAGIOS_10782 = 10782;
    public static final int NYLOCAS_ISCHYROS_10783 = 10783;
    public static final int NYLOCAS_TOXOBOLOS_10784 = 10784;
    public static final int NYLOCAS_HAGIOS_10785 = 10785;
    public static final int NYLOCAS_VASILIAS_10786 = 10786;
    public static final int NYLOCAS_VASILIAS_10787 = 10787;
    public static final int NYLOCAS_VASILIAS_10788 = 10788;
    public static final int NYLOCAS_VASILIAS_10789 = 10789;
    public static final int NYLOCAS_ISCHYROS_10791 = 10791;
    public static final int NYLOCAS_TOXOBOLOS_10792 = 10792;
    public static final int NYLOCAS_HAGIOS_10793 = 10793;
    public static final int NYLOCAS_ISCHYROS_10794 = 10794;
    public static final int NYLOCAS_TOXOBOLOS_10795 = 10795;
    public static final int NYLOCAS_HAGIOS_10796 = 10796;
    public static final int NYLOCAS_ISCHYROS_10797 = 10797;
    public static final int NYLOCAS_TOXOBOLOS_10798 = 10798;
    public static final int NYLOCAS_HAGIOS_10799 = 10799;
    public static final int NYLOCAS_ISCHYROS_10800 = 10800;
    public static final int NYLOCAS_TOXOBOLOS_10801 = 10801;
    public static final int NYLOCAS_HAGIOS_10802 = 10802;
    public static final int NYLOCAS_PRINKIPAS = 10803;
    public static final int NYLOCAS_PRINKIPAS_10804 = 10804;
    public static final int NYLOCAS_PRINKIPAS_10805 = 10805;
    public static final int NYLOCAS_PRINKIPAS_10806 = 10806;
    public static final int NYLOCAS_VASILIAS_10807 = 10807;
    public static final int NYLOCAS_VASILIAS_10808 = 10808;
    public static final int NYLOCAS_VASILIAS_10809 = 10809;
    public static final int NYLOCAS_VASILIAS_10810 = 10810;
    public static final int PESTILENT_BLOAT_10812 = 10812;
    public static final int PESTILENT_BLOAT_10813 = 10813;
    public static final int THE_MAIDEN_OF_SUGADINTI_10814 = 10814;
    public static final int THE_MAIDEN_OF_SUGADINTI_10815 = 10815;
    public static final int THE_MAIDEN_OF_SUGADINTI_10816 = 10816;
    public static final int THE_MAIDEN_OF_SUGADINTI_10817 = 10817;
    public static final int THE_MAIDEN_OF_SUGADINTI_10818 = 10818;
    public static final int THE_MAIDEN_OF_SUGADINTI_10819 = 10819;
    public static final int NYLOCAS_MATOMENOS_10820 = 10820;
    public static final int BLOOD_SPAWN_10821 = 10821;
    public static final int THE_MAIDEN_OF_SUGADINTI_10822 = 10822;
    public static final int THE_MAIDEN_OF_SUGADINTI_10823 = 10823;
    public static final int THE_MAIDEN_OF_SUGADINTI_10824 = 10824;
    public static final int THE_MAIDEN_OF_SUGADINTI_10825 = 10825;
    public static final int THE_MAIDEN_OF_SUGADINTI_10826 = 10826;
    public static final int THE_MAIDEN_OF_SUGADINTI_10827 = 10827;
    public static final int NYLOCAS_MATOMENOS_10828 = 10828;
    public static final int BLOOD_SPAWN_10829 = 10829;
    public static final int VERZIK_VITUR_10830 = 10830;
    public static final int VERZIK_VITUR_10831 = 10831;
    public static final int VERZIK_VITUR_10832 = 10832;
    public static final int VERZIK_VITUR_10833 = 10833;
    public static final int VERZIK_VITUR_10834 = 10834;
    public static final int VERZIK_VITUR_10835 = 10835;
    public static final int VERZIK_VITUR_10836 = 10836;
    public static final int WEB_10837 = 10837;
    public static final int COLLAPSING_PILLAR_10838 = 10838;
    public static final int COLLAPSING_PILLAR_10839 = 10839;
    public static final int SUPPORTING_PILLAR_10840 = 10840;
    public static final int NYLOCAS_ISCHYROS_10841 = 10841;
    public static final int NYLOCAS_TOXOBOLOS_10842 = 10842;
    public static final int NYLOCAS_HAGIOS_10843 = 10843;
    public static final int NYLOCAS_ATHANATOS_10844 = 10844;
    public static final int NYLOCAS_MATOMENOS_10845 = 10845;
    public static final int VERZIK_VITUR_10847 = 10847;
    public static final int VERZIK_VITUR_10848 = 10848;
    public static final int VERZIK_VITUR_10849 = 10849;
    public static final int VERZIK_VITUR_10850 = 10850;
    public static final int VERZIK_VITUR_10851 = 10851;
    public static final int VERZIK_VITUR_10852 = 10852;
    public static final int VERZIK_VITUR_10853 = 10853;
    public static final int WEB_10854 = 10854;
    public static final int COLLAPSING_PILLAR_10855 = 10855;
    public static final int COLLAPSING_PILLAR_10856 = 10856;
    public static final int SUPPORTING_PILLAR_10857 = 10857;
    public static final int NYLOCAS_ISCHYROS_10858 = 10858;
    public static final int NYLOCAS_TOXOBOLOS_10859 = 10859;
    public static final int NYLOCAS_HAGIOS_10860 = 10860;
    public static final int NYLOCAS_ATHANATOS_10861 = 10861;
    public static final int NYLOCAS_MATOMENOS_10862 = 10862;
    public static final int SOTETSEG_10864 = 10864;
    public static final int SOTETSEG_10865 = 10865;
    public static final int SOTETSEG_10867 = 10867;
    public static final int SOTETSEG_10868 = 10868;
    public static final int LIL_MAIDEN_10870 = 10870;
    public static final int LIL_BLOAT_10871 = 10871;
    public static final int LIL_NYLO_10872 = 10872;
    public static final int LIL_SOT_10873 = 10873;
    public static final int LIL_XARP_10874 = 10874;
    public static final int MYSTERIOUS_STRANGER_10875 = 10875;
    public static final int MYSTERIOUS_STRANGER_10876 = 10876;
    public static final int MYSTERIOUS_STRANGER_10877 = 10877;
    public static final int TOWN_CRIER_10887 = 10887;
    public static final int BAT_10888 = 10888;
    public static final int ASTEROS_ARCEUUS = 10889;
    public static final int MARTIN_HOLT = 10890;
    public static final int MARTIN_HOLT_10891 = 10891;
    public static final int MARTIN_HOLT_10892 = 10892;
    public static final int MARTIN_HOLT_10893 = 10893;
    public static final int MARTIN_HOLT_10894 = 10894;
    public static final int MARTIN_HOLT_10895 = 10895;
    public static final int PROTEST_LEADER = 10896;
    public static final int PROTESTER = 10897;
    public static final int PROTESTER_10898 = 10898;
    public static final int PROTESTER_10899 = 10899;
    public static final int PROTESTER_10900 = 10900;
    public static final int PROTESTER_10901 = 10901;
    public static final int PROTESTER_10902 = 10902;
    public static final int PROTESTER_10903 = 10903;
    public static final int PROTESTER_10904 = 10904;
    public static final int PROTESTER_10905 = 10905;
    public static final int PROTESTER_10906 = 10906;
    public static final int PROTESTER_10907 = 10907;
    public static final int PROTESTER_10908 = 10908;
    public static final int GUARD_10909 = 10909;
    public static final int GUARD_10910 = 10910;
    public static final int GUARD_10911 = 10911;
    public static final int GUARD_10912 = 10912;
    public static final int GUARD_10913 = 10913;
    public static final int GUARD_10914 = 10914;
    public static final int GUARD_10915 = 10915;
    public static final int GUARD_10916 = 10916;
    public static final int GUARD_10917 = 10917;
    public static final int GUARD_10918 = 10918;
    public static final int GUARD_10919 = 10919;
    public static final int GUARD_10920 = 10920;
    public static final int GUARD_10921 = 10921;
    public static final int COMMANDER_FULLORE = 10922;
    public static final int COMMANDER_FULLORE_10923 = 10923;
    public static final int ROYAL_GUARD_10924 = 10924;
    public static final int ROYAL_GUARD_10925 = 10925;
    public static final int COUNCILLOR_ANDREWS = 10926;
    public static final int DAVID_ANDREWS = 10927;
    public static final int TOMAS_LAWRY_10928 = 10928;
    public static final int TOMAS_LAWRY_10929 = 10929;
    public static final int CAPTAIN_GINEA_10931 = 10931;
    public static final int CABIN_BOY_HERBERT = 10932;
    public static final int CABIN_BOY_HERBERT_10933 = 10933;
    public static final int CABIN_BOY_HERBERT_10934 = 10934;
    public static final int SOPHIA_HUGHES_10935 = 10935;
    public static final int JUDGE_OF_YAMA = 10936;
    public static final int MYSTERIOUS_VOICE_10937 = 10937;
    public static final int JUDGE_OF_YAMA_10938 = 10938;
    public static final int FIRE_WAVE = 10939;
    public static final int ASSASSIN_10940 = 10940;
    public static final int ASSASSIN_10941 = 10941;
    public static final int ASSASSIN_10942 = 10942;
    public static final int COUNCILLOR_ORSON = 10943;
    public static final int COUNCILLOR_ORSON_10944 = 10944;
    public static final int MAN_10945 = 10945;
    public static final int COUNCILLOR_ORSON_10946 = 10946;
    public static final int LIZARDMAN_BRUTE_10947 = 10947;
    public static final int LIZARDMAN_10948 = 10948;
    public static final int VEOS_10949 = 10949;
    public static final int MYSTERIOUS_VOICE_10950 = 10950;
    public static final int XAMPHUR = 10951;
    public static final int MYSTERIOUS_VOICE_10952 = 10952;
    public static final int XAMPHUR_10953 = 10953;
    public static final int XAMPHUR_10954 = 10954;
    public static final int XAMPHUR_10955 = 10955;
    public static final int XAMPHUR_10956 = 10956;
    public static final int PHANTOM_HAND = 10957;
    public static final int PHANTOM_HAND_10958 = 10958;
    public static final int KUBEC_UNKAR = 10959;
    public static final int COUNCILLOR_UNKAR = 10960;
    public static final int LORD_TROBIN_ARCEUUS_10961 = 10961;
    public static final int LORD_TROBIN_ARCEUUS_10962 = 10962;
    public static final int LORD_SHIRO_SHAYZIEN = 10963;
    public static final int LORD_SHIRO_SHAYZIEN_10964 = 10964;
    public static final int LORD_SHIRO_SHAYZIEN_10965 = 10965;
    public static final int LORD_KANDUR_HOSIDIUS = 10966;
    public static final int KING_KANDUR_HOSIDIUS = 10967;
    public static final int KING_KANDUR_HOSIDIUS_10968 = 10968;
    public static final int KING_KANDUR_HOSIDIUS_10969 = 10969;
    public static final int LORD_KANDUR_HOSIDIUS_10970 = 10970;
    public static final int LORD_KANDUR_HOSIDIUS_10971 = 10971;
    public static final int LADY_VULCANA_LOVAKENGJ = 10972;
    public static final int LADY_VULCANA_LOVAKENGJ_10973 = 10973;
    public static final int LADY_SHAUNA_PISCARILIUS_10974 = 10974;
    public static final int LADY_SHAUNA_PISCARILIUS_10975 = 10975;
    public static final int ARTUR_HOSIDIUS_10976 = 10976;
    public static final int KING_ARTUR_HOSIDIUS = 10977;
    public static final int ASTEROS_ARCEUUS_10978 = 10978;
    public static final int ASTEROS_ARCEUUS_10979 = 10979;
    public static final int LORD_PANDUR_HOSIDIUS = 10980;
    public static final int PANDUR_HOSIDIUS = 10981;
    public static final int ELENA_HOSIDIUS = 10982;
    public static final int ELENA_HOSIDIUS_10983 = 10983;
    public static final int BARBARIAN_10984 = 10984;
    public static final int BARBARIAN_10985 = 10985;
    public static final int BARBARIAN_10986 = 10986;
    public static final int BARBARIAN_10987 = 10987;
    public static final int BARBARIAN_10988 = 10988;
    public static final int BARBARIAN_WARLORD = 10989;
    public static final int PHILEAS_RIMOR_10991 = 10991;
    public static final int CAPTAIN_RACHELLE_10992 = 10992;
    public static final int PROTESTER_10993 = 10993;
    public static final int PROTESTER_10994 = 10994;
    public static final int PROTESTER_10995 = 10995;
    public static final int PROTESTER_10996 = 10996;
    public static final int PROTESTER_10997 = 10997;
    public static final int PROTESTER_10998 = 10998;
    public static final int PROTESTER_10999 = 10999;
    public static final int PROTESTER_11000 = 11000;
    public static final int PROTESTER_11001 = 11001;
    public static final int PROTESTER_11002 = 11002;
    public static final int PROTESTER_11003 = 11003;
    public static final int PROTESTER_11004 = 11004;
    public static final int PROTESTER_11005 = 11005;
    public static final int PROTESTER_11006 = 11006;
    public static final int PROTESTER_11007 = 11007;
    public static final int PROTESTER_11008 = 11008;
    public static final int PROTESTER_11009 = 11009;
    public static final int PROTESTER_11010 = 11010;
    public static final int PROTESTER_11011 = 11011;
    public static final int QUEEN_ZYANYI_ARKAN = 11012;
    public static final int KUALTI = 11013;
    public static final int KUALTI_11014 = 11014;
    public static final int KUALTI_11015 = 11015;
    public static final int KUALTI_11016 = 11016;
    public static final int KUALTI_11017 = 11017;
    public static final int KUALTI_11018 = 11018;
    public static final int KING_ROALD_11019 = 11019;
    public static final int SIR_AMIK_VARZE_11020 = 11020;
    public static final int SIR_TIFFY_CASHIEN_11021 = 11021;
    public static final int KING_LATHAS_11022 = 11022;
    public static final int KING_THOROS_11023 = 11023;
    public static final int DUKE_HORACIO_11024 = 11024;
    public static final int QUEEN_ELLAMARIA_11025 = 11025;
    public static final int SEAGULL_11026 = 11026;
    public static final int SPIDER_11027 = 11027;
    public static final int ICE_CHUNKS = 11028;
    public static final int ICE_CHUNKS_11029 = 11029;
    public static final int ICE_CHUNKS_11030 = 11030;
    public static final int ICE_CHUNKS_11031 = 11031;
    public static final int MAN_11032 = 11032;
    public static final int LORD_KANDUR_HOSIDIUS_11033 = 11033;
    public static final int ELENA_HOSIDIUS_11034 = 11034;
    public static final int LADY_VULCANA_LOVAKENGJ_11035 = 11035;
    public static final int COUNCILLOR_UNKAR_11036 = 11036;
    public static final int JORRA = 11037;
    public static final int LORD_SHIRO_SHAYZIEN_11038 = 11038;
    public static final int KAHT_BALAM = 11039;
    public static final int BLAIR = 11040;
    public static final int DARYL = 11041;
    public static final int ROBYN = 11042;
    public static final int OSWALD = 11043;
    public static final int SHERYL = 11044;
    public static final int FARMER_11045 = 11045;
    public static final int SOLDIER_11046 = 11046;
    public static final int SOLDIER_11047 = 11047;
    public static final int DRUNKEN_SOLDIER = 11048;
    public static final int SOLDIER_11049 = 11049;
    public static final int FATHER_EYSSERIC = 11050;
    public static final int MIA = 11051;
    public static final int ELIJAH = 11052;
    public static final int WOMAN_11053 = 11053;
    public static final int WOMAN_11054 = 11054;
    public static final int KASTON = 11055;
    public static final int OLD_MAN_11056 = 11056;
    public static final int MAN_11057 = 11057;
    public static final int MAN_11058 = 11058;
    public static final int COMMISSIONER_ANWAR = 11059;
    public static final int CAPTAIN_BRUCE = 11060;
    public static final int SERGEANT_RICARDO = 11061;
    public static final int JESSIE = 11062;
    public static final int BANDIT_11063 = 11063;
    public static final int BANDIT_11064 = 11064;
    public static final int BANDIT_11065 = 11065;
    public static final int BOAR = 11066;
    public static final int BOAR_11067 = 11067;
    public static final int LYNX = 11068;
    public static final int LYNX_11069 = 11069;
    public static final int LYNX_11070 = 11070;
    public static final int LYNX_TAMER = 11071;
    public static final int SOLDIER_11072 = 11072;
    public static final int SERGEANT_11073 = 11073;
    public static final int SOLDIER_11074 = 11074;
    public static final int SERGEANT_11075 = 11075;
    public static final int SOLDIER_11076 = 11076;
    public static final int SERGEANT_11077 = 11077;
    public static final int SOLDIER_11078 = 11078;
    public static final int SERGEANT_11079 = 11079;
    public static final int SOLDIER_11080 = 11080;
    public static final int SERGEANT_11081 = 11081;
    public static final int SOLDIER_11082 = 11082;
    public static final int SERGEANT_11083 = 11083;
    public static final int SOLDIER_11084 = 11084;
    public static final int SERGEANT_11085 = 11085;
    public static final int SOLDIER_11086 = 11086;
    public static final int SERGEANT_11087 = 11087;
    public static final int NECROMANCER_11088 = 11088;
    public static final int RANGER_11089 = 11089;
    public static final int FAROLT = 11090;
    public static final int PANDUR_HOSIDIUS_11091 = 11091;
    public static final int GUARD_11092 = 11092;
    public static final int HEAD_GUARD = 11093;
    public static final int GUARD_11094 = 11094;
    public static final int HEAD_GUARD_11095 = 11095;
    public static final int GUARD_11096 = 11096;
    public static final int HEAD_GUARD_11097 = 11097;
    public static final int GUARD_11098 = 11098;
    public static final int HEAD_GUARD_11099 = 11099;
    public static final int GUARD_11100 = 11100;
    public static final int HEAD_GUARD_11101 = 11101;
    public static final int GUARD_11102 = 11102;
    public static final int HEAD_GUARD_11103 = 11103;
    public static final int GUARD_11104 = 11104;
    public static final int HEAD_GUARD_11105 = 11105;
    public static final int GUARD_11106 = 11106;
    public static final int HEAD_GUARD_11107 = 11107;
    public static final int DARK_WARRIOR_11109 = 11109;
    public static final int DARK_WARRIOR_11110 = 11110;
    public static final int DARK_WARRIOR_11111 = 11111;
    public static final int ISTORIA = 11112;
    public static final int ISTORIA_11113 = 11113;
    public static final int COUNCILLOR_ANDREWS_11152 = 11152;
    public static final int PHOSANIS_NIGHTMARE_11153 = 11153;
    public static final int PHOSANIS_NIGHTMARE_11154 = 11154;
    public static final int PHOSANIS_NIGHTMARE_11155 = 11155;
    public static final int SWARM_11156 = 11156;
    public static final int SRARACHA_11157 = 11157;
    public static final int SRARACHA_11158 = 11158;
    public static final int SRARACHA_11159 = 11159;
    public static final int SRARACHA_11160 = 11160;
    public static final int DUSTY_ALIV = 11161;
    public static final int MYSTERIOUS_STRANGER_11162 = 11162;
    public static final int VYREWATCH_11169 = 11169;
    public static final int VYREWATCH_11170 = 11170;
    public static final int VYREWATCH_11171 = 11171;
    public static final int VYREWATCH_11172 = 11172;
    public static final int VYREWATCH_11173 = 11173;
    public static final int SPIDER_11174 = 11174;
    public static final int SPIDER_11175 = 11175;
    public static final int SPIDER_11176 = 11176;
    public static final int RANIS_DRAKAN_11177 = 11177;
    public static final int VERZIK_VITUR_11178 = 11178;
    public static final int VERZIK_VITUR_11179 = 11179;
    public static final int VULCAN_ORVOROS = 11180;
    public static final int NYLOCAS_QUEEN = 11181;
    public static final int NYLOCAS = 11182;
    public static final int THE_MAIDEN_OF_SUGADINTI_11183 = 11183;
    public static final int PESTILENT_BLOAT_11184 = 11184;
    public static final int NYLOCAS_VASILIAS_11185 = 11185;
    public static final int SOTETSEG_11186 = 11186;
    public static final int XARPUS_11187 = 11187;
    public static final int NYLOCAS_ATHANATOS_11188 = 11188;
    public static final int NYLOCAS_ISCHYROS_11189 = 11189;
    public static final int NYLOCAS_TOXOBOLOS_11190 = 11190;
    public static final int NYLOCAS_HAGIOS_11191 = 11191;
    public static final int HESPORI_11192 = 11192;
    public static final int FLOWER_11193 = 11193;
    public static final int FLOWER_11194 = 11194;
    public static final int HILL_GIANT_11195 = 11195;
    public static final int LYNX_TAMER_11196 = 11196;
    public static final int LYNX_11197 = 11197;
    public static final int SERGEANT_11198 = 11198;
    public static final int GNOME_GUARD_11199 = 11199;
    public static final int GUARD_11200 = 11200;
    public static final int GUARD_11201 = 11201;
    public static final int GUARD_11202 = 11202;
    public static final int GUARD_11203 = 11203;
    public static final int GUARD_11204 = 11204;
    public static final int GHOST_GUARD_11205 = 11205;
    public static final int GUARD_11206 = 11206;
    public static final int GUARD_11207 = 11207;
    public static final int GUARD_11208 = 11208;
    public static final int GUARD_11209 = 11209;
    public static final int GUARD_11210 = 11210;
    public static final int PRIFDDINAS_GUARD_11211 = 11211;
    public static final int D3AD1I_F15HER = 11225;
    public static final int BOAR31337KILLER = 11226;
    public static final int ENRAGED_BOAR = 11227;
    public static final int R0CK_5MASHER = 11228;
    public static final int REGENT = 11229;
    public static final int GROUP_STORAGE_TUTOR = 11230;
    public static final int GROUP_IRON_TUTOR = 11231;
    public static final int THE_SAGE = 11232;
    public static final int LEAGUE_TUTOR = 11233;
    public static final int LEAGUES_ASSISTANT_11234 = 11234;
    public static final int DUST_DEVIL_11238 = 11238;
    public static final int ABYSSAL_DEMON_11239 = 11239;
    public static final int GREATER_NECHRYAEL_11240 = 11240;
    public static final int JELLY_11241 = 11241;
    public static final int JELLY_11242 = 11242;
    public static final int JELLY_11243 = 11243;
    public static final int JELLY_11244 = 11244;
    public static final int JELLY_11245 = 11245;
    public static final int REVENANT_MALEDICTUS = 11246;
    public static final int TZHAARKETKEH = 11247;
    public static final int TZHAARKETKEH_11248 = 11248;
    public static final int GENERAL_BENTNOZE_11249 = 11249;
    public static final int GENERAL_WARTFACE_11250 = 11250;
    public static final int GRUBFOOT_11251 = 11251;
    public static final int GRUBFOOT_11252 = 11252;
    public static final int GRUBFOOT_11254 = 11254;
    public static final int GRUBFOOT_11255 = 11255;
    public static final int GRUBFOOT_11259 = 11259;
    public static final int ZANIK_11260 = 11260;
    public static final int ZANIK_11261 = 11261;
    public static final int ZANIK_11262 = 11262;
    public static final int ZANIK_11263 = 11263;
    public static final int ZANIK_11264 = 11264;
    public static final int OLDAK = 11265;
    public static final int OLDAK_11266 = 11266;
    public static final int HIGH_PRIEST_BIGHEAD = 11267;
    public static final int SKOBLIN = 11268;
    public static final int SNOTHEAD = 11269;
    public static final int SNAILFEET = 11270;
    public static final int MOSSCHIN = 11271;
    public static final int REDEYES = 11272;
    public static final int STRONGBONES = 11273;
    public static final int SNOTHEAD_11274 = 11274;
    public static final int SNAILFEET_11275 = 11275;
    public static final int NEXLING = 11276;
    public static final int NEXLING_11277 = 11277;
    public static final int NEX = 11278;
    public static final int NEX_11279 = 11279;
    public static final int NEX_11280 = 11280;
    public static final int NEX_11281 = 11281;
    public static final int NEX_11282 = 11282;
    public static final int FUMUS = 11283;
    public static final int UMBRA = 11284;
    public static final int CRUOR = 11285;
    public static final int GLACIES = 11286;
    public static final int MESSENGER = 11287;
    public static final int ASHUELOT_REIS = 11288;
    public static final int ASHUELOT_REIS_11289 = 11289;
    public static final int SPIRITUAL_WARRIOR_11290 = 11290;
    public static final int SPIRITUAL_RANGER_11291 = 11291;
    public static final int SPIRITUAL_MAGE_11292 = 11292;
    public static final int BLOOD_REAVER = 11293;
    public static final int BLOOD_REAVER_11294 = 11294;
    public static final int GULL_11297 = 11297;
    public static final int MOSSCHIN_11298 = 11298;
    public static final int REDEYES_11299 = 11299;
    public static final int STRONGBONES_11300 = 11300;
    public static final int GHOST_11301 = 11301;
    public static final int PRIEST_11302 = 11302;
    public static final int PRIEST_11303 = 11303;
    public static final int PRIEST_11304 = 11304;
    public static final int PRIEST_11305 = 11305;
    public static final int PRIEST_11306 = 11306;
    public static final int PRIEST_11307 = 11307;
    public static final int PRIEST_11308 = 11308;
    public static final int PRIEST_11309 = 11309;
    public static final int PRIEST_11310 = 11310;
    public static final int PRIEST_11311 = 11311;
    public static final int PRIEST_11312 = 11312;
    public static final int PRIEST_11313 = 11313;
    public static final int GOBLIN_GUARD_11314 = 11314;
    public static final int GOBLIN_GUARD_11315 = 11315;
    public static final int GUARD_11316 = 11316;
    public static final int GUARD_11317 = 11317;
    public static final int GUARD_11318 = 11318;
    public static final int GUARD_11319 = 11319;
    public static final int GUARD_11320 = 11320;
    public static final int GUARD_11321 = 11321;
    public static final int GOBLIN_11322 = 11322;
    public static final int GOBLIN_11323 = 11323;
    public static final int GOBLIN_11324 = 11324;
    public static final int GOBLIN_11325 = 11325;
    public static final int GOBLIN_11326 = 11326;
    public static final int GOBLIN_11327 = 11327;
    public static final int GOBLIN_11328 = 11328;
    public static final int GOBLIN_11329 = 11329;
    public static final int GOBLIN_11330 = 11330;
    public static final int GOBLIN_11331 = 11331;
    public static final int GOBLIN_11332 = 11332;
    public static final int GOBLIN_11333 = 11333;
    public static final int GOBLIN_11334 = 11334;
    public static final int GOBLIN_11335 = 11335;
    public static final int GOBLIN_11336 = 11336;
    public static final int GOBLIN_11337 = 11337;
    public static final int GOBLIN_11338 = 11338;
    public static final int GOBLIN_11339 = 11339;
    public static final int GOBLIN_11340 = 11340;
    public static final int GOBLIN_11341 = 11341;
    public static final int GOBLIN_11342 = 11342;
    public static final int GOBLIN_11343 = 11343;
    public static final int GOBLIN_11344 = 11344;
    public static final int GOBLIN_11345 = 11345;
    public static final int GOBLIN_11346 = 11346;
    public static final int GOBLIN_11347 = 11347;
    public static final int GOBLIN_11348 = 11348;
    public static final int GOBLIN_11349 = 11349;
    public static final int GOBLIN_11350 = 11350;
    public static final int GOBLIN_11351 = 11351;
    public static final int GOBLIN_11352 = 11352;
    public static final int GOBLIN_11353 = 11353;
    public static final int GOBLIN_11354 = 11354;
    public static final int GOBLIN_11355 = 11355;
    public static final int GOBLIN_11356 = 11356;
    public static final int GOBLIN_11357 = 11357;
    public static final int GOBLIN_11358 = 11358;
    public static final int GOBLIN_11359 = 11359;
    public static final int GOBLIN_11360 = 11360;
    public static final int GOBLIN_11361 = 11361;
    public static final int GOBLIN_11362 = 11362;
    public static final int GOBLIN_11363 = 11363;
    public static final int GOBLIN_11364 = 11364;
    public static final int GOBLIN_11365 = 11365;
    public static final int GOBLIN_11366 = 11366;
    public static final int GOBLIN_11367 = 11367;
    public static final int GOBLIN_11368 = 11368;
    public static final int GOBLIN_11369 = 11369;
    public static final int GOBLIN_11370 = 11370;
    public static final int GOBLIN_11371 = 11371;
    public static final int GOBLIN_11372 = 11372;
    public static final int GOBLIN_11373 = 11373;
    public static final int GOBLIN_11374 = 11374;
    public static final int GOBLIN_11375 = 11375;
    public static final int GOBLIN_11376 = 11376;
    public static final int GOBLIN_11377 = 11377;
    public static final int GOBLIN_11378 = 11378;
    public static final int GOBLIN_11379 = 11379;
    public static final int GOBLIN_11380 = 11380;
    public static final int GOBLIN_11381 = 11381;
    public static final int PREACHER = 11382;
    public static final int DIPFLIP = 11383;
    public static final int OLDAK_11384 = 11384;
    public static final int OLDAK_11385 = 11385;
    public static final int WIZARD_DISTENTOR = 11399;
    public static final int WIZARD_DISTENTOR_11400 = 11400;
    public static final int GREATISH_GUARDIAN = 11401;
    public static final int ABYSSAL_PROTECTOR = 11402;
    public static final int THE_GREAT_GUARDIAN = 11403;
    public static final int APPRENTICE_FELIX = 11404;
    public static final int ABYSSAL_GUARDIAN_11405 = 11405;
    public static final int ABYSSAL_WALKER_11406 = 11406;
    public static final int ABYSSAL_LEECH_11407 = 11407;
    public static final int WEAK_CATALYTIC_GUARDIAN = 11408;
    public static final int RICK_11409 = 11409;
    public static final int RICK_11410 = 11410;
    public static final int MEDIUM_CATALYTIC_GUARDIAN = 11411;
    public static final int STRONG_CATALYTIC_GUARDIAN = 11412;
    public static final int OVERCHARGED_CATALYTIC_GUARDIAN = 11413;
    public static final int WEAK_ELEMENTAL_GUARDIAN = 11414;
    public static final int MEDIUM_ELEMENTAL_GUARDIAN = 11415;
    public static final int STRONG_ELEMENTAL_GUARDIAN = 11416;
    public static final int OVERCHARGED_ELEMENTAL_GUARDIAN = 11417;
    public static final int APPRENTICE_CORDELIA = 11427;
    public static final int GREATISH_GUARDIAN_11428 = 11428;
    public static final int ABYSSAL_PROTECTOR_11429 = 11429;
    public static final int BRIMSTAIL_11430 = 11430;
    public static final int BRIMSTAIL_11431 = 11431;
    public static final int ARCHMAGE_SEDRIDOR = 11432;
    public static final int ARCHMAGE_SEDRIDOR_11433 = 11433;
    public static final int AUBURY_11434 = 11434;
    public static final int AUBURY_11435 = 11435;
    public static final int WIZARD_PERSTEN = 11436;
    public static final int WIZARD_PERSTEN_11437 = 11437;
    public static final int WIZARD_PERSTEN_11438 = 11438;
    public static final int WIZARD_PERSTEN_11439 = 11439;
    public static final int APPRENTICE_TAMARA = 11440;
    public static final int APPRENTICE_TAMARA_11441 = 11441;
    public static final int APPRENTICE_TAMARA_11442 = 11442;
    public static final int APPRENTICE_CORDELIA_11443 = 11443;
    public static final int APPRENTICE_CORDELIA_11444 = 11444;
    public static final int APPRENTICE_CORDELIA_11445 = 11445;
    public static final int APPRENTICE_FELIX_11446 = 11446;
    public static final int APPRENTICE_FELIX_11447 = 11447;
    public static final int APPRENTICE_FELIX_11448 = 11448;
    public static final int WIZARD_TRAIBORN_11449 = 11449;
    public static final int ARCHMAGE_SEDRIDOR_11450 = 11450;
    public static final int MYSTERIOUS_VOICE_11451 = 11451;
    public static final int MYSTERIOUS_VOICE_11452 = 11452;
    public static final int ABYSSAL_GUARDIAN_11453 = 11453;
    public static final int ABYSSAL_WALKER_11454 = 11454;
    public static final int THE_GREAT_GUARDIAN_11455 = 11455;
    public static final int THE_GREAT_GUARDIAN_11456 = 11456;
    public static final int MENAPHITE_SHADOW = 11462;
    public static final int REANIMATED_HELLHOUND = 11463;
    public static final int APPRENTICE_TAMARA_11464 = 11464;
    public static final int APPRENTICE_TAMARA_11465 = 11465;
    public static final int SMITHING_CATALYST = 11466;
    public static final int HILL_GIANT_11467 = 11467;
    public static final int KOVAC = 11468;
    public static final int KOVAC_11469 = 11469;
    public static final int KOVAC_11470 = 11470;
    public static final int KOVAC_11472 = 11472;
    public static final int TARIK_11473 = 11473;
    public static final int MAISA_11474 = 11474;
    public static final int MAISA_11475 = 11475;
    public static final int MAISA_11476 = 11476;
    public static final int MAISA_11477 = 11477;
    public static final int SPIRIT = 11478;
    public static final int MEHHAR = 11479;
    public static final int HIGH_PRIEST_OF_SCABARAS = 11480;
    public static final int HIGH_PRIEST_OF_SCABARAS_11481 = 11481;
    public static final int CHAMPION_OF_SCABARAS = 11482;
    public static final int CHAMPION_OF_SCABARAS_11483 = 11483;
    public static final int SCARAB_SWARM_11484 = 11484;
    public static final int SHADOW_RIFT = 11485;
    public static final int OSMAN_11486 = 11486;
    public static final int OSMAN_11487 = 11487;
    public static final int SELIM = 11489;
    public static final int MENAPHITE_AKH = 11490;
    public static final int MENAPHITE_AKH_11491 = 11491;
    public static final int MENAPHITE_AKH_11492 = 11492;
    public static final int MENAPHITE_AKH_11493 = 11493;
    public static final int MENAPHITE_AKH_11495 = 11495;
    public static final int MENAPHITE_AKH_11496 = 11496;
    public static final int MENAPHITE_AKH_11497 = 11497;
    public static final int MENAPHITE_AKH_11498 = 11498;
    public static final int COENUS = 11499;
    public static final int JABARI = 11500;
    public static final int PHARAOH_KEMESIS = 11501;
    public static final int HIGH_PRIEST_11502 = 11502;
    public static final int PRIEST_11503 = 11503;
    public static final int MENAPHITE_GUARD = 11504;
    public static final int MENAPHITE_GUARD_11505 = 11505;
    public static final int MENAPHITE_GUARD_11506 = 11506;
    public static final int MENAPHITE_GUARD_11507 = 11507;
    public static final int SCARAB_MAGE_11508 = 11508;
    public static final int SCARAB_SWARM_11509 = 11509;
    public static final int SCARAB_MAGE_11510 = 11510;
    public static final int SCARAB_MAGE_11511 = 11511;
    public static final int CROCODILE_11513 = 11513;
    public static final int ROGER = 11514;
    public static final int MENAPHITE_GUARD_11515 = 11515;
    public static final int MENAPHITE_GUARD_11516 = 11516;
    public static final int MENAPHITE_GUARD_11517 = 11517;
    public static final int MENAPHITE_GUARD_11518 = 11518;
    public static final int MENAPHITE_GUARD_11519 = 11519;
    public static final int MENAPHITE_GUARD_11520 = 11520;
    public static final int MENAPHITE_GUARD_11521 = 11521;
    public static final int MENAPHITE_GUARD_11522 = 11522;
    public static final int MENAPHITE_GUARD_11523 = 11523;
    public static final int MENAPHITE_GUARD_11524 = 11524;
    public static final int MENAPHITE_GUARD_11525 = 11525;
    public static final int MENAPHITE_GUARD_11526 = 11526;
    public static final int MENAPHITE_GUARD_11527 = 11527;
    public static final int HEAD_MENAPHITE_GUARD = 11528;
    public static final int HEAD_MENAPHITE_GUARD_11529 = 11529;
    public static final int HEAD_MENAPHITE_GUARD_11530 = 11530;
    public static final int HEAD_MENAPHITE_GUARD_11531 = 11531;
    public static final int HEAD_MENAPHITE_GUARD_11532 = 11532;
    public static final int CITIZEN = 11533;
    public static final int CITIZEN_11534 = 11534;
    public static final int CITIZEN_11535 = 11535;
    public static final int CITIZEN_11536 = 11536;
    public static final int CITIZEN_11537 = 11537;
    public static final int CITIZEN_11538 = 11538;
    public static final int CITIZEN_11539 = 11539;
    public static final int CITIZEN_11540 = 11540;
    public static final int CITIZEN_11541 = 11541;
    public static final int CITIZEN_11542 = 11542;
    public static final int CITIZEN_11543 = 11543;
    public static final int CITIZEN_11544 = 11544;
    public static final int CITIZEN_11545 = 11545;
    public static final int CITIZEN_11546 = 11546;
    public static final int CITIZEN_11547 = 11547;
    public static final int CITIZEN_11548 = 11548;
    public static final int CITIZEN_11549 = 11549;
    public static final int CITIZEN_11550 = 11550;
    public static final int CITIZEN_11551 = 11551;
    public static final int CITIZEN_11552 = 11552;
    public static final int CITIZEN_11553 = 11553;
    public static final int CITIZEN_11554 = 11554;
    public static final int CITIZEN_11555 = 11555;
    public static final int CITIZEN_11556 = 11556;
    public static final int CITIZEN_11557 = 11557;
    public static final int CITIZEN_11558 = 11558;
    public static final int SELIM_11561 = 11561;
    public static final int JEX_11562 = 11562;
    public static final int JEX_11563 = 11563;
    public static final int MAISA_11564 = 11564;
    public static final int OSMAN_11565 = 11565;
    public static final int OSMAN_11566 = 11566;
    public static final int SCARAB_SWARM_11569 = 11569;
    public static final int MENAPHITE_GUARD_11570 = 11570;
    public static final int MENAPHITE_GUARD_11571 = 11571;
    public static final int MENAPHITE_GUARD_11572 = 11572;
    public static final int MENAPHITE_GUARD_11573 = 11573;
    public static final int MENAPHITE_GUARD_11574 = 11574;
    public static final int MENAPHITE_GUARD_11575 = 11575;
    public static final int COENUS_11576 = 11576;
    public static final int JOE_11577 = 11577;
    public static final int LADY_KELI = 11578;
    public static final int PRINCE_ALI = 11579;
    public static final int PRINCE_ALI_11580 = 11580;
    public static final int CROCODILE_11581 = 11581;
    public static final int CROCODILE_11582 = 11582;
    public static final int JACKAL_11583 = 11583;
    public static final int LOCUST = 11584;
    public static final int LOCUST_11585 = 11585;
    public static final int PLAGUE_FROG = 11586;
    public static final int PLAGUE_COW = 11587;
    public static final int PLAGUE_COW_11588 = 11588;
    public static final int PLAGUE_COW_11589 = 11589;
    public static final int WANDERER_11590 = 11590;
    public static final int AMASCUT = 11591;
    public static final int WORKER = 11592;
    public static final int WORKER_11593 = 11593;
    public static final int WORKER_11594 = 11594;
    public static final int WORKER_11595 = 11595;
    public static final int EMBALMER_11596 = 11596;
    public static final int CARPENTER_11597 = 11597;
    public static final int RAETUL = 11598;
    public static final int RAETUL_11599 = 11599;
    public static final int RAETUL_11600 = 11600;
    public static final int SIAMUN_11601 = 11601;
    public static final int HIGH_PRIEST_11602 = 11602;
    public static final int HIGH_PRIEST_11603 = 11603;
    public static final int HIGH_PRIEST_11604 = 11604;
    public static final int HIGH_PRIEST_11605 = 11605;
    public static final int PRIEST_11606 = 11606;
    public static final int PRIEST_11607 = 11607;
    public static final int POSSESSED_PRIEST_11608 = 11608;
    public static final int PRIEST_11609 = 11609;
    public static final int PRIEST_11610 = 11610;
    public static final int LANTHUS = 11650;
    public static final int HANNIBAL = 11651;
    public static final int TUMEKENS_GUARDIAN = 11652;
    public static final int ELIDINIS_GUARDIAN = 11653;
    public static final int APMEKEN = 11654;
    public static final int APMEKEN_11655 = 11655;
    public static final int APMEKEN_11656 = 11656;
    public static final int CRONDIS = 11657;
    public static final int GILBERT = 11658;
    public static final int CRONDIS_11659 = 11659;
    public static final int CRONDIS_11660 = 11660;
    public static final int SCABARAS = 11661;
    public static final int SCABARAS_11662 = 11662;
    public static final int SCABARAS_11663 = 11663;
    public static final int ARENA_GUARD_FRONK = 11664;
    public static final int ARENA_GUARD_NIKKOLAS = 11665;
    public static final int ARENA_GUARD_FAWRY = 11666;
    public static final int ARENA_GUARD_YON = 11667;
    public static final int ARENA_GUARD_DRAKNO = 11668;
    public static final int ARENA_GUARD_RACHI = 11669;
    public static final int ARENA_GUARD_JOBY = 11670;
    public static final int ARENA_GUARD_BENI = 11671;
    public static final int _1V1_TOURNAMENT_GUIDE = 11672;
    public static final int DUEL_GUIDE = 11673;
    public static final int CHRIS = 11674;
    public static final int FIGHTER = 11675;
    public static final int FIGHTER_11676 = 11676;
    public static final int FIGHTER_11677 = 11677;
    public static final int FIGHTER_11678 = 11678;
    public static final int FIGHTER_11679 = 11679;
    public static final int FIGHTER_11680 = 11680;
    public static final int FIGHTER_11681 = 11681;
    public static final int FIGHTER_11682 = 11682;
    public static final int FIGHTER_11683 = 11683;
    public static final int HET = 11686;
    public static final int HET_11687 = 11687;
    public static final int HET_11688 = 11688;
    public static final int OSMUMTEN = 11689;
    public static final int OSMUMTEN_11690 = 11690;
    public static final int SPIRIT_11691 = 11691;
    public static final int OSMUMTEN_11692 = 11692;
    public static final int OSMUMTEN_11693 = 11693;
    public static final int HELPFUL_SPIRIT = 11694;
    public static final int GHOST_11695 = 11695;
    public static final int AMASCUT_11696 = 11696;
    public static final int SCARAB = 11697;
    public static final int OBELISK = 11698;
    public static final int OBELISK_11699 = 11699;
    public static final int PALM_OF_RESOURCEFULNESS = 11700;
    public static final int PALM_OF_RESOURCEFULNESS_11701 = 11701;
    public static final int PALM_OF_RESOURCEFULNESS_11702 = 11702;
    public static final int PALM_OF_RESOURCEFULNESS_11703 = 11703;
    public static final int PALM_OF_RESOURCEFULNESS_11704 = 11704;
    public static final int CROCODILE_11705 = 11705;
    public static final int HETS_SEAL_PROTECTED = 11706;
    public static final int HETS_SEAL_WEAKENED = 11707;
    public static final int ORB_OF_DARKNESS = 11708;
    public static final int BABOON_BRAWLER = 11709;
    public static final int BABOON_THROWER = 11710;
    public static final int BABOON_MAGE = 11711;
    public static final int BABOON_BRAWLER_11712 = 11712;
    public static final int BABOON_THROWER_11713 = 11713;
    public static final int BABOON_MAGE_11714 = 11714;
    public static final int BABOON_SHAMAN = 11715;
    public static final int VOLATILE_BABOON = 11716;
    public static final int CURSED_BABOON = 11717;
    public static final int BABOON_THRALL = 11718;
    public static final int KEPHRI = 11719;
    public static final int KEPHRI_11720 = 11720;
    public static final int KEPHRI_11721 = 11721;
    public static final int KEPHRI_11722 = 11722;
    public static final int SCARAB_SWARM_11723 = 11723;
    public static final int SOLDIER_SCARAB = 11724;
    public static final int SPITTING_SCARAB = 11725;
    public static final int ARCANE_SCARAB = 11726;
    public static final int AGILE_SCARAB = 11727;
    public static final int EGG_11728 = 11728;
    public static final int EGG_11729 = 11729;
    public static final int ZEBAK = 11730;
    public static final int ZEBAKS_TAIL = 11731;
    public static final int ZEBAK_11732 = 11732;
    public static final int ZEBAK_11733 = 11733;
    public static final int ZEBAKS_TAIL_11734 = 11734;
    public static final int JUG = 11735;
    public static final int JUG_11736 = 11736;
    public static final int BOULDER_11737 = 11737;
    public static final int WAVE = 11738;
    public static final int BLOODY_WAVE = 11739;
    public static final int CROCODILE_11740 = 11740;
    public static final int CROCODILE_11741 = 11741;
    public static final int BLOOD_CLOUD = 11742;
    public static final int BLOOD_CLOUD_11743 = 11743;
    public static final int ELIDINIS_WARDEN = 11746;
    public static final int TUMEKENS_WARDEN = 11747;
    public static final int ELIDINIS_WARDEN_11748 = 11748;
    public static final int TUMEKENS_WARDEN_11749 = 11749;
    public static final int OBELISK_11750 = 11750;
    public static final int OBELISK_11751 = 11751;
    public static final int OBELISK_11752 = 11752;
    public static final int ELIDINIS_WARDEN_11753 = 11753;
    public static final int ELIDINIS_WARDEN_11754 = 11754;
    public static final int ELIDINIS_WARDEN_11755 = 11755;
    public static final int TUMEKENS_WARDEN_11756 = 11756;
    public static final int TUMEKENS_WARDEN_11757 = 11757;
    public static final int TUMEKENS_WARDEN_11758 = 11758;
    public static final int ELIDINIS_WARDEN_11759 = 11759;
    public static final int TUMEKENS_WARDEN_11760 = 11760;
    public static final int ELIDINIS_WARDEN_11761 = 11761;
    public static final int TUMEKENS_WARDEN_11762 = 11762;
    public static final int ELIDINIS_WARDEN_11763 = 11763;
    public static final int TUMEKENS_WARDEN_11764 = 11764;
    public static final int PHANTOM = 11767;
    public static final int PHANTOM_11768 = 11768;
    public static final int CORE = 11770;
    public static final int CORE_11771 = 11771;
    public static final int ENERGY_SIPHON = 11772;
    public static final int ZEBAKS_PHANTOM = 11774;
    public static final int BABAS_PHANTOM = 11775;
    public static final int KEPHRIS_PHANTOM = 11776;
    public static final int AKKHAS_PHANTOM = 11777;
    public static final int BABA = 11778;
    public static final int BABA_11779 = 11779;
    public static final int BABA_11780 = 11780;
    public static final int BABOON = 11781;
    public static final int BOULDER_11782 = 11782;
    public static final int BOULDER_11783 = 11783;
    public static final int RUBBLE_11784 = 11784;
    public static final int RUBBLE_11785 = 11785;
    public static final int RUBBLE_11786 = 11786;
    public static final int RUBBLE_11787 = 11787;
    public static final int AKKHA = 11789;
    public static final int AKKHA_11790 = 11790;
    public static final int AKKHA_11791 = 11791;
    public static final int AKKHA_11792 = 11792;
    public static final int AKKHA_11793 = 11793;
    public static final int AKKHA_11794 = 11794;
    public static final int AKKHA_11795 = 11795;
    public static final int AKKHA_11796 = 11796;
    public static final int AKKHAS_SHADOW = 11797;
    public static final int AKKHAS_SHADOW_11798 = 11798;
    public static final int AKKHAS_SHADOW_11799 = 11799;
    public static final int ORB_OF_LIGHTNING = 11800;
    public static final int ORB_OF_DARKNESS_11801 = 11801;
    public static final int BURNING_ORB = 11802;
    public static final int FROZEN_ORB = 11803;
    public static final int UNSTABLE_ORB = 11804;
    public static final int BANK_CAMEL = 11806;
    public static final int MAISA_11807 = 11807;
    public static final int MAISA_11808 = 11808;
    public static final int APMEKEN_11809 = 11809;
    public static final int CRONDIS_11810 = 11810;
    public static final int HET_11811 = 11811;
    public static final int TUMEKENS_GUARDIAN_11812 = 11812;
    public static final int ELIDINIS_GUARDIAN_11813 = 11813;
    public static final int MESSENGER_11814 = 11814;
    public static final int MESSENGER_11815 = 11815;
    public static final int MESSENGER_11816 = 11816;
    public static final int MESSENGER_11817 = 11817;
    public static final int AKKHITO = 11840;
    public static final int BABI = 11841;
    public static final int KEPHRITI = 11842;
    public static final int ZEBO = 11843;
    public static final int TUMEKENS_DAMAGED_GUARDIAN = 11844;
    public static final int ELIDINIS_DAMAGED_GUARDIAN = 11845;
    public static final int AKKHITO_11846 = 11846;
    public static final int BABI_11847 = 11847;
    public static final int KEPHRITI_11848 = 11848;
    public static final int ZEBO_11849 = 11849;
    public static final int TUMEKENS_DAMAGED_GUARDIAN_11850 = 11850;
    public static final int ELIDINIS_DAMAGED_GUARDIAN_11851 = 11851;
    public static final int ELIZA = 11852;
    public static final int ELIZA_11853 = 11853;
}
