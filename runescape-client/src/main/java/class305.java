import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("kl")
public class class305 {
   @ObfuscatedName("p")
   @ObfuscatedSignature(
      descriptor = "Lca;"
   )
   @Export("loginScreenRunesAnimation")
   static LoginScreenAnimation loginScreenRunesAnimation;
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "Lkp;"
   )
   public class307 field3476 = new class307();
   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "Lkr;"
   )
   class302 field3477 = new class302();
   @ObfuscatedName("v")
   @ObfuscatedSignature(
      descriptor = "Laa;"
   )
   class27 field3478 = new class27();
   @ObfuscatedName("s")
   public Object[] field3481;
   @ObfuscatedName("z")
   public Object[] field3480;
   @ObfuscatedName("j")
   public Object[] field3479;
   @ObfuscatedName("i")
   public Object[] field3482;

   class305() {
   }
}
