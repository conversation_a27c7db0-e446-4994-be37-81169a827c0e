.id                 84
.int_stack_count    1
.string_stack_count 0
.int_var_count      24
.string_var_count   25
   get_varc_int           1112
   clientclock           
   if_icmpeq              LABEL4
   jump                   LABEL14
LABEL4:
   clientclock           
   iconst                 1
   add                   
   set_varc_int           65
   iconst                 664
   iconst                 0
   sconst                 "1"
   iconst                 10616832
   if_setontimer         
   return                
LABEL14:
   clientclock           
   set_varc_int           1112
   iconst                 10616888
   if_getwidth           
   istore                 1
   chat_playername       
   removetags            
   sstore                 0
   iconst                 0
   istore                 2
   get_varc_int           41
   iconst                 3
   if_icmpeq              LABEL28
   jump                   LABEL30
LABEL28:
   iconst                 1
   istore                 2
LABEL30:
   iconst                 0
   istore                 3
   iconst                 0
   istore                 4
   iconst                 0
   istore                 5
   sconst                 "<col=004f00>"
   sstore                 1
   sconst                 "<col=0000ff>"
   sstore                 2
   sconst                 "<col=0000ff>"
   sstore                 3
   sconst                 ""
   sstore                 4
   sconst                 ""
   sstore                 5
   sconst                 ""
   sstore                 6
   sconst                 ""
   sstore                 7
   sconst                 ""
   sstore                 8
   sconst                 ""
   sstore                 9
   sconst                 ""
   sstore                 10
   sconst                 ""
   sstore                 11
   sconst                 ""
   sstore                 12
   sconst                 ""
   sstore                 13
   sconst                 ""
   sstore                 14
   sconst                 ""
   sstore                 15
   sconst                 ""
   sstore                 16
   invoke                 921
   iconst                 1
   if_icmpeq              LABEL72
   jump                   LABEL155
LABEL72:
   iconst                 16777215
   iconst                 1
   iconst                 1
   istore                 5
   istore                 4
   istore                 3
   sconst                 "<col=30ff30>"
   sconst                 "<col=9070ff>"
   sconst                 "<col=9070ff>"
   sstore                 3
   sstore                 2
   sstore                 1
   iconst                 2897
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 4
   iconst                 2899
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 5
   iconst                 2902
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 6
   iconst                 2909
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 8
   iconst                 2907
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 7
   iconst                 2911
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 9
   iconst                 2913
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 10
   iconst                 2976
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 11
   iconst                 3746
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 12
   iconst                 3748
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 13
   iconst                 3750
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 14
   sconst                 "<col=ff8f3e>"
   sstore                 15
   sconst                 "<col=12ea77>"
   sstore                 16
   jump                   LABEL225
LABEL155:
   iconst                 2896
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 4
   iconst                 2898
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 5
   iconst                 2901
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 6
   iconst                 2906
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 7
   iconst                 2908
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 8
   iconst                 2910
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 9
   iconst                 2912
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 10
   iconst                 2975
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 11
   iconst                 3745
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 12
   iconst                 3747
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 13
   iconst                 3749
   iconst                 1230
   struct_param          
   sconst                 "col"
   invoke                 3739
   sstore                 14
   sconst                 "<col=93370c>"
   sstore                 15
   sconst                 "<col=004a23>"
   sstore                 16
LABEL225:
   iload                  3
   sconst                 "col"
   invoke                 3739
   sstore                 17
   sload                  4
   sload                  5
   sload                  6
   sload                  17
   sload                  7
   sload                  8
   sload                  9
   sload                  10
   sload                  11
   sload                  12
   sload                  13
   sload                  14
   iload                  5
   invoke                 4484
   sstore                 14
   sstore                 13
   sstore                 12
   sstore                 11
   sstore                 10
   sstore                 9
   sstore                 8
   sstore                 7
   sstore                 17
   sstore                 6
   sstore                 5
   sstore                 4
   iconst                 0
   istore                 6
   iconst                 0
   istore                 7
   iconst                 0
   istore                 8
   iconst                 105
   iconst                 73
   iconst                 579
   iload                  8
   enum                  
   istore                 9
   get_varc_int           41
   iconst                 0
   if_icmpeq              LABEL274
   get_varc_int           41
   iconst                 2
   if_icmpeq              LABEL274
   jump                   LABEL330
LABEL274:
   chat_getmessagefilter 
   string_length         
   iconst                 0
   if_icmpgt              LABEL279
   jump                   LABEL330
LABEL279:
   sload                  4
   sconst                 "Public chat filtering:"
   sconst                 "</col>"
   sconst                 " "
   sconst                 "<lt>"
   chat_getmessagefilter 
   escape                
   lowercase             
   sconst                 "<gt>"
   join_string            7
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   iload                  9
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iload                  6
   iload                  7
   sub                   
   istore                 6
   iload                  8
   iconst                 1
   add                   
   istore                 8
   iconst                 105
   iconst                 73
   iconst                 579
   iload                  8
   enum                  
   istore                 9
LABEL330:
   iload                  0
   istore                 10
   iconst                 0
   istore                 11
   iconst                 -1
   istore                 12
   sconst                 ""
   sstore                 18
   sconst                 ""
   sstore                 19
   sconst                 ""
   sstore                 20
   sconst                 ""
   sstore                 21
   iconst                 -1
   istore                 13
   iconst                 0
   istore                 14
   iconst                 0
   istore                 15
   sconst                 ""
   sstore                 22
   iconst                 -1
   istore                 16
   iconst                 -1
   istore                 17
   iconst                 -1
   istore                 18
   iconst                 126
   istore                 19
   iconst                 126
   istore                 20
   iconst                 0
   activeclansettings_find_affined
   iconst                 1
   if_icmpeq              LABEL367
   jump                   LABEL387
LABEL367:
   iconst                 0
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL372
   jump                   LABEL387
LABEL372:
   sload                  0
   activeclanchannel_getuserslot
   istore                 17
   iload                  17
   iconst                 -1
   if_icmpne              LABEL379
   jump                   LABEL382
LABEL379:
   iload                  17
   activeclanchannel_getuserrank
   istore                 18
LABEL382:
   activeclanchannel_getrankkick
   iconst                 2956
   invoke                 4456
   istore                 20
   istore                 19
LABEL387:
   sconst                 ""
   sstore                 23
   iconst                 0
   istore                 21
   sconst                 ""
   sstore                 24
LABEL393:
   iload                  10
   iconst                 -1
   if_icmpne              LABEL397
   jump                   LABEL1905
LABEL397:
   iload                  9
   iconst                 -1
   if_icmpne              LABEL401
   jump                   LABEL1905
LABEL401:
   iload                  10
   chat_gethistoryex_byuid
   istore                 21
   sstore                 23                ; timestamp
   istore                 15
   sstore                 20
   sstore                 19
   sstore                 18
   istore                 12
   istore                 11
   iload                  11
   sload                  18
   iload                  15
   sload                  20
   invoke                 193
   iconst                 1
   if_icmpeq              CHAT_FILTER
   jump                   LABEL1901
CHAT_FILTER:
   sload                  20                ; Load the message
   iconst                 1                 ; Gets changed to 0 if message is blocked
   iload                  11                ; Load the messageType
   iload                  10                 ; Load the id of the messageNode
   sconst                 "chatFilterCheck"
   runelite_callback     
   pop_int               ; Pop the id of the messageNode
   pop_int               ; Pop the messageType
   iconst                 1                 ; 2nd half of conditional
   sstore                 20                ; Override the message with our filtered message
   if_icmpeq              LABEL419          ; Check if we are building this message
   jump                   LABEL1901          ; continue to next message, skipping this
LABEL419:
   iload                  11
   sload                  18
   sload                  23
   sload                  20
   sconst                 "null"
   invoke                 4742
   sload                  20
   iload                  12
   iload                  2
   sload                  0
   iload                  15
   invoke                 90
   iconst                 1
   if_icmpeq              LABEL434
   jump                   LABEL1901
LABEL434:
   iconst                 0 ; splitpmbox
   iload                  10 ; message uid
   sload                  19 ; message channel
   sload                  18 ; message name
   sload                  20 ; message
   sload                  23 ; message timestamp
   sconst                 "chatMessageBuilding"
   runelite_callback     
   pop_int                   ; pop uid
   pop_int                   ; splitpmbox
   sstore                 23 ; message timestamp
   sstore                 20 ; message
   sstore                 18 ; message name
   sstore                 19 ; message channel
   iload                  11
   switch                
      2: LABEL437
      1: LABEL437
      90: LABEL461
      91: LABEL461
      3: LABEL485
      7: LABEL485
      101: LABEL510
      5: LABEL531
      6: LABEL567
      103: LABEL592
      104: LABEL592
      110: LABEL592
      109: LABEL613
      9: LABEL634
      111: LABEL663
      112: LABEL688
      41: LABEL713
      44: LABEL932
      43: LABEL1097
      46: LABEL1293
      14: LABEL1348
      107: LABEL1378
      113: LABEL1417
      114: LABEL1438
   jump                   LABEL1465
LABEL437:
   sload                  23
   sload                  18
   sconst                 ":"
   join_string            2
   sconst                 "null"
   invoke                 4742
   sload                  4
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL461:
   sload                  23
   sload                  18
   sconst                 ":"
   join_string            2
   sconst                 "null"
   invoke                 4742
   sload                  6
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL485:
   sload                  23
   sconst                 "From "
   sload                  18
   sconst                 ":"
   join_string            3
   sconst                 "privChatUsername"
   runelite_callback     
   sconst                 "null"
   invoke                 4742
   sload                  5
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL510:
   sload                  23
   sload                  9
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL531:
   sload                  23
   sload                  5
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   get_varbit             1627
   iconst                 0
   if_icmpeq              LABEL555
   jump                   LABEL566
LABEL555:
   iload                  12
   iconst                 500
   add                   
   iconst                 1
   add                   
   set_varc_int           65
   iconst                 664
   iconst                 0
   sconst                 "1"
   iconst                 10616832
   if_setontimer         
LABEL566:
   jump                   LABEL1482
LABEL567:
   sload                  23
   sconst                 "To "
   sload                  18
   sconst                 ":"
   join_string            3
   sconst                 "privChatUsername"
   runelite_callback     
   sconst                 "null"
   invoke                 4742
   sload                  5
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL592:
   sload                  23
   sload                  10
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL613:
   sload                  23
   sconst                 "<col=1a31f2>"
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL634:
   sload                  23
   sconst                 "["
   sload                  3
   sload                  19
   sconst                 "</col>"
   sconst                 "] "
   sload                  18
   sconst                 ":"
   join_string            7
   sconst                 "null"
   invoke                 4742
   sload                  7
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL663:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 24
   sload                  23
   sconst                 "<col=1a31f2>"
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL688:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 24
   sload                  23
   sconst                 "<col=1a31f2>"
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL713:
   iconst                 1
   activeclansettings_find_affined
   iconst                 1
   if_icmpeq              LABEL718
   jump                   LABEL765
LABEL718:
   iconst                 1
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL723
   jump                   LABEL765
LABEL723:
   sload                  20
   invoke                 5501
   iconst                 1
   if_icmpeq              LABEL728
   jump                   LABEL765
LABEL728:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 24
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  12
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
   jump                   LABEL931
LABEL765:
   iconst                 0
   activeclansettings_find_affined
   iconst                 1
   if_icmpeq              LABEL770
   jump                   LABEL899
LABEL770:
   iconst                 0
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL775
   jump                   LABEL899
LABEL775:
   sload                  18
   removetags            
   activeclansettings_getaffinedslot
   istore                 17
   iload                  17
   iconst                 -1
   if_icmpne              LABEL783
   jump                   LABEL862
LABEL783:
   iload                  17
   activeclansettings_getaffinedrank
   invoke                 4302
   istore                 16
   sstore                 22
   iload                  16
   iconst                 -1
   if_icmpne              LABEL792
   jump                   LABEL825
LABEL792:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iload                  16
   iconst                 13
   iconst                 13
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  8
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
   jump                   LABEL861
LABEL825:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sload                  2
   sload                  22
   sconst                 "</col>"
   sconst                 " "
   sload                  18
   sconst                 ":"
   join_string            6
   sload                  8
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL861:
   jump                   LABEL898
LABEL862:
   iconst                 -1
   invoke                 4302
   istore                 16
   sstore                 22
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iload                  16
   iconst                 13
   iconst                 13
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  8
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL898:
   jump                   LABEL931
LABEL899:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  8
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL931:
   jump                   LABEL1482
LABEL932:
   activeclansettings_find_listened
   iconst                 1
   if_icmpeq              LABEL936
   jump                   LABEL1064
LABEL936:
   activeclanchannel_find_listened
   iconst                 1
   if_icmpeq              LABEL940
   jump                   LABEL1064
LABEL940:
   sload                  18
   removetags            
   activeclansettings_getaffinedslot
   istore                 17
   iload                  17
   iconst                 -1
   if_icmpne              LABEL948
   jump                   LABEL1027
LABEL948:
   iload                  17
   activeclansettings_getaffinedrank
   invoke                 4302
   istore                 16
   sstore                 22
   iload                  16
   iconst                 -1
   if_icmpne              LABEL957
   jump                   LABEL990
LABEL957:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iload                  16
   iconst                 13
   iconst                 13
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  11
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
   jump                   LABEL1026
LABEL990:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sload                  2
   sload                  22
   sconst                 "</col>"
   sconst                 " "
   sload                  18
   sconst                 ":"
   join_string            6
   sload                  11
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL1026:
   jump                   LABEL1063
LABEL1027:
   iconst                 -1
   invoke                 4302
   istore                 16
   sstore                 22
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iload                  16
   iconst                 13
   iconst                 13
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  11
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL1063:
   jump                   LABEL1096
LABEL1064:
   sload                  23
   sconst                 "["
   sload                  2
   sload                  19
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sload                  18
   sconst                 ":"
   join_string            2
   sload                  11
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL1096:
   jump                   LABEL1482
LABEL1097:
   sload                  20
   invoke                 5309
   iconst                 1
   if_icmpeq              LABEL1102
   jump                   LABEL1167
LABEL1102:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 18
   sload                  20
   sconst                 "</col>"
   sconst                 "</col>"
   sload                  14
   append                
   invoke                 3302
   sstore                 20
   iconst                 1
   activeclansettings_find_affined
   iconst                 1
   if_icmpeq              LABEL1118
   jump                   LABEL1145
LABEL1118:
   sload                  23
   sconst                 "["
   sload                  2
   activeclansettings_getclanname
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   sload                  14
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1166
LABEL1145:
   sload                  23
   sconst                 ""
   sconst                 "null"
   invoke                 4742
   sload                  14
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
LABEL1166:
   jump                   LABEL1292
LABEL1167:
   sload                  20
   invoke                 6264
   iconst                 1
   if_icmpeq              LABEL1172
   jump                   LABEL1224
LABEL1172:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 18
   iconst                 2
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL1181
   jump                   LABEL1205
LABEL1181:
   sload                  23
   sconst                 "["
   sload                  2
   sconst                 "PvP Arena"
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   sload                  20
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1223
LABEL1205:
   sload                  23
   sconst                 ""
   sconst                 "null"
   invoke                 4742
   sload                  20
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
LABEL1223:
   jump                   LABEL1292
LABEL1224:
   sload                  20
   sconst                 "</col>"
   sconst                 "</col>"
   sload                  13
   append                
   invoke                 3302
   sstore                 20
   iconst                 0
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL1236
   jump                   LABEL1267
LABEL1236:
   sload                  23
   sconst                 "["
   sload                  2
   activeclanchannel_getclanname
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sconst                 ""
   sload                  13
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
   jump                   LABEL1292
LABEL1267:
   sload                  23
   sconst                 ""
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sconst                 ""
   sload                  13
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL1292:
   jump                   LABEL1482
LABEL1293:
   activeclanchannel_find_listened
   iconst                 1
   if_icmpeq              LABEL1297
   jump                   LABEL1325
LABEL1297:
   sload                  23
   sconst                 "["
   sload                  2
   activeclanchannel_getclanname
   sconst                 "</col>"
   sconst                 "]"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sconst                 ""
   sload                  20
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
   jump                   LABEL1347
LABEL1325:
   sload                  23
   sconst                 ""
   sconst                 "null"
   invoke                 4742
   iconst                 -1
   iconst                 0
   iconst                 0
   sconst                 ""
   sload                  20
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 4483
   istore                 7
LABEL1347:
   jump                   LABEL1482
LABEL1348:
   sload                  20
   invoke                 2066
   istore                 13
   sstore                 21
   sstore                 20
   sload                  23
   sload                  1
   sconst                 "Broadcast:"
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   sload                  17
   sload                  20
   sconst                 "</col>"
   join_string            3
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 203
   istore                 7
   jump                   LABEL1482
LABEL1378:
   clientclock           
   iload                  12
   sub                   
   iconst                 500
   if_icmpgt              LABEL1384
   jump                   LABEL1399
LABEL1384:
   sconst                 "jk :P"
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1416
LABEL1399:
   sload                  23
   sload                  20
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
LABEL1416:
   jump                   LABEL1482
LABEL1417:
   sload                  23
   sload                  15
   sload                  20
   sconst                 "</col>"
   join_string            3
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL1438:
   sload                  20
   invoke                 632
   sstore                 20
   sstore                 18
   sload                  23
   sload                  18
   sconst                 ": "
   sload                  16
   sload                  20
   sconst                 "</col>"
   join_string            5
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
   jump                   LABEL1482
LABEL1465:
   sload                  23
   sload                  20
   sconst                 "null"
   invoke                 4742
   iload                  8
   iload                  9
   iconst                 10616888
   iload                  1
   iconst                 3
   iconst                 14
   iload                  6
   iconst                 0
   iconst                 103
   iload                  3
   iload                  4
   invoke                 199
   istore                 7
LABEL1482:
   iload                  9
   if_clearops           
   iload                  11
   switch                
      1: LABEL1487
      2: LABEL1487
      3: LABEL1487
      6: LABEL1487
      7: LABEL1487
      9: LABEL1487
      90: LABEL1487
      91: LABEL1487
      106: LABEL1487
      41: LABEL1487
      44: LABEL1487
      101: LABEL1591
      103: LABEL1643
      104: LABEL1643
      110: LABEL1643
      14: LABEL1686
      109: LABEL1746
      111: LABEL1789
      112: LABEL1832
   jump                   LABEL1875
LABEL1487:
   sconst                 "<col=ffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   sload                  0
   sload                  18
   removetags            
   compare               
   iconst                 0
   if_icmpne              LABEL1515
   jump                   LABEL1590
LABEL1515:
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1519
   jump                   LABEL1524
LABEL1519:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1532
LABEL1524:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1532:
   iconst                 8
   sconst                 "Report"
   iload                  9
   if_setop              
   iload                  11
   iconst                 9
   if_icmpeq              LABEL1540
   jump                   LABEL1553
LABEL1540:
   clan_getchatcount     
   iconst                 0
   if_icmpgt              LABEL1544
   jump                   LABEL1552
LABEL1544:
   clan_getchatrank      
   clan_getchatminkick   
   if_icmpge              LABEL1548
   jump                   LABEL1552
LABEL1548:
   iconst                 9
   sconst                 "Kick"
   iload                  9
   if_setop              
LABEL1552:
   jump                   LABEL1590
LABEL1553:
   iload                  11
   iconst                 41
   if_icmpeq              LABEL1557
   jump                   LABEL1590
LABEL1557:
   iload                  18
   iload                  19
   if_icmpge              LABEL1561
   jump                   LABEL1590
LABEL1561:
   iconst                 0
   activeclanchannel_find_affined
   iconst                 1
   if_icmpeq              LABEL1566
   jump                   LABEL1590
LABEL1566:
   sload                  18
   removetags            
   activeclanchannel_getuserslot
   istore                 17
   iload                  17
   iconst                 -1
   if_icmpeq              LABEL1578
   iload                  17
   activeclanchannel_getuserrank
   iconst                 -1
   if_icmple              LABEL1578
   jump                   LABEL1590
LABEL1578:
   iconst                 9
   sconst                 "Kick"
   iload                  9
   if_setop              
   iload                  18
   iload                  20
   if_icmpge              LABEL1586
   jump                   LABEL1590
LABEL1586:
   iconst                 10
   sconst                 "Ban"
   iload                  9
   if_setop              
LABEL1590:
   jump                   LABEL1887
LABEL1591:
   sconst                 "<col=ffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   invoke                 5548
   iconst                 1
   if_icmpeq              LABEL1616
   jump                   LABEL1621
LABEL1616:
   iconst                 1
   sconst                 "Accept invitation"
   iload                  9
   if_setop              
   jump                   LABEL1625
LABEL1621:
   iconst                 1
   sconst                 "Accept trade"
   iload                  9
   if_setop              
LABEL1625:
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1629
   jump                   LABEL1634
LABEL1629:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1642
LABEL1634:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1642:
   jump                   LABEL1887
LABEL1643:
   sconst                 "<col=ffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iconst                 2
   sconst                 "Accept challenge"
   iload                  9
   if_setop              
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1672
   jump                   LABEL1677
LABEL1672:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1685
LABEL1677:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1685:
   jump                   LABEL1887
LABEL1686:
   sload                  21
   string_length         
   iconst                 0
   if_icmpgt              LABEL1691
   jump                   LABEL1720
LABEL1691:
   iload                  13
   iconst                 -1
   if_icmpne              LABEL1695
   jump                   LABEL1720
LABEL1695:
   iconst                 6
   sconst                 "Open"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Check"
   iload                  9
   if_setop              
   iconst                 2065
   iload                  9
   if_getlayer           
   iload                  8
   iconst                 3158271
   sconst                 "Iii"
   iload                  9
   if_setonmouseover     
   iconst                 2065
   iload                  9
   if_getlayer           
   iload                  8
   iload                  3
   sconst                 "Iii"
   iload                  9
   if_setonmouseleave    
   jump                   LABEL1728
LABEL1720:
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
LABEL1728:
   iconst                 9
   sconst                 "Clear history"
   iload                  9
   if_setop              
   sconst                 "<col=ff9040>"
   sconst                 "Notification"
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 2064
   iconst                 -2147483644
   sload                  21
   iload                  13
   sconst                 "isi"
   iload                  9
   if_setonop            
   jump                   LABEL1887
LABEL1746:
   sconst                 "<col=0xffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 2
   sconst                 "Form clan"
   iload                  9
   if_setop              
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1775
   jump                   LABEL1780
LABEL1775:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1788
LABEL1780:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1788:
   jump                   LABEL1887
LABEL1789:
   sconst                 "<col=0xffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 2
   sconst                 "Form group"
   iload                  9
   if_setop              
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1818
   jump                   LABEL1823
LABEL1818:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1831
LABEL1823:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1831:
   jump                   LABEL1887
LABEL1832:
   sconst                 "<col=0xffffff>"
   sload                  18
   sconst                 "</col>"
   join_string            3
   iload                  9
   if_setopbase          
   iconst                 2
   sconst                 "Group with"
   iload                  9
   if_setop              
   iconst                 86
   iconst                 -2147483644
   sconst                 "event_opbase"
   iload                  11
   sconst                 "isi"
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iload                  15
   iconst                 1
   if_icmpeq              LABEL1861
   jump                   LABEL1866
LABEL1861:
   iconst                 6
   sconst                 "Message"
   iload                  9
   if_setop              
   jump                   LABEL1874
LABEL1866:
   iconst                 6
   sconst                 "Add friend"
   iload                  9
   if_setop              
   iconst                 7
   sconst                 "Add ignore"
   iload                  9
   if_setop              
LABEL1874:
   jump                   LABEL1887
LABEL1875:
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
LABEL1887:
   iload                  6
   iload                  7
   sub                   
   istore                 6
   iload                  8
   iconst                 1
   add                   
   istore                 8
   iconst                 105
   iconst                 73
   iconst                 579
   iload                  8
   enum                  
   istore                 9
LABEL1901:
   iload                  10
   chat_getprevuid       
   istore                 10
   jump                   LABEL393
LABEL1905:
   iload                  8
   istore                 22
LABEL1907:
   iload                  9
   iconst                 -1
   if_icmpne              LABEL1911
   jump                   LABEL1994
LABEL1911:
   iload                  9
   if_clearops           
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonop            
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseover     
   iconst                 -1
   sconst                 ""
   iload                  9
   if_setonmouseleave    
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 0
   iload                  9
   if_setsize            
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   cc_find               
   iconst                 1
   if_icmpeq              LABEL1939
   jump                   LABEL1943
LABEL1939:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL1943:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 1
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL1953
   jump                   LABEL1957
LABEL1953:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL1957:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 2
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL1967
   jump                   LABEL1971
LABEL1967:
   sconst                 ""
   cc_settext            
   iconst                 1
   cc_sethide            
LABEL1971:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 3
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL1981
   jump                   LABEL1983
LABEL1981:
   iconst                 1
   cc_sethide            
LABEL1983:
   iload                  8
   iconst                 1
   add                   
   istore                 8
   iconst                 105
   iconst                 73
   iconst                 579
   iload                  8
   enum                  
   istore                 9
   jump                   LABEL1907
LABEL1994:
   iload                  6
   iconst                 2
   sub                   
   istore                 6
   iconst                 0
   iload                  6
   sub                   
   istore                 6
   iconst                 10616888
   if_getheight          
   istore                 23
   iload                  6
   iload                  23
   if_icmpgt              LABEL2009
   jump                   LABEL2011
LABEL2009:
   iload                  6
   istore                 23
LABEL2011:
   iload                  22
   istore                 8
LABEL2013:
   iload                  8
   iconst                 0
   if_icmpgt              LABEL2017
   jump                   LABEL2100
LABEL2017:
   iload                  8
   iconst                 1
   sub                   
   istore                 8
   iconst                 105
   iconst                 73
   iconst                 579
   iload                  8
   enum                  
   istore                 9
   iload                  9
   if_gety               
   iload                  23
   add                   
   iconst                 2
   sub                   
   istore                 6
   iload                  9
   if_getx               
   iload                  6
   iconst                 0
   iconst                 0
   iload                  9
   if_setposition        
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   cc_find               
   iconst                 1
   if_icmpeq              LABEL2049
   jump                   LABEL2054
LABEL2049:
   cc_getx               
   iload                  6
   iconst                 0
   iconst                 0
   cc_setposition        
LABEL2054:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 1
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL2064
   jump                   LABEL2069
LABEL2064:
   cc_getx               
   iload                  6
   iconst                 0
   iconst                 0
   cc_setposition        
LABEL2069:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 2
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL2079
   jump                   LABEL2084
LABEL2079:
   cc_getx               
   iload                  6
   iconst                 0
   iconst                 0
   cc_setposition        
LABEL2084:
   iconst                 10616888
   iload                  8
   iconst                 4
   multiply              
   iconst                 3
   add                   
   cc_find               
   iconst                 1
   if_icmpeq              LABEL2094
   jump                   LABEL2099
LABEL2094:
   cc_getx               
   iload                  6
   iconst                 0
   iconst                 0
   cc_setposition        
LABEL2099:
   jump                   LABEL2013
LABEL2100:
   iconst                 0
   iload                  23
   iconst                 10616888
   if_setscrollsize      
   iconst                 10617389
   iconst                 10616888
   get_varc_int           7
   iload                  23
   get_varc_int           8
   sub                   
   add                   
   invoke                 72
   iconst                 10616888
   if_getscrolly         
   iload                  23
   set_varc_int           8
   set_varc_int           7
   return                
