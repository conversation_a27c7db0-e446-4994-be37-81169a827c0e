import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("nr")
public class class389 {
   @ObfuscatedName("f")
   @ObfuscatedSignature(
      descriptor = "Lnr;"
   )
   public static final class389 field4516 = new class389(0);
   @ObfuscatedName("w")
   @ObfuscatedSignature(
      descriptor = "Lnr;"
   )
   static final class389 field4515 = new class389(1);
   @ObfuscatedName("v")
   @ObfuscatedGetter(
      intValue = -1237713179
   )
   final int field4514;

   class389(int var1) {
      this.field4514 = var1;
   }
}
