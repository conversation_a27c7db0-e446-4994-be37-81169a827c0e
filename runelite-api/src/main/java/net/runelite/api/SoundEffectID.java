/*
 * Copyright (c) 2018, SomeoneWithAnInternetConnection
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

/**
 * Utility class used for mapping sound effect IDs.
 */
public final class SoundEffectID
{
	public final static int UI_BOOP = 2266;
	public final static int GE_INCREMENT_PLOP = 3929;
	public final static int GE_DECREMENT_PLOP = 3930;
	public final static int GE_ADD_OFFER_DINGALING = 3925;
	public final static int GE_COLLECT_BLOOP = 3928;
	public final static int GE_COIN_TINKLE = 3924;

	public final static int CLOSE_DOOR = 60;
	public final static int OPEN_DOOR = 62;
	public final static int ITEM_DROP = 2739;
	public final static int ITEM_PICKUP = 2582;
	public final static int PICK_PLANT_BLOOP = 2581;

	public final static int BURY_BONES = 2738;
	public final static int TINDER_STRIKE = 2597;
	public final static int FIRE_WOOSH = 2596;
	public final static int TREE_FALLING = 2734;
	public final static int TREE_CHOP = 2735;
	public final static int MINING_TINK = 3220;
	public final static int COOK_WOOSH = 2577;
	public final static int MAGIC_SPLASH_BOING = 227;
	public final static int SMITH_ANVIL_TINK = 3790;
	public final static int SMITH_ANVIL_TONK = 3791;

	/**
	 * Used for random event NPCs spawning, and the imp teleport.
	 */
	public final static int NPC_TELEPORT_WOOSH = 1930;

	public final static int TELEPORT_VWOOP = 200;
	public final static int ZERO_DAMAGE_SPLAT = 511;
	public final static int TAKE_DAMAGE_SPLAT = 510;
	public final static int ATTACK_HIT = 2498;

	public final static int PRAYER_ACTIVATE_THICK_SKIN = 2690;
	public final static int PRAYER_ACTIVATE_BURST_OF_STRENGTH = 2688;
	public final static int PRAYER_ACTIVATE_CLARITY_OF_THOUGHT = 2664;
	public final static int PRAYER_ACTIVATE_SHARP_EYE_RIGOUR = 2685;
	public final static int PRAYER_ACTIVATE_MYSTIC_WILL_AUGURY = 2670;
	public final static int PRAYER_ACTIVATE_ROCK_SKIN = 2684;
	public final static int PRAYER_ACTIVATE_SUPERHUMAN_STRENGTH = 2689;
	public final static int PRAYER_ACTIVATE_IMPROVED_REFLEXES = 2662;
	public final static int PRAYER_ACTIVATE_RAPID_RESTORE_PRESERVE = 2679;
	public final static int PRAYER_ACTIVATE_RAPID_HEAL = 2678;
	public final static int PRAYER_ACTIVATE_PROTECT_ITEM = 1982;
	public final static int PRAYER_ACTIVATE_HAWK_EYE = 2666;
	public final static int PRAYER_ACTIVATE_MYSTIC_LORE = 2668;
	public final static int PRAYER_ACTIVATE_STEEL_SKIN = 2687;
	public final static int PRAYER_ACTIVATE_ULTIMATE_STRENGTH = 2691;
	public final static int PRAYER_ACTIVATE_INCREDIBLE_REFLEXES = 2667;
	public final static int PRAYER_ACTIVATE_PROTECT_FROM_MAGIC = 2675;
	public final static int PRAYER_ACTIVATE_PROTECT_FROM_MISSILES = 2677;
	public final static int PRAYER_ACTIVATE_PROTECT_FROM_MELEE = 2676;
	public final static int PRAYER_ACTIVATE_EAGLE_EYE = 2665;
	public final static int PRAYER_ACTIVATE_MYSTIC_MIGHT = 2669;
	public final static int PRAYER_ACTIVATE_RETRIBUTION = 2682;
	public final static int PRAYER_ACTIVATE_REDEMPTION = 2680;
	public final static int PRAYER_ACTIVATE_SMITE = 2686;
	public final static int PRAYER_ACTIVATE_CHIVALRY = 3826;
	public final static int PRAYER_ACTIVATE_PIETY = 3825;
	public final static int PRAYER_DEACTIVE_VWOOP = 2663;
	public final static int PRAYER_DEPLETE_TWINKLE = 2672;

	public final static int TOWN_CRIER_BELL_DING = 3813;
	public final static int TOWN_CRIER_BELL_DONG = 3817;
	public final static int TOWN_CRIER_SHOUT_SQUEAK = 3816;

}
