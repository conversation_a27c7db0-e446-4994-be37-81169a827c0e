import net.runelite.mapping.Implements;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("kq")
@Implements("MusicPatchNode2")
public class MusicPatchNode2 {
   @ObfuscatedName("bo")
   @ObfuscatedGetter(
      intValue = 199509955
   )
   static int field3356;
   @ObfuscatedName("f")
   byte[] field3353;
   @ObfuscatedName("w")
   byte[] field3351;
   @ObfuscatedName("v")
   @ObfuscatedGetter(
      intValue = 57176299
   )
   int field3345;
   @ObfuscatedName("s")
   @ObfuscatedGetter(
      intValue = 836275101
   )
   int field3346;
   @ObfuscatedName("z")
   @ObfuscatedGetter(
      intValue = -9164575
   )
   int field3347;
   @ObfuscatedName("j")
   @ObfuscatedGetter(
      intValue = -1018793487
   )
   int field3352;
   @ObfuscatedName("i")
   @ObfuscatedGetter(
      intValue = 963851919
   )
   int field3349;
   @ObfuscatedName("n")
   @ObfuscatedGetter(
      intValue = 46834767
   )
   int field3354;
   @ObfuscatedName("l")
   @ObfuscatedGetter(
      intValue = 928751949
   )
   int field3350;

   MusicPatchNode2() {
   }
}
