.id                 223
.int_stack_count    0
.string_stack_count 0
.int_var_count      5
.string_var_count   3
   sconst                 "<col=0000ff>"
   sstore                 0
   iconst                 0
   istore                 0
   iconst                 6250335
   istore                 1
   invoke                 921
   iconst                 1
   if_icmpeq              LABEL10
   jump                   LABEL20
LABEL10:
   sconst                 "<col=9090ff>"
   iconst                 16777215
   iconst                 12566463
   istore                 1
   istore                 0
   sstore                 0
   iconst                 1
   iconst                 10616887
   if_settextshadow      
   jump                   LABEL23
LABEL20:
   iconst                 0
   iconst                 10616887
   if_settextshadow      
LABEL23:
   iload                  0
   iconst                 10616887
   if_setcolour          
   get_varc_string        335
   string_length         
   istore                 2
   get_varc_string        335
   escape                
   sstore                 1
   sconst                 ""
   sstore                 2
   iconst                 0
   istore                 3
   invoke                 1972
   istore                 4
   get_varbit             8119
   iconst                 1
   if_icmpeq              LABEL42
   jump                   LABEL210
LABEL42:
   invoke                 3160
   iconst                 1
   if_icmpeq              LABEL46
   jump                   LABEL51
LABEL46:
   sconst                 "<img=22>"
   chat_playername       
   join_string            2
   sstore                 2
   jump                   LABEL68
LABEL51:
   invoke                 5849
   iconst                 1
   if_icmpeq              LABEL55
   jump                   LABEL60
LABEL55:
   sconst                 "<img=52>"
   chat_playername       
   join_string            2
   sstore                 2
   jump                   LABEL68
LABEL60:
   chat_playername       
   sstore                 2
LABEL68:
   get_varc_int           945
   switch                
      1: LABEL71
      2: LABEL76
      3: LABEL81
      4: LABEL86
   jump                   LABEL97
LABEL71:
   sload                  2
   sconst                 " (channel)"
   append                
   sstore                 2
   jump                   LABEL97
LABEL76:
   sload                  2
   sconst                 " (clan)"
   append                
   sstore                 2
   jump                   LABEL97
LABEL81:
   sload                  2
   sconst                 " (guest clan)"
   append                
   sstore                 2
   jump                   LABEL97
LABEL86:
   invoke                 5262
   iconst                 1
   if_icmpeq              LABEL90
   jump                   LABEL95
LABEL90:
   sload                  2
   sconst                 " (group)"
   append                
   sstore                 2
   jump                   LABEL97
LABEL95:
   iconst                 0
   set_varc_int           945
LABEL97:
   iload                  4
   iconst                 1
   if_icmpeq              LABEL101
   jump                   LABEL105
LABEL101:
   sload                  2
   sconst                 "<img=19>"
   append                
   sstore                 2
LABEL105:
   sload                  2
   sconst                 ": "
   sload                  0
   sload                  1
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   get_varc_string        335
   invoke                 1353
   iconst                 1
   if_icmpeq              LABEL118
   jump                   LABEL130
LABEL118:
   iload                  2
   iconst                 79
   if_icmplt              LABEL122
   jump                   LABEL129
LABEL122:
   sload                  2
   sload                  0
   sconst                 "*"
   sconst                 "</col>"
   join_string            3
   append                
   sstore                 2
LABEL129:
   jump                   LABEL141
LABEL130:
   iload                  2
   iconst                 80
   if_icmplt              LABEL134
   jump                   LABEL141
LABEL134:
   sload                  2
   sload                  0
   sconst                 "*"
   sconst                 "</col>"
   join_string            3
   append                
   sstore                 2
LABEL141:
   sload                  2
   iconst                 2147483647
   iconst                 495
   parawidth             
   istore                 3
   iload                  4
   iconst                 1
   if_icmpeq              LABEL150
   jump                   LABEL167
LABEL150:
   iconst                 60
   iconst                 5
   iload                  3
   add                   
   invoke                 1045
   iconst                 30
   iconst                 0
   iconst                 0
   iconst                 10616868
   if_setsize            
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 2
   iconst                 10616868
   if_setposition        
   jump                   LABEL179
LABEL167:
   iconst                 0
   iconst                 30
   iconst                 0
   iconst                 1
   iconst                 10616868
   if_setsize            
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 2
   iconst                 10616868
   if_setposition        
LABEL179:
   iload                  3
   iconst                 10616887
   if_getwidth           
   if_icmpgt              LABEL184
   jump                   LABEL190
LABEL184:
   iconst                 2
   iconst                 2
   iconst                 0
   iconst                 10616887
   if_settextalign       
   jump                   LABEL195
LABEL190:
   iconst                 0
   iconst                 2
   iconst                 0
   iconst                 10616887
   if_settextalign       
LABEL195:
   iconst                 10616887
   if_clearops           
   iconst                 -1
   sconst                 ""
   iconst                 10616887
   if_setonmouserepeat   
   iconst                 -1
   sconst                 ""
   iconst                 10616887
   if_setonmouseleave    
   iconst                 -1
   sconst                 ""
   iconst                 10616887
   if_setonop            
   jump                   LABEL269
LABEL210:
   invoke                 3160
   iconst                 1
   if_icmpeq              LABEL214
   jump                   LABEL219
LABEL214:
   sconst                 "<img=22>"
   sconst                 " You must set a name before you can chat."
   join_string            2
   sstore                 2
   jump                   LABEL236
LABEL219:
   invoke                 5849
   iconst                 1
   if_icmpeq              LABEL223
   jump                   LABEL228
LABEL223:
   sconst                 "<img=52>"
   sconst                 " You must set a name before you can chat."
   join_string            2
   sstore                 2
   jump                   LABEL236
LABEL228:           
   sconst                 " You must set a name before you can chat."
   sstore                 2
LABEL236:
   iconst                 1
   iconst                 2
   iconst                 0
   iconst                 10616887
   if_settextalign       
   iconst                 10
   sconst                 "Configure"
   iconst                 10616887
   if_setop              
   sconst                 "<col=ff9040>"
   sconst                 "Display name"
   sconst                 "</col>"
   join_string            3
   iconst                 10616887
   if_setopbase          
   iconst                 45
   iconst                 -2147483645
   iload                  1
   sconst                 "Ii"
   iconst                 10616887
   if_setonmouserepeat   
   iconst                 45
   iconst                 -2147483645
   iload                  0
   sconst                 "Ii"
   iconst                 10616887
   if_setonmouseleave    
   iconst                 489
   iconst                 -2147483644
   iconst                 1024
   sconst                 "ii"
   iconst                 10616887
   if_setonop            
LABEL269:
   sload                  2
   iconst                 10616887
   if_settext            
   sconst                 "setChatboxInput"
   runelite_callback     
   iconst                 3
   iconst                 16
   iconst                 1
   iconst                 0
   iconst                 10616887
   if_setsize            
   return                
