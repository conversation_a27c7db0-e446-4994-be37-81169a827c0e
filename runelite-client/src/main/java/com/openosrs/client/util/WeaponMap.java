package com.openosrs.client.util;

import java.util.HashMap;
import net.runelite.api.ItemID;

public class WeaponMap
{
	public static HashMap<Integer, WeaponStyle> StyleMap = new HashMap<>();

	static
	{
		//Melee
		StyleMap.put(ItemID._3RD_AGE_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID._3RD_AGE_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID._3RD_AGE_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_BLUDGEON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_DAGGER_P, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_DAGGER_P_13269, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_DAGGER_P_13271, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_TENTACLE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_TENTACLE_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_WHIP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_WHIP_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ABYSSAL_WHIP_20405, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_DAGGERP_5676, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_DAGGERP_5694, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SPEARP_5712, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SPEARP_5726, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ADAMANT_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ALE_OF_THE_GODS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANCIENT_GODSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANCIENT_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANGER_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANGER_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANGER_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ANGER_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.AMYS_SAW, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARCEUUS_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARCLIGHT, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARMADYL_GODSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARMADYL_GODSWORD_20593, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARMADYL_GODSWORD_22665, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ARMADYL_GODSWORD_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ASSORTED_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BANDOS_GODSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BANDOS_GODSWORD_20782, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BANDOS_GODSWORD_21060, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BANDOS_GODSWORD_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BARBTAIL_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BARRELCHEST_ANCHOR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BEACH_BOXING_GLOVES, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BEACH_BOXING_GLOVES_11706, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BIRTHDAY_BALLOONS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BIRTHDAY_CAKE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_DAGGERP_5682, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_DAGGERP_5700, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SALAMANDER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SPEARP_5734, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SPEARP_5736, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLACK_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25870, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25872, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25874, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25876, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25878, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25880, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLADE_OF_SAELDOR_C_25882, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLESSED_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLISTERWOOD_SICKLE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLUE_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLUE_FLOWERS_8936, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BLURITE_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_CLUB, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_DAGGER_P, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_DAGGER_P_8876, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_DAGGER_P_8878, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BONE_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BOXING_GLOVES, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BOXING_GLOVES_7673, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRINE_SABRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_DAGGERP_5670, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_DAGGERP_5688, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SPEARP_5704, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SPEARP_5718, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRONZE_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BRUMA_TORCH, WeaponStyle.MELEE);
		StyleMap.put(ItemID.BUTTERFLY_NET, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CANDY_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CATTLEPROD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CHAOTIC_HANDEGG, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CLEAVER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_HALBERD_ATTUNED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_HALBERD_BASIC, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_HALBERD_PERFECTED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CORRUPTED_SCEPTRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRIER_BELL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_AXE_23862, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_AXE_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_110, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_110_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_210, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_210_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_24125, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_310, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_310_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_410, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_410_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_510, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_510_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_610, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_610_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_710, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_710_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_810, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_810_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_910, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_910_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_ATTUNED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_BASIC, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_FULL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_FULL_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HALBERD_PERFECTED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HARPOON_23864, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_HARPOON_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_PICKAXE_23863, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_PICKAXE_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CRYSTAL_SCEPTRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.CURSED_GOBLIN_HAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DARKLIGHT, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DARK_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DECORATIVE_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DECORATIVE_SWORD_4503, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DECORATIVE_SWORD_4508, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_0, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_100, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_25, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_25516, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_50, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DHAROKS_GREATAXE_75, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DINHS_BULWARK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_2H_SWORD_20559, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_AXE_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_CLAWS_20784, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_DAGGER_20407, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_DAGGERP_5680, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_DAGGERP_5698, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HARPOON_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HASTAKP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HASTAP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HASTAP_22737, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HASTAP_22740, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_HUNTER_LANCE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_PICKAXE_12797, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_PICKAXE_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_PICKAXE_OR_25376, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SCIMITAR_20406, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SCIMITAR_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SPEARP_5716, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SPEARP_5730, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.DRAGON_WARHAMMER_20785, WeaponStyle.MELEE);
		StyleMap.put(ItemID.EASTER_BASKET, WeaponStyle.MELEE);
		StyleMap.put(ItemID.EGG_WHISK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ELDER_MAUL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ELDER_MAUL_21205, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE1, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE2, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE3, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE4, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ENCHANTED_LYRE5, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GOBLIN_PAINT_CANNON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.EXCALIBUR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.EXCALIBUR_8280, WeaponStyle.MELEE);
		StyleMap.put(ItemID.FLAMTAER_HAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.FREMENNIK_BLADE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.FROZEN_ABYSSAL_WHIP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GADDERHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GHRAZI_RAPIER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GHRAZI_RAPIER_23628, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GILDED_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GILDED_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GILDED_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GILDED_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GLOWING_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GOLDEN_TENCH, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_HAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_MAUL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_MAUL_12848, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_MAUL_20557, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_MAUL_24225, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GRANITE_MAUL_24227, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GREEN_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR_0, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR_100, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR_25, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR_50, WeaponStyle.MELEE);
		StyleMap.put(ItemID.GUTHANS_WARSPEAR_75, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HARRYS_CUTLASS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HAM_JOINT, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HAND_FAN, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HILL_GIANT_CLUB, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HOLY_HANDEGG, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HOLY_GHRAZI_RAPIER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HOLY_SCYTHE_OF_VITUR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HOLY_SCYTHE_OF_VITUR_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.HOSIDIUS_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_AXE_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_AXE_UNCHARGED_25371, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_HARPOON_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_HARPOON_UNCHARGED_25367, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_PICKAXE_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INFERNAL_PICKAXE_UNCHARGED_25369, WeaponStyle.MELEE);
		StyleMap.put(ItemID.INQUISITORS_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_DAGGERP_5668, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_DAGGERP_5686, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SPEARP_5706, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SPEARP_5720, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.IRON_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.JADE_MACHETE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.KATANA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.KITCHEN_KNIFE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.KERIS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LARGE_SPADE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LEAFBLADED_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LEAFBLADED_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LEAFBLADED_SPEAR_4159, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LEAFBLADED_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LOVAKENGJ_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LUCKY_CUTLASS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.LYRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MACHETE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAGIC_BUTTERFLY_NET, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAGIC_SECATEURS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAGIC_SECATEURS_NZ, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAPLE_BLACKJACK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAPLE_BLACKJACKD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MAPLE_BLACKJACKO, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MEAT_TENDERISER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MERFOLK_TRIDENT, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_DAGGERP_5674, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_DAGGERP_5692, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SPEARP_5710, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SPEARP_5724, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MITHRIL_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MIXED_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.MOUSE_TOY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_I, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_16893, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_I_16892, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NOOSE_WAND, WeaponStyle.MELEE);
		StyleMap.put(ItemID.NUNCHAKU, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OAK_BLACKJACK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OAK_BLACKJACKD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OAK_BLACKJACKO, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OILY_FISHING_ROD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OILY_PEARL_FISHING_ROD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.OPAL_MACHETE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ORANGE_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ORANGE_SALAMANDER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.PEACEFUL_HANDEGG, WeaponStyle.MELEE);
		StyleMap.put(ItemID.PET_ROCK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.PISCARILIUS_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.PROP_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.PURPLE_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAPIER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6774, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6775, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6776, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6777, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6778, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RAT_POLE_6779, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RED_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RED_FLOWERS_8938, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RED_SALAMANDER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RED_TOPAZ_MACHETE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ROCK_HAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ROYAL_SCEPTRE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUBBER_CHICKEN, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUBBER_CHICKEN_22666, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_BATTLEAXE_20552, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_DAGGERP_5678, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_DAGGERP_5696, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR_20402, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR_23330, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR_23332, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR_23334, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SCIMITAR_26262, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SPEARP_5714, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SPEARP_5728, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.RUNE_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SANGUINE_SCYTHE_OF_VITUR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SANGUINE_SCYTHE_OF_VITUR_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARADOMINS_BLESSED_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARADOMIN_GODSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARADOMIN_GODSWORD_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARADOMIN_MJOLNIR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARADOMIN_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SARAS_BLESSED_SWORD_FULL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SCYTHE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SCYTHE_OF_VITUR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SCYTHE_OF_VITUR_22664, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SCYTHE_OF_VITUR_UNCHARGED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SECRET_SANTA_PRESENT_RED, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SECRET_SANTA_PRESENT_BLUE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SECRET_SANTA_PRESENT_GREEN, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SECRET_SANTA_PRESENT_BLACK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SECRET_SANTA_PRESENT_GOLD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SEVERED_LEG_24792, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SHADOW_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SHAYZIEN_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SILVERLIGHT, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SILVERLIGHT_6745, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SILVERLIGHT_8279, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SILVER_SICKLE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SILVER_SICKLE_B, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SNOWBALL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STALE_BAGUETTE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STATIUSS_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STATIUSS_WARHAMMER_23620, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_DAGGERP_5672, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_DAGGERP_5690, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SPEARP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SPEARP_5708, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SPEARP_5722, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STEEL_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.STONE_BOWL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SWAMP_LIZARD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.SWIFT_BLADE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TOKTZXILAK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TOKTZXILAK_20554, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TOKTZXILEK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS_0, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS_100, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS_25, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS_50, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TORAGS_HAMMERS_75, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAINING_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_ADAMANT_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_AXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_BRONZE_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_CANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_DRAGON_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_HARPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_IRON_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_MITHRIL_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_PICKAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_RUNE_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TRAILBLAZER_STEEL_TROPHY, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TROLLWEISS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TWISTED_BANNER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TZHAARKETEM, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TZHAARKETOM, WeaponStyle.MELEE);
		StyleMap.put(ItemID.TZHAARKETOM_T, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL_0, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL_100, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL_25, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL_50, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VERACS_FLAIL_75, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VESTAS_BLIGHTED_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VESTAS_LONGSWORD_INACTIVE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VESTAS_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VESTAS_LONGSWORD_23615, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VESTAS_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VIGGORAS_CHAINMACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VIGGORAS_CHAINMACE_U, WeaponStyle.MELEE);
		StyleMap.put(ItemID.VOLCANIC_ABYSSAL_WHIP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WESTERN_BANNER_1, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WESTERN_BANNER_2, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WESTERN_BANNER_3, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WESTERN_BANNER_4, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_2H_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_BATTLEAXE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_CLAWS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_DAGGER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_DAGGERP, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_DAGGERP_6595, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_DAGGERP_6597, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_HALBERD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_LONGSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_MACE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_SCIMITAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WHITE_WARHAMMER, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILDERNESS_SWORD_1, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILDERNESS_SWORD_2, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILDERNESS_SWORD_3, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILDERNESS_SWORD_4, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILLOW_BLACKJACK, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILLOW_BLACKJACKD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WILLOW_BLACKJACKO, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WOLFBANE, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WOODEN_SPOON, WeaponStyle.MELEE);
		StyleMap.put(ItemID.WOODEN_SWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.YELLOW_FLOWERS, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ZAMORAKIAN_HASTA, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ZAMORAKIAN_SPEAR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ZAMORAK_GODSWORD, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ZAMORAK_GODSWORD_OR, WeaponStyle.MELEE);
		StyleMap.put(ItemID.ZOMBIE_HEAD, WeaponStyle.MELEE);

		//Ranged
		StyleMap.put(ItemID._3RD_AGE_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_DARTP_5633, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_DARTP_5640, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_KNIFEP_5659, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_KNIFEP_5666, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ADAMANT_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.AMETHYST_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.AMETHYST_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.AMETHYST_DARTP_25855, WeaponStyle.RANGE);
		StyleMap.put(ItemID.AMETHYST_DARTP_25857, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ARMADYL_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ARMADYL_CROSSBOW_23611, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_CHINCHOMPA, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_DARTP_5631, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_DARTP_5638, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_KNIFEP_5658, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLACK_KNIFEP_5665, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BLURITE_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_DARTP_5628, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_DARTP_5635, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_KNIFEP_5654, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_KNIFEP_5661, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BRONZE_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_INACTIVE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25869, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25884, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25886, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25888, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25890, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25892, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25894, WeaponStyle.RANGE);
		StyleMap.put(ItemID.BOW_OF_FAERDHINEN_C_25896, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CHINCHOMPA, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CHINCHOMPA_10033, WeaponStyle.RANGE);
		StyleMap.put(ItemID.COMP_OGRE_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CORRUPTED_BOW_ATTUNED, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CORRUPTED_BOW_BASIC, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CORRUPTED_BOW_PERFECTED, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRAWS_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRAWS_BOW_U, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_110, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_110_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_210, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_210_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_310, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_310_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_410, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_410_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_510, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_510_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_610, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_610_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_710, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_710_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_810, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_810_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_910, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_910_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_24123, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_ATTUNED, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_BASIC, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_FULL, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_FULL_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_INACTIVE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CRYSTAL_BOW_PERFECTED, WeaponStyle.RANGE);
		StyleMap.put(ItemID.CURSED_GOBLIN_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW_12765, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW_12766, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW_12767, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW_12768, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DARK_BOW_20408, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DORGESHUUN_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_DARTP_11233, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_DARTP_11234, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_HUNTER_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_HUNTER_CROSSBOW_T, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_HUNTER_CROSSBOW_B, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_KNIFEP_22808, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_KNIFEP_22810, WeaponStyle.RANGE);
		StyleMap.put(ItemID.DRAGON_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.HEAVY_BALLISTA, WeaponStyle.RANGE);
		StyleMap.put(ItemID.HEAVY_BALLISTA_23630, WeaponStyle.RANGE);
		StyleMap.put(ItemID.HOLY_WATER, WeaponStyle.RANGE);
		StyleMap.put(ItemID.HUNTERS_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_DART_P, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_DARTP_5636, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_KNIFEP_5655, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_KNIFEP_5662, WeaponStyle.RANGE);
		StyleMap.put(ItemID.IRON_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW_0, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW_100, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW_25, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW_50, WeaponStyle.RANGE);
		StyleMap.put(ItemID.KARILS_CROSSBOW_75, WeaponStyle.RANGE);
		StyleMap.put(ItemID.LIGHT_BALLISTA, WeaponStyle.RANGE);
		StyleMap.put(ItemID.LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAGIC_COMP_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAGIC_LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAGIC_SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAGIC_SHORTBOW_20558, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAGIC_SHORTBOW_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAPLE_LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MAPLE_SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_DARTP_5632, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_DARTP_5639, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_KNIFEP_5657, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_KNIFEP_5664, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MITHRIL_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MONKEY_TALISMAN, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MORRIGANS_JAVELIN, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MORRIGANS_JAVELIN_23619, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MORRIGANS_THROWING_AXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.MUD_PIE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.NEW_CRYSTAL_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.NEW_CRYSTAL_BOW_4213, WeaponStyle.RANGE);
		StyleMap.put(ItemID.NEW_CRYSTAL_BOW_16888, WeaponStyle.RANGE);
		StyleMap.put(ItemID.NEW_CRYSTAL_BOW_I, WeaponStyle.RANGE);
		StyleMap.put(ItemID.NEW_CRYSTAL_BOW_I_16889, WeaponStyle.RANGE);
		StyleMap.put(ItemID.OAK_LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.OAK_SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.OGRE_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.PHOENIX_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RED_CHINCHOMPA, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RED_CHINCHOMPA_10034, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_CROSSBOW_OR, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_CROSSBOW_23601, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_DARTP_5634, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_DARTP_5641, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_KNIFEP_5660, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_KNIFEP_5667, WeaponStyle.RANGE);
		StyleMap.put(ItemID.RUNE_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.SEERCULL, WeaponStyle.RANGE);
		StyleMap.put(ItemID.SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.SIGNED_OAK_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STARTER_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_CROSSBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_DART, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_DARTP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_DARTP_5630, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_DARTP_5637, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_KNIFE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_KNIFEP, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_KNIFEP_5656, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_KNIFEP_5663, WeaponStyle.RANGE);
		StyleMap.put(ItemID.STEEL_THROWNAXE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.TOKTZXILUL, WeaponStyle.RANGE);
		StyleMap.put(ItemID.TOXIC_BLOWPIPE, WeaponStyle.RANGE);
		StyleMap.put(ItemID.TOXIC_BLOWPIPE_EMPTY, WeaponStyle.RANGE);
		StyleMap.put(ItemID.TRAINING_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.TWISTED_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.WILLOW_COMP_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.WILLOW_LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.WILLOW_SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.YEW_COMP_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.YEW_LONGBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.YEW_SHORTBOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ZARYTE_BOW, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ZARYTE_BOW_26239, WeaponStyle.RANGE);
		StyleMap.put(ItemID.ZARYTE_CROSSBOW, WeaponStyle.RANGE);

		//Magic
		StyleMap.put(ItemID._3RD_AGE_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_0, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_100, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_25, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_50, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_75, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AHRIMS_STAFF_23653, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.AIR_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ANCIENT_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ANCIENT_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.APPRENTICE_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ARMADYL_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BANDOS_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BEGINNER_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BLISTERWOOD_FLAIL, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BROKEN_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BRYOPHYTAS_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.BRYOPHYTAS_STAFF_UNCHARGED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CORRUPTED_STAFF_ATTUNED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CORRUPTED_STAFF_BASIC, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CORRUPTED_STAFF_PERFECTED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CRYSTAL_STAFF_ATTUNED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CRYSTAL_STAFF_BASIC, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CRYSTAL_STAFF_PERFECTED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.CURSED_GOBLIN_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.DAWNBRINGER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.DRAMEN_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.DUST_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.EARTH_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ELDRITCH_NIGHTMARE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.FIRE_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.GUTHIX_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.GUTHIX_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.HARMONISED_NIGHTMARE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.HOLY_SANGUINESTI_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.HOLY_SANGUINESTI_STAFF_UNCHARGED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.IBANS_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.IBANS_STAFF_1410, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.IBANS_STAFF_U, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.IVANDIS_FLAIL, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.KODAI_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.KODAI_WAND_23626, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.LAVA_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.LAVA_BATTLESTAFF_21198, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.LUNAR_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MAGIC_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MASTER_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MASTER_WAND_20560, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MIST_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MUD_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_AIR_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_DUST_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_EARTH_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_FIRE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_LAVA_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_LAVA_STAFF_21200, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_MIST_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_MUD_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_SMOKE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_STEAM_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_STEAM_STAFF_12796, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.MYSTIC_WATER_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.NIGHTMARE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9045, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9046, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9047, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9048, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9049, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9050, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_9051, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_13074, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_13075, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_13077, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_13078, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_16176, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_21445, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_21446, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_26948, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.PHARAOHS_SCEPTRE_26950, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_1, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_10, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_2, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_3, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_4, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_5, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_6, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_7, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_8, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ROD_OF_IVANDIS_9, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SANGUINESTI_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SANGUINESTI_STAFF_UNCHARGED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SARADOMIN_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SARADOMIN_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SKULL_SCEPTRE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SKULL_SCEPTRE_I, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SLAYERS_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SLAYERS_STAFF_E, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.SMOKE_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_AIR, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_BALANCE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_BOB_THE_CAT, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_EARTH, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_FIRE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_LIGHT, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_THE_DEAD, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_THE_DEAD_23613, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STAFF_OF_WATER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STARTER_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STEAM_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.STEAM_BATTLESTAFF_12795, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TEACHER_WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.THAMMARONS_SCEPTRE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.THAMMARONS_SCEPTRE_U, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TOKTZMEJTAL, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TOXIC_STAFF_OF_THE_DEAD, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TOXIC_STAFF_UNCHARGED, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TRIDENT_OF_THE_SEAS, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TRIDENT_OF_THE_SEAS_E, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TRIDENT_OF_THE_SEAS_FULL, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TRIDENT_OF_THE_SWAMP, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.TRIDENT_OF_THE_SWAMP_E, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.UNCHARGED_TOXIC_TRIDENT, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.UNCHARGED_TOXIC_TRIDENT_E, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.UNCHARGED_TRIDENT, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.UNCHARGED_TRIDENT_E, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.VOID_KNIGHT_MACE, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.VOID_KNIGHT_MACE_BROKEN, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.VOLATILE_NIGHTMARE_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.VOLATILE_NIGHTMARE_STAFF_25517, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.WAND, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.WATER_BATTLESTAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.WHITE_MAGIC_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ZAMORAK_CROZIER, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ZAMORAK_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ZURIELS_STAFF, WeaponStyle.MAGIC);
		StyleMap.put(ItemID.ZURIELS_STAFF_23617, WeaponStyle.MAGIC);
		//what the fuck...
		StyleMap.put(ItemID.GNOMEBALL, WeaponStyle.MAGIC);
	}
}
