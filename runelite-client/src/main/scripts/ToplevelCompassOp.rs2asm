.id                 1050
.int_stack_count    1
.string_stack_count 0
.int_var_count      1
.string_var_count   0
   iload                  0
   iconst                 4
   if_icmpgt              LABEL10
   get_varbit             542
   iconst                 1
   if_icmpeq              LABEL10
   get_varbit             4606
   iconst                 0
   if_icmpne              LABEL10
   jump                   LABEL11
LABEL10:
   return                
LABEL11:
   iconst                 2266
   iconst                 1
   iconst                 0
   sound_synth           
   iload                  0
   switch                
      1: LABEL18
      2: LABEL25
      3: LABEL32
      4: LABEL39
   jump                   LABEL45
LABEL18:
   iconst                 225
   iconst                 5
   randominc             
   add                   
   sconst                 "lookPreservePitch"
   runelite_callback     
   iconst                 0
   cam_forceangle        
   jump                   LABEL45
LABEL25:
   iconst                 225
   iconst                 5
   randominc             
   add                   
   sconst                 "lookPreservePitch"
   runelite_callback     
   iconst                 1536
   cam_forceangle        
   jump                   LABEL45
LABEL32:
   iconst                 225
   iconst                 5
   randominc             
   add                   
   sconst                 "lookPreservePitch"
   runelite_callback     
   iconst                 1024
   cam_forceangle        
   jump                   LABEL45
LABEL39:
   iconst                 225
   iconst                 5
   randominc             
   add                   
   sconst                 "lookPreservePitch"
   runelite_callback     
   iconst                 512
   cam_forceangle        
LABEL45:
   return                
