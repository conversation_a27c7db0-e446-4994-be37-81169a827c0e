import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("rr")
public class class469 {
   @ObfuscatedName("v")
   @ObfuscatedGetter(
      intValue = 675682833
   )
   static final int field4922 = (int)(Math.pow(2.0, 4.0) - 1.0);
   @ObfuscatedName("s")
   @ObfuscatedGetter(
      intValue = 202722443
   )
   static final int field4921 = (int)(Math.pow(2.0, 8.0) - 1.0);
}
