/*
 * Copyright (c) 2016-2017, <PERSON> <<PERSON><PERSON>@tuta.io>
 * Copyright (c) 2017, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON>EQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.bosstimer;

import com.google.common.collect.ImmutableMap;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.Map;
import net.runelite.api.ItemID;
import net.runelite.api.NpcID;
import net.runelite.client.util.RSTimeUnit;

enum Boss
{
	ALCHEMICAL_HYDRA(NpcID.ALCHEMICAL_HYDRA_8622, 43, RSTimeUnit.GAME_TICKS, ItemID.IKKLE_HYDRA),
	ARTIO(NpcID.ARTIO, 27, RSTimeUnit.GAME_TICKS, ItemID.CALLISTO_CUB),
	CALVARION(NpcID.CALVARION_11994, 31, RSTimeUnit.GAME_TICKS, ItemID.VETION_JR),
	CALLISTO(NpcID.CALLISTO_6609, 30, RSTimeUnit.GAME_TICKS, ItemID.CALLISTO_CUB),
	CERBERUS(NpcID.CERBERUS, 15, RSTimeUnit.GAME_TICKS, ItemID.HELLPUPPY),
	CHAOS_ELEMENTAL(NpcID.CHAOS_ELEMENTAL, 51, RSTimeUnit.GAME_TICKS, ItemID.PET_CHAOS_ELEMENTAL),
	CHAOS_FANATIC(NpcID.CHAOS_FANATIC, 51, RSTimeUnit.GAME_TICKS, ItemID.ANCIENT_STAFF),
	COMMANDER_ZILYANA(NpcID.COMMANDER_ZILYANA, 34, RSTimeUnit.GAME_TICKS, ItemID.PET_ZILYANA),
	CORPOREAL_BEAST(NpcID.CORPOREAL_BEAST, 51, RSTimeUnit.GAME_TICKS, ItemID.PET_DARK_CORE),
	CRAZY_ARCHAEOLOGIST(NpcID.CRAZY_ARCHAEOLOGIST, 51, RSTimeUnit.GAME_TICKS, ItemID.FEDORA),
	DAGANNOTH_PRIME(NpcID.DAGANNOTH_PRIME, 131, RSTimeUnit.GAME_TICKS, ItemID.PET_DAGANNOTH_PRIME),
	DAGANNOTH_REX(NpcID.DAGANNOTH_REX, 131, RSTimeUnit.GAME_TICKS, ItemID.PET_DAGANNOTH_REX),
	DAGANNOTH_SUPREME(NpcID.DAGANNOTH_SUPREME, 131, RSTimeUnit.GAME_TICKS, ItemID.PET_DAGANNOTH_SUPREME),
	DERANGED_ARCHAEOLOGIST(NpcID.DERANGED_ARCHAEOLOGIST, 51, RSTimeUnit.GAME_TICKS, ItemID.UNIDENTIFIED_LARGE_FOSSIL),
	//DUSK(NpcID.DUSK_7889, 5, ChronoUnit.MINUTES, ItemID.NOON), //Removed because they don't respawn?
	GENERAL_GRAARDOR(NpcID.GENERAL_GRAARDOR, 34, RSTimeUnit.GAME_TICKS, ItemID.PET_GENERAL_GRAARDOR),
	GIANT_MOLE(NpcID.GIANT_MOLE, 16, RSTimeUnit.GAME_TICKS, ItemID.BABY_MOLE),
	KALPHITE_QUEEN(NpcID.KALPHITE_QUEEN_965, 51, RSTimeUnit.GAME_TICKS, ItemID.KALPHITE_PRINCESS),
	KING_BLACK_DRAGON(NpcID.KING_BLACK_DRAGON, 16, RSTimeUnit.GAME_TICKS, ItemID.PRINCE_BLACK_DRAGON),
	KRAKEN(NpcID.KRAKEN, 15, RSTimeUnit.GAME_TICKS, ItemID.PET_KRAKEN),
	KREEARRA(NpcID.KREEARRA, 34, RSTimeUnit.GAME_TICKS, ItemID.PET_KREEARRA),
	KRIL_TSUTSAROTH(NpcID.KRIL_TSUTSAROTH, 34, RSTimeUnit.GAME_TICKS, ItemID.PET_KRIL_TSUTSAROTH),
	LEVIATHAN(12214, 34, RSTimeUnit.GAME_TICKS, 28252),
	NEX(NpcID.NEX, 31, RSTimeUnit.GAME_TICKS, ItemID.NEXLING),
	PHANTOM_MUSPAH(NpcID.PHANTOM_MUSPAH_12080, 51, RSTimeUnit.GAME_TICKS, ItemID.MUPHIN),
	SARACHNIS(NpcID.SARACHNIS, 31, RSTimeUnit.GAME_TICKS, ItemID.SRARACHA),
	SCORPIA(NpcID.SCORPIA, 26, RSTimeUnit.GAME_TICKS, ItemID.SCORPIAS_OFFSPRING),
	SPINDEL(NpcID.SPINDEL, 26, RSTimeUnit.GAME_TICKS, ItemID.VENENATIS_SPIDERLING),
	THERMONUCLEAR_SMOKE_DEVIL(NpcID.THERMONUCLEAR_SMOKE_DEVIL, 15, RSTimeUnit.GAME_TICKS, ItemID.PET_SMOKE_DEVIL),
	VARDORVIS(12223, 34, RSTimeUnit.GAME_TICKS, 28248),
	VENENATIS(NpcID.VENENATIS_6610, 29, RSTimeUnit.GAME_TICKS, ItemID.VENENATIS_SPIDERLING),
	VETION(NpcID.VETION_6612, 34, RSTimeUnit.GAME_TICKS, ItemID.VETION_JR),
	ZALCANO(NpcID.ZALCANO_9050, 61, RSTimeUnit.GAME_TICKS, ItemID.SMOLCANO) //default 60 ticks

	;

	private static final Map<Integer, Boss> bosses;

	private final int id;
	private final Duration spawnTime;
	private final int itemSpriteId;

	static
	{
		ImmutableMap.Builder<Integer, Boss> builder = new ImmutableMap.Builder<>();

		for (Boss boss : values())
		{
			builder.put(boss.getId(), boss);
		}

		bosses = builder.build();
	}

	Boss(int id, long period, TemporalUnit unit, int itemSpriteId)
	{
		this.id = id;
		this.spawnTime = Duration.of(period, unit);
		this.itemSpriteId = itemSpriteId;
	}

	public int getId()
	{
		return id;
	}

	public Duration getSpawnTime()
	{
		return spawnTime;
	}

	public int getItemSpriteId()
	{
		return itemSpriteId;
	}

	public static Boss find(int id)
	{
		return bosses.get(id);
	}
}
