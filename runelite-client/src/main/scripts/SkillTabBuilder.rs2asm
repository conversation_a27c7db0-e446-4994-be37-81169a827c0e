.id                 395
.int_stack_count    3
.string_stack_count 1
.int_var_count      11
.string_var_count   4
   iconst                 83
   iconst                 49
   iconst                 1497
   iload                  0
   enum                  
   istore                 3
   iload                  0
   stat                  
   tostring              
   cc_settext            
   iload                  0
   stat_base             
   istore                 4
   iload                  0                   ; load the skill id from arguments
   iload                  4                   ; load the current real skill level
   sconst                 "skillTabBaseLevel" ; push event name
   runelite_callback     ; invoke callback
   istore                 4                   ; store the (possibly) edited real skill level
   pop_int                                    ; pop the skill id we pushed
   iload                  4
   tostring              
   cc_settext             1
   iload                  0
   stat_xp               
   istore                 5
   sconst                 ","
   sstore                 1
   sload                  0
   sconst                 " XP:"
   join_string            2
   sstore                 2
   iload                  5
   sload                  1
   invoke                 46
   sstore                 3
   iconst                 0
   istore                 6
   get_varbit             4181
   iconst                 0
   if_icmpeq              LABEL35
   jump                   LABEL66
LABEL35:
   iload                  4
   iconst                 99
   sconst                 "skillTabMaxLevel"   ; push event name
   runelite_callback     ; invoke callback
   if_icmplt              LABEL39
   jump                   LABEL65
LABEL39:
   iconst                 105
   iconst                 105
   iconst                 256
   iload                  4
   iconst                 1
   add                   
   enum                  
   istore                 6
   sload                  2
   sconst                 "|Next level at:|Remaining XP:"
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   iload                  6
   sload                  1
   invoke                 46
   sconst                 "|"
   iload                  6
   iload                  5
   sub                   
   sload                  1
   invoke                 46
   join_string            4
   append                
   sstore                 3
LABEL65:
   jump                   LABEL84
LABEL66:
   sload                  2
   sconst                 "|Next level at:"
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   iconst                 105
   iconst                 105
   iconst                 256
   iload                  4
   iconst                 1
   add                   
   enum                  
   sload                  1
   invoke                 46
   join_string            2
   append                
   sstore                 3
LABEL84:
   iconst                 0
   istore                 7
   iconst                 0
   istore                 8
   iconst                 0
   istore                 9
   iconst                 0
   istore                 10
   invoke                 1138
   iconst                 0
   if_icmpne              LABEL96
   jump                   LABEL306
LABEL96:
   iload                  0
   invoke                 1936
   istore                 7
   iload                  7
   iconst                 -1
   if_icmpne              LABEL103
   jump                   LABEL137
LABEL103:
   iload                  7
   iconst                 10
   div                   
   istore                 7
   iload                  7
   iload                  5
   if_icmpgt              LABEL111
   jump                   LABEL137
LABEL111:
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP to regain:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  7
   iload                  5
   sub                   
   sload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
   iconst                 1
   istore                 8
LABEL137:
   iload                  8
   iconst                 0
   if_icmpeq              LABEL141
   jump                   LABEL306
LABEL141:
   get_varp               1588
   iconst                 0
   if_icmpgt              LABEL145
   jump                   LABEL306
LABEL145:
   iload                  0
   switch                
      0: LABEL148
      2: LABEL148
      6: LABEL148
      4: LABEL201
      1: LABEL254
   jump                   LABEL306
LABEL148:
   iconst                 20
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL155
   jump                   LABEL178
LABEL155:
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   sload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
   jump                   LABEL200
LABEL178:
   iconst                 1
   istore                 9
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
LABEL200:
   jump                   LABEL306
LABEL201:
   iconst                 30
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL208
   jump                   LABEL231
LABEL208:
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   sload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
   jump                   LABEL253
LABEL231:
   iconst                 1
   istore                 9
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
LABEL253:
   jump                   LABEL306
LABEL254:
   iconst                 40
   invoke                 2031
   istore                 10
   iload                  10
   iconst                 0
   if_icmpgt              LABEL261
   jump                   LABEL284
LABEL261:
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   iload                  10
   sload                  1
   invoke                 46
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
   jump                   LABEL306
LABEL284:
   iconst                 1
   istore                 9
   sload                  2
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "XP permitted:"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 2
   sload                  3
   sconst                 "|"
   sconst                 "<col=7c0808>"
   sconst                 "<col=ef1020>"
   invoke                 6850
   sconst                 "NONE"
   sconst                 "</col>"
   join_string            4
   append                
   sstore                 3
LABEL306:
   iload                  1
   iconst                 5
   cc_find                1
   iconst                 1
   if_icmpeq              LABEL312
   jump                   LABEL322
LABEL312:
   iload                  9
   iconst                 1
   if_icmpeq              LABEL316
   jump                   LABEL319
LABEL316:
   iconst                 0
   cc_sethide             1
   jump                   LABEL321
LABEL319:
   iconst                 1
   cc_sethide             1
LABEL321:
   jump                   LABEL349
LABEL322:
   iload                  1
   iconst                 5
   iconst                 5
   cc_create              1
   iconst                 6
   iconst                 0
   iconst                 0
   iconst                 1
   cc_setposition         1
   iconst                 19
   iconst                 19
   iconst                 0
   iconst                 0
   cc_setsize             1
   iconst                 940
   cc_setgraphic          1
   iconst                 65793
   cc_setgraphicshadow    1
   iload                  9
   iconst                 1
   if_icmpeq              LABEL344
   jump                   LABEL347
LABEL344:
   iconst                 0
   cc_sethide             1
   jump                   LABEL349
LABEL347:
   iconst                 1
   cc_sethide             1
LABEL349:
   iload                  3
   iconst                 1
   if_icmpeq              LABEL353
   jump                   LABEL372
LABEL353:
   map_members           
   iconst                 0
   if_icmpeq              LABEL357
   jump                   LABEL372
LABEL357:
   get_varc_int           103
   iconst                 0
   if_icmpeq              LABEL361
   jump                   LABEL372
LABEL361:
   sconst                 "<col=ff0000>"
   sload                  0
   sconst                 ":"
   sconst                 "</col>"
   join_string            4
   sstore                 2
   sconst                 "<col=ff0000>"
   sconst                 "Members Only"
   sconst                 "</col>"
   join_string            3
   sstore                 3
LABEL372:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL376
   jump                   LABEL403
LABEL376:
   iconst                 2367
   iconst                 -2147483644
   iconst                 -2147483645
   iconst                 -1
   iload                  2
   sload                  2
   sload                  3
   iconst                 495
   sconst                 "iIiIssf"
   iload                  1
   if_setonop            
   get_varc_int           218
   iload                  1
   if_icmpeq              LABEL391
   jump                   LABEL402
LABEL391:
   get_varc_int           217
   iconst                 -1
   if_icmpeq              LABEL395
   jump                   LABEL402
LABEL395:
   iload                  1
   iconst                 -1
   iload                  2
   sload                  2
   sload                  3
   iconst                 495
   invoke                 2344
LABEL402:
   jump                   LABEL418
LABEL403:
   iconst                 992
   iconst                 -2147483645
   iconst                 -1
   iload                  2
   sload                  2
   sload                  3
   iconst                 495
   iconst                 25
   iconst                 5
   div                   
   sconst                 "IiIssfi"
   iload                  1
   if_setonmouserepeat   
   iconst                 0
   set_varc_int           2
LABEL418:
   return                
