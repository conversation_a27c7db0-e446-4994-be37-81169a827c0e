package net.unethicalite.api.packets;

public interface PacketFields
{
	String OPOBJ1 = "field2909";	// "field3013"
	String IF_BUTTON9 = "field2974";	// "field3015"
	String RESUME_PAUSEBUTTON = "field2995";	// "field2967"
	String RESUME_P_COUNTDIALOG = "field2946";	// "field2972"
	String IF_BUTTON8 = "field2996";	// "field2989"
	String OPOBJ5 = "field2961";	// "field2978"
	String IF_BUTTON5 = "field2905";	// "field2960"
	String OPOBJ4 = "field2955";	// "field2976"
	String IF_BUTTON4 = "field2915";	// "field2956"
	String OPOBJ3 = "field2920";	// "field2997"
	String IF_BUTTON7 = "field2963";	// "field3002"
	String OPOBJ2 = "field2919";	// "field2987"
	String IF_BUTTON6 = "field2954";	// "field2968"
	String OPLOCT = "field2992";	// "field3057"
	String OPNPCT = "field2911";	// "field2984"
	String OPPLAYERT = "field2924";	// "field3022"
	String RESUME_P_NAMEDIALOG = "field2928";	// "field3048"
	String RESUME_P_STRINGDIALOG = "field2985";	// "field3033"
	String OPOBJT = "field2980";	// "field3010"
	String IF_BUTTONT = "field2925";	// "field2963"
	String OPNPC2 = "field2948";	// "field3030"
	String OPPLAYER6 = "field2965";	// "field2969"
	String OPNPC3 = "field2984";	// "field3036"
	String OPPLAYER7 = "field2947";	// "field2980"
	String OPLOC2 = "field3003";	// "field3049"
	String OPPLAYER8 = "field2959";	// "field3019"
	String OPLOC1 = "field3005";	// "field2990"
	String OPNPC1 = "field2939";	// "field3004"
	String OPLOC4 = "field2936";	// "field2999"
	String OPPLAYER2 = "field2994";	// "field2975"
	String OPLOC3 = "field3009";	// "field3041"
	String OPPLAYER3 = "field2986";	// "field3003"
	String OPNPC4 = "field2940";	// "field2977"
	String OPPLAYER4 = "field2991";	// "field2962"
	String OPNPC5 = "field2907";	// "field3046"
	String OPPLAYER5 = "field2953";	// "field2995"
	String OPLOC5 = "field2937";	// "field2994"
	String CLOSE_MODAL = "field2987";	// "field3001"
	String OPPLAYER1 = "field2970";	// "field2965"
	String MOVE_GAMECLICK = "field2962";	// "field2957"
	String IF_BUTTON1 = "field2934";	// "field3006"
	String IF_BUTTON3 = "field2930";	// "field3011"
	String IF_BUTTON2 = "field2983";	// "field3032"
	String EVENT_MOUSE_CLICK = "field2957";	// "field3051"
	String IF_BUTTON10 = "field2952";	// "field3043"
	String WRITEINT = "writeInt";	// "writeInt"
	String WRITESHORTLE = "writeShortLE";	// "method7550"
	String WRITESHORTADD = "writeShortAdd";	// "method7551"
	String WRITEBYTE = "writeByte";	// "writeByte"
	String writeShortLE = "writeIntME";	// "method7565"
	String WRITEINTIME = "writeIntIME";	// "writeShortLE"
	String WRITEBYTEADD = "writeByteAdd";	// "method7687"
	String WRITEBYTESUB = "writeByteSub";	// "method7596"
	String WRITEINTLE = "writeIntLE";	// "method7563"
	String WRITESHORTADDLE = "writeShortAddLE";	// "method7641"
	String WRITEBYTENEG = "writeByteNeg";	// "method7542"
	String WRITESHORT = "writeShort";	// "writeShort"
}
