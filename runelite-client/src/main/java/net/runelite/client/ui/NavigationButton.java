/*
 * Copyright (c) 2017-2018, <PERSON> <<EMAIL>>
 * Copyright (c) 2018, <PERSON> <<EMAIL>>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.ui;

import java.awt.image.BufferedImage;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * UI navigation button.
 */
@Data
@Builder
@EqualsAndHashCode(of = {"tooltip"})
public class NavigationButton
{
	/**
	 * Icon of button.
	 */
	private final BufferedImage icon;

	/**
	 * If the button is tab or not
	 */
	@Builder.Default
	private boolean tab = true;

	/**
	 * Tooltip to show when hovered.
	 */
	@Builder.Default
	private final String tooltip = "";

	/**
	 * Button selection state
	 */
	private boolean selected;

	/**
	 * On click action of the button.
	 */
	private Runnable onClick;

	/**
	 * On select action of the button.
	 */
	private Runnable onSelect;

	/**
	 * Plugin panel, used when expanding and contracting sidebar.
	 */
	private PluginPanel panel;

	/**
	 * The order in which the button should be displayed in the side bar. (from lower to higher)
	 */
	private int priority;

	/**
	 * Map of key-value pairs for setting the popup menu
	 */
	private Map<String, Runnable> popup;
}
