<!--
  Fast configuration for local mode, ie. all members reside on the same host. Setting ip_ttl to 0 means that
  no multicast packet will make it outside the local host.
  Therefore, this configuration will NOT work to cluster members residing on different hosts !
  Author: <PERSON><PERSON> Ban
-->

<config xmlns="urn:org:jgroups"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="urn:org:jgroups http://www.jgroups.org/schema/jgroups.xsd"
		version="5.0.0">
	<UDP
			mcast_port="${jgroups.udp.mcast_port:45588}"
			thread_pool.min_threads="2"
			thread_pool.max_threads="200"
			thread_pool.keep_alive_time="5000"

			ip_ttl="0"/>
	<RED/>
	<PING/>
	<MERGE3 max_interval="30000"
			min_interval="10000"/>
	<FD_SOCK2/>
	<FD_ALL3/>
	<VERIFY_SUSPECT timeout="1500"/>
	<pbcast.NAKACK2 xmit_interval="500"/>
	<UNICAST3 xmit_interval="500"/>
	<pbcast.STABLE desired_avg_gossip="50000"
				   max_bytes="8m"/>
	<pbcast.GMS print_local_addr="false" join_timeout="3000"/>
	<UFC max_credits="2M"
		 min_threshold="0.4"/>
	<MFC max_credits="2M"
		 min_threshold="0.4"/>
	<FRAG2 frag_size="60K"/>
</config>