.id                 327
.int_stack_count    1
.string_stack_count 0
.int_var_count      1
.string_var_count   0
   ; Attach specbar redraw listeners to the weapon name text instead of to
   ; auto retaliate text (which is var0). Test by enabling "Hide auto retaliate"
   ; and using a spec.
   iconst                 38862849 ; 593.1 - weapon name widget
   istore                 0        ; overwrite script parameter which is the autoretail text
   iload                  0
   invoke                 187
   iconst                 186
   iload                  0
   iconst                 301
   iconst                 300
   iconst                 284
   iconst                 3
   sconst                 "IY"
   iload                  0
   if_setonvartransmit   
   iconst                 186
   iload                  0
   iconst                 94
   iconst                 1
   sconst                 "IY"
   iload                  0
   if_setoninvtransmit   
   return                
