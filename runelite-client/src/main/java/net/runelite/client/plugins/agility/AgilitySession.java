package net.runelite.client.plugins.agility;

import com.google.common.collect.EvictingQueue;
import lombok.Getter;
import lombok.Setter;
import net.runelite.api.Client;
import net.runelite.api.Skill;
import net.runelite.client.plugins.xptracker.XpTrackerService;
import java.time.Duration;
import java.time.Instant;

@Getter
@Setter
class AgilitySession
{
	private final Courses course;
	private Instant lastLapCompleted;
	private int totalLaps;
	private int lapsTillGoal;
	private final EvictingQueue<Duration> lastLapTimes = EvictingQueue.create(30);
	private int lapsPerHour;

	AgilitySession(Courses course)
	{
		this.course = course;
	}

	void incrementLapCount(Client client, XpTrackerService xpTrackerService)
	{
		calculateLapsPerHour();

		++totalLaps;
		System.out.println("Lap count: " + totalLaps);

		final int currentExp = client.getSkillExperience(Skill.AGILITY);
		final int goalXp = xpTrackerService.getEndGoalXp(Skill.AGILITY);
		final int goalRemainingXp = goalXp - currentExp;
		double courseTotalExp = course.getTotalXpProvider().apply(client);
		if (course == Courses.PYRAMID)
		{
			// agility pyramid has a bonus exp drop on the last obstacle that scales with player level and caps at 1000
			// the bonus is not already accounted for in the total exp number in the courses enum
			courseTotalExp += Math.min(300 + 8 * client.getRealSkillLevel(Skill.AGILITY), 1000);
		}

		lapsTillGoal = goalRemainingXp > 0 ? (int) Math.ceil(goalRemainingXp / courseTotalExp) : 0;
	}

	void calculateLapsPerHour()
	{
		Instant now = Instant.now();

		if (lastLapCompleted != null)
		{
			Duration timeSinceLastLap = Duration.between(lastLapCompleted, now);

			if (!timeSinceLastLap.isNegative())
			{
				lastLapTimes.add(timeSinceLastLap);

				Duration sum = Duration.ZERO;
				for (Duration lapTime : lastLapTimes)
				{
					sum = sum.plus(lapTime);
				}

				Duration averageLapTime = sum.dividedBy(lastLapTimes.size());
				lapsPerHour = (int) (Duration.ofHours(1).toMillis() / averageLapTime.toMillis());
			}
		}

		lastLapCompleted = now;
	}
}