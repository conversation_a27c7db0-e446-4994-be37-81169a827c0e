import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("pv")
public class class419 {
   @ObfuscatedName("n")
   @ObfuscatedGetter(
      intValue = -2090868225
   )
   static int field4661;
   @ObfuscatedName("fh")
   @ObfuscatedSignature(
      descriptor = "Llm;"
   )
   @Export("archive18")
   static Archive archive18;
   @ObfuscatedName("f")
   float[] field4664;
   @ObfuscatedName("w")
   @ObfuscatedGetter(
      intValue = -309107279
   )
   int field4660;

   class419(float[] var1, int var2) {
      this.field4664 = var1;
      this.field4660 = var2;
   }
}
