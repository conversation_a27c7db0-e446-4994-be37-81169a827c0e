/*
 * Copyright (c) 2018, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.xptracker;

import net.runelite.api.Client;
import net.runelite.api.Varbits;
import net.runelite.api.WorldType;

enum XpWorldType
{
	NORMAL,
	TOURNEY,
	DMM
	{
		@Override
		int modifier(Client client)
		{
			return 5;
		}
	},
	LEAGUE
	{
		@Override
		int modifier(Client client)
		{
			if (client.getVarbitValue(Varbits.LEAGUE_RELIC_6) != 0)
			{
				return 16;
			}
			if (client.getVarbitValue(Varbits.LEAGUE_RELIC_4) != 0)
			{
				return 12;
			}
			if (client.getVarbitValue(Varbits.LEAGUE_RELIC_2) != 0)
			{
				return 8;
			}
			return 5;
		}
	};

	int modifier(Client client)
	{
		return 1;
	}

	static XpWorldType of(WorldType type)
	{
		switch (type)
		{
			case NOSAVE_MODE:
				return TOURNEY;
			case DEADMAN:
				return DMM;
			default:
				return NORMAL;
		}
	}
}
