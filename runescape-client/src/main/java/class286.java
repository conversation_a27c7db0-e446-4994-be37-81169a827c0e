import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedGetter;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("ki")
public final class class286 {
   @ObfuscatedName("f")
   @ObfuscatedGetter(
      longValue = -6773866295660205201L
   )
   static long field3335;
   @ObfuscatedName("w")
   @ObfuscatedGetter(
      longValue = -514573897747409403L
   )
   static long field3337;
   @ObfuscatedName("fn")
   @ObfuscatedSignature(
      descriptor = "Llm;"
   )
   @Export("archive15")
   static Archive archive15;
}
