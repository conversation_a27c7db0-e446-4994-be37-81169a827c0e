.id                 187
.int_stack_count    1
.string_stack_count 0
.int_var_count      5
.string_var_count   1
   iconst                 0
   istore                 1
   map_members           
   iconst                 1
   if_icmpeq              LABEL9
   get_varbit             5314
   iconst                 1
   if_icmpeq              LABEL9
   jump                   LABEL14
LABEL9:
   iconst                 94
   iconst                 3
   inv_getobj
   invoke                 3648
   istore                 1
LABEL14:
   iload                  1
   iconst                 0
   if_icmple              LABEL18
   jump                   LABEL28
LABEL18:
   iconst                 1                   ; What we compare the boolean with
   iconst                 0                   ; <PERSON>olean
   sconst                 "drawSpecbarAnyway"
   runelite_callback
   if_icmpeq              LABEL38
   iconst                 1
   iconst                 38862883
   if_sethide
   iconst                 190
   iconst                 28
   iconst                 0
   iconst                 0
   iconst                 38862850
   if_setsize
   return
LABEL28:
   invoke                 1972
   iconst                 1
   if_icmpeq              LABEL32
   jump                   LABEL38
LABEL32:
   iconst                 190
   iconst                 16
   iconst                 0
   iconst                 0
   iconst                 38862850
   if_setsize
LABEL38:
   iconst                 0
   istore                 2
   iconst                 38862883
   if_gethide
   iconst                 1
   if_icmpeq              LABEL45
   jump                   LABEL47
LABEL45:
   iconst                 1
   istore                 2
LABEL47:
   iconst                 0
   iconst                 38862883
   if_sethide
   get_varp               301
   iconst                 0
   if_icmpgt              LABEL54
   jump                   LABEL58
LABEL54:
   iconst                 16776960
   iconst                 38862888
   if_setcolour
   jump                   LABEL61
LABEL58:
   iconst                 16
   iconst                 38862888
   if_setcolour
LABEL61:
   get_varp               300
   istore                 3
   iload                  3
   iconst                 0
   if_icmplt              LABEL67
   jump                   LABEL69
LABEL67:
   iconst                 0
   istore                 3
LABEL69:
   sconst                 "Special Attack: "
   iload                  3
   iconst                 10
   div
   tostring
   sconst                 "%"
   join_string            3
   iconst                 38862888
   if_settext
   iload                  0
   iload                  3
   iload                  2
   invoke                 189
   iconst                 38862884
   iconst                 0
   invoke                 835
   pop_int                                          ; Specbar is fully built here
   iload                  1
   iconst                 0
   if_icmple              RETURN                    ; Return if the weapon isn't supposed to have a spec
   jump                   CONTINUE                  ; Idk why I'm doing it like this but it's the jagex way
   RETURN:
      return
   CONTINUE:
   iload                  3
   iload                  1
   if_icmpge              LABEL86
   jump                   LABEL90
LABEL86:
   iconst                 3767611
   iconst                 38862887
   if_setcolour
   jump                   LABEL93
LABEL90:
   iconst                 12907
   iconst                 38862887
   if_setcolour
LABEL93:
   iconst                 38862884
   iconst                 0
   invoke                 835
   pop_int
   iconst                 94
   iconst                 3
   inv_getobj
   istore                 4
   iconst                 111
   iconst                 115
   iconst                 1739
   iload                  4
   enum
   sconst                 " ("
   iload                  4
   invoke                 3648
   iconst                 10
   div
   tostring
   sconst                 "%)"
   join_string            4
   sstore                 0
   iload                  4
   switch
      22737: LABEL118
      22740: LABEL118
      22743: LABEL118
      22731: LABEL118
      22734: LABEL118
   jump                   LABEL126
LABEL118:
   iconst                 111
   iconst                 115
   iconst                 1739
   iload                  4
   enum
   sconst                 " 5-100% "
   join_string            2
   sstore                 0
LABEL126:
   get_varbit             5712
   iconst                 0
   if_icmpeq              LABEL130
   jump                   LABEL192
LABEL130:
   iload                  4
   iconst                 11235
   if_icmpeq              LABEL146
   iload                  4
   iconst                 20408
   if_icmpeq              LABEL146
   iload                  4
   iconst                 12765
   if_icmpeq              LABEL146
   iload                  4
   iconst                 12768
   if_icmpeq              LABEL146
   iload                  4
   iconst                 12767
   if_icmpeq              LABEL146
   jump                   LABEL176
LABEL146:
   iconst                 94
   iconst                 13
   inv_getobj
   iconst                 11212
   if_icmpeq              LABEL167
   iconst                 94
   iconst                 13
   inv_getobj
   iconst                 11227
   if_icmpeq              LABEL167
   iconst                 94
   iconst                 13
   inv_getobj
   iconst                 11228
   if_icmpeq              LABEL167
   iconst                 94
   iconst                 13
   inv_getobj
   iconst                 11229
   if_icmpeq              LABEL167
   jump                   LABEL176
LABEL167:
   sconst                 "Descent of Dragons: Deal a double attack with dragon arrows that inflicts up to 50% more damage (minimum damage of 8 per hit). ("
   iload                  4
   invoke                 3648
   iconst                 10
   div
   tostring
   sconst                 "%)"
   join_string            3
   sstore                 0
LABEL176:
   iconst                 526
   iconst                 -2147483645
   iconst                 -1
   iconst                 38862890
   sload                  0
   iconst                 25
   iconst                 160
   sconst                 "IiIsii"
   iconst                 38862883
   if_setonmouserepeat
   iconst                 40
   iconst                 38862890
   sconst                 "I"
   iconst                 38862883
   if_setonmouseleave
   jump                   LABEL196
LABEL192:
   iconst                 -1
   sconst                 ""
   iconst                 38862883
   if_setonmouserepeat
LABEL196:
   return                
