package net.runelite.client.util;

import java.util.HashMap;
import net.runelite.api.ItemID;

public class WeaponSpeedMap
{
	public static HashMap<Integer, Integer> SpeedMap = new HashMap<>();

	static
	{
		//Melee
		SpeedMap.put(ItemID._3RD_AGE_AXE, 5);
		SpeedMap.put(ItemID._3RD_AGE_LONGSWORD, 5);
		SpeedMap.put(ItemID._3RD_AGE_PICKAXE, 5);
		SpeedMap.put(ItemID.ABYSSAL_BLUDGEON, 4);
		SpeedMap.put(ItemID.ABYSSAL_DAGGER, 4);
		SpeedMap.put(ItemID.ABYSSAL_DAGGER_P, 4);
		SpeedMap.put(ItemID.ABYSSAL_DAGGER_P_13269, 4);
		SpeedMap.put(ItemID.ABYSSAL_DAGGER_P_13271, 4);
		SpeedMap.put(ItemID.ABYSSAL_TENTACLE, 4);
		SpeedMap.put(ItemID.ABYSSAL_WHIP, 4);
		SpeedMap.put(ItemID.ABYSSAL_WHIP_20405, 4);
		SpeedMap.put(ItemID.ADAMANT_2H_SWORD, 7);
		SpeedMap.put(ItemID.ADAMANT_AXE, 5);
		SpeedMap.put(ItemID.ADAMANT_BATTLEAXE, 6);
		SpeedMap.put(ItemID.ADAMANT_CANE, 5);
		SpeedMap.put(ItemID.ADAMANT_CLAWS, 4);
		SpeedMap.put(ItemID.ADAMANT_DAGGER, 4);
		SpeedMap.put(ItemID.ADAMANT_DAGGERP, 4);
		SpeedMap.put(ItemID.ADAMANT_DAGGERP_5676, 4);
		SpeedMap.put(ItemID.ADAMANT_DAGGERP_5694, 4);
		SpeedMap.put(ItemID.ADAMANT_HALBERD, 7);
		SpeedMap.put(ItemID.ADAMANT_HASTA, 5);
		SpeedMap.put(ItemID.ADAMANT_LONGSWORD, 5);
		SpeedMap.put(ItemID.ADAMANT_MACE, 5);
		SpeedMap.put(ItemID.ADAMANT_PICKAXE, 5);
		SpeedMap.put(ItemID.ADAMANT_SCIMITAR, 4);
		SpeedMap.put(ItemID.ADAMANT_SPEAR, 5);
		SpeedMap.put(ItemID.ADAMANT_SPEARP, 5);
		SpeedMap.put(ItemID.ADAMANT_SPEARP_5712, 5);
		SpeedMap.put(ItemID.ADAMANT_SPEARP_5726, 5);
		SpeedMap.put(ItemID.ADAMANT_SWORD, 4);
		SpeedMap.put(ItemID.ADAMANT_WARHAMMER, 6);
		SpeedMap.put(ItemID.ALE_OF_THE_GODS, 4);
		SpeedMap.put(ItemID.ANCIENT_MACE, 5);
		SpeedMap.put(ItemID.ANGER_BATTLEAXE, 6);
		SpeedMap.put(ItemID.ANGER_MACE, 5);
		SpeedMap.put(ItemID.ANGER_SPEAR, 5);
		SpeedMap.put(ItemID.ANGER_SWORD, 4);
		SpeedMap.put(ItemID.AMYS_SAW, 5);
		SpeedMap.put(ItemID.ARCEUUS_BANNER, 5);
		SpeedMap.put(ItemID.ARCLIGHT, 4);
		SpeedMap.put(ItemID.ARMADYL_GODSWORD, 6);
		SpeedMap.put(ItemID.ARMADYL_GODSWORD_20593, 6);
		SpeedMap.put(ItemID.ARMADYL_GODSWORD_22665, 6);
		SpeedMap.put(ItemID.ARMADYL_GODSWORD_OR, 6);
		SpeedMap.put(ItemID.ASSORTED_FLOWERS, 4);
		SpeedMap.put(ItemID.BANDOS_GODSWORD, 6);
		SpeedMap.put(ItemID.BANDOS_GODSWORD_20782, 6);
		SpeedMap.put(ItemID.BANDOS_GODSWORD_21060, 6);
		SpeedMap.put(ItemID.BANDOS_GODSWORD_OR, 6);
		SpeedMap.put(ItemID.BARBTAIL_HARPOON, 4);
		SpeedMap.put(ItemID.BARRELCHEST_ANCHOR, 6);
		SpeedMap.put(ItemID.BEACH_BOXING_GLOVES, 4);
		SpeedMap.put(ItemID.BEACH_BOXING_GLOVES_11706, 4);
		SpeedMap.put(ItemID.BIRTHDAY_BALLOONS, 4);
		SpeedMap.put(ItemID.BIRTHDAY_CAKE, 4);
		SpeedMap.put(ItemID.BLACK_2H_SWORD, 7);
		SpeedMap.put(ItemID.BLACK_AXE, 5);
		SpeedMap.put(ItemID.BLACK_BATTLEAXE, 6);
		SpeedMap.put(ItemID.BLACK_CANE, 5);
		SpeedMap.put(ItemID.BLACK_CLAWS, 4);
		SpeedMap.put(ItemID.BLACK_DAGGER, 4);
		SpeedMap.put(ItemID.BLACK_DAGGERP, 4);
		SpeedMap.put(ItemID.BLACK_DAGGERP_5682, 4);
		SpeedMap.put(ItemID.BLACK_DAGGERP_5700, 4);
		SpeedMap.put(ItemID.BLACK_FLOWERS, 4);
		SpeedMap.put(ItemID.BLACK_HALBERD, 7);
		SpeedMap.put(ItemID.BLACK_LONGSWORD, 5);
		SpeedMap.put(ItemID.BLACK_MACE, 5);
		SpeedMap.put(ItemID.BLACK_PICKAXE, 5);
		SpeedMap.put(ItemID.BLACK_SALAMANDER, 4);
		SpeedMap.put(ItemID.BLACK_SCIMITAR, 4);
		SpeedMap.put(ItemID.BLACK_SPEAR, 5);
		SpeedMap.put(ItemID.BLACK_SPEARP, 5);
		SpeedMap.put(ItemID.BLACK_SPEARP_5734, 5);
		SpeedMap.put(ItemID.BLACK_SPEARP_5736, 5);
		SpeedMap.put(ItemID.BLACK_SWORD, 4);
		SpeedMap.put(ItemID.BLACK_WARHAMMER, 6);
		SpeedMap.put(ItemID.BLADE_OF_SAELDOR, 4);
		SpeedMap.put(ItemID.BLADE_OF_SAELDOR_C, 4);
		SpeedMap.put(ItemID.BLADE_OF_SAELDOR_INACTIVE, 4);
		SpeedMap.put(ItemID.BLESSED_AXE, 5);
		SpeedMap.put(ItemID.BLISTERWOOD_SICKLE, 5);
		SpeedMap.put(ItemID.BLUE_FLOWERS, 4);
		SpeedMap.put(ItemID.BLUE_FLOWERS_8936, 4);
		SpeedMap.put(ItemID.BLURITE_SWORD, 5);
		SpeedMap.put(ItemID.BONE_CLUB, 6);
		SpeedMap.put(ItemID.BONE_DAGGER, 4);
		SpeedMap.put(ItemID.BONE_DAGGER_P, 4);
		SpeedMap.put(ItemID.BONE_DAGGER_P_8876, 4);
		SpeedMap.put(ItemID.BONE_DAGGER_P_8878, 4);
		SpeedMap.put(ItemID.BONE_SPEAR, 6);
		SpeedMap.put(ItemID.BOXING_GLOVES, 4);
		SpeedMap.put(ItemID.BOXING_GLOVES_7673, 4);
		SpeedMap.put(ItemID.BRINE_SABRE, 4);
		SpeedMap.put(ItemID.BRONZE_2H_SWORD, 7);
		SpeedMap.put(ItemID.BRONZE_AXE, 5);
		SpeedMap.put(ItemID.BRONZE_BATTLEAXE, 6);
		SpeedMap.put(ItemID.BRONZE_CLAWS, 4);
		SpeedMap.put(ItemID.BRONZE_DAGGER, 4);
		SpeedMap.put(ItemID.BRONZE_DAGGERP, 4);
		SpeedMap.put(ItemID.BRONZE_DAGGERP_5670, 4);
		SpeedMap.put(ItemID.BRONZE_DAGGERP_5688, 4);
		SpeedMap.put(ItemID.BRONZE_HALBERD, 7);
		SpeedMap.put(ItemID.BRONZE_HASTA, 5);
		SpeedMap.put(ItemID.BRONZE_LONGSWORD, 5);
		SpeedMap.put(ItemID.BRONZE_MACE, 5);
		SpeedMap.put(ItemID.BRONZE_PICKAXE, 5);
		SpeedMap.put(ItemID.BRONZE_SCIMITAR, 4);
		SpeedMap.put(ItemID.BRONZE_SPEAR, 5);
		SpeedMap.put(ItemID.BRONZE_SPEARP, 5);
		SpeedMap.put(ItemID.BRONZE_SPEARP_5704, 5);
		SpeedMap.put(ItemID.BRONZE_SPEARP_5718, 5);
		SpeedMap.put(ItemID.BRONZE_SWORD, 4);
		SpeedMap.put(ItemID.BRONZE_WARHAMMER, 6);
		SpeedMap.put(ItemID.BRUMA_TORCH, 4);
		SpeedMap.put(ItemID.BUTTERFLY_NET, 4);
		SpeedMap.put(ItemID.CANDY_CANE, 5);
		SpeedMap.put(ItemID.CATTLEPROD, 4);
		SpeedMap.put(ItemID.CHAOTIC_HANDEGG, 4);
		SpeedMap.put(ItemID.CLEAVER, 4);
		SpeedMap.put(ItemID.CORRUPTED_AXE, 5);
		SpeedMap.put(ItemID.CORRUPTED_HALBERD_ATTUNED, 4);
		SpeedMap.put(ItemID.CORRUPTED_HALBERD_BASIC, 4);
		SpeedMap.put(ItemID.CORRUPTED_HALBERD_PERFECTED, 4);
		SpeedMap.put(ItemID.CORRUPTED_HARPOON, 5);
		SpeedMap.put(ItemID.CORRUPTED_PICKAXE, 5);
		SpeedMap.put(ItemID.CORRUPTED_SCEPTRE, 5);
		SpeedMap.put(ItemID.CRIER_BELL, 4);
		SpeedMap.put(ItemID.CRYSTAL_AXE, 5);
		SpeedMap.put(ItemID.CRYSTAL_AXE_23862, 5);
		SpeedMap.put(ItemID.CRYSTAL_AXE_INACTIVE, 5);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_110, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_110_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_210, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_210_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_24125, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_310, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_310_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_410, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_410_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_510, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_510_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_610, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_610_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_710, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_710_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_810, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_810_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_910, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_910_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_ATTUNED, 4);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_BASIC, 4);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_FULL, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_FULL_I, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_INACTIVE, 7);
		SpeedMap.put(ItemID.CRYSTAL_HALBERD_PERFECTED, 4);
		SpeedMap.put(ItemID.CRYSTAL_HARPOON, 5);
		SpeedMap.put(ItemID.CRYSTAL_HARPOON_23864, 5);
		SpeedMap.put(ItemID.CRYSTAL_HARPOON_INACTIVE, 5);
		SpeedMap.put(ItemID.CRYSTAL_PICKAXE, 5);
		SpeedMap.put(ItemID.CRYSTAL_PICKAXE_23863, 5);
		SpeedMap.put(ItemID.CRYSTAL_PICKAXE_INACTIVE, 5);
		SpeedMap.put(ItemID.CRYSTAL_SCEPTRE, 5);
		SpeedMap.put(ItemID.CURSED_GOBLIN_HAMMER, 4);
		SpeedMap.put(ItemID.DARKLIGHT, 5);
		SpeedMap.put(ItemID.DARK_DAGGER, 4);
		SpeedMap.put(ItemID.DECORATIVE_SWORD, 5);
		SpeedMap.put(ItemID.DECORATIVE_SWORD_4503, 5);
		SpeedMap.put(ItemID.DECORATIVE_SWORD_4508, 5);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE, 7);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE_0, 7);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE_100, 7);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE_25, 7);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE_50, 7);
		SpeedMap.put(ItemID.DHAROKS_GREATAXE_75, 7);
		SpeedMap.put(ItemID.DINHS_BULWARK, 7);
		SpeedMap.put(ItemID.DRAGON_2H_SWORD, 7);
		SpeedMap.put(ItemID.DRAGON_2H_SWORD_20559, 7);
		SpeedMap.put(ItemID.DRAGON_AXE, 5);
		SpeedMap.put(ItemID.DRAGON_BATTLEAXE, 6);
		SpeedMap.put(ItemID.DRAGON_CANE, 5);
		SpeedMap.put(ItemID.DRAGON_CLAWS, 4);
		SpeedMap.put(ItemID.DRAGON_CLAWS_20784, 4);
		SpeedMap.put(ItemID.DRAGON_DAGGER, 4);
		SpeedMap.put(ItemID.DRAGON_DAGGER_20407, 4);
		SpeedMap.put(ItemID.DRAGON_DAGGERP, 4);
		SpeedMap.put(ItemID.DRAGON_DAGGERP_5680, 4);
		SpeedMap.put(ItemID.DRAGON_DAGGERP_5698, 4);
		SpeedMap.put(ItemID.DRAGON_HALBERD, 7);
		SpeedMap.put(ItemID.DRAGON_HARPOON, 5);
		SpeedMap.put(ItemID.DRAGON_HASTA, 4);
		SpeedMap.put(ItemID.DRAGON_HASTAKP, 4);
		SpeedMap.put(ItemID.DRAGON_HASTAP, 4);
		SpeedMap.put(ItemID.DRAGON_HASTAP_22737, 4);
		SpeedMap.put(ItemID.DRAGON_HASTAP_22740, 4);
		SpeedMap.put(ItemID.DRAGON_HUNTER_LANCE, 4);
		SpeedMap.put(ItemID.DRAGON_LONGSWORD, 5);
		SpeedMap.put(ItemID.DRAGON_MACE, 5);
		SpeedMap.put(ItemID.DRAGON_PICKAXE, 5);
		SpeedMap.put(ItemID.DRAGON_PICKAXE_12797, 5);
		SpeedMap.put(ItemID.DRAGON_PICKAXE_OR, 5);
		SpeedMap.put(ItemID.DRAGON_SCIMITAR, 4);
		SpeedMap.put(ItemID.DRAGON_SCIMITAR_20406, 4);
		SpeedMap.put(ItemID.DRAGON_SCIMITAR_OR, 4);
		SpeedMap.put(ItemID.DRAGON_SPEAR, 5);
		SpeedMap.put(ItemID.DRAGON_SPEARP, 5);
		SpeedMap.put(ItemID.DRAGON_SPEARP_5716, 5);
		SpeedMap.put(ItemID.DRAGON_SPEARP_5730, 5);
		SpeedMap.put(ItemID.DRAGON_SWORD, 4);
		SpeedMap.put(ItemID.DRAGON_WARHAMMER, 6);
		SpeedMap.put(ItemID.DRAGON_WARHAMMER_20785, 6);
		SpeedMap.put(ItemID.EASTER_BASKET, 4);
		SpeedMap.put(ItemID.EGG_WHISK, 4);
		SpeedMap.put(ItemID.ELDER_MAUL, 6);
		SpeedMap.put(ItemID.ELDER_MAUL_21205, 6);
		SpeedMap.put(ItemID.ENCHANTED_LYRE, 4);
		SpeedMap.put(ItemID.ENCHANTED_LYRE1, 4);
		SpeedMap.put(ItemID.ENCHANTED_LYRE2, 4);
		SpeedMap.put(ItemID.ENCHANTED_LYRE3, 4);
		SpeedMap.put(ItemID.ENCHANTED_LYRE4, 4);
		SpeedMap.put(ItemID.ENCHANTED_LYRE5, 4);
		SpeedMap.put(ItemID.GOBLIN_PAINT_CANNON, 3);
		SpeedMap.put(ItemID.EXCALIBUR, 5);
		SpeedMap.put(ItemID.EXCALIBUR_8280, 5);
		SpeedMap.put(ItemID.FREMENNIK_BLADE, 4);
		SpeedMap.put(ItemID.FROZEN_ABYSSAL_WHIP, 4);
		SpeedMap.put(ItemID.GADDERHAMMER, 5);
		SpeedMap.put(ItemID.GHRAZI_RAPIER, 4);
		SpeedMap.put(ItemID.GHRAZI_RAPIER_23628, 4);
		SpeedMap.put(ItemID.GILDED_2H_SWORD, 7);
		SpeedMap.put(ItemID.GILDED_HASTA, 5);
		SpeedMap.put(ItemID.GILDED_SCIMITAR, 4);
		SpeedMap.put(ItemID.GILDED_SPEAR, 5);
		SpeedMap.put(ItemID.GLOWING_DAGGER, 4);
		SpeedMap.put(ItemID.GOLDEN_TENCH, 4);
		SpeedMap.put(ItemID.GRANITE_HAMMER, 4);
		SpeedMap.put(ItemID.GRANITE_LONGSWORD, 5);
		SpeedMap.put(ItemID.GRANITE_MAUL, 7);
		SpeedMap.put(ItemID.GRANITE_MAUL_12848, 7);
		SpeedMap.put(ItemID.GRANITE_MAUL_20557, 7);
		SpeedMap.put(ItemID.GRANITE_MAUL_24225, 7);
		SpeedMap.put(ItemID.GRANITE_MAUL_24227, 7);
		SpeedMap.put(ItemID.GREEN_BANNER, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR_0, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR_100, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR_25, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR_50, 5);
		SpeedMap.put(ItemID.GUTHANS_WARSPEAR_75, 5);
		SpeedMap.put(ItemID.HALBERD, 7);
		SpeedMap.put(ItemID.HARRYS_CUTLASS, 5);
		SpeedMap.put(ItemID.HAM_JOINT, 3);
		SpeedMap.put(ItemID.HAND_FAN, 4);
		SpeedMap.put(ItemID.HILL_GIANT_CLUB, 7);
		SpeedMap.put(ItemID.HOLY_HANDEGG, 4);
		SpeedMap.put(ItemID.HOSIDIUS_BANNER, 5);
		SpeedMap.put(ItemID.INFERNAL_AXE, 5);
		SpeedMap.put(ItemID.INFERNAL_AXE_UNCHARGED, 5);
		SpeedMap.put(ItemID.INFERNAL_HARPOON, 5);
		SpeedMap.put(ItemID.INFERNAL_HARPOON_UNCHARGED, 5);
		SpeedMap.put(ItemID.INFERNAL_PICKAXE, 5);
		SpeedMap.put(ItemID.INFERNAL_PICKAXE_UNCHARGED, 5);
		SpeedMap.put(ItemID.INQUISITORS_MACE, 4);
		SpeedMap.put(ItemID.IRON_2H_SWORD, 7);
		SpeedMap.put(ItemID.IRON_AXE, 5);
		SpeedMap.put(ItemID.IRON_BATTLEAXE, 6);
		SpeedMap.put(ItemID.IRON_CLAWS, 4);
		SpeedMap.put(ItemID.IRON_DAGGER, 4);
		SpeedMap.put(ItemID.IRON_DAGGERP, 4);
		SpeedMap.put(ItemID.IRON_DAGGERP_5668, 4);
		SpeedMap.put(ItemID.IRON_DAGGERP_5686, 4);
		SpeedMap.put(ItemID.IRON_HALBERD, 7);
		SpeedMap.put(ItemID.IRON_HASTA, 5);
		SpeedMap.put(ItemID.IRON_LONGSWORD, 5);
		SpeedMap.put(ItemID.IRON_MACE, 5);
		SpeedMap.put(ItemID.IRON_PICKAXE, 5);
		SpeedMap.put(ItemID.IRON_SCIMITAR, 4);
		SpeedMap.put(ItemID.IRON_SPEAR, 5);
		SpeedMap.put(ItemID.IRON_SPEARP, 5);
		SpeedMap.put(ItemID.IRON_SPEARP_5706, 5);
		SpeedMap.put(ItemID.IRON_SPEARP_5720, 5);
		SpeedMap.put(ItemID.IRON_SWORD, 4);
		SpeedMap.put(ItemID.IRON_WARHAMMER, 6);
		SpeedMap.put(ItemID.JADE_MACHETE, 5);
		SpeedMap.put(ItemID.KATANA, 4);
		SpeedMap.put(ItemID.KITCHEN_KNIFE, 4);
		SpeedMap.put(ItemID.KERIS, 4);
		SpeedMap.put(ItemID.LARGE_SPADE, 4);
		SpeedMap.put(ItemID.LEAFBLADED_BATTLEAXE, 6);
		SpeedMap.put(ItemID.LEAFBLADED_SPEAR, 5);
		SpeedMap.put(ItemID.LEAFBLADED_SPEAR_4159, 5);
		SpeedMap.put(ItemID.LEAFBLADED_SWORD, 4);
		SpeedMap.put(ItemID.LOVAKENGJ_BANNER, 5);
		SpeedMap.put(ItemID.LUCKY_CUTLASS, 5);
		SpeedMap.put(ItemID.LYRE, 4);
		SpeedMap.put(ItemID.MACE, 5);
		SpeedMap.put(ItemID.MACHETE, 5);
		SpeedMap.put(ItemID.MAGIC_BUTTERFLY_NET, 4);
		SpeedMap.put(ItemID.MAGIC_SECATEURS, 5);
		SpeedMap.put(ItemID.MAGIC_SECATEURS_NZ, 5);
		SpeedMap.put(ItemID.MAPLE_BLACKJACK, 4);
		SpeedMap.put(ItemID.MAPLE_BLACKJACKD, 4);
		SpeedMap.put(ItemID.MAPLE_BLACKJACKO, 4);
		SpeedMap.put(ItemID.MEAT_TENDERISER, 6);
		SpeedMap.put(ItemID.MERFOLK_TRIDENT, 5);
		SpeedMap.put(ItemID.MITHRIL_2H_SWORD, 7);
		SpeedMap.put(ItemID.MITHRIL_AXE, 5);
		SpeedMap.put(ItemID.MITHRIL_BATTLEAXE, 6);
		SpeedMap.put(ItemID.MITHRIL_CLAWS, 4);
		SpeedMap.put(ItemID.MITHRIL_DAGGER, 4);
		SpeedMap.put(ItemID.MITHRIL_DAGGERP, 4);
		SpeedMap.put(ItemID.MITHRIL_DAGGERP_5674, 4);
		SpeedMap.put(ItemID.MITHRIL_DAGGERP_5692, 4);
		SpeedMap.put(ItemID.MITHRIL_HALBERD, 7);
		SpeedMap.put(ItemID.MITHRIL_HASTA, 5);
		SpeedMap.put(ItemID.MITHRIL_LONGSWORD, 5);
		SpeedMap.put(ItemID.MITHRIL_MACE, 5);
		SpeedMap.put(ItemID.MITHRIL_PICKAXE, 5);
		SpeedMap.put(ItemID.MITHRIL_SCIMITAR, 4);
		SpeedMap.put(ItemID.MITHRIL_SPEAR, 5);
		SpeedMap.put(ItemID.MITHRIL_SPEARP, 5);
		SpeedMap.put(ItemID.MITHRIL_SPEARP_5710, 5);
		SpeedMap.put(ItemID.MITHRIL_SPEARP_5724, 5);
		SpeedMap.put(ItemID.MITHRIL_SWORD, 4);
		SpeedMap.put(ItemID.MITHRIL_WARHAMMER, 6);
		SpeedMap.put(ItemID.MIXED_FLOWERS, 4);
		SpeedMap.put(ItemID.MOUSE_TOY, 4);
		SpeedMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL, 7);
		SpeedMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_I, 7);
		SpeedMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_16893, 7);
		SpeedMap.put(ItemID.NEW_CRYSTAL_HALBERD_FULL_I_16892, 7);
		SpeedMap.put(ItemID.NOOSE_WAND, 4);
		SpeedMap.put(ItemID.NUNCHAKU, 5);
		SpeedMap.put(ItemID.OAK_BLACKJACK, 4);
		SpeedMap.put(ItemID.OAK_BLACKJACKD, 4);
		SpeedMap.put(ItemID.OAK_BLACKJACKO, 4);
		SpeedMap.put(ItemID.OILY_FISHING_ROD, 5);
		SpeedMap.put(ItemID.OILY_PEARL_FISHING_ROD, 5);
		SpeedMap.put(ItemID.OPAL_MACHETE, 5);
		SpeedMap.put(ItemID.ORANGE_FLOWERS, 4);
		SpeedMap.put(ItemID.ORANGE_SALAMANDER, 4);
		SpeedMap.put(ItemID.PEACEFUL_HANDEGG, 4);
		SpeedMap.put(ItemID.PET_ROCK, 4);
		SpeedMap.put(ItemID.PISCARILIUS_BANNER, 5);
		SpeedMap.put(ItemID.PROP_SWORD, 4);
		SpeedMap.put(ItemID.PURPLE_FLOWERS, 4);
		SpeedMap.put(ItemID.RAPIER, 4);
		SpeedMap.put(ItemID.RAT_POLE, 5);
		SpeedMap.put(ItemID.RAT_POLE_6774, 5);
		SpeedMap.put(ItemID.RAT_POLE_6775, 5);
		SpeedMap.put(ItemID.RAT_POLE_6776, 5);
		SpeedMap.put(ItemID.RAT_POLE_6777, 5);
		SpeedMap.put(ItemID.RAT_POLE_6778, 5);
		SpeedMap.put(ItemID.RAT_POLE_6779, 5);
		SpeedMap.put(ItemID.RED_FLOWERS, 4);
		SpeedMap.put(ItemID.RED_FLOWERS_8938, 4);
		SpeedMap.put(ItemID.RED_SALAMANDER, 4);
		SpeedMap.put(ItemID.RED_TOPAZ_MACHETE, 5);
		SpeedMap.put(ItemID.ROYAL_SCEPTRE, 4);
		SpeedMap.put(ItemID.RUBBER_CHICKEN, 4);
		SpeedMap.put(ItemID.RUBBER_CHICKEN_22666, 4);
		SpeedMap.put(ItemID.RUNE_2H_SWORD, 7);
		SpeedMap.put(ItemID.RUNE_AXE, 5);
		SpeedMap.put(ItemID.RUNE_BATTLEAXE, 6);
		SpeedMap.put(ItemID.RUNE_BATTLEAXE_20552, 6);
		SpeedMap.put(ItemID.RUNE_CANE, 5);
		SpeedMap.put(ItemID.RUNE_CLAWS, 4);
		SpeedMap.put(ItemID.RUNE_DAGGER, 4);
		SpeedMap.put(ItemID.RUNE_DAGGERP, 4);
		SpeedMap.put(ItemID.RUNE_DAGGERP_5678, 4);
		SpeedMap.put(ItemID.RUNE_DAGGERP_5696, 4);
		SpeedMap.put(ItemID.RUNE_HALBERD, 7);
		SpeedMap.put(ItemID.RUNE_HASTA, 5);
		SpeedMap.put(ItemID.RUNE_LONGSWORD, 5);
		SpeedMap.put(ItemID.RUNE_MACE, 5);
		SpeedMap.put(ItemID.RUNE_PICKAXE, 5);
		SpeedMap.put(ItemID.RUNE_SCIMITAR, 4);
		SpeedMap.put(ItemID.RUNE_SCIMITAR_20402, 4);
		SpeedMap.put(ItemID.RUNE_SCIMITAR_23330, 4);
		SpeedMap.put(ItemID.RUNE_SCIMITAR_23332, 4);
		SpeedMap.put(ItemID.RUNE_SCIMITAR_23334, 4);
		SpeedMap.put(ItemID.RUNE_SPEAR, 5);
		SpeedMap.put(ItemID.RUNE_SPEARP, 5);
		SpeedMap.put(ItemID.RUNE_SPEARP_5714, 5);
		SpeedMap.put(ItemID.RUNE_SPEARP_5728, 5);
		SpeedMap.put(ItemID.RUNE_SWORD, 4);
		SpeedMap.put(ItemID.RUNE_WARHAMMER, 6);
		SpeedMap.put(ItemID.SARADOMINS_BLESSED_SWORD, 4);
		SpeedMap.put(ItemID.SARADOMIN_GODSWORD, 6);
		SpeedMap.put(ItemID.SARADOMIN_GODSWORD_OR, 6);
		SpeedMap.put(ItemID.SARADOMIN_MJOLNIR, 6);
		SpeedMap.put(ItemID.SARADOMIN_SWORD, 4);
		SpeedMap.put(ItemID.SARAS_BLESSED_SWORD_FULL, 4);
		SpeedMap.put(ItemID.SCYTHE, 6);
		SpeedMap.put(ItemID.SCYTHE_OF_VITUR, 5);
		SpeedMap.put(ItemID.SCYTHE_OF_VITUR_22664, 5);
		SpeedMap.put(ItemID.SCYTHE_OF_VITUR_UNCHARGED, 5);
		SpeedMap.put(ItemID.SEVERED_LEG_24792, 4);
		SpeedMap.put(ItemID.SHADOW_SWORD, 6);
		SpeedMap.put(ItemID.SHAYZIEN_BANNER, 5);
		SpeedMap.put(ItemID.SILVERLIGHT, 5);
		SpeedMap.put(ItemID.SILVERLIGHT_6745, 5);
		SpeedMap.put(ItemID.SILVERLIGHT_8279, 5);
		SpeedMap.put(ItemID.SILVER_SICKLE, 5);
		SpeedMap.put(ItemID.SILVER_SICKLE_B, 5);
		SpeedMap.put(ItemID.SNOWBALL, 4);
		SpeedMap.put(ItemID.SPEAR, 5);
		SpeedMap.put(ItemID.STALE_BAGUETTE, 4);
		SpeedMap.put(ItemID.STATIUSS_WARHAMMER, 5);
		SpeedMap.put(ItemID.STATIUSS_WARHAMMER_23620, 5);
		SpeedMap.put(ItemID.STEEL_2H_SWORD, 7);
		SpeedMap.put(ItemID.STEEL_AXE, 5);
		SpeedMap.put(ItemID.STEEL_BATTLEAXE, 6);
		SpeedMap.put(ItemID.STEEL_CLAWS, 4);
		SpeedMap.put(ItemID.STEEL_DAGGER, 4);
		SpeedMap.put(ItemID.STEEL_DAGGERP, 4);
		SpeedMap.put(ItemID.STEEL_DAGGERP_5672, 4);
		SpeedMap.put(ItemID.STEEL_DAGGERP_5690, 4);
		SpeedMap.put(ItemID.STEEL_HALBERD, 7);
		SpeedMap.put(ItemID.STEEL_HASTA, 5);
		SpeedMap.put(ItemID.STEEL_LONGSWORD, 5);
		SpeedMap.put(ItemID.STEEL_PICKAXE, 5);
		SpeedMap.put(ItemID.STEEL_SCIMITAR, 4);
		SpeedMap.put(ItemID.STEEL_SPEAR, 5);
		SpeedMap.put(ItemID.STEEL_SPEARP, 5);
		SpeedMap.put(ItemID.STEEL_SPEARP_5708, 5);
		SpeedMap.put(ItemID.STEEL_SPEARP_5722, 5);
		SpeedMap.put(ItemID.STEEL_SWORD, 4);
		SpeedMap.put(ItemID.STEEL_WARHAMMER, 6);
		SpeedMap.put(ItemID.SWAMP_LIZARD, 5);
		SpeedMap.put(ItemID.SWIFT_BLADE, 3);
		SpeedMap.put(ItemID.TOKTZXILAK, 4);
		SpeedMap.put(ItemID.TOKTZXILAK_20554, 4);
		SpeedMap.put(ItemID.TOKTZXILEK, 4);
		SpeedMap.put(ItemID.TORAGS_HAMMERS, 5);
		SpeedMap.put(ItemID.TORAGS_HAMMERS_0, 5);
		SpeedMap.put(ItemID.TORAGS_HAMMERS_100, 5);
		SpeedMap.put(ItemID.TORAGS_HAMMERS_25, 5);
		SpeedMap.put(ItemID.TORAGS_HAMMERS_50, 5);
		SpeedMap.put(ItemID.TORAGS_HAMMERS_75, 5);
		SpeedMap.put(ItemID.TRAINING_SWORD, 4);
		SpeedMap.put(ItemID.TRAILBLAZER_AXE, 5);
		SpeedMap.put(ItemID.TRAILBLAZER_BANNER, 5);
		SpeedMap.put(ItemID.TRAILBLAZER_CANE, 5);
		SpeedMap.put(ItemID.TRAILBLAZER_HARPOON, 5);
		SpeedMap.put(ItemID.TRAILBLAZER_PICKAXE, 5);
		SpeedMap.put(ItemID.TROLLWEISS, 5);
		SpeedMap.put(ItemID.TWISTED_BANNER, 5);
		SpeedMap.put(ItemID.TZHAARKETEM, 5);
		SpeedMap.put(ItemID.TZHAARKETOM, 7);
		SpeedMap.put(ItemID.TZHAARKETOM_T, 7);
		SpeedMap.put(ItemID.VERACS_FLAIL, 5);
		SpeedMap.put(ItemID.VERACS_FLAIL_0, 5);
		SpeedMap.put(ItemID.VERACS_FLAIL_100, 5);
		SpeedMap.put(ItemID.VERACS_FLAIL_25, 5);
		SpeedMap.put(ItemID.VERACS_FLAIL_50, 5);
		SpeedMap.put(ItemID.VERACS_FLAIL_75, 5);
		SpeedMap.put(ItemID.VESTAS_BLIGHTED_LONGSWORD, 5);
		SpeedMap.put(ItemID.VESTAS_LONGSWORD_INACTIVE, 5);
		SpeedMap.put(ItemID.VESTAS_LONGSWORD, 5);
		SpeedMap.put(ItemID.VESTAS_LONGSWORD_23615, 5);
		SpeedMap.put(ItemID.VESTAS_SPEAR, 5);
		SpeedMap.put(ItemID.VIGGORAS_CHAINMACE, 4);
		SpeedMap.put(ItemID.VIGGORAS_CHAINMACE_U, 4);
		SpeedMap.put(ItemID.VOLCANIC_ABYSSAL_WHIP, 4);
		SpeedMap.put(ItemID.WESTERN_BANNER_1, 5);
		SpeedMap.put(ItemID.WESTERN_BANNER_2, 5);
		SpeedMap.put(ItemID.WESTERN_BANNER_3, 5);
		SpeedMap.put(ItemID.WESTERN_BANNER_4, 5);
		SpeedMap.put(ItemID.WHITE_2H_SWORD, 7);
		SpeedMap.put(ItemID.WHITE_BATTLEAXE, 6);
		SpeedMap.put(ItemID.WHITE_CLAWS, 4);
		SpeedMap.put(ItemID.WHITE_DAGGER, 4);
		SpeedMap.put(ItemID.WHITE_DAGGERP, 4);
		SpeedMap.put(ItemID.WHITE_DAGGERP_6595, 4);
		SpeedMap.put(ItemID.WHITE_DAGGERP_6597, 4);
		SpeedMap.put(ItemID.WHITE_FLOWERS, 4);
		SpeedMap.put(ItemID.WHITE_HALBERD, 7);
		SpeedMap.put(ItemID.WHITE_LONGSWORD, 5);
		SpeedMap.put(ItemID.WHITE_MACE, 5);
		SpeedMap.put(ItemID.WHITE_SCIMITAR, 4);
		SpeedMap.put(ItemID.WHITE_SWORD, 4);
		SpeedMap.put(ItemID.WHITE_WARHAMMER, 6);
		SpeedMap.put(ItemID.WILDERNESS_SWORD_1, 4);
		SpeedMap.put(ItemID.WILDERNESS_SWORD_2, 4);
		SpeedMap.put(ItemID.WILDERNESS_SWORD_3, 4);
		SpeedMap.put(ItemID.WILDERNESS_SWORD_4, 4);
		SpeedMap.put(ItemID.WILLOW_BLACKJACK, 4);
		SpeedMap.put(ItemID.WILLOW_BLACKJACKD, 4);
		SpeedMap.put(ItemID.WILLOW_BLACKJACKO, 4);
		SpeedMap.put(ItemID.WOLFBANE, 4);
		SpeedMap.put(ItemID.WOODEN_SPOON, 5);
		SpeedMap.put(ItemID.WOODEN_SWORD, 4);
		SpeedMap.put(ItemID.YELLOW_FLOWERS, 4);
		SpeedMap.put(ItemID.ZAMORAKIAN_HASTA, 5);
		SpeedMap.put(ItemID.ZAMORAKIAN_SPEAR, 5);
		SpeedMap.put(ItemID.ZAMORAK_GODSWORD, 6);
		SpeedMap.put(ItemID.ZAMORAK_GODSWORD_OR, 6);
		SpeedMap.put(ItemID.ZOMBIE_HEAD, 6);

		//Ranged
		SpeedMap.put(ItemID._3RD_AGE_BOW, 4);
		SpeedMap.put(ItemID.ADAMANT_CROSSBOW, 6);
		SpeedMap.put(ItemID.ADAMANT_DART, 3);
		SpeedMap.put(ItemID.ADAMANT_DARTP, 3);
		SpeedMap.put(ItemID.ADAMANT_DARTP_5633, 3);
		SpeedMap.put(ItemID.ADAMANT_DARTP_5640, 3);
		SpeedMap.put(ItemID.ADAMANT_KNIFE, 3);
		SpeedMap.put(ItemID.ADAMANT_KNIFEP, 3);
		SpeedMap.put(ItemID.ADAMANT_KNIFEP_5659, 3);
		SpeedMap.put(ItemID.ADAMANT_KNIFEP_5666, 3);
		SpeedMap.put(ItemID.ADAMANT_THROWNAXE, 5);
		SpeedMap.put(ItemID.ARMADYL_CROSSBOW, 6);
		SpeedMap.put(ItemID.ARMADYL_CROSSBOW_23611, 6);
		SpeedMap.put(ItemID.BLACK_CHINCHOMPA, 4);
		SpeedMap.put(ItemID.BLACK_DART, 3);
		SpeedMap.put(ItemID.BLACK_DARTP, 3);
		SpeedMap.put(ItemID.BLACK_DARTP_5631, 3);
		SpeedMap.put(ItemID.BLACK_DARTP_5638, 3);
		SpeedMap.put(ItemID.BLACK_KNIFE, 3);
		SpeedMap.put(ItemID.BLACK_KNIFEP, 3);
		SpeedMap.put(ItemID.BLACK_KNIFEP_5658, 3);
		SpeedMap.put(ItemID.BLACK_KNIFEP_5665, 3);
		SpeedMap.put(ItemID.BLURITE_CROSSBOW, 6);
		SpeedMap.put(ItemID.BRONZE_CROSSBOW, 6);
		SpeedMap.put(ItemID.BRONZE_DART, 3);
		SpeedMap.put(ItemID.BRONZE_DARTP, 3);
		SpeedMap.put(ItemID.BRONZE_DARTP_5628, 3);
		SpeedMap.put(ItemID.BRONZE_DARTP_5635, 3);
		SpeedMap.put(ItemID.BRONZE_KNIFE, 3);
		SpeedMap.put(ItemID.BRONZE_KNIFEP, 3);
		SpeedMap.put(ItemID.BRONZE_KNIFEP_5654, 3);
		SpeedMap.put(ItemID.BRONZE_KNIFEP_5661, 3);
		SpeedMap.put(ItemID.BRONZE_THROWNAXE, 5);
		SpeedMap.put(ItemID.CHINCHOMPA, 4);
		SpeedMap.put(ItemID.CHINCHOMPA_10033, 4);
		SpeedMap.put(ItemID.COMP_OGRE_BOW, 5);
		SpeedMap.put(ItemID.CORRUPTED_BOW_ATTUNED, 5);
		SpeedMap.put(ItemID.CORRUPTED_BOW_BASIC, 5);
		SpeedMap.put(ItemID.CORRUPTED_BOW_PERFECTED, 5);
		SpeedMap.put(ItemID.CRAWS_BOW, 4);
		SpeedMap.put(ItemID.CRAWS_BOW_U, 4);
		SpeedMap.put(ItemID.CROSSBOW, 6);
		SpeedMap.put(ItemID.CRYSTAL_BOW, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_110, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_110_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_210, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_210_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_310, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_310_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_410, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_410_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_510, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_510_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_610, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_610_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_710, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_710_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_810, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_810_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_910, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_910_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_24123, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_ATTUNED, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_BASIC, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_FULL, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_FULL_I, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_INACTIVE, 5);
		SpeedMap.put(ItemID.CRYSTAL_BOW_PERFECTED, 5);
		SpeedMap.put(ItemID.CURSED_GOBLIN_BOW, 6);
		SpeedMap.put(ItemID.DARK_BOW, 9);
		SpeedMap.put(ItemID.DARK_BOW_12765, 9);
		SpeedMap.put(ItemID.DARK_BOW_12766, 9);
		SpeedMap.put(ItemID.DARK_BOW_12767, 9);
		SpeedMap.put(ItemID.DARK_BOW_12768, 9);
		SpeedMap.put(ItemID.DARK_BOW_20408, 9);
		SpeedMap.put(ItemID.DART, 3);
		SpeedMap.put(ItemID.DORGESHUUN_CROSSBOW, 5);
		SpeedMap.put(ItemID.DRAGON_CROSSBOW, 6);
		SpeedMap.put(ItemID.DRAGON_DART, 3);
		SpeedMap.put(ItemID.DRAGON_DARTP, 3);
		SpeedMap.put(ItemID.DRAGON_DARTP_11233, 3);
		SpeedMap.put(ItemID.DRAGON_DARTP_11234, 3);
		SpeedMap.put(ItemID.DRAGON_HUNTER_CROSSBOW, 6);
		SpeedMap.put(ItemID.DRAGON_KNIFE, 3);
		SpeedMap.put(ItemID.DRAGON_KNIFEP, 3);
		SpeedMap.put(ItemID.DRAGON_KNIFEP_22808, 3);
		SpeedMap.put(ItemID.DRAGON_KNIFEP_22810, 3);
		SpeedMap.put(ItemID.DRAGON_THROWNAXE, 5);
		SpeedMap.put(ItemID.HEAVY_BALLISTA, 7);
		SpeedMap.put(ItemID.HEAVY_BALLISTA_23630, 7);
		SpeedMap.put(ItemID.HOLY_WATER, 3);
		SpeedMap.put(ItemID.HUNTERS_CROSSBOW, 4);
		SpeedMap.put(ItemID.IRON_CROSSBOW, 6);
		SpeedMap.put(ItemID.IRON_DART, 3);
		SpeedMap.put(ItemID.IRON_DARTP, 3);
		SpeedMap.put(ItemID.IRON_DARTP_5636, 3);
		SpeedMap.put(ItemID.IRON_KNIFE, 3);
		SpeedMap.put(ItemID.IRON_KNIFEP, 3);
		SpeedMap.put(ItemID.IRON_KNIFEP_5655, 3);
		SpeedMap.put(ItemID.IRON_KNIFEP_5662, 3);
		SpeedMap.put(ItemID.IRON_THROWNAXE, 5);
		SpeedMap.put(ItemID.KARILS_CROSSBOW, 4);
		SpeedMap.put(ItemID.KARILS_CROSSBOW_0, 4);
		SpeedMap.put(ItemID.KARILS_CROSSBOW_100, 4);
		SpeedMap.put(ItemID.KARILS_CROSSBOW_25, 4);
		SpeedMap.put(ItemID.KARILS_CROSSBOW_50, 4);
		SpeedMap.put(ItemID.KARILS_CROSSBOW_75, 4);
		SpeedMap.put(ItemID.LIGHT_BALLISTA, 7);
		SpeedMap.put(ItemID.LONGBOW, 6);
		SpeedMap.put(ItemID.MAGIC_COMP_BOW, 5);
		SpeedMap.put(ItemID.MAGIC_LONGBOW, 6);
		SpeedMap.put(ItemID.MAGIC_SHORTBOW, 4);
		SpeedMap.put(ItemID.MAGIC_SHORTBOW_20558, 4);
		SpeedMap.put(ItemID.MAGIC_SHORTBOW_I, 4);
		SpeedMap.put(ItemID.MAPLE_LONGBOW, 6);
		SpeedMap.put(ItemID.MAPLE_SHORTBOW, 4);
		SpeedMap.put(ItemID.MITHRIL_DART, 3);
		SpeedMap.put(ItemID.MITHRIL_DARTP, 3);
		SpeedMap.put(ItemID.MITHRIL_DARTP_5632, 3);
		SpeedMap.put(ItemID.MITHRIL_DARTP_5639, 3);
		SpeedMap.put(ItemID.MITHRIL_KNIFE, 3);
		SpeedMap.put(ItemID.MITHRIL_KNIFEP, 3);
		SpeedMap.put(ItemID.MITHRIL_KNIFEP_5657, 3);
		SpeedMap.put(ItemID.MITHRIL_KNIFEP_5664, 3);
		SpeedMap.put(ItemID.MITHRIL_THROWNAXE, 5);
		SpeedMap.put(ItemID.MITHRIL_CROSSBOW, 6);
		SpeedMap.put(ItemID.MONKEY_TALISMAN, 4);
		SpeedMap.put(ItemID.MORRIGANS_JAVELIN, 6);
		SpeedMap.put(ItemID.MORRIGANS_JAVELIN_23619, 6);
		SpeedMap.put(ItemID.MORRIGANS_THROWING_AXE, 5);
		SpeedMap.put(ItemID.MUD_PIE, 4);
		SpeedMap.put(ItemID.NEW_CRYSTAL_BOW, 5);
		SpeedMap.put(ItemID.NEW_CRYSTAL_BOW_4213, 5);
		SpeedMap.put(ItemID.NEW_CRYSTAL_BOW_16888, 5);
		SpeedMap.put(ItemID.NEW_CRYSTAL_BOW_I, 5);
		SpeedMap.put(ItemID.NEW_CRYSTAL_BOW_I_16889, 5);
		SpeedMap.put(ItemID.OAK_LONGBOW, 6);
		SpeedMap.put(ItemID.OAK_SHORTBOW, 4);
		SpeedMap.put(ItemID.OGRE_BOW, 8);
		SpeedMap.put(ItemID.PHOENIX_CROSSBOW, 6);
		SpeedMap.put(ItemID.RED_CHINCHOMPA, 4);
		SpeedMap.put(ItemID.RED_CHINCHOMPA_10034, 4);
		SpeedMap.put(ItemID.RUNE_CROSSBOW, 6);
		SpeedMap.put(ItemID.RUNE_CROSSBOW_23601, 6);
		SpeedMap.put(ItemID.RUNE_DART, 3);
		SpeedMap.put(ItemID.RUNE_DARTP, 3);
		SpeedMap.put(ItemID.RUNE_DARTP_5634, 3);
		SpeedMap.put(ItemID.RUNE_DARTP_5641, 3);
		SpeedMap.put(ItemID.RUNE_KNIFE, 3);
		SpeedMap.put(ItemID.RUNE_KNIFEP, 3);
		SpeedMap.put(ItemID.RUNE_KNIFEP_5660, 3);
		SpeedMap.put(ItemID.RUNE_KNIFEP_5667, 3);
		SpeedMap.put(ItemID.RUNE_THROWNAXE, 5);
		SpeedMap.put(ItemID.SEERCULL, 5);
		SpeedMap.put(ItemID.SHORTBOW, 4);
		SpeedMap.put(ItemID.SIGNED_OAK_BOW, 6);
		SpeedMap.put(ItemID.STARTER_BOW, 4);
		SpeedMap.put(ItemID.STEEL_CROSSBOW, 6);
		SpeedMap.put(ItemID.STEEL_DART, 3);
		SpeedMap.put(ItemID.STEEL_DARTP, 3);
		SpeedMap.put(ItemID.STEEL_DARTP_5630, 3);
		SpeedMap.put(ItemID.STEEL_DARTP_5637, 3);
		SpeedMap.put(ItemID.STEEL_KNIFE, 3);
		SpeedMap.put(ItemID.STEEL_KNIFEP, 3);
		SpeedMap.put(ItemID.STEEL_KNIFEP_5656, 3);
		SpeedMap.put(ItemID.STEEL_KNIFEP_5663, 3);
		SpeedMap.put(ItemID.STEEL_THROWNAXE, 5);
		SpeedMap.put(ItemID.TOKTZXILUL, 4);
		SpeedMap.put(ItemID.TOXIC_BLOWPIPE, 3);
		SpeedMap.put(ItemID.TOXIC_BLOWPIPE_EMPTY, 3);
		SpeedMap.put(ItemID.TRAINING_BOW, 4);
		SpeedMap.put(ItemID.TWISTED_BOW, 6);
		SpeedMap.put(ItemID.WILLOW_COMP_BOW, 5);
		SpeedMap.put(ItemID.WILLOW_LONGBOW, 6);
		SpeedMap.put(ItemID.WILLOW_SHORTBOW, 4);
		SpeedMap.put(ItemID.YEW_COMP_BOW, 5);
		SpeedMap.put(ItemID.YEW_LONGBOW, 6);
		SpeedMap.put(ItemID.YEW_SHORTBOW, 4);

		//Magic
		SpeedMap.put(ItemID._3RD_AGE_WAND, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_0, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_100, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_25, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_50, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_75, 5);
		SpeedMap.put(ItemID.AHRIMS_STAFF_23653, 5);
		SpeedMap.put(ItemID.AIR_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.ANCIENT_CROZIER, 5);
		SpeedMap.put(ItemID.ANCIENT_STAFF, 5);
		SpeedMap.put(ItemID.APPRENTICE_WAND, 5);
		SpeedMap.put(ItemID.ARMADYL_CROZIER, 5);
		SpeedMap.put(ItemID.BANDOS_CROZIER, 5);
		SpeedMap.put(ItemID.BATTLESTAFF, 5);
		SpeedMap.put(ItemID.BEGINNER_WAND, 5);
		SpeedMap.put(ItemID.BLISTERWOOD_FLAIL, 5);
		SpeedMap.put(ItemID.BRYOPHYTAS_STAFF, 5);
		SpeedMap.put(ItemID.BRYOPHYTAS_STAFF_UNCHARGED, 5);
		SpeedMap.put(ItemID.CORRUPTED_STAFF_ATTUNED, 4);
		SpeedMap.put(ItemID.CORRUPTED_STAFF_BASIC, 4);
		SpeedMap.put(ItemID.CORRUPTED_STAFF_PERFECTED, 4);
		SpeedMap.put(ItemID.CRYSTAL_STAFF_ATTUNED, 4);
		SpeedMap.put(ItemID.CRYSTAL_STAFF_BASIC, 4);
		SpeedMap.put(ItemID.CRYSTAL_STAFF_PERFECTED, 4);
		SpeedMap.put(ItemID.CURSED_GOBLIN_STAFF, 5);
		SpeedMap.put(ItemID.DAWNBRINGER, 4);
		SpeedMap.put(ItemID.DRAMEN_STAFF, 5);
		SpeedMap.put(ItemID.DUST_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.EARTH_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.ELDRITCH_NIGHTMARE_STAFF, 5);
		SpeedMap.put(ItemID.FIRE_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.GUTHIX_CROZIER, 5);
		SpeedMap.put(ItemID.GUTHIX_STAFF, 5);
		SpeedMap.put(ItemID.HARMONISED_NIGHTMARE_STAFF, 4);
		SpeedMap.put(ItemID.IBANS_STAFF, 5);
		SpeedMap.put(ItemID.IBANS_STAFF_1410, 5);
		SpeedMap.put(ItemID.IBANS_STAFF_U, 5);
		SpeedMap.put(ItemID.IVANDIS_FLAIL, 5);
		SpeedMap.put(ItemID.KODAI_WAND, 5);
		SpeedMap.put(ItemID.KODAI_WAND_23626, 5);
		SpeedMap.put(ItemID.LAVA_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.LAVA_BATTLESTAFF_21198, 5);
		SpeedMap.put(ItemID.LUNAR_STAFF, 5);
		SpeedMap.put(ItemID.MAGIC_STAFF, 5);
		SpeedMap.put(ItemID.MASTER_WAND, 5);
		SpeedMap.put(ItemID.MASTER_WAND_20560, 5);
		SpeedMap.put(ItemID.MIST_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.MUD_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_AIR_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_DUST_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_EARTH_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_FIRE_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_LAVA_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_LAVA_STAFF_21200, 5);
		SpeedMap.put(ItemID.MYSTIC_MIST_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_MUD_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_SMOKE_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_STEAM_STAFF, 5);
		SpeedMap.put(ItemID.MYSTIC_STEAM_STAFF_12796, 5);
		SpeedMap.put(ItemID.MYSTIC_WATER_STAFF, 5);
		SpeedMap.put(ItemID.NIGHTMARE_STAFF, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9045, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9046, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9047, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9048, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9049, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9050, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_9051, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_13074, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_13075, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_13077, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_13078, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_16176, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_21445, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_21446, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_26948, 5);
		SpeedMap.put(ItemID.PHARAOHS_SCEPTRE_26950, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_1, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_10, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_2, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_3, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_4, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_5, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_6, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_7, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_8, 5);
		SpeedMap.put(ItemID.ROD_OF_IVANDIS_9, 5);
		SpeedMap.put(ItemID.SANGUINESTI_STAFF, 4);
		SpeedMap.put(ItemID.SANGUINESTI_STAFF_UNCHARGED, 4);
		SpeedMap.put(ItemID.SARADOMIN_CROZIER, 5);
		SpeedMap.put(ItemID.SARADOMIN_STAFF, 5);
		SpeedMap.put(ItemID.SKULL_SCEPTRE, 5);
		SpeedMap.put(ItemID.SKULL_SCEPTRE_I, 5);
		SpeedMap.put(ItemID.SLAYERS_STAFF, 5);
		SpeedMap.put(ItemID.SLAYERS_STAFF_E, 5);
		SpeedMap.put(ItemID.SMOKE_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.STAFF, 5);
		SpeedMap.put(ItemID.STAFF_OF_AIR, 5);
		SpeedMap.put(ItemID.STAFF_OF_BALANCE, 5);
		SpeedMap.put(ItemID.STAFF_OF_BOB_THE_CAT, 5);
		SpeedMap.put(ItemID.STAFF_OF_EARTH, 5);
		SpeedMap.put(ItemID.STAFF_OF_FIRE, 5);
		SpeedMap.put(ItemID.STAFF_OF_LIGHT, 5);
		SpeedMap.put(ItemID.STAFF_OF_THE_DEAD, 5);
		SpeedMap.put(ItemID.STAFF_OF_THE_DEAD_23613, 5);
		SpeedMap.put(ItemID.STAFF_OF_WATER, 5);
		SpeedMap.put(ItemID.STARTER_STAFF, 4);
		SpeedMap.put(ItemID.STEAM_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.STEAM_BATTLESTAFF_12795, 5);
		SpeedMap.put(ItemID.TEACHER_WAND, 5);
		SpeedMap.put(ItemID.THAMMARONS_SCEPTRE, 5);
		SpeedMap.put(ItemID.THAMMARONS_SCEPTRE_U, 5);
		SpeedMap.put(ItemID.TOKTZMEJTAL, 5);
		SpeedMap.put(ItemID.TOXIC_STAFF_OF_THE_DEAD, 5);
		SpeedMap.put(ItemID.TOXIC_STAFF_UNCHARGED, 5);
		SpeedMap.put(ItemID.TRIDENT_OF_THE_SEAS, 4);
		SpeedMap.put(ItemID.TRIDENT_OF_THE_SEAS_E, 4);
		SpeedMap.put(ItemID.TRIDENT_OF_THE_SEAS_FULL, 4);
		SpeedMap.put(ItemID.TRIDENT_OF_THE_SWAMP, 4);
		SpeedMap.put(ItemID.TRIDENT_OF_THE_SWAMP_E, 4);
		SpeedMap.put(ItemID.UNCHARGED_TOXIC_TRIDENT, 4);
		SpeedMap.put(ItemID.UNCHARGED_TOXIC_TRIDENT_E, 4);
		SpeedMap.put(ItemID.UNCHARGED_TRIDENT, 4);
		SpeedMap.put(ItemID.UNCHARGED_TRIDENT_E, 4);
		SpeedMap.put(ItemID.VOID_KNIGHT_MACE, 5);
		SpeedMap.put(ItemID.VOID_KNIGHT_MACE_BROKEN, 5);
		SpeedMap.put(ItemID.VOLATILE_NIGHTMARE_STAFF, 5);
		SpeedMap.put(ItemID.WAND, 5);
		SpeedMap.put(ItemID.WATER_BATTLESTAFF, 5);
		SpeedMap.put(ItemID.WHITE_MAGIC_STAFF, 5);
		SpeedMap.put(ItemID.ZAMORAK_CROZIER, 5);
		SpeedMap.put(ItemID.ZAMORAK_STAFF, 5);
		SpeedMap.put(ItemID.ZURIELS_STAFF, 5);
		SpeedMap.put(ItemID.ZURIELS_STAFF_23617, 5);
		//what the fuck...
		SpeedMap.put(ItemID.GNOMEBALL, 5);
	}
}
