{
  "12946": [ // <PERSON> Du<PERSON>on
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 35,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "13203": [ // Desert Mining Camp Underground
    {
      "rx1": 11,
      "ry1": 15,
      "rx2": 14,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 16,
      "ry1": 9,
      "rx2": 20,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    }
  ],
  "13358": [ // Polnivneach
    {
      "rx1": 33,
      "ry1": 49,
      "rx2": 34,
      "ry2": 50,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 24,
      "ry1": 11,
      "rx2": 25,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    }
  ],
  "11828": [ // Falador Castle Gate
    {
      "rx1": 18,
      "ry1": 21,
      "rx2": 21,
      "ry2": 22,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 17,
      "ry1": 17,
      "rx2": 17,
      "ry2": 21,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 23,
      "ry1": 20,
      "rx2": 31,
      "ry2": 20,
      "z1": 0,
      "z2": 0
    },
    { // Eastern part of the castle
      "rx1": 36,
      "ry1": 15,
      "rx2": 36,
      "ry2": 16,
      "z1": 0,
      "z2": 0
    },
    { // Rising Sun Inn
      "rx1": 12,
      "ry1": 51,
      "rx2": 12,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    }
  ],
  "12895":[ // Prif NW
    { // Entrance North
      "rx1": 61,
      "ry1": 14,
      "rx2": 63,
      "ry2": 17,
      "z1": 0,
      "z2": 2
    },
    { // Entrance West
      "rx1": 49,
      "ry1": 0,
      "rx2": 52,
      "ry2": 5,
      "z1": 0,
      "z2": 2
    },
    { // Balcony North
      "rx1": 57,
      "ry1": 12,
      "rx2": 59,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    { // Balcony West
      "rx1": 51,
      "ry1": 7,
      "rx2": 54,
      "ry2": 9,
      "z1": 0,
      "z2": 0
    },
    { // Roof Peak Corner
      "rx1": 60,
      "ry1": 5,
      "rx2": 61,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    },
    { // Roof Peak Centre
      "rx1": 61,
      "ry1": 0,
      "rx2": 63,
      "ry2": 4,
      "z1": 0,
      "z2": 2
    },
    { // Upper Floor Roof
      "rx1": 56,
      "ry1": 8,
      "rx2": 58,
      "ry2": 10,
      "z1": 2,
      "z2": 2
    },
    { // Amlodd Tower Upper Floor
      "rx1": 18,
      "ry1": 17,
      "rx2": 24,
      "ry2": 22,
      "z1": 2,
      "z2": 2
    },
    { // Hefin Tower Upper Floor
      "rx1": 44,
      "ry1": 42,
      "rx2": 49,
      "ry2": 48,
      "z1": 2,
      "z2": 2
    },
    {
      "rx1": 53,
      "ry1": 44,
      "rx2": 53,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    },
    { // Tatie's house
      "rx1": 28,
      "ry1": 5,
      "rx2": 35,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    }
  ],
  "13151":[ // Prif NE
    { // Entrance North
      "rx1": 0,
      "ry1": 14,
      "rx2": 2,
      "ry2": 17,
      "z1": 0,
      "z2": 2
    },
    { // Entrance East
      "rx1": 11,
      "ry1": 0,
      "rx2": 14,
      "ry2": 5,
      "z1": 0,
      "z2": 2
    },
    { // Balcony North
      "rx1": 4,
      "ry1": 12,
      "rx2": 6,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    { // Balcony East
      "rx1": 9,
      "ry1": 7,
      "rx2": 12,
      "ry2": 9,
      "z1": 0,
      "z2": 0
    },
    { // Roof Peak Corner
      "rx1": 2,
      "ry1": 5,
      "rx2": 3,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    },
    { // Roof Peak Centre
      "rx1": 0,
      "ry1": 0,
      "rx2": 2,
      "ry2": 4,
      "z1": 0,
      "z2": 2
    },
    { // Upper Floor Roof
      "rx1": 5,
      "ry1": 8,
      "rx2": 7,
      "ry2": 10,
      "z1": 2,
      "z2": 2
    },
    { // Meilyr Tower Upper Floor
      "rx1": 14,
      "ry1": 42,
      "rx2": 19,
      "ry2": 48,
      "z1": 2,
      "z2": 2
    },
    { // Cryws Tower Upper Floor
      "rx1": 39,
      "ry1": 17,
      "rx2": 45,
      "ry2": 22,
      "z1": 2,
      "z2": 2
    }
  ],
  "12894": [ // Prif SW
    { // Entrance South
      "rx1": 61,
      "ry1": 52,
      "rx2": 63,
      "ry2": 55,
      "z1": 0,
      "z2": 2
    },
    { // Balcony South
      "rx1": 57,
      "ry1": 54,
      "rx2": 59,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    },
    { // Balcony West
      "rx1": 51,
      "ry1": 60,
      "rx2": 54,
      "ry2": 62,
      "z1": 0,
      "z2": 0
    },
    { // Roof Peak Corner
      "rx1": 60,
      "ry1": 63,
      "rx2": 61,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    },
    { // Upper Floor Roof
      "rx1": 56,
      "ry1": 59,
      "rx2": 58,
      "ry2": 61,
      "z1": 2,
      "z2": 2
    },
    { // Iowerth Tower Upper Floor
      "rx1": 44,
      "ry1": 21,
      "rx2": 49,
      "ry2": 27,
      "z1": 2,
      "z2": 2
    },
    { // Ithell Tower Upper Floor
      "rx1": 18,
      "ry1": 47,
      "rx2": 24,
      "ry2": 52,
      "z1": 2,
      "z2": 2
    },
    {
      "rx1": 46,
      "ry1": 52,
      "rx2": 46,
      "ry2": 52,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 47,
      "rx2": 40,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 26,
      "rx2": 40,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 22,
      "rx2": 53,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    { // Crafting shop
      "rx1": 21,
      "ry1": 60,
      "rx2": 23,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    }
  ],
  "13150": [ // Prif SE
    { // Entrance South
      "rx1": 0,
      "ry1": 52,
      "rx2": 2,
      "ry2": 55,
      "z1": 0,
      "z2": 2
    },
    { // Balcony South
      "rx1": 4,
      "ry1": 54,
      "rx2": 6,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    },
    { // Balcony East
      "rx1": 9,
      "ry1": 60,
      "rx2": 12,
      "ry2": 62,
      "z1": 0,
      "z2": 0
    },
    { // Upper Floor Roof
      "rx1": 5,
      "ry1": 59,
      "rx2": 7,
      "ry2": 61,
      "z1": 2,
      "z2": 2
    },
    { // Cadarn Tower Upper Floor
      "rx1": 39,
      "ry1": 47,
      "rx2": 45,
      "ry2": 52,
      "z1": 2,
      "z2": 2
    },
    { // Traehaearn Tower Upper Floor
      "rx1": 14,
      "ry1": 21,
      "rx2": 19,
      "ry2": 27,
      "z1": 2,
      "z2": 2
    }
  ],
  "14386": [ // South Meiyerditch
    {
      "rx1": 21,
      "ry1": 19,
      "rx2": 22,
      "ry2": 23,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 35,
      "ry1": 43,
      "rx2": 38,
      "ry2": 43,
      "z1": 1,
      "z2": 1
    }
  ],
  "14387": [ // North Meiyerditch
    {
      "rx1": 50,
      "ry1": 4,
      "rx2": 50,
      "ry2": 4,
      "z1": 0,
      "z2": 0
    }
  ],
  "14388": [ // Darkmeyer
    {
      "rx1": 34,
      "ry1": 54,
      "rx2": 36,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 58,
      "ry1": 53,
      "rx2": 60,
      "ry2": 55,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 25,
      "rx2": 49,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 39,
      "ry1": 26,
      "rx2": 50,
      "ry2": 41,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 38,
      "ry1": 27,
      "rx2": 51,
      "ry2": 40,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 37,
      "ry1": 28,
      "rx2": 52,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 36,
      "ry1": 29,
      "rx2": 53,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 35,
      "ry1": 30,
      "rx2": 35,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    }
  ],
  "10291": [ // Ardougne
    {
      "rx1": 27,
      "ry1": 30,
      "rx2": 29,
      "ry2": 35,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 16,
      "ry1": 32,
      "rx2": 19,
      "ry2": 37,
      "z1": 0,
      "z2": 1
    }
  ],
  "12854": [ // Varrock Castle
    {
      "rx1": 0,
      "ry1": 11,
      "rx2": 23,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 20,
      "ry1": 23,
      "rx2": 26,
      "ry2": 34,
      "z1": 1,
      "z2": 1
    }
  ],
  "12852": [ // South Varrock
    {
      "rx1": 42,
      "ry1": 58,
      "rx2": 52,
      "ry2": 58,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 63,
      "rx2": 54,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "12596": [ // Champion's Guild
    {
      "rx1": 52,
      "ry1": 22,
      "rx2": 58,
      "ry2": 23,
      "z1": 0,
      "z2": 0
    }
  ],
  "8253": [ // Lunar Isle
    {
      "rx1": 49,
      "ry1": 12,
      "rx2": 56,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 41,
      "ry1": 18,
      "rx2": 44,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 34,
      "ry1": 19,
      "rx2": 36,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 28,
      "ry1": 15,
      "rx2": 29,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 0,
      "rx2": 29,
      "ry2": 1,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 34,
      "ry1": 4,
      "rx2": 37,
      "ry2": 5,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 46,
      "ry1": 5,
      "rx2": 50,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    }
  ],
  "8252": [ // Lunar Isle South
    {
      "rx1": 23,
      "ry1": 62,
      "rx2": 28,
      "ry2": 62,
      "z1": 0,
      "z2": 0
    }
  ],
  "6456": [ // Mess Hall
    {
      "rx1": 47,
      "ry1": 38,
      "rx2": 48,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    }
  ],
  "6713": [ // Kourend Castle East
    {
      "rx1": 28,
      "ry1": 20,
      "rx2": 28,
      "ry2": 21,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 28,
      "ry1": 29,
      "rx2": 28,
      "ry2": 30,
      "z1": 0,
      "z2": 0
    }
  ],
  "6715": [ // North East Arceuus
    {
      "rx1": 20,
      "ry1": 5,
      "rx2": 50,
      "ry2": 29,
      "z1": 0,
      "z2": 2
    },
    {
      "rx1": 13,
      "ry1": 5,
      "rx2": 19,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 13,
      "ry1": 24,
      "rx2": 19,
      "ry2": 29,
      "z1": 0,
      "z2": 0
    }
  ],
  "6459": [ // Arceuus Library
    {
      "rx1": 7,
      "ry1": 8,
      "rx2": 26,
      "ry2": 25,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 7,
      "ry1": 38,
      "rx2": 26,
      "ry2": 55,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 7,
      "ry1": 8,
      "rx2": 26,
      "ry2": 25,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 13,
      "ry1": 27,
      "rx2": 13,
      "ry2": 36,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 39,
      "ry1": 38,
      "rx2": 58,
      "ry2": 55,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 31,
      "ry1": 44,
      "rx2": 34,
      "ry2": 50,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 40,
      "ry1": 21,
      "rx2": 43,
      "ry2": 24,
      "z1": 0,
      "z2": 2
    },
    {
      "rx1": 27,
      "ry1": 41,
      "rx2": 38,
      "ry2": 50,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 13,
      "ry1": 26,
      "rx2": 24,
      "ry2": 37,
      "z1": 1,
      "z2": 1
    }
  ],
  "6970": [ // Piscarilius West
    {
      "rx1": 60,
      "ry1": 8,
      "rx2": 60,
      "ry2": 8,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 41,
      "rx2": 45,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 23,
      "ry1": 17,
      "rx2": 24,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 56,
      "ry1": 49,
      "rx2": 59,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    }
  ],
  "7226": [ // Piscarilius East
    {
      "rx1": 5,
      "ry1": 6,
      "rx2": 5,
      "ry2": 13,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 35,
      "ry1": 9,
      "rx2": 36,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 14,
      "ry1": 28,
      "rx2": 16,
      "ry2": 29,
      "z1": 0,
      "z2": 0
    }
  ],
  "6203": [ // Lovakenj North East
    {
      "rx1": 19,
      "ry1": 13,
      "rx2": 21,
      "ry2": 16,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 13,
      "ry1": 4,
      "rx2": 15,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    }
  ],
  "10288": [ // Yanille East
    {
      "rx1": 27,
      "ry1": 12,
      "rx2": 33,
      "ry2": 19,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 47,
      "ry1": 19,
      "rx2": 48,
      "ry2": 22,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 52,
      "ry1": 32,
      "rx2": 54,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 31,
      "rx2": 55,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 54,
      "ry1": 30,
      "rx2": 56,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 55,
      "ry1": 29,
      "rx2": 57,
      "ry2": 31,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 8,
      "rx2": 48,
      "ry2": 8,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 57,
      "ry1": 26,
      "rx2": 57,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    }
  ],
  "10032": [ // Yanille West
    {
      "rx1": 35,
      "ry1": 23,
      "rx2": 36,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 35,
      "ry1": 14,
      "rx2": 36,
      "ry2": 16,
      "z1": 0,
      "z2": 0
    }
  ],
  "9775": [ // Jigjig
    {
      "rx1": 50,
      "ry1": 32,
      "rx2": 50,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 56,
      "ry1": 33,
      "rx2": 56,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 55,
      "ry1": 43,
      "rx2": 55,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 45,
      "ry1": 41,
      "rx2": 46,
      "ry2": 41,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 43,
      "rx2": 51,
      "ry2": 45,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 45,
      "ry1": 36,
      "rx2": 45,
      "ry2": 36,
      "z1": 0,
      "z2": 0
    }
  ],
  "10806": [ // Seers' Village
    {
      "rx1": 22,
      "ry1": 41,
      "rx2": 23,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 28,
      "ry1": 14,
      "rx2": 28,
      "ry2": 17,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 11,
      "ry1": 13,
      "rx2": 17,
      "ry2": 13,
      "z1": 0,
      "z2": 0
    }
  ],
  "10549": [ // Ranging Guild
    {
      "rx1": 42,
      "ry1": 49,
      "rx2": 46,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 57,
      "ry1": 34,
      "rx2": 61,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 27,
      "ry1": 34,
      "rx2": 31,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 40,
      "rx2": 49,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 47,
      "ry1": 44,
      "rx2": 48,
      "ry2": 44,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 41,
      "rx2": 50,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 11,
      "ry1": 55,
      "rx2": 16,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 48,
      "rx2": 42,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 32,
      "ry1": 36,
      "rx2": 32,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    }
  ],
  "10293": [ // Fishing Guild
    {
      "rx1": 57,
      "ry1": 2,
      "rx2": 58,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    }
  ],
  "9781": [ // Gnome Stronghold SE
    {
      "rx1": 42,
      "ry1": 5,
      "rx2": 45,
      "ry2": 8,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 39,
      "ry1": 14,
      "rx2": 41,
      "ry2": 16,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 45,
      "ry1": 14,
      "rx2": 50,
      "ry2": 16,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 46,
      "ry1": 13,
      "rx2": 50,
      "ry2": 13,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 15,
      "rx2": 44,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 55,
      "ry1": 16,
      "rx2": 58,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 26,
      "ry1": 24,
      "rx2": 28,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 25,
      "rx2": 25,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 12,
      "ry1": 23,
      "rx2": 15,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 11,
      "ry1": 30,
      "rx2": 17,
      "ry2": 36,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 12,
      "ry1": 39,
      "rx2": 15,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 13,
      "ry1": 27,
      "rx2": 14,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 2,
      "ry1": 4,
      "rx2": 4,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 2,
      "ry1": 10,
      "rx2": 4,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 7,
      "ry1": 9,
      "rx2": 10,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 3,
      "ry1": 7,
      "rx2": 3,
      "ry2": 9,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 5,
      "ry1": 11,
      "rx2": 6,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 26,
      "rx2": 43,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 26,
      "rx2": 56,
      "ry2": 29,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 44,
      "ry1": 27,
      "rx2": 44,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 52,
      "ry1": 27,
      "rx2": 52,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 45,
      "ry1": 28,
      "rx2": 51,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 4,
      "rx2": 56,
      "ry2": 11,
      "z1": 0,
      "z2": 1
    }
  ],
  "9525": [ // Gnome Stronghold SW
    {
      "rx1": 46,
      "ry1": 22,
      "rx2": 49,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 23,
      "rx2": 51,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 51,
      "ry1": 28,
      "rx2": 56,
      "ry2": 33,
      "z1": 0,
      "z2": 2
    },
    {
      "rx1": 53,
      "ry1": 30,
      "rx2": 58,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 48,
      "rx2": 57,
      "ry2": 49,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 54,
      "ry1": 49,
      "rx2": 58,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 56,
      "ry1": 35,
      "rx2": 56,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 46,
      "ry1": 40,
      "rx2": 49,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 39,
      "ry1": 42,
      "rx2": 41,
      "ry2": 44,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 45,
      "rx2": 41,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 44,
      "ry1": 53,
      "rx2": 48,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 20,
      "ry1": 57,
      "rx2": 24,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 29,
      "ry1": 57,
      "rx2": 33,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 59,
      "rx2": 28,
      "ry2": 59,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 23,
      "ry1": 55,
      "rx2": 31,
      "ry2": 62,
      "z1": 0,
      "z2": 1
    }
  ],
  "9526": [ // Gnome Stronghold NE
    {
      "rx1": 40,
      "ry1": 13,
      "rx2": 46,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 9,
      "rx2": 52,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 16,
      "rx2": 54,
      "ry2": 20,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 13,
      "rx2": 50,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 47,
      "ry1": 14,
      "rx2": 49,
      "ry2": 14,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 15,
      "rx2": 51,
      "ry2": 21,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 48,
      "ry1": 26,
      "rx2": 51,
      "ry2": 30,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 39,
      "rx2": 51,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 31,
      "rx2": 50,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 29,
      "rx2": 44,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 24,
      "ry1": 20,
      "rx2": 32,
      "ry2": 21,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 10,
      "ry1": 30,
      "rx2": 11,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 24,
      "ry1": 43,
      "rx2": 32,
      "ry2": 44,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 12,
      "ry1": 48,
      "rx2": 16,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 19,
      "ry1": 57,
      "rx2": 23,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 28,
      "ry1": 57,
      "rx2": 32,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    }
  ],
  "9782": [ // Gnome Stronghold NE
    {
      "rx1": 4,
      "ry1": 7,
      "rx2": 7,
      "ry2": 10,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 10,
      "ry1": 6,
      "rx2": 13,
      "ry2": 12,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 10,
      "ry1": 9,
      "rx2": 14,
      "ry2": 13,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 16,
      "ry1": 7,
      "rx2": 19,
      "ry2": 10,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 6,
      "rx2": 47,
      "ry2": 9,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 6,
      "rx2": 52,
      "ry2": 7,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 7,
      "rx2": 56,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    },

    {
      "rx1": 16,
      "ry1": 22,
      "rx2": 18,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 6,
      "ry1": 31,
      "rx2": 10,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 15,
      "ry1": 30,
      "rx2": 19,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 11,
      "ry1": 32,
      "rx2": 14,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 17,
      "ry1": 27,
      "rx2": 17,
      "ry2": 29,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 15,
      "ry1": 52,
      "rx2": 19,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 15,
      "ry1": 44,
      "rx2": 19,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 17,
      "ry1": 49,
      "rx2": 17,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 12,
      "ry1": 46,
      "rx2": 14,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 17,
      "ry1": 35,
      "rx2": 17,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 20,
      "ry1": 46,
      "rx2": 25,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 33,
      "rx2": 25,
      "ry2": 45,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 20,
      "ry1": 32,
      "rx2": 25,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 26,
      "ry1": 39,
      "rx2": 29,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 16,
      "ry1": 38,
      "rx2": 18,
      "ry2": 42,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 19,
      "ry1": 40,
      "rx2": 29,
      "ry2": 40,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 15,
      "ry1": 21,
      "rx2": 19,
      "ry2": 25,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 31,
      "ry1": 23,
      "rx2": 35,
      "ry2": 26,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 28,
      "ry1": 30,
      "rx2": 30,
      "ry2": 33,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 36,
      "ry1": 30,
      "rx2": 39,
      "ry2": 33,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 33,
      "ry1": 27,
      "rx2": 33,
      "ry2": 35,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 31,
      "ry1": 32,
      "rx2": 35,
      "ry2": 32,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 34,
      "ry1": 44,
      "rx2": 34,
      "ry2": 51,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 32,
      "ry1": 52,
      "rx2": 36,
      "ry2": 54,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 37,
      "ry1": 46,
      "rx2": 39,
      "ry2": 50,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 29,
      "ry1": 47,
      "rx2": 32,
      "ry2": 49,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 33,
      "ry1": 48,
      "rx2": 36,
      "ry2": 48,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 31,
      "ry1": 43,
      "rx2": 33,
      "ry2": 46,
      "z1": 0,
      "z2": 2
    },
    {
      "rx1": 31,
      "ry1": 47,
      "rx2": 31,
      "ry2": 49,
      "z1": 0,
      "z2": 2
    },
    {
      "rx1": 49,
      "ry1": 22,
      "rx2": 51,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 30,
      "rx2": 52,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 57,
      "ry1": 31,
      "rx2": 61,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 38,
      "rx2": 51,
      "ry2": 42,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 48,
      "ry1": 44,
      "rx2": 52,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 57,
      "ry1": 45,
      "rx2": 61,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 52,
      "rx2": 51,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 52,
      "rx2": 54,
      "ry2": 56,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 53,
      "ry1": 37,
      "rx2": 57,
      "ry2": 41,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 27,
      "rx2": 50,
      "ry2": 29,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 32,
      "rx2": 56,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 35,
      "rx2": 50,
      "ry2": 43,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 46,
      "rx2": 56,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 49,
      "rx2": 50,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 32,
      "rx2": 47,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 46,
      "rx2": 47,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 33,
      "rx2": 42,
      "ry2": 45,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 38,
      "ry1": 39,
      "rx2": 41,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 38,
      "ry1": 40,
      "rx2": 48,
      "ry2": 40,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 31,
      "ry1": 34,
      "rx2": 33,
      "ry2": 36,
      "z1": 2,
      "z2": 2
    },
    {
      "rx1": 37,
      "ry1": 39,
      "rx2": 39,
      "ry2": 41,
      "z1": 2,
      "z2": 2
    }
  ],
  "9531": [ // Jatiszo
    {
      "rx1": 4,
      "ry1": 23,
      "rx2": 6,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    }
  ],
  "9275": [ // Neitiznot
    {
      "rx1": 58,
      "ry1": 22,
      "rx2": 60,
      "ry2": 24,
      "z1": 0,
      "z2": 0
    }
  ],
  "11575": [ // Burthorpe
    {
      "rx1": 25,
      "ry1": 24,
      "rx2": 35,
      "ry2": 24,
      "z1": 0,
      "z2": 0
    }
  ],
  "11319": [ // Warrior's Guild
    {
      "rx1": 21,
      "ry1": 15,
      "rx2": 61,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 32,
      "ry1": 13,
      "rx2": 44,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    }
  ],
  "11061": [ // West Catherby & Keep Le Faye
    {
      "rx1": 11,
      "ry1": 6,
      "rx2": 11,
      "ry2": 13,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 14,
      "ry1": 3,
      "rx2": 22,
      "ry2": 6,
      "z1": 2,
      "z2": 2
    },
    {
      "rx1": 55,
      "ry1": 20,
      "rx2": 59,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    }
  ],
  "11317": [ // East Catherby
    {
      "rx1": 5,
      "ry1": 48,
      "rx2": 7,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 16,
      "ry1": 47,
      "rx2": 19,
      "ry2": 47,
      "z1": 0,
      "z2": 0
    }
  ],
  "5945": [ // West Shayzien Encampment
    {
      "rx1": 1,
      "ry1": 0,
      "rx2": 2,
      "ry2": 3,
      "z1": 0,
      "z2": 1
    }
  ],
  "10040": [ // Lighthouse
    {
      "rx1": 9,
      "ry1": 53,
      "rx2": 16,
      "ry2": 60,
      "z1": 1,
      "z2": 2
    },
    {
      "rx1": 12,
      "ry1": 51,
      "rx2": 13,
      "ry2": 52,
      "z1": 0,
      "z2": 1
    }
  ],
  "9799": [ // Lighthouse (During Horror from the Deep)
    {
      "rx1": 9,
      "ry1": 53,
      "rx2": 16,
      "ry2": 60,
      "z1": 1,
      "z2": 2
    },
    {
      "rx1": 12,
      "ry1": 51,
      "rx2": 13,
      "ry2": 52,
      "z1": 0,
      "z2": 1
    }
  ],
  "10547": [ // SE Ardy
    {
      "rx1": 53,
      "ry1": 1,
      "rx2": 63,
      "ry2": 5,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 34,
      "ry1": 54,
      "rx2": 36,
      "ry2": 54,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 48,
      "ry1": 50,
      "rx2": 51,
      "ry2": 50,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 55,
      "rx2": 43,
      "ry2": 59,
      "z1": 0,
      "z2": 0
    }
  ],
  "11570": [ // Rimmington Dock
    {
      "rx1": 28,
      "ry1": 19,
      "rx2": 39,
      "ry2": 23,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 17,
      "ry1": 28,
      "rx2": 36,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    }
  ],
  "11058": [ // Brimhaven Docks
    {
      "rx1": 22,
      "ry1": 32,
      "rx2": 26,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    }
  ],
  "10284": [ // Corsair's Cove East
    {
      "rx1": 7,
      "ry1": 47,
      "rx2": 12,
      "ry2": 52,
      "z1": 0,
      "z2": 0
    }
  ],
  "10028": [ // Corsair's Cove West
    {
      "rx1": 47,
      "ry1": 44,
      "rx2": 51,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    }
  ],
  "10545": [ // Port Khazard
    {
      "rx1": 45,
      "ry1": 31,
      "rx2": 49,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    }
  ],
  "11825": [ // Musa Point Docks
    {
      "rx1": 1,
      "ry1": 5,
      "rx2": 7,
      "ry2": 5,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 7,
      "ry1": 4,
      "rx2": 7,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    }
  ],
  "14902": [ // Bill Teach's Ship at Port Phasmatys
    {
      "rx1": 0,
      "ry1": 33,
      "rx2": 4,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    }
  ],
  "14646": [ // Port Phasmatys
    {
      "rx1": 27,
      "ry1": 25,
      "rx2": 30,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 46,
      "ry1": 47,
      "rx2": 50,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "14647": [ // Ectofuntus
    {
      "rx1": 46,
      "ry1": 0,
      "rx2": 50,
      "ry2": 11,
      "z1": 0,
      "z2": 0
    }
  ],
  "14638": [ // Mos Le'Harmless Port
    {
      "rx1": 20,
      "ry1": 2,
      "rx2": 43,
      "ry2": 6,
      "z1": 0,
      "z2": 0
    }
  ],
  "6968": [ // Hosidius Town
    {
      "rx1": 17,
      "ry1": 26,
      "rx2": 18,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    }
  ],
  "6995": [ // Ancient Cavern
    {
      "rx1": 42,
      "ry1": 15,
      "rx2": 47,
      "ry2": 20,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 31,
      "ry1": 26,
      "rx2": 43,
      "ry2": 41,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 33,
      "rx2": 30,
      "ry2": 35,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 29,
      "ry1": 27,
      "rx2": 30,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 33,
      "rx2": 50,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 35,
      "ry1": 53,
      "rx2": 41,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    }
  ],
  "11056": [ // North Tai Bwo Wannai
    {
      "rx1": 32,
      "ry1": 1,
      "rx2": 36,
      "ry2": 5,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 1,
      "rx2": 52,
      "ry2": 5,
      "z1": 0,
      "z2": 0
    }
  ],
  "11055": [ // South Tai Bwo Wannai
    {
      "rx1": 55,
      "ry1": 56,
      "rx2": 58,
      "ry2": 59,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 36,
      "ry1": 29,
      "rx2": 39,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 35,
      "ry1": 30,
      "rx2": 40,
      "ry2": 33,
      "z1": 0,
      "z2": 0
    }
  ],
  "7749": [ // Pyramid Plunder
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 2
    }
  ],
  "11310": [ // Shilo Village
    {
      "rx1": 26,
      "ry1": 52,
      "rx2": 28,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 26,
      "ry1": 58,
      "rx2": 28,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 27,
      "ry1": 55,
      "rx2": 27,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 33,
      "ry1": 52,
      "rx2": 35,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 29,
      "ry1": 53,
      "rx2": 32,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 44,
      "ry1": 49,
      "rx2": 46,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 50,
      "rx2": 47,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 51,
      "rx2": 48,
      "ry2": 55,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 41,
      "ry1": 52,
      "rx2": 49,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 36,
      "ry1": 53,
      "rx2": 40,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 43,
      "ry1": 44,
      "rx2": 47,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 45,
      "ry1": 43,
      "rx2": 45,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 51,
      "rx2": 55,
      "ry2": 55,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 53,
      "rx2": 56,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 52,
      "ry1": 37,
      "rx2": 54,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 8,
      "ry1": 18,
      "rx2": 9,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 29,
      "rx2": 53,
      "ry2": 36,
      "z1": 0,
      "z2": 0
    }
  ],
  "12340": [ // Draynor Manor
    {
      "rx1": 30,
      "ry1": 24,
      "rx2": 43,
      "ry2": 24,
      "z1": 0,
      "z2": 0
    }
  ],
  "9265": [ // Lletya
    {
      "rx1": 23,
      "ry1": 34,
      "rx2": 23,
      "ry2": 37,
      "z1": 0,
      "z2": 1
    },
    {
      "rx1": 30,
      "ry1": 32,
      "rx2": 30,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 30,
      "ry1": 39,
      "rx2": 30,
      "ry2": 39,
      "z1": 0,
      "z2": 0
    }
  ],
  "15148": [ // Harmony Island
    {
      "rx1": 30,
      "ry1": 24,
      "rx2": 40,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 41,
      "ry1": 18,
      "rx2": 48,
      "ry2": 36,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 41,
      "ry1": 37,
      "rx2": 45,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 34,
      "ry1": 57,
      "rx2": 38,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    }
  ],
  "11423": [ // NW Keldagrim
    {
      "rx1": 60,
      "ry1": 11,
      "rx2": 63,
      "ry2": 36,
      "z1": 0,
      "z2": 1
    }
  ],
  "11678": [ // SE Keldagrim
    {
      "rx1": 49,
      "ry1": 46,
      "rx2": 49,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 56,
      "rx2": 51,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "11679": [ // NE Keldagrim
    {
      "rx1": 50,
      "ry1": 0,
      "rx2": 51,
      "ry2": 1,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 0,
      "ry1": 11,
      "rx2": 3,
      "ry2": 36,
      "z1": 0,
      "z2": 1
    }
  ],
  "8008": [ // Temple of Light Dungeon
    {
      "rx1": 34,
      "ry1": 45,
      "rx2": 54,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    }
  ],
  "12349": [ // Mage Arena
    {
      "rx1": 31,
      "ry1": 48,
      "rx2": 36,
      "ry2": 53,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 23,
      "ry1": 8,
      "rx2": 44,
      "ry2": 50,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 10,
      "ry1": 18,
      "rx2": 56,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 12,
      "ry1": 16,
      "rx2": 54,
      "ry2": 40,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 14,
      "ry1": 14,
      "rx2": 52,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 16,
      "ry1": 12,
      "rx2": 50,
      "ry2": 44,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 18,
      "ry1": 10,
      "rx2": 48,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 20,
      "ry1": 8,
      "rx2": 46,
      "ry2": 48,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 55,
      "ry1": 38,
      "rx2": 55,
      "ry2": 38,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 24,
      "ry1": 51,
      "rx2": 24,
      "ry2": 51,
      "z1": 0,
      "z2": 0
    }
  ],
  "6198": [ // Woodcutting Guild West
    {
      "rx1": 30,
      "ry1": 22,
      "rx2": 39,
      "ry2": 41,
      "z1": 0,
      "z2": 2
    }
  ],
  "12082": [ // Port Sarim
    {
      "rx1": 31,
      "ry1": 27,
      "rx2": 56,
      "ry2": 31,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 24,
      "ry1": 6,
      "rx2": 28,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 31,
      "ry1": 6,
      "rx2": 48,
      "ry2": 10,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 15,
      "ry1": 3,
      "rx2": 17,
      "ry2": 4,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 27,
      "ry1": 0,
      "rx2": 42,
      "ry2": 0,
      "z1": 0,
      "z2": 0
    }
  ],
  "12081": [ // South Port Sarim
    {
      "rx1": 27,
      "ry1": 60,
      "rx2": 42,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 26,
      "ry1": 50,
      "rx2": 42,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    }
  ],
  "11316": [ // Entrana
    {
      "rx1": 7,
      "ry1": 0,
      "rx2": 26,
      "ry2": 4,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 1,
      "ry1": 2,
      "rx2": 8,
      "ry2": 2,
      "z1": 0,
      "z2": 0
    }
  ],
  "14391": [ // Morytania Farm
    {
      "rx1": 18,
      "ry1": 21,
      "rx2": 38,
      "ry2": 25,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 10,
      "ry1": 23,
      "rx2": 17,
      "ry2": 23,
      "z1": 0,
      "z2": 0
    }
  ],
  "10835": [ // Dorgesh-Kaan North
    {
      "rx1": 14,
      "ry1": 16,
      "rx2": 14,
      "ry2": 24,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 6,
      "ry1": 1,
      "rx2": 14,
      "ry2": 7,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 8,
      "ry1": 35,
      "rx2": 11,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 41,
      "ry1": 47,
      "rx2": 62,
      "ry2": 62,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 42,
      "ry1": 31,
      "rx2": 62,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 11,
      "rx2": 62,
      "ry2": 30,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 46,
      "ry1": 29,
      "rx2": 47,
      "ry2": 30,
      "z1": 0,
      "z2": 0
    }
  ],
  "10834": [ // Dorgesh-Kaan South
    {
      "rx1": 0,
      "ry1": 41,
      "rx2": 14,
      "ry2": 61,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 30,
      "ry1": 18,
      "rx2": 32,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 30,
      "ry1": 15,
      "rx2": 32,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },{
      "rx1": 37,
      "ry1": 18,
      "rx2": 39,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 37,
      "ry1": 15,
      "rx2": 39,
      "ry2": 15,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 1,
      "rx2": 63,
      "ry2": 19,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 7,
      "ry1": 5,
      "rx2": 21,
      "ry2": 18,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 7,
      "ry1": 19,
      "rx2": 9,
      "ry2": 26,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 49,
      "ry1": 34,
      "rx2": 62,
      "ry2": 50,
      "z1": 0,
      "z2": 0
    }
  ],
  "7496": [ // Temple of Light
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 2
    }
  ],
  "15008": [ // Fossil Island Underwater West
    {
      "rx1": 10,
      "ry1": 7,
      "rx2": 12,
      "ry2": 10,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 3,
      "ry1": 11,
      "rx2": 8,
      "ry2": 26,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 9,
      "ry1": 20,
      "rx2": 11,
      "ry2": 22,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 19,
      "ry1": 10,
      "rx2": 19,
      "ry2": 11,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 29,
      "ry1": 11,
      "rx2": 29,
      "ry2": 11,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 33,
      "ry1": 15,
      "rx2": 33,
      "ry2": 15,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 34,
      "ry1": 16,
      "rx2": 34,
      "ry2": 16,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 61,
      "ry2": 6,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 34,
      "ry1": 7,
      "rx2": 39,
      "ry2": 10,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 0,
      "ry1": 46,
      "rx2": 11,
      "ry2": 63,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 29,
      "ry1": 20,
      "rx2": 41,
      "ry2": 26,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 36,
      "ry1": 30,
      "rx2": 42,
      "ry2": 35,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 40,
      "ry1": 36,
      "rx2": 48,
      "ry2": 39,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 43,
      "ry1": 40,
      "rx2": 48,
      "ry2": 40,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 43,
      "ry1": 41,
      "rx2": 44,
      "ry2": 42,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 39,
      "ry1": 36,
      "rx2": 39,
      "ry2": 36,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 37,
      "ry1": 21,
      "rx2": 58,
      "ry2": 35,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 49,
      "ry1": 41,
      "rx2": 50,
      "ry2": 41,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 53,
      "ry1": 24,
      "rx2": 63,
      "ry2": 39,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 62,
      "ry1": 40,
      "rx2": 63,
      "ry2": 41,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 43,
      "ry1": 11,
      "rx2": 44,
      "ry2": 12,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 46,
      "ry1": 10,
      "rx2": 47,
      "ry2": 10,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 57,
      "ry1": 14,
      "rx2": 58,
      "ry2": 17,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 59,
      "ry1": 17,
      "rx2": 59,
      "ry2": 17,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 42,
      "ry1": 19,
      "rx2": 42,
      "ry2": 19,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 44,
      "ry1": 17,
      "rx2": 52,
      "ry2": 18,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 45,
      "ry1": 16,
      "rx2": 52,
      "ry2": 16,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 48,
      "ry1": 15,
      "rx2": 49,
      "ry2": 15,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 39,
      "ry1": 14,
      "rx2": 40,
      "ry2": 14,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 23,
      "ry1": 8,
      "rx2": 23,
      "ry2": 8,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 22,
      "ry1": 10,
      "rx2": 22,
      "ry2": 10,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 18,
      "ry1": 8,
      "rx2": 18,
      "ry2": 8,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 13,
      "ry1": 7,
      "rx2": 13,
      "ry2": 7,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 0,
      "ry1": 7,
      "rx2": 2,
      "ry2": 22,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 62,
      "ry1": 18,
      "rx2": 62,
      "ry2": 18,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 58,
      "ry1": 40,
      "rx2": 58,
      "ry2": 40,
      "z1": 1,
      "z2": 1
    }
  ],
  "15264": [ // Fossil Island Underwater East
    {
      "rx1": 0,
      "ry1": 19,
      "rx2": 1,
      "ry2": 22,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 2,
      "ry1": 23,
      "rx2": 2,
      "ry2": 23,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 0,
      "ry1": 41,
      "rx2": 0,
      "ry2": 41,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 3,
      "ry1": 40,
      "rx2": 3,
      "ry2": 40,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 5,
      "ry1": 30,
      "rx2": 6,
      "ry2": 31,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 3,
      "ry1": 24,
      "rx2": 6,
      "ry2": 25,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 9,
      "ry1": 23,
      "rx2": 11,
      "ry2": 23,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 11,
      "ry1": 24,
      "rx2": 13,
      "ry2": 29,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 14,
      "ry1": 25,
      "rx2": 14,
      "ry2": 26,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 61,
      "ry1": 28,
      "rx2": 63,
      "ry2": 31,
      "z1": 1,
      "z2": 1
    }
  ],
  "7236": [ // Waterbirth Island Dungeon Lower Level West
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 1,
      "z2": 1
    }
  ],
  "7492": [ // Waterbirth Island Dungeon Lower Level Centre
    {
      "rx1": 0,
      "ry1": 14,
      "rx2": 2,
      "ry2": 16,
      "z1": 1,
      "z2": 1
    },
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "7748": [ // Waterbirth Island Dungeon Lower Level East
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 1,
      "z2": 2
    }
  ],
  "10290": [ // Ardougne Monastery
    {
      "rx1": 43,
      "ry1": 17,
      "rx2": 49,
      "ry2": 17,
      "z1": 0,
      "z2": 0
    }
  ],
  "12597": [ // Varrock West
    {
      "rx1": 19,
      "ry1": 33,
      "rx2": 25,
      "ry2": 34,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 29,
      "ry1": 38,
      "rx2": 30,
      "ry2": 44,
      "z1": 0,
      "z2": 0
    }
  ],
  "12341": [ // Barbarian Village
     {
       "rx1": 22,
       "ry1": 35,
       "rx2": 27,
       "ry2": 38,
       "z1": 1,
       "z2": 1
     }
  ],
  "11325": [ // Weiss
    {
      "rx1": 43,
      "ry1": 37,
      "rx2": 46,
      "ry2": 41,
      "z1": 0,
      "z2": 0
    }
  ],
  "11826": [ // Rimmington
    {
      "rx1": 7,
      "ry1": 7,
      "rx2": 8,
      "ry2": 8,
      "z1": 0,
      "z2": 0
    }
  ],
  "12113": [ // Tolna's Rift
    {
      "rx1": 0,
      "ry1": 32,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "7513": [ // POH styles 1-4
    {
      "rx1": 24,
      "ry1": 0,
      "rx2": 31,
      "ry2": 7,
      "z1": 0,
      "z2": 3
    }
  ],
  "7769": [ // POH styles 5-8
    {
      "rx1": 24,
      "ry1": 0,
      "rx2": 31,
      "ry2": 7,
      "z1": 0,
      "z2": 3
    }
  ],
  "8025": [ // POH styles 9-12
    {
      "rx1": 24,
      "ry1": 0,
      "rx2": 31,
      "ry2": 7,
      "z1": 0,
      "z2": 3
    }
  ],
  "7243": [ // Cabin Fever ships
    {
      "rx1": 21,
      "ry1": 23,
      "rx2": 25,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 30,
      "ry1": 19,
      "rx2": 34,
      "ry2": 42,
      "z1": 0,
      "z2": 0
    }
  ],
  "12336": [ // Tutorial Island
    { // bank front
      "rx1": 46,
      "ry1": 46,
      "rx2": 53,
      "ry2": 46,
      "z1": 0,
      "z2": 0
    },
    { // bank back
      "rx1": 47,
      "ry1": 54,
      "rx2": 48,
      "ry2": 54,
      "z1": 0,
      "z2": 0
    },
    { // monastery
      "rx1": 57,
      "ry1": 34,
      "rx2": 57,
      "ry2": 35,
      "z1": 0,
      "z2": 0
    },
    { // monastery z1
      "rx1": 43,
      "ry1": 33,
      "rx2": 52,
      "ry2": 36,
      "z1": 1,
      "z2": 1
    }
  ],
  "11830": [ // Goblin Village & mind altar
    { // Grim Tales tower
      "rx1": 21,
      "ry1": 10,
      "rx2": 27,
      "ry2": 15,
      "z1": 2,
      "z2": 2
    }
  ],
  "10292": [ // North & North of East Ardougne (south of Fishing Guild)
    {
      "rx1": 19,
      "ry1": 16,
      "rx2": 24,
      "ry2": 20,
      "z1": 0,
      "z2": 0
    }
  ],
  "6475": [ // Misthalin Mystery manor
    {
      "rx1": 30,
      "ry1": 23,
      "rx2": 40,
      "ry2": 23,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 25,
      "ry1": 35,
      "rx2": 29,
      "ry2": 37,
      "z1": 0,
      "z2": 0
    }
  ],
  "9035": [ // Chaos runecrafting altar
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 3
    }
  ],
  "14908": [ // Fossil Island House on the Hill
    {
      "rx1": 45,
      "ry1": 23,
      "rx2": 45,
      "ry2": 23,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 48,
      "ry1": 22,
      "rx2": 48,
      "ry2": 22,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 57,
      "ry1": 22,
      "rx2": 58,
      "ry2": 24,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 60,
      "ry1": 26,
      "rx2": 60,
      "ry2": 32,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 61,
      "ry1": 27,
      "rx2": 61,
      "ry2": 31,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 58,
      "ry1": 26,
      "rx2": 58,
      "ry2": 32,
      "z1": 1,
      "z2": 1
    }
  ],
  "10803": [ // Witchaven
    {
      "rx1": 15,
      "ry1": 27,
      "rx2": 16,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    }
  ],
  "11829": [ // North of Falador
    {
      "rx1": 59,
      "ry1": 1,
      "rx2": 59,
      "ry2": 1,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 63,
      "ry1": 1,
      "rx2": 63,
      "ry2": 1,
      "z1": 0,
      "z2": 0
    }
  ],
  "12084": [ // East Falador
    {
      "rx1": 11,
      "ry1": 62,
      "rx2": 11,
      "ry2": 62,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 31,
      "ry1": 60,
      "rx2": 31,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 40,
      "ry1": 60,
      "rx2": 40,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 52,
      "ry1": 60,
      "rx2": 52,
      "ry2": 60,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 53,
      "ry1": 59,
      "rx2": 53,
      "ry2": 59,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 54,
      "ry1": 58,
      "rx2": 54,
      "ry2": 58,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 55,
      "ry1": 57,
      "rx2": 55,
      "ry2": 57,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 56,
      "ry1": 56,
      "rx2": 56,
      "ry2": 56,
      "z1": 0,
      "z2": 0
    },
    {
      "rx1": 50,
      "ry1": 28,
      "rx2": 50,
      "ry2": 28,
      "z1": 0,
      "z2": 0
    }
  ],
  "12085": [ // North of East Falador
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 0,
      "ry2": 0,
      "z1": 0,
      "z2": 0
    }
  ],
  "8527": [ // Braindeath Island distillery
    {
      "rx1": 26,
      "ry1": 35,
      "rx2": 52,
      "ry2": 50,
      "z1": 2,
      "z2": 2
    }
  ],
   "14231": [ // Barrows Underground
    {
      "rx1": 0,
      "ry1": 0,
      "rx2": 63,
      "ry2": 63,
      "z1": 0,
      "z2": 0
    }
  ],
  "10300": [ // Etceteria
      {
        "rx1": 57,
        "ry1": 53,
        "rx2": 57,
        "ry2": 57,
        "z1": 0,
        "z2": 0
      }
  ],
  "8276": [ // Monkey Madness II airship platform
    {
      "rx1": 18,
      "ry1": 9,
      "rx2": 56,
      "ry2": 59,
      "z1": 1,
      "z2": 1
    }
  ]
}
