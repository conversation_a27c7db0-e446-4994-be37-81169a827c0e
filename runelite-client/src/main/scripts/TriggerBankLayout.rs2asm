.id                 276
.int_stack_count    17
.string_stack_count 0
.int_var_count      17
.string_var_count   0

; Check if we should allow server to relayout bank
   iconst                 1                      ; true
   iconst                 0                      ; load active boolean
   sconst                 "getSearchingTagTab"   ; push event name
   runelite_callback     ; invoke callback
   if_icmpne              RELAYOUT

; Let layout continue if current bank tab is 0
   get_varbit             4150
   iconst                 0
   if_icmpeq              RELAYOUT

; Reset the current bank tab to 0 otherwise
   iconst                 0
   set_varbit             4150

   sconst                 "Server attempted to reset bank tab."
   sconst                 "debug"
   runelite_callback     

RELAYOUT:
   iload                  0
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   iload                  9
   iload                  10
   iload                  11
   iload                  12
   iload                  13
   iload                  14
   iload                  15
   iload                  16
   invoke                 277
   return                
