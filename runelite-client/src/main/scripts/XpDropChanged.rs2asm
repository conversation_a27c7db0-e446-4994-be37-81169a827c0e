.id                 1004
.int_stack_count    34
.string_stack_count 0
.int_var_count      49
.string_var_count   1
   iload                  0
   iconst                 1
   if_icmpeq              LABEL4
   jump                   LABEL9
LABEL4:
   get_varc_int           953
   iconst                 -1
   if_icmpeq              LABEL8
   jump                   LABEL9
LABEL8:
   return                
LABEL9:
   iconst                 23
   iconst                 1
   add                   
   istore                 34
   iload                  34
   define_array           83
   iload                  34
   define_array           65641
   iconst                 0
   istore                 35
   iconst                 0
   istore                 36
   iload                  0
   iconst                 1
   if_icmpeq              LABEL25
   jump                   LABEL93
LABEL25:
   get_varc_int           953
   iconst                 -1
   if_icmpne              LABEL29
   jump                   LABEL92
LABEL29:
   get_varc_int           960
   iconst                 -10
   if_icmpeq              LABEL33
   jump                   LABEL39
LABEL33:
   iload                  1
   iload                  3
   iload                  8
   iload                  9
   invoke                 997
   jump                   LABEL63
LABEL39:
   iload                  35
   iconst                 105
   iconst                 83
   iconst                 681
   get_varc_int           953
   enum                  
   set_array_int         
   iload                  35
   get_varc_int           960
   set_array_int          1
   iload                  35
   get_array_int         
   iconst                 -1
   if_icmpne              LABEL54
   jump                   LABEL63
LABEL54:
   iload                  35
   get_array_int          1
   iconst                 0
   if_icmpgt              LABEL59
   jump                   LABEL63
LABEL59:
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL63:
   get_varc_int           954
   get_varc_int           955
   get_varc_int           956
   get_varc_int           957
   get_varc_int           958
   get_varc_int           959
   iconst                 -1
   set_varc_int           959
   set_varc_int           958
   set_varc_int           957
   set_varc_int           956
   set_varc_int           955
   set_varc_int           954
   set_varc_int           953
   get_varc_int           961
   get_varc_int           962
   get_varc_int           963
   get_varc_int           964
   get_varc_int           965
   get_varc_int           966
   iconst                 -1
   set_varc_int           966
   set_varc_int           965
   set_varc_int           964
   set_varc_int           963
   set_varc_int           962
   set_varc_int           961
   set_varc_int           960
   jump                   LABEL25
LABEL92:
   jump                   LABEL530
LABEL93:
   sconst                 "newXpDrop"
   runelite_callback
   get_varp               3504
   istore                 48
   iload                  48
   iconst                 0
   if_icmpeq              LABEL1000
   jump                   LABEL1001
LABEL1000:
   iconst                 1
   istore                 48
LABEL1001:
   iconst                 10
   stat_xp               
   iload                  25
   sub
   istore                 36
   iload                  36
   iload                  48
   div
   istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL102
   jump                   LABEL112
LABEL102:
   iload                  35
   iconst                 10
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL112:
   iconst                 0
   stat_xp               
   iload                  11
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL121
   jump                   LABEL131
LABEL121:
   iload                  35
   iconst                 0
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL131:
   iconst                 2
   stat_xp               
   iload                  12
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL140
   jump                   LABEL150
LABEL140:
   iload                  35
   iconst                 2
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL150:
   iconst                 4
   stat_xp               
   iload                  13
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL159
   jump                   LABEL169
LABEL159:
   iload                  35
   iconst                 4
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL169:
   iconst                 6
   stat_xp               
   iload                  14
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL178
   jump                   LABEL188
LABEL178:
   iload                  35
   iconst                 6
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL188:
   iconst                 1
   stat_xp               
   iload                  15
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL197
   jump                   LABEL207
LABEL197:
   iload                  35
   iconst                 1
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL207:
   iconst                 3
   stat_xp               
   iload                  16
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              HP_XP_GAINED
   jump                   LABEL226
HP_XP_GAINED:
   iload                  35
   iconst                 3
   set_array_int         
   iload                  35
   iload                  36
   sconst                 "hpXpGained"
   runelite_callback
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL226:
   iconst                 5
   stat_xp               
   iload                  17
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL235
   jump                   LABEL245
LABEL235:
   iload                  35
   iconst                 5
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL245:
   iconst                 16
   stat_xp               
   iload                  18
   sub                   
   istore                 36
      iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL254
   jump                   LABEL264
LABEL254:
   iload                  35
   iconst                 16
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL264:
   iconst                 15
   stat_xp               
   iload                  19
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL273
   jump                   LABEL283
LABEL273:
   iload                  35
   iconst                 15
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL283:
   iconst                 17
   stat_xp               
   iload                  20
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL292
   jump                   LABEL302
LABEL292:
   iload                  35
   iconst                 17
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL302:
   iconst                 12
   stat_xp               
   iload                  21
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL311
   jump                   LABEL321
LABEL311:
   iload                  35
   iconst                 12
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL321:
   iconst                 20
   stat_xp               
   iload                  22
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL330
   jump                   LABEL340
LABEL330:
   iload                  35
   iconst                 20
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL340:
   iconst                 14
   stat_xp               
   iload                  23
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL349
   jump                   LABEL359
LABEL349:
   iload                  35
   iconst                 14
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL359:
   iconst                 13
   stat_xp               
   iload                  24
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL368
   jump                   LABEL378
LABEL368:
   iload                  35
   iconst                 13
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL378:
   iconst                 7
   stat_xp               
   iload                  26
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL387
   jump                   LABEL397
LABEL387:
   iload                  35
   iconst                 7
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL397:
   iconst                 11
   stat_xp               
   iload                  27
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL406
   jump                   LABEL416
LABEL406:
   iload                  35
   iconst                 11
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL416:
   iconst                 8
   stat_xp               
   iload                  28
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL425
   jump                   LABEL435
LABEL425:
   iload                  35
   iconst                 8
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL435:
   iconst                 9
   stat_xp               
   iload                  29
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL444
   jump                   LABEL454
LABEL444:
   iload                  35
   iconst                 9
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL454:
   iconst                 18
   stat_xp               
   iload                  30
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL463
   jump                   LABEL473
LABEL463:
   iload                  35
   iconst                 18
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL473:
   iconst                 19
   stat_xp               
   iload                  31
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL482
   jump                   LABEL492
LABEL482:
   iload                  35
   iconst                 19
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL492:
   iconst                 22
   stat_xp               
   iload                  32
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL501
   jump                   LABEL511
LABEL501:
   iload                  35
   iconst                 22
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL511:
   iconst                 21
   stat_xp               
   iload                  33
   sub                   
   istore                 36
   iload                  36
      iload                  48
      div
      istore                 36
   iload                  36
   iconst                 0
   if_icmpgt              LABEL520
   jump                   LABEL530
LABEL520:
   iload                  35
   iconst                 21
   set_array_int         
   iload                  35
   iload                  36
   set_array_int          1
   iload                  35
   iconst                 1
   add                   
   istore                 35
LABEL530:
   iconst                 0
   istore                 37
   iconst                 0
   istore                 38
   iconst                 494
   istore                 39
   iconst                 494
   istore                 40
   iconst                 16
   istore                 41
   iconst                 0
   istore                 42
   iconst                 0
   istore                 43
   iconst                 0
   istore                 44
   iconst                 0
   istore                 45
   iconst                 0
   istore                 46
   iconst                 -1
   istore                 47
   sconst                 ""
   sstore                 0
   iload                  35
   iconst                 0
   if_icmpgt              LABEL558
   jump                   LABEL799
LABEL558:
   iload                  16
   iconst                 0
   if_icmpgt              LABEL562
   jump                   LABEL799
LABEL562:
   invoke                 6065
   iconst                 1
   if_icmpeq              LABEL566
   jump                   LABEL799
LABEL566:
   get_varbit             4693
   iconst                 1
   if_icmpeq              LABEL570
   jump                   LABEL581
LABEL570:
   invoke                 1972
   iconst                 0
   if_icmpeq              LABEL574
   jump                   LABEL581
LABEL574:
   iconst                 495
   iconst                 495
   iconst                 25
   istore                 41
   istore                 40
   istore                 39
   jump                   LABEL595
LABEL581:
   get_varbit             4693
   iconst                 2
   if_icmpeq              LABEL585
   jump                   LABEL595
LABEL585:
   invoke                 1972
   iconst                 0
   if_icmpeq              LABEL589
   jump                   LABEL595
LABEL589:
   iconst                 496
   iconst                 496
   iconst                 25
   istore                 41
   istore                 40
   istore                 39
LABEL595:
   iload                  8
   if_getheight          
   istore                 42
   iload                  42
   iconst                 100
   if_icmplt              LABEL602
   jump                   LABEL604
LABEL602:
   iconst                 100
   istore                 42
LABEL604:
   iload                  41
   iconst                 105
   iconst                 105
   iconst                 1171
   get_varbit             4722
   enum                  
   multiply              
   iload                  42
   div                   
   iconst                 1
   add                   
   istore                 43
LABEL616:
   iload                  37
   iload                  35
   if_icmplt              LABEL620
   jump                   LABEL794
LABEL620:
   iload                  38
   iconst                 0
   if_icmpeq              LABEL624
   jump                   LABEL633
LABEL624:
   iload                  0
   iconst                 0
   if_icmpeq              LABEL628
   jump                   LABEL633
LABEL628:
   iload                  37
   get_array_int         
   set_varc_int           72
   iconst                 1
   istore                 38
LABEL633:
   get_varc_int           71
   iconst                 0
   if_icmpgt              LABEL637
   jump                   LABEL648
LABEL637:
   get_varc_int           71
   clientclock           
   iload                  43
   sub                   
   if_icmpgt              LABEL643
   jump                   LABEL648
LABEL643:
   get_varc_int           71
   iload                  43
   add                   
   istore                 44
   jump                   LABEL650
LABEL648:
   clientclock           
   istore                 44
LABEL650:
   iload                  44
   clientclock           
   iload                  43
   iload                  10
   multiply              
   add                   
   if_icmplt              LABEL658
   jump                   LABEL791
LABEL658:
   iconst                 105
   iconst                 73
   iconst                 1163
   get_varc_int           70
   enum                  
   istore                 47
   iconst                 0
   iload                  47
   if_sethide            
   iload                  37
   get_array_int          1
   istore                 46
   iload                  47
   iconst                 5
   iconst                 1
   cc_create             
   iconst                 83
   iconst                 100
   iconst                 255
   iload                  37
   get_array_int         
   enum                  
   cc_setgraphic         
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 5
   cc_setposition        
   iconst                 1
   cc_sethide            
   iconst                 1
   istore                 45
   iload                  37
   iconst                 1
   add                   
   istore                 37
LABEL694:
   get_varbit             4696
   iconst                 1
   if_icmpeq              LABEL698
   jump                   LABEL742
LABEL698:
   iload                  37
   iload                  35
   if_icmplt              LABEL702
   jump                   LABEL742
LABEL702:
   iload                  45
   iconst                 5
   if_icmplt              LABEL706
   jump                   LABEL742
LABEL706:
   iload                  46
   iconst                 1000000
   if_icmplt              LABEL710
   jump                   LABEL742
LABEL710:
   iload                  46
   iload                  37
   get_array_int          1
   add                   
   istore                 46
   iload                  45
   iconst                 1
   add                   
   istore                 45
   iload                  47
   iconst                 5
   iload                  45
   cc_create             
   iconst                 83
   iconst                 100
   iconst                 255
   iload                  37
   get_array_int         
   enum                  
   cc_setgraphic         
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 5
   cc_setposition        
   iconst                 1
   cc_sethide            
   iload                  37
   iconst                 1
   add                   
   istore                 37
   jump                   LABEL694
LABEL742:
   iload                  46
   sconst                 ","
   invoke                 46
   sconst                 "xpDropAddDamage"
   runelite_callback
   sstore                 0
   iload                  0
   iconst                 1
   if_icmpeq              LABEL750
   jump                   LABEL755
LABEL750:
   sconst                 "<img=11>"
   sconst                 " "
   sload                  0
   join_string            3
   sstore                 0
LABEL755:
   iload                  47
   iconst                 0
   cc_find               
   iconst                 1
   if_icmpeq              LABEL761
   jump                   LABEL776
LABEL761:
   sload                  0
   cc_settext            
   iconst                 0
   iconst                 0
   iconst                 0
   iconst                 5
   cc_setposition        
   iconst                 1
   cc_sethide            
   iload                  47
   iload                  41
   iload                  39
   iload                  40
   sload                  0
   invoke                 996
LABEL776:
   iconst                 1005
   iload                  47
   iload                  44
   sconst                 "Ii"
   iload                  47
   if_setontimer         
   iload                  44
   set_varc_int           71
   get_varc_int           70
   iconst                 1
   add                   
   iload                  10
   mod                   
   set_varc_int           70
   jump                   LABEL793
LABEL791:
   iload                  35
   istore                 37
LABEL793:
   jump                   LABEL616
LABEL794:
   iload                  1
   iload                  3
   iload                  8
   iload                  9
   invoke                 997
LABEL799:
   iload                  0
   iconst                 0
   if_icmpeq              LABEL803
   jump                   LABEL822
LABEL803:
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   iload                  9
   invoke                 999
   iload                  1
   iload                  2
   iload                  3
   iload                  4
   iload                  5
   iload                  6
   iload                  7
   iload                  8
   iload                  9
   iload                  10
   invoke                 1003
LABEL822:
   return                
