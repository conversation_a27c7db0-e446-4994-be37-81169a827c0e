/*
 * Copyright (c) 2020, <PERSON> <<PERSON>@sigterm.info>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.ui;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import org.junit.Test;

public class ContainableFrameTest
{
	@Test
	public void testJdk8231564()
	{
		assertTrue(ContainableFrame.jdk8231564("11.0.8"));
		assertFalse(ContainableFrame.jdk8231564("11.0.7"));
		assertFalse(ContainableFrame.jdk8231564("1.8.0_261"));
		assertFalse(ContainableFrame.jdk8231564("12.0.0"));
		assertFalse(ContainableFrame.jdk8231564("13.0.0"));
		assertFalse(ContainableFrame.jdk8231564("14.0.0"));
		assertTrue(ContainableFrame.jdk8231564("15"));
		assertTrue(ContainableFrame.jdk8231564("11.0.16.1"));
	}

	@Test
	public void testJdk8243925()
	{
		assertTrue(ContainableFrame.jdk8243925("11.0.16.1"));
		assertFalse(ContainableFrame.jdk8243925("11.0.8"));
		assertFalse(ContainableFrame.jdk8243925("11.0.4"));
	}
}