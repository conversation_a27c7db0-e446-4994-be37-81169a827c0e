/*
 * Copyright (c) 2018 Abex
 * Copyright (c) 2018, NotFoxtrot <https://github.com/NotFoxtrot>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *     list of conditions and the following disclaimer.
 *  2. Redistributions in binary form must reproduce the above copyright notice,
 *     this list of conditions and the following disclaimer in the documentation
 *     and/or other materials provided with the distribution.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *  ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *  DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 *  ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON>QUENTIAL DAMAGES
 *  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 *  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 *  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 *  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.client.plugins.timetracking.farming;

import javax.annotation.Nullable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import net.runelite.api.ItemID;
import net.runelite.api.NullItemID;
import net.runelite.client.TickConstants;

@RequiredArgsConstructor
@Getter
public enum Produce
{
	WEEDS("Weeds", ItemID.WEEDS, TickConstants.WEEDS, 4),
	SCARECROW("Scarecrow", ItemID.SCARECROW, TickConstants.SCARECROW, 4),

	// Allotment crops
	POTATO("Potato", "Potatoes", PatchImplementation.ALLOTMENT, ItemID.POTATO, TickConstants.POTATO, 5, 0, 3),
	ONION("Onion", "Onions", PatchImplementation.ALLOTMENT, ItemID.ONION, TickConstants.ONION, 5, 0, 3),
	CABBAGE("Cabbage", "Cabbages", PatchImplementation.ALLOTMENT, ItemID.CABBAGE, TickConstants.CABBAGE, 5, 0, 3),
	TOMATO("Tomato", "Tomatoes", PatchImplementation.ALLOTMENT, ItemID.TOMATO, TickConstants.TOMATO, 5, 0, 3),
	SWEETCORN("Sweetcorn", PatchImplementation.ALLOTMENT, ItemID.SWEETCORN, TickConstants.SWEETCORN, 7, 0, 3),
	STRAWBERRY("Strawberry", "Strawberries", PatchImplementation.ALLOTMENT, ItemID.STRAWBERRY, TickConstants.STRAWBERRY, 7, 0, 3),
	WATERMELON("Watermelon", "Watermelons", PatchImplementation.ALLOTMENT, ItemID.WATERMELON, TickConstants.WATERMELON, 9, 0, 3),
	SNAPE_GRASS("Snape grass", PatchImplementation.ALLOTMENT, ItemID.SNAPE_GRASS, TickConstants.SNAPE_GRASS, 8, 0, 3),

	// Flower crops
	MARIGOLD("Marigold", "Marigolds", PatchImplementation.FLOWER, ItemID.MARIGOLDS, TickConstants.MARIGOLD, 5),
	ROSEMARY("Rosemary", PatchImplementation.FLOWER, ItemID.ROSEMARY, TickConstants.ROSEMARY, 5),
	NASTURTIUM("Nasturtium", "Nasturtiums", PatchImplementation.FLOWER, ItemID.NASTURTIUMS, TickConstants.NASTURTIUM, 5),
	WOAD("Woad", PatchImplementation.FLOWER, ItemID.WOAD_LEAF, TickConstants.WOAD, 5),
	LIMPWURT("Limpwurt", "Limpwurt roots", PatchImplementation.FLOWER, ItemID.LIMPWURT_ROOT, TickConstants.LIMPWURT, 5),
	WHITE_LILY("White lily", "White lillies", PatchImplementation.FLOWER, ItemID.WHITE_LILY, TickConstants.WHITE_LILY, 5),

	// Bush crops
	REDBERRIES("Redberry", "Redberries", PatchImplementation.BUSH, ItemID.REDBERRIES, TickConstants.REDBERRIES, 6, 20, 5),
	CADAVABERRIES("Cadavaberry", "Cadava berries", PatchImplementation.BUSH, ItemID.CADAVA_BERRIES, TickConstants.CADAVABERRIES, 7, 20, 5),
	DWELLBERRIES("Dwellberry", "Dwellberries", PatchImplementation.BUSH, ItemID.DWELLBERRIES, TickConstants.DWELLBERRIES, 8, 20, 5),
	JANGERBERRIES("Jangerberry", "Jangerberries", PatchImplementation.BUSH, ItemID.JANGERBERRIES, TickConstants.JANGERBERRIES, 9, 20, 5),
	WHITEBERRIES("Whiteberry", "White berries", PatchImplementation.BUSH, ItemID.WHITE_BERRIES, TickConstants.WHITEBERRIES, 9, 20, 5),
	POISON_IVY("Poison ivy", "Poison ivy berries", PatchImplementation.BUSH, ItemID.POISON_IVY_BERRIES, TickConstants.POISON_IVY, 9, 20, 5),

	// Hop crops
	BARLEY("Barley", PatchImplementation.HOPS, ItemID.BARLEY, TickConstants.BARLEY, 5, 0, 3),
	HAMMERSTONE("Hammerstone", PatchImplementation.HOPS, ItemID.HAMMERSTONE_HOPS, TickConstants.HAMMERSTONE, 5, 0, 3),
	ASGARNIAN("Asgarnian", PatchImplementation.HOPS, ItemID.ASGARNIAN_HOPS, TickConstants.ASGARNIAN, 6, 0, 3),
	JUTE("Jute", PatchImplementation.HOPS, ItemID.JUTE_FIBRE, TickConstants.JUTE, 6, 0, 3),
	YANILLIAN("Yanillian", PatchImplementation.HOPS, ItemID.YANILLIAN_HOPS, TickConstants.YANILLIAN, 7, 0, 3),
	KRANDORIAN("Krandorian", PatchImplementation.HOPS, ItemID.KRANDORIAN_HOPS, TickConstants.KRANDORIAN, 8, 0, 3),
	WILDBLOOD("Wildblood", PatchImplementation.HOPS, ItemID.WILDBLOOD_HOPS, TickConstants.WILDBLOOD, 9, 0, 3),

	// Herb crops
	GUAM("Guam", PatchImplementation.HERB, ItemID.GUAM_LEAF, TickConstants.GUAM, 5, 0, 3),
	MARRENTILL("Marrentill", PatchImplementation.HERB, ItemID.MARRENTILL, TickConstants.MARRENTILL, 5, 0, 3),
	TARROMIN("Tarromin", PatchImplementation.HERB, ItemID.TARROMIN, TickConstants.TARROMIN, 5, 0, 3),
	HARRALANDER("Harralander", PatchImplementation.HERB, ItemID.HARRALANDER, TickConstants.HARRALANDER, 5, 0, 3),
	RANARR("Ranarr", PatchImplementation.HERB, ItemID.RANARR_WEED, TickConstants.RANARR, 5, 0, 3),
	TOADFLAX("Toadflax", PatchImplementation.HERB, ItemID.TOADFLAX, TickConstants.TOADFLAX, 5, 0, 3),
	IRIT("Irit", PatchImplementation.HERB, ItemID.IRIT_LEAF, TickConstants.IRIT, 5, 0, 3),
	AVANTOE("Avantoe", PatchImplementation.HERB, ItemID.AVANTOE, TickConstants.AVANTOE, 5, 0, 3),
	KWUARM("Kwuarm", PatchImplementation.HERB, ItemID.KWUARM, TickConstants.KWUARM, 5, 0, 3),
	SNAPDRAGON("Snapdragon", PatchImplementation.HERB, ItemID.SNAPDRAGON, TickConstants.SNAPDRAGON, 5, 0, 3),
	CADANTINE("Cadantine", PatchImplementation.HERB, ItemID.CADANTINE, TickConstants.CADANTINE, 5, 0, 3),
	LANTADYME("Lantadyme", PatchImplementation.HERB, ItemID.LANTADYME, TickConstants.LANTADYME, 5, 0, 3),
	DWARF_WEED("Dwarf weed", PatchImplementation.HERB, ItemID.DWARF_WEED, TickConstants.DWARF_WEED, 5, 0, 3),
	TORSTOL("Torstol", PatchImplementation.HERB, ItemID.TORSTOL, TickConstants.TORSTOL, 5, 0, 3),
	GOUTWEED("Goutweed", PatchImplementation.HERB, ItemID.GOUTWEED, TickConstants.GOUTWEED, 5, 0, 2),
	ANYHERB("Any herb", PatchImplementation.HERB, ItemID.GUAM_LEAF, TickConstants.ANYHERB, 5, 0, 3),

	// Tree crops
	OAK("Oak", "Oak tree", PatchImplementation.TREE, ItemID.OAK_LOGS, TickConstants.OAK, 5),
	WILLOW("Willow", "Willow tree", PatchImplementation.TREE, ItemID.WILLOW_LOGS, TickConstants.WILLOW, 7),
	MAPLE("Maple", "Maple tree", PatchImplementation.TREE, ItemID.MAPLE_LOGS, TickConstants.MAPLE, 9),
	YEW("Yew", "Yew tree", PatchImplementation.TREE, ItemID.YEW_LOGS, TickConstants.YEW, 11),
	MAGIC("Magic", "Magic tree", PatchImplementation.TREE, ItemID.MAGIC_LOGS, TickConstants.MAGIC, 13),

	// Fruit tree crops
	APPLE("Apple", "Apple tree", PatchImplementation.FRUIT_TREE, ItemID.COOKING_APPLE, TickConstants.APPLE, 7, 45, 7),
	BANANA("Banana", "Banana tree", PatchImplementation.FRUIT_TREE, ItemID.BANANA, TickConstants.BANANA, 7, 45, 7),
	ORANGE("Orange", "Orange tree", PatchImplementation.FRUIT_TREE, ItemID.ORANGE, TickConstants.ORANGE, 7, 45, 7),
	CURRY("Curry", "Curry tree", PatchImplementation.FRUIT_TREE, ItemID.CURRY_LEAF, TickConstants.CURRY, 7, 45, 7),
	PINEAPPLE("Pineapple", "Pineapple plant", PatchImplementation.FRUIT_TREE, ItemID.PINEAPPLE, TickConstants.PINEAPPLE, 7, 45, 7),
	PAPAYA("Papaya", "Papaya tree", PatchImplementation.FRUIT_TREE, ItemID.PAPAYA_FRUIT, TickConstants.PAPAYA, 7, 45, 7),
	PALM("Palm", "Palm tree", PatchImplementation.FRUIT_TREE, ItemID.COCONUT, TickConstants.PALM, 7, 45, 7),
	DRAGONFRUIT("Dragonfruit", "Dragonfruit tree", PatchImplementation.FRUIT_TREE, ItemID.DRAGONFRUIT, TickConstants.DRAGONFRUIT, 7, 45, 7),

	// Cactus
	CACTUS("Cactus", PatchImplementation.CACTUS, ItemID.CACTUS_SPINE, TickConstants.CACTUS, 8, 20, 4),
	POTATO_CACTUS("Potato cactus", "Potato cacti", PatchImplementation.CACTUS, ItemID.POTATO_CACTUS, TickConstants.POTATO_CACTUS, 8, 5, 7),

	// Hardwood
	TEAK("Teak", PatchImplementation.HARDWOOD_TREE, ItemID.TEAK_LOGS, TickConstants.TEAK, 8),
	MAHOGANY("Mahogany", PatchImplementation.HARDWOOD_TREE, ItemID.MAHOGANY_LOGS, TickConstants.MAHOGANY, 9),

	// Anima
	ATTAS("Attas", PatchImplementation.ANIMA, NullItemID.NULL_22940, TickConstants.ATTAS, 9),
	IASOR("Iasor", PatchImplementation.ANIMA, NullItemID.NULL_22939, TickConstants.IASOR, 9),
	KRONOS("Kronos", PatchImplementation.ANIMA, NullItemID.NULL_22938, TickConstants.KRONOS, 9),

	// Special crops
	SEAWEED("Seaweed", PatchImplementation.SEAWEED, ItemID.GIANT_SEAWEED, TickConstants.SEAWEED, 5, 0, 4),
	GRAPE("Grape", PatchImplementation.GRAPES, ItemID.GRAPES, TickConstants.GRAPE, 8, 0, 5),
	MUSHROOM("Mushroom", PatchImplementation.MUSHROOM, ItemID.MUSHROOM, TickConstants.MUSHROOM, 7, 0, 7),
	BELLADONNA("Belladonna", PatchImplementation.BELLADONNA, ItemID.CAVE_NIGHTSHADE, TickConstants.BELLADONNA, 5),
	CALQUAT("Calquat", PatchImplementation.CALQUAT, ItemID.CALQUAT_FRUIT, TickConstants.CALQUAT, 9, 0, 7),
	SPIRIT_TREE("Spirit tree", PatchImplementation.SPIRIT_TREE, ItemID.SPIRIT_TREE, TickConstants.SPIRIT_TREE, 13),
	CELASTRUS("Celastrus", "Celastrus tree", PatchImplementation.CELASTRUS, ItemID.BATTLESTAFF, TickConstants.CELASTRUS, 6, 0, 4),
	REDWOOD("Redwood", "Redwood tree", PatchImplementation.REDWOOD, ItemID.REDWOOD_LOGS, TickConstants.REDWOOD, 11),
	HESPORI("Hespori", PatchImplementation.HESPORI, NullItemID.NULL_23044, TickConstants.HESPORI, 4, 0, 2),
	CRYSTAL_TREE("Crystal tree", PatchImplementation.CRYSTAL_TREE, ItemID.CRYSTAL_SHARDS, TickConstants.CRYSTAL_TREE, 7),

	// Compost bins
	EMPTY_COMPOST_BIN("Compost Bin", PatchImplementation.COMPOST, ItemID.COMPOST_BIN, TickConstants.EMPTY_COMPOST_BIN, 1, 0, 0), // Dummy produce for the empty state
	COMPOST("Compost", PatchImplementation.COMPOST, ItemID.COMPOST, TickConstants.COMPOST, 3, 0, 15),
	SUPERCOMPOST("Supercompost", PatchImplementation.COMPOST, ItemID.SUPERCOMPOST, TickConstants.SUPERCOMPOST, 3, 0, 15),
	ULTRACOMPOST("Ultracompost", PatchImplementation.COMPOST, ItemID.ULTRACOMPOST, TickConstants.ULTRACOMPOST, 3, 0, 15), // Ultra doesn't compost,
	ROTTEN_TOMATO("Rotten Tomato", PatchImplementation.COMPOST, ItemID.ROTTEN_TOMATO, TickConstants.ROTTEN_TOMATO, 3, 0, 15),
	EMPTY_BIG_COMPOST_BIN("Big Compost Bin", PatchImplementation.COMPOST, ItemID.COMPOST_BIN, TickConstants.EMPTY_BIG_COMPOST_BIN, 1, 0, 0), // Dummy produce for the empty state
	BIG_COMPOST("Compost", PatchImplementation.BIG_COMPOST, ItemID.COMPOST, TickConstants.BIG_COMPOST, 3, 0, 30),
	BIG_SUPERCOMPOST("Supercompost", PatchImplementation.BIG_COMPOST, ItemID.SUPERCOMPOST, TickConstants.BIG_SUPERCOMPOST, 3, 0, 30),
	BIG_ULTRACOMPOST("Ultracompost", PatchImplementation.BIG_COMPOST, ItemID.ULTRACOMPOST, TickConstants.BIG_SUPERCOMPOST, 3, 0, 30), // Ultra doesn't compost
	BIG_ROTTEN_TOMATO("Rotten Tomato", PatchImplementation.BIG_COMPOST, ItemID.ROTTEN_TOMATO, TickConstants.BIG_ROTTEN_TOMATO, 3, 0, 30);
	/**
	 * User-visible name
	 */
	private final String name;
	/**
	 * Farming contract names
	 */
	private final String contractName;
	/**
	 * Patch type for the crop
	 */
	private final PatchImplementation patchImplementation;
	/**
	 * User-visible item icon
	 */
	private final int itemID;
	/**
	 * How many minutes per growth tick
	 */
	private final int tickrate;
	/**
	 * How many states this crop has during growth. Typically tickcount+1
	 */
	private final int stages;
	/**
	 * How many minutes to regrow crops, or zero if it doesn't regrow
	 */
	private final int regrowTickrate;
	/**
	 * How many states this crop has during harvest.
	 * This is often called lives.
	 */
	private final int harvestStages;

	Produce(String name, int itemID, int tickrate, int stages, int regrowTickrate, int harvestStages)
	{
		this(name, name, null, itemID, tickrate, stages, regrowTickrate, harvestStages);
	}

	Produce(String name, PatchImplementation patchImplementation, int itemID, int tickrate, int stages, int regrowTickrate, int harvestStages)
	{
		this(name, name, patchImplementation, itemID, tickrate, stages, regrowTickrate, harvestStages);
	}

	Produce(String name, String contractName, PatchImplementation patchImplementation, int itemID, int tickrate, int stages)
	{
		this(name, contractName, patchImplementation, itemID, tickrate, stages, 0, 1);
	}

	Produce(String name, PatchImplementation patchImplementation, int itemID, int tickrate, int stages)
	{
		this(name, name, patchImplementation, itemID, tickrate, stages, 0, 1);
	}

	Produce(String name, int itemID, int tickrate, int stages)
	{
		this(name, name, null, itemID, tickrate, stages, 0, 1);
	}

	@Nullable
	static Produce getByItemID(int itemId)
	{
		for (Produce produce : Produce.values())
		{
			if (produce.getItemID() == itemId)
			{
				return produce;
			}
		}
		return null;
	}

	@Nullable
	static Produce getByContractName(String contractName)
	{
		for (Produce produce : Produce.values())
		{
			if (produce.getContractName().equalsIgnoreCase(contractName))
			{
				return produce;
			}
		}
		return null;
	}
}
