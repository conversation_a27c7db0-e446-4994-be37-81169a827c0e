/*
 * Copyright (c) 2018, Lotto <https://github.com/devLotto>
 * Copyright (c) 2018, Soy<PERSON><PERSON> <https://github.com/SoyChai>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 *    list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 *    this list of conditions and the following disclaimer in the documentation
 *    and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR
 * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package net.runelite.api;

// Note: This class won't always be complete: these sprites were manually gathered
// through the cache and widget inspector. Please add new sprites as you happen to use them.
public final class SpriteID
{
	public static final int RS2_CHATBOX_BUTTONS = 0;
	public static final int RS2_TABS_ROW_BOTTOM = 1;
	public static final int RS2_TABS_ROW_TOP = 2;
	public static final int RS2_CHATBOX_EDGE_TOP = 3;
	public static final int RS2_WINDOW_FRAME_EDGE_LEFT = 4;
	public static final int RS2_CHATBOX_EDGE_LEFT = 5;
	public static final int RS2_MINIMAP_FRAME_EDGE_RIGHT = 6;
	public static final int RS2_SIDE_PANEL_EDGES_LEFT_AND_RIGHT = 7;
	public static final int RS2_WINDOW_FRAME_EDGE_TOP = 8;
	public static final int RS2_MINIMAP_FRAME_EDGE_LEFT = 9;
	public static final int RS2_SIDE_PANEL_EDGE_LEFT_UPPER = 10;
	public static final int RS2_SIDE_PANEL_EDGE_LEFT_LOWER_JOINING_CHATBOX_EDGE_RIGHT = 11;
	public static final int RS2_MINIMAP_FRAME = 12;
	public static final int RS2_CHATBOX_BACKGROUND = 13;
	public static final int RS2_SIDE_PANEL_BACKGROUND = 14;
	public static final int SPELL_WIND_STRIKE = 15;
	public static final int SPELL_CONFUSE = 16;
	public static final int SPELL_WATER_STRIKE = 17;
	public static final int SPELL_LVL_1_ENCHANT = 18;
	public static final int SPELL_EARTH_STRIKE = 19;
	public static final int SPELL_WEAKEN = 20;
	public static final int SPELL_FIRE_STRIKE = 21;
	public static final int SPELL_BONES_TO_BANANAS = 22;
	public static final int SPELL_WIND_BOLT = 23;
	public static final int SPELL_CURSE = 24;
	public static final int SPELL_LOW_LEVEL_ALCHEMY = 25;
	public static final int SPELL_WATER_BOLT = 26;
	public static final int SPELL_VARROCK_TELEPORT = 27;
	public static final int SPELL_LVL_2_ENCHANT = 28;
	public static final int SPELL_EARTH_BOLT = 29;
	public static final int SPELL_LUMBRIDGE_TELEPORT = 30;
	public static final int SPELL_TELEKINETIC_GRAB = 31;
	public static final int SPELL_FIRE_BOLT = 32;
	public static final int SPELL_FALADOR_TELEPORT = 33;
	public static final int SPELL_CRUMBLE_UNDEAD = 34;
	public static final int SPELL_WIND_BLAST = 35;
	public static final int SPELL_SUPERHEAT_ITEM = 36;
	public static final int SPELL_CAMELOT_TELEPORT = 37;
	public static final int SPELL_WATER_BLAST = 38;
	public static final int SPELL_LVL_3_ENCHANT = 39;
	public static final int SPELL_EARTH_BLAST = 40;
	public static final int SPELL_HIGH_LEVEL_ALCHEMY = 41;
	public static final int SPELL_CHARGE_WATER_ORB = 42;
	public static final int SPELL_LVL_4_ENCHANT = 43;
	public static final int SPELL_FIRE_BLAST = 44;
	public static final int SPELL_CHARGE_EARTH_ORB = 45;
	public static final int SPELL_WIND_WAVE = 46;
	public static final int SPELL_CHARGE_FIRE_ORB = 47;
	public static final int SPELL_WATER_WAVE = 48;
	public static final int SPELL_CHARGE_AIR_ORB = 49;
	public static final int SPELL_LVL_5_ENCHANT = 50;
	public static final int SPELL_EARTH_WAVE = 51;
	public static final int SPELL_FIRE_WAVE = 52;
	public static final int SPELL_IBAN_BLAST = 53;
	public static final int SPELL_ARDOUGNE_TELEPORT = 54;
	public static final int SPELL_WATCHTOWER_TELEPORT = 55;
	public static final int SPELL_VULNERABILITY = 56;
	public static final int SPELL_ENFEEBLE = 57;
	public static final int SPELL_STUN = 58;
	public static final int SPELL_FLAMES_OF_ZAMORAK = 59;
	public static final int SPELL_CLAWS_OF_GUTHIX = 60;
	public static final int SPELL_SARADOMIN_STRIKE = 61;
	public static final int UNUSED_SPELL_CALL_ANIMAL = 62;
	public static final int UNUSED_SPELL_RAISE_SKELETON = 63;
	public static final int UNUSED_SPELL_SUMMON_DEMON = 64;
	public static final int SPELL_WIND_STRIKE_DISABLED = 65;
	public static final int SPELL_CONFUSE_DISABLED = 66;
	public static final int SPELL_WATER_STRIKE_DISABLED = 67;
	public static final int SPELL_LVL_1_ENCHANT_DISABLED = 68;
	public static final int SPELL_EARTH_STRIKE_DISABLED = 69;
	public static final int SPELL_WEAKEN_DISABLED = 70;
	public static final int SPELL_FIRE_STRIKE_DISABLED = 71;
	public static final int SPELL_BONES_TO_BANANAS_DISABLED = 72;
	public static final int SPELL_WIND_BOLT_DISABLED = 73;
	public static final int SPELL_CURSE_DISABLED = 74;
	public static final int SPELL_LOW_LEVEL_ALCHEMY_DISABLED = 75;
	public static final int SPELL_WATER_BOLT_DISABLED = 76;
	public static final int SPELL_VARROCK_TELEPORT_DISABLED = 77;
	public static final int SPELL_LVL_2_ENCHANT_DISABLED = 78;
	public static final int SPELL_EARTH_BOLT_DISABLED = 79;
	public static final int SPELL_LUMBRIDGE_TELEPORT_DISABLED = 80;
	public static final int SPELL_TELEKINETIC_GRAB_DISABLED = 81;
	public static final int SPELL_FIRE_BOLT_DISABLED = 82;
	public static final int SPELL_FALADOR_TELEPORT_DISABLED = 83;
	public static final int SPELL_CRUMBLE_UNDEAD_DISABLED = 84;
	public static final int SPELL_WIND_BLAST_DISABLED = 85;
	public static final int SPELL_SUPERHEAT_ITEM_DISABLED = 86;
	public static final int SPELL_CAMELOT_TELEPORT_DISABLED = 87;
	public static final int SPELL_WATER_BLAST_DISABLED = 88;
	public static final int SPELL_LVL_3_ENCHANT_DISABLED = 89;
	public static final int SPELL_EARTH_BLAST_DISABLED = 90;
	public static final int SPELL_HIGH_LEVEL_ALCHEMY_DISABLED = 91;
	public static final int SPELL_CHARGE_WATER_ORB_DISABLED = 92;
	public static final int SPELL_LVL_4_ENCHANT_DISABLED = 93;
	public static final int SPELL_FIRE_BLAST_DISABLED = 94;
	public static final int SPELL_CHARGE_EARTH_ORB_DISABLED = 95;
	public static final int SPELL_WIND_WAVE_DISABLED = 96;
	public static final int SPELL_CHARGE_FIRE_ORB_DISABLED = 97;
	public static final int SPELL_WATER_WAVE_DISABLED = 98;
	public static final int SPELL_CHARGE_AIR_ORB_DISABLED = 99;
	public static final int SPELL_LVL_5_ENCHANT_DISABLED = 100;
	public static final int SPELL_EARTH_WAVE_DISABLED = 101;
	public static final int SPELL_FIRE_WAVE_DISABLED = 102;
	public static final int SPELL_IBAN_BLAST_DISABLED = 103;
	public static final int SPELL_ARDOUGNE_TELEPORT_DISABLED = 104;
	public static final int SPELL_WATCHTOWER_TELEPORT_DISABLED = 105;
	public static final int SPELL_VULNERABILITY_DISABLED = 106;
	public static final int SPELL_ENFEEBLE_DISABLED = 107;
	public static final int SPELL_STUN_DISABLED = 108;
	public static final int SPELL_FLAMES_OF_ZAMORAK_DISABLED = 109;
	public static final int SPELL_CLAWS_OF_GUTHIX_DISABLED = 110;
	public static final int SPELL_SARADOMIN_STRIKE_DISABLED = 111;
	public static final int UNUSED_SPELL_CALL_ANIMAL_DISABLED = 112;
	public static final int UNUSED_SPELL_RAISE_SKELETON_DISABLED = 113;
	public static final int UNUSED_SPELL_SUMMON_DEMON_DISABLED = 114;
	public static final int PRAYER_THICK_SKIN = 115;
	public static final int PRAYER_BURST_OF_STRENGTH = 116;
	public static final int PRAYER_CLARITY_OF_THOUGHT = 117;
	public static final int PRAYER_ROCK_SKIN = 118;
	public static final int PRAYER_SUPERHUMAN_STRENGTH = 119;
	public static final int PRAYER_IMPROVED_REFLEXES = 120;
	public static final int PRAYER_RAPID_RESTORE = 121;
	public static final int PRAYER_RAPID_HEAL = 122;
	public static final int PRAYER_PROTECT_ITEM = 123;
	public static final int PRAYER_STEEL_SKIN = 124;
	public static final int PRAYER_ULTIMATE_STRENGTH = 125;
	public static final int PRAYER_INCREDIBLE_REFLEXES = 126;
	public static final int PRAYER_PROTECT_FROM_MAGIC = 127;
	public static final int PRAYER_PROTECT_FROM_MISSILES = 128;
	public static final int PRAYER_PROTECT_FROM_MELEE = 129;
	public static final int PRAYER_REDEMPTION = 130;
	public static final int PRAYER_RETRIBUTION = 131;
	public static final int PRAYER_SMITE = 132;
	public static final int PRAYER_SHARP_EYE = 133;
	public static final int PRAYER_MYSTIC_WILL = 134;
	public static final int PRAYER_THICK_SKIN_DISABLED = 135;
	public static final int PRAYER_BURST_OF_STRENGTH_DISABLED = 136;
	public static final int PRAYER_CLARITY_OF_THOUGHT_DISABLED = 137;
	public static final int PRAYER_ROCK_SKIN_DISABLED = 138;
	public static final int PRAYER_SUPERHUMAN_STRENGTH_DISABLED = 139;
	public static final int PRAYER_IMPROVED_REFLEXES_DISABLED = 140;
	public static final int PRAYER_RAPID_RESTORE_DISABLED = 141;
	public static final int PRAYER_RAPID_HEAL_DISABLED = 142;
	public static final int PRAYER_PROTECT_ITEM_DISABLED = 143;
	public static final int PRAYER_STEEL_SKIN_DISABLED = 144;
	public static final int PRAYER_ULTIMATE_STRENGTH_DISABLED = 145;
	public static final int PRAYER_INCREDIBLE_REFLEXES_DISABLED = 146;
	public static final int PRAYER_PROTECT_FROM_MAGIC_DISABLED = 147;
	public static final int PRAYER_PROTECT_FROM_MISSILES_DISABLED = 148;
	public static final int PRAYER_PROTECT_FROM_MELEE_DISABLED = 149;
	public static final int PRAYER_REDEMPTION_DISABLED = 150;
	public static final int PRAYER_RETRIBUTION_DISABLED = 151;
	public static final int PRAYER_SMITE_DISABLED = 152;
	public static final int PRAYER_SHARP_EYE_DISABLED = 153;
	public static final int PRAYER_MYSTIC_WILL_DISABLED = 154;
	public static final int ACTIVATED_PRAYER_BACKGROUND = 155;
	public static final int EQUIPMENT_SLOT_HEAD = 156;
	public static final int EQUIPMENT_SLOT_CAPE = 157;
	public static final int EQUIPMENT_SLOT_NECK = 158;
	public static final int EQUIPMENT_SLOT_WEAPON = 159;
	public static final int EQUIPMENT_SLOT_RING = 160;
	public static final int EQUIPMENT_SLOT_TORSO = 161;
	public static final int EQUIPMENT_SLOT_SHIELD = 162;
	public static final int EQUIPMENT_SLOT_LEGS = 163;
	public static final int EQUIPMENT_SLOT_HANDS = 164;
	public static final int EQUIPMENT_SLOT_FEET = 165;
	public static final int EQUIPMENT_SLOT_AMMUNITION = 166;
	/* Unmapped: 167 */
	public static final int TAB_COMBAT = 168;
	public static final int COMPASS_TEXTURE = 169;
	public static final int EQUIPMENT_SLOT_TILE = 170;
	public static final int IRON_RIVETS_SQUARE = 171;
	public static final int IRON_RIVETS_VERTICAL = 172;
	public static final int IRON_RIVETS_HORIZONTAL = 173;
	public static final int STATS_TILE_HALF_LEFT = 174;
	public static final int STATS_TILE_HALF_RIGHT_WITH_SLASH = 175;
	public static final int STATS_TILE_HALF_RIGHT = 176;
	public static final int STATS_TILE_HALF_LEFT_SELECTED = 177;
	public static final int STATS_TILE_HALF_RIGHT_SELECTED = 178;
	public static final int EQUIPMENT_SLOT_SELECTED = 179;
	public static final int PURO_PURO_ROUND_CHECK_BOX = 180;
	public static final int PURO_PURO_ROUND_CHECK_BOX_CHECKED_RED = 181;
	public static final int STATS_TILE_HALF_RIGHT_WITH_SLASH_SELECTED = 182;
	public static final int STATS_TILE_HALF_LEFT_BLACK = 183;
	public static final int STATS_TILE_HALF_RIGHT_BLACK = 184;
	public static final int MUSIC_PLAYER_BUTTON = 185;
	public static final int MUSIC_PLAYER_BUTTON_SELECTED = 186;
	/* Unmapped: 187~194 */
	public static final int UNKNOWN_BUTTON_SQUARE_SMALL = 195;
	public static final int UNKNOWN_BUTTON_SQUARE_SMALL_SELECTED = 196;
	public static final int SKILL_ATTACK = 197;
	public static final int SKILL_STRENGTH = 198;
	public static final int SKILL_DEFENCE = 199;
	public static final int SKILL_RANGED = 200;
	public static final int SKILL_PRAYER = 201;
	public static final int SKILL_MAGIC = 202;
	public static final int SKILL_HITPOINTS = 203;
	public static final int SKILL_AGILITY = 204;
	public static final int SKILL_HERBLORE = 205;
	public static final int SKILL_THIEVING = 206;
	public static final int SKILL_CRAFTING = 207;
	public static final int SKILL_FLETCHING = 208;
	public static final int SKILL_MINING = 209;
	public static final int SKILL_SMITHING = 210;
	public static final int SKILL_FISHING = 211;
	public static final int SKILL_COOKING = 212;
	public static final int SKILL_FIREMAKING = 213;
	public static final int SKILL_WOODCUTTING = 214;
	public static final int SKILL_RUNECRAFT = 215;
	public static final int SKILL_SLAYER = 216;
	public static final int SKILL_FARMING = 217;
	public static final int UNKNOWN_SHOVEL = 218;
	public static final int RAT_PITS_ZONE_RAT = 219;
	public static final int SKILL_HUNTER = 220;
	public static final int SKILL_CONSTRUCTION = 221;
	public static final int SKILL_TOTAL = 222;
	public static final int UNKNOWN_EMPTY_VIAL = 223;
	public static final int UNKNOWN_DRAGON_DAGGER_P = 224;
	/* Unmapped: 225~232 */
	public static final int COMBAT_STYLE_AXE_BLOCK = 233;
	public static final int COMBAT_STYLE_AXE_CHOP = 234;
	public static final int COMBAT_STYLE_AXE_HACK = 235;
	public static final int COMBAT_STYLE_AXE_SMASH = 236;
	public static final int COMBAT_STYLE_SWORD_BLOCK = 237;
	public static final int COMBAT_STYLE_SWORD_SLASH = 238;
	public static final int COMBAT_STYLE_SWORD_CHOP = 239;
	public static final int COMBAT_STYLE_SWORD_STAB = 240;
	public static final int COMBAT_STYLE_SPEAR_LUNGE = 241;
	public static final int COMBAT_STYLE_SPEAR_POUND = 242;
	public static final int COMBAT_STYLE_MACE_BLOCK = 243;
	public static final int COMBAT_STYLE_MACE_PUMMEL = 244;
	public static final int COMBAT_STYLE_MACE_SPIKE = 245;
	public static final int COMBAT_STYLE_MACE_POUND = 246;
	public static final int COMBAT_STYLE_UNARMED_PUNCH = 247;
	public static final int COMBAT_STYLE_UNARMED_KICK = 248;
	public static final int COMBAT_STYLE_UNARMED_BLOCK = 249;
	public static final int COMBAT_STYLE_SPEAR_BLOCK = 250;
	public static final int COMBAT_STYLE_SPEAR_SWIPE = 251;
	public static final int COMBAT_STYLE_STAFF_BLOCK = 252;
	public static final int COMBAT_STYLE_HAMMER_BLOCK = 253;
	public static final int UNUSED_COMBAT_STYLE_HAMMER = 254;
	public static final int COMBAT_STYLE_HAMMER_POUND = 255;
	public static final int COMBAT_STYLE_HAMMER_PUMMEL = 256;
	public static final int UNUSED_COMBAT_STYLE_STAKE = 257;
	public static final int COMBAT_STYLE_CROSSBOW_ACCURATE = 258;
	public static final int COMBAT_STYLE_CROSSBOW_RAPID = 259;
	public static final int COMBAT_STYLE_CROSSBOW_LONGRANGE = 260;
	public static final int COMBAT_STYLE_SCYTHE_BLOCK = 261;
	public static final int COMBAT_STYLE_SCYTHE_CHOP = 262;
	public static final int COMBAT_STYLE_MAGIC_ACCURATE = 263;
	public static final int COMBAT_STYLE_MAGIC_RAPID = 264;
	public static final int COMBAT_STYLE_MAGIC_LONGRANGE = 265;
	public static final int COMBAT_STYLE_STAFF_BASH = 266;
	public static final int COMBAT_STYLE_STAFF_POUND = 267;
	public static final int COMBAT_STYLE_BOW_ACCURATE = 268;
	public static final int COMBAT_STYLE_BOW_RAPID = 269;
	public static final int COMBAT_STYLE_BOW_LONGRANGE = 270;
	public static final int COMBAT_STYLE_SCYTHE_JAB = 271;
	public static final int COMBAT_STYLE_SCYTHE_REAP = 272;
	public static final int COMBAT_STYLE_PICKAXE_BLOCK = 273;
	public static final int COMBAT_STYLE_PICKAXE_SPIKE = 274;
	public static final int COMBAT_STYLE_PICKAXE_SMASH = 275;
	public static final int COMBAT_STYLE_PICKAXE_IMPALE = 276;
	public static final int COMBAT_STYLE_CLAWS_LUNGE = 277;
	public static final int COMBAT_STYLE_CLAWS_SLASH = 278;
	public static final int COMBAT_STYLE_CLAWS_CHOP = 279;
	public static final int COMBAT_STYLE_CLAWS_BLOCK = 280;
	public static final int COMBAT_STYLE_CHINCHOMPA_LONG_FUSE = 281;
	public static final int COMBAT_STYLE_CHINCHOMPA_MEDIUM_FUSE = 282;
	public static final int COMBAT_STYLE_HALBERD_BLOCK = 283;
	public static final int COMBAT_STYLE_HALBERD_JAB = 284;
	public static final int COMBAT_STYLE_HALBERD_SWIPE = 285;
	public static final int COMBAT_STYLE_WHIP_FLICK = 286;
	public static final int COMBAT_STYLE_WHIP_LASH = 287;
	public static final int COMBAT_STYLE_CHINCHOMPA_SHORT_FUSE = 288;
	public static final int COMBAT_STYLE_SALAMANDER_SCORCH = 289;
	public static final int COMBAT_STYLE_SALAMANDER_FLARE = 290;
	public static final int COMBAT_STYLE_SALAMANDER_BLAZE = 291;
	/* Unmapped: 292 */
	public static final int COMBAT_STYLE_BUTTON_NARROW = 293;
	public static final int COMBAT_STYLE_BUTTON_NARROW_SELECTED = 294;
	public static final int COMBAT_STYLE_BUTTON_THIN = 295;
	public static final int COMBAT_STYLE_BUTTON_THIN_SELECTED = 296;
	public static final int DIALOG_BACKGROUND = 297;
	/* Unmapped: 298 */
	public static final int RS2_YELLOW_CLICK_ANIMATION_1 = 299;
	public static final int RS2_MINIMAP_MARKER_RED_ITEM = 300;
	public static final int RS2_SWORD_POINTED_LEFT = 301;
	public static final int RS2_SWORD_POINTED_RIGHT = 302;
	public static final int RS2_SWORD_POINTED_RIGHT_SHADOWED = 303;
	public static final int RS2_SWORD_POINTED_LEFT_SHADOWED = 304;
	public static final int RS2_TAB_STONE_FAR_LEFT_SELECTED = 305;
	public static final int RS2_TAB_STONE_MIDDLE_LEFT_SELECTED = 306;
	public static final int RS2_TAB_STONE_MIDDLE_SELECTED = 307;
	public static final int RS2_BUTTON_BACK_ARROW = 308;
	public static final int RS2_BUTTON_FORWARD_ARROW = 309;
	public static final int IRON_RIVETS_CORNER_TOP_LEFT = 310;
	public static final int IRON_RIVETS_CORNER_TOP_RIGHT = 311;
	public static final int IRON_RIVETS_CORNER_BOTTOM_LEFT = 312;
	public static final int IRON_RIVETS_CORNER_BOTTOM_RIGHT = 313;
	public static final int IRON_RIVETS_EDGE_TOP = 314;
	public static final int IRON_RIVETS_EDGE_RIGHT = 315;
	public static final int RS2_SCROLLBAR_ARROW_UP = 316;
	public static final int MAP_ICON_SMALL_TREE = 317;
	public static final int TEXTURE_INFERNAL_CAPE = 318;
	public static final int SPELL_BIND = 319;
	public static final int SPELL_SNARE = 320;
	public static final int SPELL_ENTANGLE = 321;
	public static final int SPELL_CHARGE = 322;
	public static final int SPELL_TROLLHEIM_TELEPORT = 323;
	public static final int SPELL_MAGIC_DART = 324;
	public static final int SPELL_ICE_RUSH = 325;
	public static final int SPELL_ICE_BURST = 326;
	public static final int SPELL_ICE_BLITZ = 327;
	public static final int SPELL_ICE_BARRAGE = 328;
	public static final int SPELL_SMOKE_RUSH = 329;
	public static final int SPELL_SMOKE_BURST = 330;
	public static final int SPELL_SMOKE_BLITZ = 331;
	public static final int SPELL_SMOKE_BARRAGE = 332;
	public static final int SPELL_BLOOD_RUSH = 333;
	public static final int SPELL_BLOOD_BURST = 334;
	public static final int SPELL_BLOOD_BLITZ = 335;
	public static final int SPELL_BLOOD_BARRAGE = 336;
	public static final int SPELL_SHADOW_RUSH = 337;
	public static final int SPELL_SHADOW_BURST = 338;
	public static final int SPELL_SHADOW_BLITZ = 339;
	public static final int SPELL_SHADOW_BARRAGE = 340;
	public static final int SPELL_PADDEWWA_TELEPORT = 341;
	public static final int SPELL_SENNTISTEN_TELEPORT = 342;
	public static final int SPELL_KHARYRLL_TELEPORT = 343;
	public static final int SPELL_LASSAR_TELEPORT = 344;
	public static final int SPELL_DAREEYAK_TELEPORT = 345;
	public static final int SPELL_CARRALLANGAR_TELEPORT = 346;
	public static final int SPELL_ANNAKARL_TELEPORT = 347;
	public static final int SPELL_GHORROCK_TELEPORT = 348;
	public static final int SPELL_TELEOTHER_LUMBRIDGE = 349;
	public static final int SPELL_TELEOTHER_FALADOR = 350;
	public static final int SPELL_TELEOTHER_CAMELOT = 351;
	public static final int SPELL_TELE_BLOCK = 352;
	public static final int SPELL_LVL_6_ENCHANT = 353;
	public static final int SPELL_BONES_TO_PEACHES = 354;
	public static final int SPELL_TELEPORT_TO_HOUSE = 355;
	public static final int SPELL_LUMBRIDGE_HOME_TELEPORT = 356;
	public static final int SPELL_TELEPORT_TO_APE_ATOLL = 357;
	public static final int SPELL_ENCHANT_CROSSBOW_BOLT = 358;
	public static final int SPELL_TELEPORT_TO_BOUNTY_TARGET = 359;
	public static final int SPELL_TELEPORT_TO_KOUREND = 360;
	public static final int SPELL_LVL_7_ENCHANT = 361;
	public static final int SPELL_WIND_SURGE = 362;
	public static final int SPELL_WATER_SURGE = 363;
	public static final int SPELL_EARTH_SURGE = 364;
	public static final int SPELL_FIRE_SURGE = 365;
	/* Unmapped: 366, 367, 368 */
	public static final int SPELL_BIND_DISABLED = 369;
	public static final int SPELL_SNARE_DISABLED = 370;
	public static final int SPELL_ENTANGLE_DISABLED = 371;
	public static final int SPELL_CHARGE_DISABLED = 372;
	public static final int SPELL_TROLLHEIM_TELEPORT_DISABLED = 373;
	public static final int SPELL_MAGIC_DART_DISABLED = 374;
	public static final int SPELL_ICE_RUSH_DISABLED = 375;
	public static final int SPELL_ICE_BURST_DISABLED = 376;
	public static final int SPELL_ICE_BLITZ_DISABLED = 377;
	public static final int SPELL_ICE_BARRAGE_DISABLED = 378;
	public static final int SPELL_SMOKE_RUSH_DISABLED = 379;
	public static final int SPELL_SMOKE_BURST_DISABLED = 380;
	public static final int SPELL_SMOKE_BLITZ_DISABLED = 381;
	public static final int SPELL_SMOKE_BARRAGE_DISABLED = 382;
	public static final int SPELL_BLOOD_RUSH_DISABLED = 383;
	public static final int SPELL_BLOOD_BURST_DISABLED = 384;
	public static final int SPELL_BLOOD_BLITZ_DISABLED = 385;
	public static final int SPELL_BLOOD_BARRAGE_DISABLED = 386;
	public static final int SPELL_SHADOW_RUSH_DISABLED = 387;
	public static final int SPELL_SHADOW_BURST_DISABLED = 388;
	public static final int SPELL_SHADOW_BLITZ_DISABLED = 389;
	public static final int SPELL_SHADOW_BARRAGE_DISABLED = 390;
	public static final int SPELL_PADDEWWA_TELEPORT_DISABLED = 391;
	public static final int SPELL_SENNTISTEN_TELEPORT_DISABLED = 392;
	public static final int SPELL_KHARYRLL_TELEPORT_DISABLED = 393;
	public static final int SPELL_LASSAR_TELEPORT_DISABLED = 394;
	public static final int SPELL_DAREEYAK_TELEPORT_DISABLED = 395;
	public static final int SPELL_CARRALLANGAR_TELEPORT_DISABLED = 396;
	public static final int SPELL_ANNAKARL_TELEPORT_DISABLED = 397;
	public static final int SPELL_GHORROCK_TELEPORT_DISABLED = 398;
	public static final int SPELL_TELEOTHER_LUMBRIDGE_DISABLED = 399;
	public static final int SPELL_TELEOTHER_FALADOR_DISABLED = 400;
	public static final int SPELL_TELEOTHER_CAMELOT_DISABLED = 401;
	public static final int SPELL_TELE_BLOCK_DISABLED = 402;
	public static final int SPELL_LVL_6_ENCHANT_DISABLED = 403;
	public static final int SPELL_BONES_TO_PEACHES_DISABLED = 404;
	public static final int SPELL_TELEPORT_TO_HOUSE_DISABLED = 405;
	public static final int SPELL_LUMBRIDGE_HOME_TELEPORT_DISABLED = 406;
	public static final int SPELL_TELEPORT_TO_APE_ATOLL_DISABLED = 407;
	public static final int SPELL_ENCHANT_CROSSBOW_BOLT_DISABLED = 408;
	public static final int SPELL_TELEPORT_TO_BOUNTY_TARGET_DISABLED = 409;
	public static final int SPELL_TELEPORT_TO_KOUREND_DISABLED = 410;
	public static final int SPELL_LVL_7_ENCHANT_DISABLED = 411;
	public static final int SPELL_WIND_SURGE_DISABLED = 412;
	public static final int SPELL_WATER_SURGE_DISABLED = 413;
	public static final int SPELL_EARTH_SURGE_DISABLED = 414;
	public static final int SPELL_FIRE_SURGE_DISABLED = 415;
	/* Unmapped: 416, 417, 418 */
	public static final int UNKNOWN_STANCE_ICON_1 = 419;
	public static final int UNKNOWN_STANCE_ICON_2 = 420;
	public static final int UNKNOWN_STANCE_ICON_3 = 421;
	public static final int MINIMAP_DESTINATION_FLAG = 422;
	public static final int CHATBOX_BADGE_CROWN_PLAYER_MODERATOR = 423;
	public static final int RED_GUIDE_ARROW = 424;
	public static final int BACK_ARROW_BUTTON_SMALL = 425;
	public static final int FORWARD_ARROW_BUTTON_SMALL = 426;
	public static final int UNKNOWN_X_3D_RENDER = 427;
	public static final int WELCOME_SCREEN_BUTTON_MARBLE = 428;
	public static final int WELCOME_SCREEN_BUTTON_CLICK_HERE_TO_PLAY = 429;
	public static final int WELCOME_SCREEN_BANK_CHEST = 430;
	public static final int WELCOME_SCREEN_COINS = 431;
	public static final int WELCOME_SCREEN_KEY = 432;
	public static final int WELCOME_SCREEN_KEYRING = 433;
	public static final int WELCOME_SCREEN_PEN_AND_INKPOT = 434;
	public static final int WELCOME_SCREEN_SWORD = 435;
	public static final int WELCOME_SCREEN_SCROLL_MESSAGE_OF_THE_WEEK = 436;
	public static final int WELCOME_SCREEN_SEALED_ENVELOPE = 437;
	public static final int WELCOME_SCREEN_BUTTON_COBBLESTONE = 438;
	public static final int PLAYER_KILLER_SKULL = 439;
	public static final int OVERHEAD_PROTECT_FROM_MELEE = 440;
	public static final int MINIMAP_GUIDE_ARROW_YELLOW = 441;
	public static final int MULTI_COMBAT_ZONE_CROSSED_SWORDS = 442;
	public static final int DUEL_ARENA_ZONE_SHINING_AXE = 443;
	public static final int BANK_PIN_MARBLE_BACKGROUND = 444;
	public static final int BANK_PIN_MARBLE_BACKGROUND_RED = 445;
	public static final int BANK_PIN_MARBLE_BUTTON_RED = 446;
	public static final int TEXTURE_TRAPDOOR = 447;
	public static final int TEXTURE_WATER = 448;
	public static final int TEXTURE_BRICKS_STONE = 449;
	public static final int TEXTURE_BRICKS = 450;
	public static final int TEXTURE_CHEST = 451;
	public static final int TEXTURE_WOOD_DARK = 452;
	public static final int TEXTURE_ROOF_TILES = 453;
	public static final int TEXTURE_454 = 454;
	public static final int TEXTURE_LEAVES = 455;
	public static final int TEXTURE_TREE_STUMP = 456;
	public static final int TEXTURE_STONE = 457;
	public static final int TEXTURE_PORTCULLIS = 458;
	public static final int TEXTURE_PAINTING_MOUNTAIN = 459;
	public static final int TEXTURE_PAINTING_KING = 460;
	public static final int TEXTURE_MARBLE = 461;
	public static final int TEXTURE_WOOD = 462;
	public static final int TEXTURE_FALLING_RAIN_EFFECT = 463;
	public static final int TEXTURE_WOOD_PALE = 464;
	public static final int TEXTURE_SCALY = 465;
	public static final int TEXTURE_BOOKSHELVES = 466;
	public static final int TEXTURE_ROOF_TILES_SCALY_EDGE = 467;
	public static final int TEXTURE_PLANKS = 468;
	public static final int TEXTURE_BRICKS_STONE_DIRTY = 469;
	public static final int TEXTURE_WATER_TURQUOISE = 470;
	public static final int TEXTURE_COBWEB = 471;
	public static final int TEXTURE_ROOF_TILES_SCALY = 472;
	public static final int TEXTURE_473 = 473;
	public static final int TEXTURE_FERN_LEAF = 474;
	public static final int TEXTURE_LEAVES_475 = 475;
	public static final int TEXTURE_LAVA = 476;
	public static final int TEXTURE_477 = 477;
	public static final int TEXTURE_LEAVES_MAPLE = 478;
	public static final int TEXTURE_MAGIC_TREE_STARS_EFFECT = 479;
	public static final int TEXTURE_BRICKS_COBBLESTONE = 480;
	public static final int TEXTURE_481 = 481;
	public static final int TEXTURE_482 = 482;
	public static final int TEXTURE_483 = 483;
	public static final int TEXTURE_PAINTING_ELVEN_ARCHER = 484;
	public static final int TEXTURE_LAVA_485 = 485;
	public static final int TEXTURE_LEAVES_486 = 486;
	public static final int TEXTURE_TILES_STONE = 487;
	public static final int TEXTURE_ROOF_TILES_STONE = 488;
	public static final int UNKNOWN_SMALL_GREEN_UP_ARROW = 489;
	public static final int TEXTURE_COBBLESTONE = 490;
	public static final int TEXTURE_BRICKS_SANDSTONE = 491;
	public static final int TEXTURE_HEIROGLYPHS = 492;
	public static final int TEXTURE_493 = 493;
	/* Unmapped: 494~497 */
	public static final int LOGIN_SCREEN_RUNESCAPE_LOGO = 498;
	public static final int LOGIN_SCREEN_DIALOG_BACKGROUND = 499;
	public static final int LOGIN_SCREEN_BUTTON_BACKGROUND = 500;
	public static final int UNKNOWN_FIRE_RUNE_ALPHA_MASK = 501;
	public static final int PRAYER_HAWK_EYE = 502;
	public static final int PRAYER_MYSTIC_LORE = 503;
	public static final int PRAYER_EAGLE_EYE = 504;
	public static final int PRAYER_MYSTIC_MIGHT = 505;
	public static final int PRAYER_HAWK_EYE_DISABLED = 506;
	public static final int PRAYER_MYSTIC_LORE_DISABLED = 507;
	public static final int PRAYER_EAGLE_EYE_DISABLED = 508;
	public static final int PRAYER_MYSTIC_MIGHT_DISABLED = 509;
	public static final int MINIMAP_MARKER_RED_ITEM = 510;
	public static final int MINIMAP_MARKER_YELLOW_NPC = 511;
	public static final int MINIMAP_MARKER_WHITE_PLAYER = 512;
	public static final int MINIMAP_MARKER_GREEN_PLAYER_FRIEND = 513;
	public static final int MINIMAP_MARKER_BLUE_PLAYER_TEAM_CAPE = 514;
	public static final int YELLOW_CLICK_ANIMATION_1 = 515;
	public static final int YELLOW_CLICK_ANIMATION_2 = 516;
	public static final int YELLOW_CLICK_ANIMATION_3 = 517;
	public static final int YELLOW_CLICK_ANIMATION_4 = 518;
	public static final int RED_CLICK_ANIMATION_1 = 519;
	public static final int RED_CLICK_ANIMATION_2 = 520;
	public static final int RED_CLICK_ANIMATION_3 = 521;
	public static final int RED_CLICK_ANIMATION_4 = 522;
	public static final int PLAYER_KILLER_SKULL_523 = 523;
	public static final int FIGHT_PITS_WINNER_SKULL_RED = 524;
	public static final int BOUNTY_HUNTER_TARGET_WEALTH_5_VERY_HIGH = 525;
	public static final int BOUNTY_HUNTER_TARGET_WEALTH_4_HIGH = 526;
	public static final int BOUNTY_HUNTER_TARGET_WEALTH_3_MEDIUM = 527;
	public static final int BOUNTY_HUNTER_TARGET_WEALTH_2_LOW = 528;
	public static final int HOUSE_LOADING_SCREEN = 529;
	public static final int TEXTURE_ROUGH_STONE = 530;
	public static final int TEXTURE_WATER_531 = 531;
	public static final int TEXTURE_MARBLE_ROUGH = 532;
	public static final int TEXTURE_ROOF_TILES_SLATE_GREY = 533;
	public static final int UNKNOWN_SMALL_RED_DOWN_ARROW = 534;
	public static final int WINDOW_CLOSE_BUTTON = 535;
	public static final int WINDOW_CLOSE_BUTTON_HOVERED = 536;
	public static final int WINDOW_CLOSE_BUTTON_PARCHMENT = 537;
	public static final int WINDOW_CLOSE_BUTTON_PARCHMENT_HOVERED = 538;
	public static final int WINDOW_CLOSE_BUTTON_RED_X = 539;
	public static final int WINDOW_CLOSE_BUTTON_RED_X_HOVERED = 540;
	public static final int WINDOW_CLOSE_BUTTON_BROWN_X = 541;
	public static final int WINDOW_CLOSE_BUTTON_BROWN_X_HOVERED = 542;
	public static final int SPELL_BAKE_PIE = 543;
	public static final int SPELL_MOONCLAN_TELEPORT = 544;
	public static final int SPELL_WATERBIRTH_TELEPORT = 545;
	public static final int UNUSED_SPELL_BOW_AND_ARROW = 546;
	public static final int SPELL_BARBARIAN_TELEPORT = 547;
	public static final int SPELL_SUPERGLASS_MAKE = 548;
	public static final int SPELL_KHAZARD_TELEPORT = 549;
	public static final int SPELL_STRING_JEWELLERY = 550;
	public static final int SPELL_BOOST_POTION_SHARE = 551;
	public static final int SPELL_MAGIC_IMBUE = 552;
	public static final int SPELL_FERTILE_SOIL = 553;
	public static final int SPELL_STAT_RESTORE_POT_SHARE = 554;
	public static final int SPELL_FISHING_GUILD_TELEPORT = 555;
	public static final int SPELL_CATHERBY_TELEPORT = 556;
	public static final int SPELL_ICE_PLATEAU_TELEPORT = 557;
	public static final int SPELL_ENERGY_TRANSFER = 558;
	public static final int SPELL_CURE_OTHER = 559;
	public static final int SPELL_HEAL_OTHER = 560;
	public static final int SPELL_VENGEANCE_OTHER = 561;
	public static final int SPELL_CURE_ME = 562;
	public static final int SPELL_GEOMANCY = 563;
	public static final int SPELL_VENGEANCE = 564;
	public static final int SPELL_CURE_GROUP = 565;
	public static final int SPELL_HEAL_GROUP = 566;
	public static final int SPELL_CURE_PLANT = 567;
	public static final int SPELL_NPC_CONTACT = 568;
	public static final int SPELL_TELE_GROUP_MOONCLAN = 569;
	public static final int SPELL_TELE_GROUP_WATERBIRTH = 570;
	public static final int SPELL_TELE_GROUP_BARBARIAN = 571;
	public static final int SPELL_TELE_GROUP_KHAZARD = 572;
	public static final int SPELL_TELE_GROUP_FISHING_GUILD = 573;
	public static final int SPELL_TELE_GROUP_CATHERBY = 574;
	public static final int SPELL_TELE_GROUP_ICE_PLATEAU = 575;
	public static final int SPELL_STAT_SPY = 576;
	public static final int SPELL_MONSTER_EXAMINE = 577;
	public static final int SPELL_HUMIDIFY = 578;
	public static final int SPELL_HUNTER_KIT = 579;
	public static final int SPELL_DREAM = 580;
	public static final int SPELL_PLANK_MAKE = 581;
	public static final int SPELL_SPELLBOOK_SWAP = 582;
	public static final int SPELL_TAN_LEATHER = 583;
	public static final int SPELL_RECHARGE_DRAGONSTONE = 584;
	public static final int SPELL_SPIN_FLAX = 585;
	public static final int SPELL_OURANIA_TELEPORT = 586;
	/* Unmapped: 587~592 */
	public static final int SPELL_BAKE_PIE_DISABLED = 593;
	public static final int SPELL_MOONCLAN_TELEPORT_DISABLED = 594;
	public static final int SPELL_WATERBIRTH_TELEPORT_DISABLED = 595;
	public static final int UNUSED_SPELL_BOW_AND_ARROW_DISABLED = 596;
	public static final int SPELL_BARBARIAN_TELEPORT_DISABLED = 597;
	public static final int SPELL_SUPERGLASS_MAKE_DISABLED = 598;
	public static final int SPELL_KHAZARD_TELEPORT_DISABLED = 599;
	public static final int SPELL_STRING_JEWELLERY_DISABLED = 600;
	public static final int SPELL_BOOST_POTION_SHARE_DISABLED = 601;
	public static final int SPELL_MAGIC_IMBUE_DISABLED = 602;
	public static final int SPELL_FERTILE_SOIL_DISABLED = 603;
	public static final int SPELL_STAT_RESTORE_POT_SHARE_DISABLED = 604;
	public static final int SPELL_FISHING_GUILD_TELEPORT_DISABLED = 605;
	public static final int SPELL_CATHERBY_TELEPORT_DISABLED = 606;
	public static final int SPELL_ICE_PLATEAU_TELEPORT_DISABLED = 607;
	public static final int SPELL_ENERGY_TRANSFER_DISABLED = 608;
	public static final int SPELL_CURE_OTHER_DISABLED = 609;
	public static final int SPELL_HEAL_OTHER_DISABLED = 610;
	public static final int SPELL_VENGEANCE_OTHER_DISABLED = 611;
	public static final int SPELL_CURE_ME_DISABLED = 612;
	public static final int SPELL_GEOMANCY_DISABLED = 613;
	public static final int SPELL_VENGEANCE_DISABLED = 614;
	public static final int SPELL_CURE_GROUP_DISABLED = 615;
	public static final int SPELL_HEAL_GROUP_DISABLED = 616;
	public static final int SPELL_CURE_PLANT_DISABLED = 617;
	public static final int SPELL_NPC_CONTACT_DISABLED = 618;
	public static final int SPELL_TELE_GROUP_MOONCLAN_DISABLED = 619;
	public static final int SPELL_TELE_GROUP_WATERBIRTH_DISABLED = 620;
	public static final int SPELL_TELE_GROUP_BARBARIAN_DISABLED = 621;
	public static final int SPELL_TELE_GROUP_KHAZARD_DISABLED = 622;
	public static final int SPELL_TELE_GROUP_FISHING_GUILD_DISABLED = 623;
	public static final int SPELL_TELE_GROUP_CATHERBY_DISABLED = 624;
	public static final int SPELL_TELE_GROUP_ICE_PLATEAU_DISABLED = 625;
	public static final int SPELL_STAT_SPY_DISABLED = 626;
	public static final int SPELL_MONSTER_EXAMINE_DISABLED = 627;
	public static final int SPELL_HUMIDIFY_DISABLED = 628;
	public static final int SPELL_HUNTER_KIT_DISABLED = 629;
	public static final int SPELL_DREAM_DISABLED = 630;
	public static final int SPELL_PLANK_MAKE_DISABLED = 631;
	public static final int SPELL_SPELLBOOK_SWAP_DISABLED = 632;
	public static final int SPELL_TAN_LEATHER_DISABLED = 633;
	public static final int SPELL_RECHARGE_DRAGONSTONE_DISABLED = 634;
	public static final int SPELL_SPIN_FLAX_DISABLED = 635;
	public static final int SPELL_OURANIA_TELEPORT_DISABLED = 636;
	/* Unmapped: 637~642 */
	public static final int TEXTURE_ROOF_TILES_SLATE_DIRTY = 643;
	public static final int TEXTURE_POLISHED_TIMBER = 644;
	/* Unmapped: 645~648 */
	public static final int EQUIPMENT_WEIGHT = 649;
	public static final int WORLD_MAP_KEY_EFFECTS_THUNDERBOLT = 650;
	public static final int UNKNOWN_PRAYER_ICON = 651;
	public static final int UNKNOWN_BUTTON_LONG_NARROW = 652;
	public static final int COMBAT_STYLE_BUTTON = 653;
	public static final int COMBAT_STYLE_BUTTON_SELECTED = 654;
	public static final int COMBAT_AUTO_RETALIATE_BUTTON = 655;
	public static final int COMBAT_AUTO_RETALIATE_BUTTON_SELECTED = 656;
	public static final int COMBAT_SPECIAL_ATTACK_BUTTON = 657;
	public static final int UNKNOWN_COMBAT_BUTTON = 658;
	public static final int OPTIONS_SCREEN_BRIGHTNESS = 659;
	public static final int OPTIONS_MUSIC_VOLUME = 660;
	public static final int OPTIONS_SOUND_EFFECT_VOLUME = 661;
	public static final int OPTIONS_CHAT_EFFECTS = 662;
	public static final int OPTIONS_MOUSE_BUTTONS = 663;
	public static final int OPTIONS_SPLIT_PRIVATE_CHAT = 664;
	public static final int OPTIONS_ACCEPT_AID = 665;
	public static final int OPTIONS_MUSIC_DISABLED = 666;
	public static final int OPTIONS_SOUND_EFFECTS_DISABLED = 667;
	public static final int UNKNOWN_SWORD_GRIP_ICON = 668;
	public static final int OPTIONS_WALKING = 669;
	public static final int OPTIONS_RUNNING = 670;
	public static final int OPTIONS_WALKING_DISABLED = 671;
	public static final int OPTIONS_RUNNING_DISABLED = 672;
	public static final int OPTIONS_AREA_SOUND_VOLUME = 673;
	public static final int OPTIONS_AREA_SOUND_DISABLED = 674;
	public static final int EQUIPMENT_EQUIPMENT_STATS = 675;
	public static final int OPTIONS_HOUSE_OPTIONS = 676;
	public static final int OPTIONS_RUN_ENERGY = 677;
	public static final int OPTIONS_HOUSE_VIEWER = 678;
	public static final int OPTIONS_SLIDER_1_OF_4 = 679;
	public static final int OPTIONS_SLIDER_2_OF_4 = 680;
	public static final int OPTIONS_SLIDER_3_OF_4 = 681;
	public static final int OPTIONS_SLIDER_4_OF_4 = 682;
	public static final int OPTIONS_SLIDER_AND_THUMB_1_OF_4 = 683;
	public static final int OPTIONS_SLIDER_AND_THUMB_2_OF_4 = 684;
	public static final int OPTIONS_SLIDER_AND_THUMB_3_OF_4 = 685;
	public static final int OPTIONS_SLIDER_AND_THUMB_4_OF_4 = 686;
	public static final int OPTIONS_SLIDER_AND_THUMB_1_OF_5 = 687;
	public static final int OPTIONS_SLIDER_AND_THUMB_2_OF_5 = 688;
	public static final int OPTIONS_SLIDER_AND_THUMB_3_OF_5 = 689;
	public static final int OPTIONS_SLIDER_AND_THUMB_4_OF_5 = 690;
	public static final int OPTIONS_SLIDER_AND_THUMB_5_OF_5 = 691;
	public static final int OPTIONS_SLIDER_1_OF_5 = 692;
	public static final int OPTIONS_SLIDER_2_OF_5 = 693;
	public static final int OPTIONS_SLIDER_3_OF_5 = 694;
	public static final int OPTIONS_SLIDER_4_OF_5 = 695;
	public static final int OPTIONS_SLIDER_5_OF_5 = 696;
	public static final int OPTIONS_ROUND_CHECK_BOX = 697;
	public static final int OPTIONS_ROUND_CHECK_BOX_CROSSED = 698;
	public static final int OPTIONS_ROUND_CHECK_BOX_CHECKED = 699;
	public static final int EMOTE_YES = 700;
	public static final int EMOTE_NO = 701;
	public static final int EMOTE_THINK = 702;
	public static final int EMOTE_BOW = 703;
	public static final int EMOTE_ANGRY = 704;
	public static final int EMOTE_CRY = 705;
	public static final int EMOTE_LAUGH = 706;
	public static final int EMOTE_CHEER = 707;
	public static final int EMOTE_WAVE = 708;
	public static final int EMOTE_BECKON = 709;
	public static final int EMOTE_DANCE = 710;
	public static final int EMOTE_CLAP = 711;
	public static final int EMOTE_PANIC = 712;
	public static final int EMOTE_JIG = 713;
	public static final int EMOTE_SPIN = 714;
	public static final int EMOTE_HEADBANG = 715;
	public static final int EMOTE_JUMP_FOR_JOY = 716;
	public static final int EMOTE_RASPBERRY = 717;
	public static final int EMOTE_YAWN = 718;
	public static final int EMOTE_SALUTE = 719;
	public static final int EMOTE_SHRUG = 720;
	public static final int EMOTE_BLOW_KISS = 721;
	public static final int EMOTE_GLASS_BOX = 722;
	public static final int EMOTE_CLIMB_ROPE = 723;
	public static final int EMOTE_LEAN = 724;
	public static final int EMOTE_GLASS_WALL = 725;
	public static final int EMOTE_GOBLIN_BOW = 726;
	public static final int EMOTE_GOBLIN_SALUTE = 727;
	public static final int EMOTE_SCARED = 728;
	public static final int EMOTE_SLAP_HEAD = 729;
	public static final int EMOTE_STAMP = 730;
	public static final int EMOTE_FLAP = 731;
	public static final int EMOTE_IDEA = 732;
	public static final int EMOTE_ZOMBIE_WALK = 733;
	public static final int EMOTE_ZOMBIE_DANCE = 734;
	public static final int EMOTE_RABBIT_HOP = 735;
	public static final int EMOTE_SKILLCAPE = 736;
	public static final int EMOTE_ZOMBIE_HAND = 737;
	public static final int EMOTE_AIR_GUITAR = 738;
	public static final int EMOTE_JOG = 739;
	public static final int EMOTE_SHRUG_LOCKED = 740;
	public static final int EMOTE_BLOW_KISS_LOCKED = 741;
	public static final int EMOTE_GLASS_BOX_LOCKED = 742;
	public static final int EMOTE_CLIMB_ROPE_LOCKED = 743;
	public static final int EMOTE_LEAN_LOCKED = 744;
	public static final int EMOTE_GLASS_WALL_LOCKED = 745;
	public static final int EMOTE_GOBLIN_BOW_LOCKED = 746;
	public static final int EMOTE_GOBLIN_SALUTE_LOCKED = 747;
	public static final int EMOTE_SCARED_LOCKED = 748;
	public static final int EMOTE_SLAP_HEAD_LOCKED = 749;
	public static final int EMOTE_STAMP_LOCKED = 750;
	public static final int EMOTE_FLAP_LOCKED = 751;
	public static final int EMOTE_IDEA_LOCKED = 752;
	public static final int EMOTE_ZOMBIE_WALK_LOCKED = 753;
	public static final int EMOTE_ZOMBIE_DANCE_LOCKED = 754;
	public static final int EMOTE_RABBIT_HOP_LOCKED = 755;
	public static final int EMOTE_SKILLCAPE_LOCKED = 756;
	public static final int EMOTE_ZOMBIE_HAND_LOCKED = 757;
	public static final int EMOTE_AIR_GUITAR_LOCKED = 758;
	public static final int EMOTE_JOG_LOCKED = 759;
	public static final int COMBAT_STYLE_DEFENSIVE_CASTING_SHIELD = 760;
	public static final int OPTIONS_SQUARE_BUTTON = 761;
	public static final int OPTIONS_SQUARE_BUTTON_SELECTED = 762;
	public static final int TEXTURE_FALLING_SNOW_EFFECT = 763;
	/* Unmapped: 764 */
	public static final int BARBARIAN_ASSAULT_WAVE_ICON = 765;
	public static final int BARBARIAN_ASSAULT_EAR_ICON = 766;
	public static final int BARBARIAN_ASSAULT_MOUTH_ICON = 767;
	public static final int BARBARIAN_ASSAULT_HORN_FOR_ATTACKER_ICON = 768;
	public static final int BARBARIAN_ASSAULT_HORN_FOR_DEFENDER_ICON = 769;
	public static final int BARBARIAN_ASSAULT_HORN_FOR_COLLECTOR_ICON = 770;
	public static final int BARBARIAN_ASSAULT_HORN_FOR_HEALER_ICON = 771;
	public static final int UNKNOWN_ARROW_RIGHT_YELLOW = 772;
	public static final int SCROLLBAR_ARROW_UP = 773;
	public static final int RS2_TAB_COMBAT = 774;
	public static final int RS2_TAB_STATS = 775;
	public static final int TAB_QUESTS = 776;
	public static final int RS2_TAB_INVENTORY = 777;
	public static final int RS2_TAB_EQUIPMENT = 778;
	public static final int RS2_TAB_PRAYER = 779;
	public static final int TAB_MAGIC = 780;
	public static final int RS2_TAB_FRIENDS_CHAT = 781;
	public static final int TAB_FRIENDS = 782;
	public static final int TAB_IGNORES = 783;
	public static final int RS2_TAB_LOGOUT = 784;
	public static final int RS2_TAB_OPTIONS = 785;
	public static final int RS2_TAB_EMOTES = 786;
	public static final int RS2_TAB_MUSIC = 787;
	public static final int SCROLLBAR_ARROW_DOWN = 788;
	public static final int SCROLLBAR_THUMB_TOP = 789;
	public static final int SCROLLBAR_THUMB_MIDDLE = 790;
	public static final int SCROLLBAR_THUMB_BOTTOM = 791;
	public static final int SCROLLBAR_THUMB_MIDDLE_DARK = 792;
	public static final int UNKNOWN_DECORATED_ARROW_UP = 793;
	public static final int UNKNOWN_DECORATED_ARROW_DOWN = 794;
	public static final int UNKNOWN_DECORATED_FRAME_TOP = 795;
	public static final int UNKNOWN_DECORATED_FRAME_MIDDLE = 796;
	public static final int UNKNOWN_DECORATED_FRAME_BOTTOM = 797;
	public static final int UNKNOWN_DECORATED_MIDDLE = 798;
	public static final int UNKNOWN_WINDOW_CLOSE_BUTTON_BROWN_X = 799;
	public static final int UNKNOWN_WINDOW_CLOSE_BUTTON_BROWN_X_HOVERED = 800;
	public static final int RS2_SCROLLBAR_ARROW_UP_801 = 801;
	public static final int RS2_SCROLLBAR_ARROW_DOWN = 802;
	public static final int EMOTE_PENGUIN_SHIVER = 803;
	public static final int EMOTE_PENGUIN_SPIN = 804;
	public static final int EMOTE_PENGUIN_CLAP = 805;
	public static final int EMOTE_PENGUIN_BOW = 806;
	public static final int EMOTE_PENGUIN_CHEER = 807;
	public static final int EMOTE_PENGUIN_WAVE = 808;
	public static final int EMOTE_PENGUIN_PREEN = 809;
	public static final int EMOTE_PENGUIN_FLAP = 810;
	public static final int LOGIN_SCREEN_MUSIC_BUTTON = 811;
	public static final int SLAYER_REWARDS_AND_POLL_HISTORY_BUTTON = 812;
	public static final int SLAYER_REWARDS_AND_POLL_HISTORY_BUTTON_SELECTED = 813;
	public static final int LOGIN_SCREEN_FREE_WORLD_BACKGROUND = 814;
	public static final int LOGIN_SCREEN_REGION_USA = 815;
	public static final int LOGIN_SCREEN_SORTING_ARROW_UP_DISABLED = 816;
	public static final int LOGIN_SCREEN_WORLD_STAR_FREE = 817;
	public static final int LOGIN_SCREEN_WORLD_SELECT_BUTTON = 818;
	/* Unmapped: 819 */
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_TOP = 820;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_LEFT = 821;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_BOTTOM = 822;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_RIGHT = 823;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_TOP_LEFT = 824;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_TOP_RIGHT = 825;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_BOTTOM_LEFT = 826;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_BOTTOM_RIGHT = 827;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_HORIZONTAL = 828;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_LEFT = 829;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_RIGHT = 830;
	public static final int BOTTOM_LINE_MODE_WINDOW_CLOSE_BUTTON_SMALL = 831;
	public static final int BOTTOM_LINE_MODE_WINDOW_CLOSE_BUTTON_SMALL_HOVERED = 832;
	public static final int UNKNOWN_BUTTON_METAL_CORNERED = 833;
	public static final int UNKNOWN_BUTTON_METAL_CORNERED_HOVERED = 834;
	public static final int QUESTS_PAGE_ICON_BLUE_QUESTS = 835;
	public static final int QUESTS_PAGE_ICON_GREEN_ACHIEVEMENT_DIARIES = 836;
	public static final int CYRISUS_CHEST = 837;
	public static final int MONSTER_EXAMINE_STATS_ICON = 838;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_BOTTOM = 839;
	public static final int BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_TOP = 840;
	public static final int BOTTOM_LINE_MODE_EDGE_VERTICAL = 841;
	public static final int BOTTOM_LINE_MODE_INTERSECTION_TOP = 842;
	public static final int BOTTOM_LINE_MODE_INTERSECTION_BOTTOM = 843;
	public static final int BOTTOM_LINE_MODE_INTERSECTION_LEFT = 844;
	public static final int BOTTOM_LINE_MODE_INTERSECTION_RIGHT = 845;
	public static final int BOTTOM_LINE_MODE_CORNER_TOP_LEFT = 846;
	public static final int BOTTOM_LINE_MODE_CORNER_TOP_RIGHT = 847;
	public static final int BOTTOM_LINE_MODE_CORNER_BOTTOM_LEFT = 848;
	public static final int BOTTOM_LINE_MODE_CORNER_BOTTOM_RIGHT = 849;
	public static final int BOTTOM_LINE_MODE_INTERSECTION_MIDDLE = 850;
	/* Unmapped: 851~860 */
	public static final int UNKNOWN_GLASS_STREAKS = 861;
	public static final int WITCHS_HOUSE_PIANO_MUSICAL_NOTES_BEAMED = 862;
	public static final int WITCHS_HOUSE_PIANO_MUSICAL_NOTE_CROTCHET = 863;
	public static final int WITCHS_HOUSE_PIANO_MUSICAL_NOTE_MINIM = 864;
	public static final int WITCHS_HOUSE_PIANO_TREBLE_CLEF = 865;
	public static final int TEXTURE_ROOF_TILES_SLATE_DARK = 866;
	public static final int PURO_PURO_GOURMET_IMPLING = 867;
	public static final int PURO_PURO_BABY_IMPLING = 868;
	public static final int PURO_PURO_DRAGON_IMPLING = 869;
	public static final int PURO_PURO_NATURE_IMPLING = 870;
	public static final int PURO_PURO_ECLECTIC_IMPLING = 871;
	public static final int PURO_PURO_IMPLING_IN_JAR = 872;
	public static final int PURO_PURO_YOUNG_IMPLING = 873;
	public static final int PURO_PURO_MAGPIE_IMPLING = 874;
	public static final int PURO_PURO_ESSENCE_IMPLING = 875;
	public static final int PURO_PURO_EARTH_IMPLING = 876;
	public static final int PURO_PURO_NINJA_IMPLING = 877;
	public static final int PURO_PURO_LUCKY_IMPLING = 878;
	public static final int PURO_PURO_THUMB_UP_BUTTON = 879;
	public static final int PURO_PURO_THUMB_UP_BUTTON_HOVERED = 880;
	public static final int UNUSED_TAB_COMBAT = 881;
	public static final int UNUSED_TAB_STATS = 882;
	public static final int UNUSED_TAB_QUESTS = 883;
	public static final int UNUSED_TAB_EQUIPMENT = 885;
	public static final int UNUSED_TAB_PRAYER = 886;
	public static final int UNUSED_TAB_MAGIC = 887;
	public static final int UNUSED_TAB_FRIENDS = 888;
	public static final int UNUSED_TAB_IGNORES = 889;
	public static final int UNUSED_TAB_LOGOUT = 890;
	public static final int UNUSED_TAB_OPTIONS = 891;
	public static final int UNUSED_TAB_EMOTES = 892;
	public static final int UNUSED_TAB_MUSIC = 893;
	public static final int UNUSED_TAB_HOUSE = 894;
	public static final int UNUSED_TAB_SUMMONING = 896;
	public static final int RESIZEABLE_MODE_SIDE_PANEL_BACKGROUND = 897;
	public static final int TAB_STATS = 898;
	public static final int UNUSED_TAB_QUESTS_899 = 899;
	public static final int TAB_INVENTORY = 900;
	public static final int TAB_EQUIPMENT = 901;
	public static final int TAB_PRAYER = 902;
	public static final int UNUSED_TAB_MAGIC_903 = 903;
	public static final int TAB_FRIENDS_CHAT = 904;
	public static final int TAB_LOGOUT = 907;
	public static final int TAB_OPTIONS = 908;
	public static final int TAB_EMOTES = 909;
	public static final int TAB_MUSIC = 910;
	public static final int UNUSED_TAB_COMBAT_911 = 911;
	public static final int EQUIPMENT_ITEMS_LOST_ON_DEATH = 912;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_TOP_LEFT = 913;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_TOP_RIGHT = 914;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_BOTTOM_LEFT = 915;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_BOTTOM_RIGHT = 916;
	public static final int EQUIPMENT_BUTTON_EDGE_LEFT = 917;
	public static final int EQUIPMENT_BUTTON_EDGE_TOP = 918;
	public static final int EQUIPMENT_BUTTON_EDGE_RIGHT = 919;
	public static final int EQUIPMENT_BUTTON_EDGE_BOTTOM = 920;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_TOP_LEFT_HOVERED = 921;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_TOP_RIGHT_HOVERED = 922;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_BOTTOM_LEFT_HOVERED = 923;
	public static final int EQUIPMENT_BUTTON_METAL_CORNER_BOTTOM_RIGHT_HOVERED = 924;
	public static final int EQUIPMENT_BUTTON_EDGE_LEFT_HOVERED = 925;
	public static final int EQUIPMENT_BUTTON_EDGE_TOP_HOVERED = 926;
	public static final int EQUIPMENT_BUTTON_EDGE_RIGHT_HOVERED = 927;
	public static final int EQUIPMENT_BUTTON_EDGE_BOTTOM_HOVERED = 928;
	public static final int WORLD_MAP_BUTTON_METAL_CORNER_TOP_LEFT = 929;
	public static final int WORLD_MAP_BUTTON_METAL_CORNER_TOP_RIGHT = 930;
	public static final int WORLD_MAP_BUTTON_METAL_CORNER_BOTTOM_LEFT = 931;
	public static final int WORLD_MAP_BUTTON_METAL_CORNER_BOTTOM_RIGHT = 932;
	public static final int WORLD_MAP_BUTTON_EDGE_LEFT = 933;
	public static final int WORLD_MAP_BUTTON_EDGE_TOP = 934;
	public static final int WORLD_MAP_BUTTON_EDGE_RIGHT = 935;
	public static final int WORLD_MAP_BUTTON_EDGE_BOTTOM = 936;
	public static final int TRADE_EXCLAMATION_MARK_ITEM_REMOVAL_WARNING = 937;
	public static final int UNKNOWN_PURO_PURO_THUMB_UP = 938;
	public static final int UNKNOWN_PURO_PURO_THUMB_UP_HOVERED = 939;
	public static final int UNKNOWN_DISABLED_ICON = 940;
	public static final int UNKNOWN_ROUND_CHECK_BUTTON = 941;
	public static final int UNKNOWN_ROUND_CHECK_BUTTON_CHECKED = 942;
	public static final int UNKNOWN_LARGE_BUTTON_WITH_RED_X = 943;
	public static final int UNUSED_PRAYER_PROTECT_FROM_SUMMONING = 944;
	public static final int PRAYER_CHIVALRY = 945;
	public static final int PRAYER_PIETY = 946;
	public static final int PRAYER_PRESERVE = 947;
	public static final int UNUSED_PRAYER_PROTECT_FROM_SUMMONING_DISABLED = 948;
	public static final int PRAYER_CHIVALRY_DISABLED = 949;
	public static final int PRAYER_PIETY_DISABLED = 950;
	public static final int PRAYER_PRESERVE_DISABLED = 951;
	public static final int UNKNOWN_SLANTED_TAB = 952;
	public static final int UNKNOWN_SLANTED_TAB_HOVERED = 953;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_TOP = 954;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_LEFT = 955;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_BOTTOM = 956;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_EDGE_RIGHT = 957;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_TOP_LEFT = 958;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_TOP_RIGHT = 959;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_BOTTOM_LEFT = 960;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_CORNER_BOTTOM_RIGHT = 961;
	public static final int UNUSED_BOTTOM_LINE_MODE_EDGE_HORIZONTAL = 962;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_LEFT = 963;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_RIGHT = 964;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_BOTTOM = 965;
	public static final int UNUSED_BOTTOM_LINE_MODE_SIDE_PANEL_INTERSECTION_TOP = 966;
	public static final int UNUSED_BOTTOM_LINE_MODE_EDGE_VERTICAL = 967;
	public static final int UNUSED_BOTTOM_LINE_MODE_INTERSECTION_TOP = 968;
	public static final int UNUSED_BOTTOM_LINE_MODE_INTERSECTION_BOTTOM = 969;
	public static final int UNUSED_BOTTOM_LINE_MODE_INTERSECTION_LEFT = 970;
	public static final int UNUSED_BOTTOM_LINE_MODE_INTERSECTION_RIGHT = 971;
	public static final int UNUSED_BOTTOM_LINE_MODE_CORNER_TOP_LEFT = 972;
	public static final int UNUSED_BOTTOM_LINE_MODE_CORNER_TOP_RIGHT = 973;
	public static final int UNUSED_BOTTOM_LINE_MODE_CORNER_BOTTOM_LEFT = 974;
	public static final int UNUSED_BOTTOM_LINE_MODE_CORNER_BOTTOM_RIGHT = 975;
	public static final int UNUSED_BOTTOM_LINE_MODE_INTERSECTION_MIDDLE = 976;
	/* Unmapped: 977~986 */
	public static final int UNKNOWN_BORDER_EDGE_HORIZONTAL = 987;
	public static final int UNKNOWN_BORDER_EDGE_VERTICAL = 988;
	public static final int UNKNOWN_BORDER_EDGE_HORIZONTAL_989 = 989;
	public static final int UNKNOWN_BORDER_EDGE_VERTICAL_990 = 990;
	public static final int UNKNOWN_BORDER_CORNER_TOP_LEFT = 991;
	public static final int UNKNOWN_BORDER_CORNER_TOP_RIGHT = 992;
	public static final int UNKNOWN_BORDER_CORNER_BOTTOM_LEFT = 993;
	public static final int UNKNOWN_BORDER_CORNER_BOTTOM_RIGHT = 994;
	public static final int UNKNOWN_BORDER_EDGE_HORIZONTAL_995 = 995;
	public static final int UNKNOWN_BORDER_INTERSECTION_LEFT = 996;
	public static final int UNKNOWN_BORDER_INTERSECTION_RIGHT = 997;
	public static final int STASH_UNITS_SLANTED_TAB_EDGE_LEFT = 998;
	public static final int STASH_UNITS_SLANTED_TAB_MIDDLE = 999;
	public static final int STASH_UNITS_SLANTED_TAB_EDGE_RIGHT = 1000;
	public static final int STASH_UNITS_SLANTED_TAB_EDGE_LEFT_HOVERED = 1001;
	public static final int STASH_UNITS_SLANTED_TAB_MIDDLE_HOVERED = 1002;
	public static final int STASH_UNITS_SLANTED_TAB_EDGE_RIGHT_HOVERED = 1003;
	public static final int UNKNOWN_BUTTON_METAL_CORNERS = 1013;
	public static final int UNKNOWN_BUTTON_METAL_CORNERS_HOVERED = 1014;
	public static final int UNKNOWN_SLANTED_TAB_LONG = 1015;
	public static final int UNKNOWN_SLANTED_TAB_LONG_HOVERED = 1016;
	public static final int CHATBOX = 1017;
	public static final int CHATBOX_BUTTONS_BACKGROUND_STONES = 1018;
	public static final int CHATBOX_BUTTON = 3051;
	public static final int CHATBOX_BUTTON_HOVERED = 3052;
	public static final int CHATBOX_BUTTON_NEW_MESSAGES = 3055;
	public static final int CHATBOX_BUTTON_SELECTED = 3053;
	public static final int CHATBOX_BUTTON_SELECTED_HOVERED = 3054;
	public static final int CHATBOX_REPORT_BUTTON = 3057;
	public static final int CHATBOX_REPORT_BUTTON_HOVERED = 3058;
	public static final int TAB_STONE_TOP_LEFT_SELECTED = 1026;
	public static final int TAB_STONE_TOP_RIGHT_SELECTED = 1027;
	public static final int TAB_STONE_BOTTOM_LEFT_SELECTED = 1028;
	public static final int TAB_STONE_BOTTOM_RIGHT_SELECTED = 1029;
	public static final int TAB_STONE_MIDDLE_SELECTED = 1030;
	public static final int FIXED_MODE_SIDE_PANEL_BACKGROUND = 1031;
	public static final int FIXED_MODE_TABS_ROW_BOTTOM = 1032;
	public static final int OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_LEFT_UPPER = 1033;
	public static final int OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_LEFT_LOWER = 1034;
	public static final int OLD_SCHOOl_MODE_SIDE_PANEL_EDGE_RIGHT = 1035;
	public static final int FIXED_MODE_TABS_TOP_ROW = 1036;
	public static final int FIXED_MODE_MINIMAP_LEFT_EDGE = 1037;
	public static final int FIXED_MODE_MINIMAP_RIGHT_EDGE = 1038;
	public static final int FIXED_MODE_WINDOW_FRAME_EDGE_TOP = 1039;
	public static final int DIALOG_BACKGROUND_BRIGHTER = 1040;
	public static final int BANK_DEPOSIT_INVENTORY = 1041;
	public static final int BANK_DEPOSIT_EQUIPMENT = 1042;
	public static final int BANK_SEARCH = 1043;
	public static final int MINIMAP_MARKER_PURPLE_PLAYER_FRIENDS_CHAT = 1044;
	public static final int OPTIONS_PROFANITY_FILTER = 1045;
	public static final int PLAYER_KILLER_SKULL_1046 = 1046;
	public static final int PLAYER_KILLING_DISABLED_OVERLAY = 1047;
	public static final int UNKNOWN_BUTTON_MIDDLE = 1048;
	public static final int UNKNOWN_BUTTON_MIDDLE_SELECTED = 1049;
	public static final int LIST_SORTING_ARROW_ASCENDING = 1050;
	public static final int LIST_SORTING_ARROW_DESCENDING = 1051;
	public static final int TAB_HOUSE_UNUSED = 1052;
	public static final int TAB_QUESTS_RED_MINIGAMES = 1053;
	public static final int QUESTS_PAGE_ICON_RED_MINIGAMES = 1054;
	public static final int TAB_HOUSE_UNUSED_1055 = 1055;
	public static final int UNUSED_TAB_QUESTS_RED_MINIGAMES = 1056;
	public static final int OPTIONS_DATA_ORBS = 1057;
	public static final int MINIMAP_ORB_PRAYER_ICON_ACTIVATED = 1058;
	public static final int MINIMAP_ORB_EMPTY = 1059;
	public static final int MINIMAP_ORB_HITPOINTS = 1060;
	public static final int MINIMAP_ORB_HITPOINTS_POISON = 1061;
	public static final int MINIMAP_ORB_HITPOINTS_DISEASE = 1062;
	public static final int MINIMAP_ORB_PRAYER = 1063;
	public static final int MINIMAP_ORB_RUN = 1064;
	public static final int MINIMAP_ORB_RUN_ACTIVATED = 1065;
	public static final int MINIMAP_ORB_PRAYER_ACTIVATED = 1066;
	public static final int MINIMAP_ORB_HITPOINTS_ICON = 1067;
	public static final int MINIMAP_ORB_PRAYER_ICON = 1068;
	public static final int MINIMAP_ORB_WALK_ICON = 1069;
	public static final int MINIMAP_ORB_RUN_ICON = 1070;
	public static final int MINIMAP_ORB_FRAME = 1071;
	public static final int MINIMAP_ORB_FRAME_HOVERED = 1072;
	public static final int OPTIONS_CAMERA = 1073;
	public static final int BANK_DEPOSIT_LOOTING_BAG = 1074;
	public static final int TEXTURE_LAVA_RED = 1075;
	public static final int TEXTURE_LAVA_WHITE = 1076;
	public static final int BANK_TAB = 1077;
	public static final int BANK_TAB_HOVERED = 1078;
	public static final int BANK_TAB_SELECTED = 1079;
	public static final int BANK_TAB_EMPTY = 1080;
	public static final int BANK_ALL_ITEMS_TAB_ICON = 1081;
	public static final int BANK_ADD_TAB_ICON = 1082;
	public static final int BANK_SHOW_MENU_ICON = 1083;
	public static final int OPTIONS_DISPLAY = 1084;
	public static final int OPTIONS_CHAT = 1085;
	public static final int OPTIONS_ROOFS = 1086;
	public static final int OPTIONS_XP_TO_NEXT_LEVEL = 1087;
	public static final int OPTIONS_CONTROLS = 1088;
	public static final int OPTIONS_LOGIN_LOGOUT_NOTIFICATION_TIMEOUT = 1089;
	public static final int EQUIPMENT_GUIDE_PRICES = 1090;
	public static final int OPTIONS_SIDE_PANELS = 1091;
	public static final int MINIMAP_ORB_RUN_ICON_SLOWED_DEPLETION = 1092;
	public static final int FRIENDS_PREVIOUS_USERNAME = 1093;
	public static final int UNKNOWN_MAP_ICON_INFORMATION_I = 1094;
	public static final int BOUNTY_HUNTER_TARGET_NONE = 1095;
	public static final int BOUNTY_HUNTER_TARGET_WEALTH_1_VERY_LOW = 1096;
	public static final int DEADMAN_BANK_KEYS_5 = 1097;
	public static final int ABLEGAMERS_PROMO_BANNER = 1098;
	public static final int YOUNGMINDS_PROMO_BANNER = 1099;
	public static final int DONATEGAMES_PROMO_BANNER = 1100;
	public static final int MINIMAP_ORB_HITPOINTS_VENOM = 1102;
	public static final int PAYPAL_DONATE_BUTTON = 1103;
	public static final int GAMEBLAST15_PROMO_BANNER = 1104;
	public static final int SPECIALEFFECT_PROMO_BANNER = 1105;
	public static final int GE_MAKE_OFFER_SELL = 1106;
	public static final int GE_MAKE_OFFER_SELL_HOVERED = 1107;
	public static final int GE_MAKE_OFFER_BUY = 1108;
	public static final int GE_MAKE_OFFER_BUY_HOVERED = 1109;
	public static final int GE_BUTTON = 1110;
	public static final int GE_BUTTON_HOVERED = 1111;
	public static final int GE_GUIDE_PRICE = 1112;
	public static final int GE_SEARCH = 1113;
	public static final int GE_FAST_DECREMENT_ARROW = 1114;
	public static final int GE_FAST_INCREMENT_ARROW = 1115;
	public static final int GE_DECREMENT_BUTTON = 1116;
	public static final int GE_INCREMENT_BUTTON = 1117;
	public static final int GE_COLLECTION_BOX_OFFER_BUY = 1118;
	public static final int GE_COLLECTION_BOX_OFFER_SELL = 1119;
	public static final int GE_SELECTED_ITEM_BOX = 1120;
	public static final int GE_SELECTED_ITEM_BOX_GLOWING = 1121;
	public static final int GE_BACK_ARROW_BUTTON = 1122;
	public static final int GE_NUMBER_FIELD_EDGE_LEFT = 1123;
	public static final int GE_NUMBER_FIELD_MIDDLE = 1124;
	public static final int GE_NUMBER_FIELD_EDGE_RIGHT = 1125;
	public static final int GE_CANCEL_OFFER_BUTTON = 1126;
	public static final int GE_CANCEL_OFFER_BUTTON_HOVERED = 1127;
	public static final int DIALOG_BONDS_MEMBERSHIP_JEWEL = 1128;
	public static final int DIALOG_BONDS_MEMBERSHIP_JEWEL_SMALL = 1129;
	public static final int WORLD_SWITCHER_STAR_FREE = 1130;
	public static final int WORLD_SWITCHER_STAR_MEMBERS = 1131;
	public static final int WORLD_SWITCHER_REGION_NONE = 1132;
	public static final int WORLD_SWITCHER_REGION_USA = 1133;
	public static final int WORLD_SWITCHER_REGION_CANADA = 1134;
	public static final int WORLD_SWITCHER_REGION_UK = 1135;
	public static final int WORLD_SWITCHER_REGION_NETHERLANDS = 1136;
	public static final int WORLD_SWITCHER_REGION_AUSTRALIA = 1137;
	public static final int WORLD_SWITCHER_REGION_SWEDEN = 1138;
	public static final int WORLD_SWITCHER_REGION_FINLAND = 1139;
	public static final int WORLD_SWITCHER_REGION_GERMANY = 1140;
	public static final int BUTTON_CORNER_TOP_LEFT = 1141;
	public static final int BUTTON_EDGE_TOP = 1142;
	public static final int BUTTON_CORNER_TOP_RIGHT = 1143;
	public static final int BUTTON_EDGE_LEFT = 1144;
	public static final int BUTTON_MIDDLE = 1145;
	public static final int BUTTON_EDGE_RIGHT = 1146;
	public static final int BUTTON_CORNER_BOTTOM_LEFT = 1147;
	public static final int BUTTON_EDGE_BOTTOM = 1148;
	public static final int BUTTON_CORNER_BOTTOM_RIGHT = 1149;
	public static final int BUTTON_CORNER_TOP_LEFT_SELECTED = 1150;
	public static final int BUTTON_EDGE_TOP_SELECTED = 1151;
	public static final int BUTTON_CORNER_TOP_RIGHT_SELECTED = 1152;
	public static final int BUTTON_EDGE_LEFT_SELECTED = 1153;
	public static final int BUTTON_MIDDLE_SELECTED = 1154;
	public static final int BUTTON_EDGE_RIGHT_SELECTED = 1155;
	public static final int BUTTON_CORNER_BOTTOM_LEFT_SELECTED = 1156;
	public static final int BUTTON_EDGE_BOTTOM_SELECTED = 1157;
	public static final int BUTTON_CORNER_BOTTOM_RIGHT_SELECTED = 1158;
	public static final int OPTIONS_TRANSPARENT_CHATBOX = 1159;
	public static final int OPTIONS_TRANSPARENT_SIDE_PANEL = 1160;
	public static final int OPTIONS_KEYBINDINGS = 1161;
	public static final int OPTIONS_SCROLL_WHEEL_ZOOM = 1162;
	public static final int OPTIONS_HIDE_PRIVATE_CHAT = 1163;
	public static final int OPTIONS_NOTIFICATIONS = 1164;
	public static final int OPTIONS_SHIFT_CLICK_DROP = 1165;
	public static final int OPTIONS_FOLLOWER_RIGHT_CLICK_MENU = 1166;
	public static final int OPTIONS_PRAYER_TOOLTIPS = 1167;
	public static final int OPTIONS_SPECIAL_ATTACK_TOOLTIP = 1168;
	public static final int OPTIONS_FIXED_MODE_DISABLED = 1169;
	public static final int OPTIONS_RESIZEABLE_MODE_DISABLED = 1170;
	public static final int OPTIONS_FIXED_MODE_DISABLED_VERTICAL = 1171;
	public static final int OPTIONS_RESIZEABLE_MODE_DISABLED_VERTICAL = 1172;
	public static final int RESIZEABLE_MODE_TABS_TOP_ROW = 1173;
	public static final int RESIZEABLE_MODE_TABS_BOTTOM_ROW = 1174;
	public static final int RESIZEABLE_MODE_SIDE_PANEL_EDGE_LEFT = 1175;
	public static final int RESIZEABLE_MODE_SIDE_PANEL_EDGE_RIGHT = 1176;
	public static final int RESIZEABLE_MODE_MINIMAP_AND_COMPASS_FRAME = 1177;
	public static final int RESIZEABLE_MODE_MINIMAP_ALPHA_MASK = 1178;
	public static final int RESIZEABLE_MODE_COMPASS_ALPHA_MASK = 1179;
	public static final int RESIZEABLE_MODE_TAB_STONE_MIDDLE = 1180;
	public static final int RESIZEABLE_MODE_TAB_STONE_MIDDLE_SELECTED = 1181;
	public static final int FIXED_MODE_MINIMAP_AND_COMPASS_FRAME = 1182;
	public static final int FIXED_MODE_MINIMAP_ALPHA_MASK = 1183;
	public static final int FIXED_MODE_COMPASS_ALPHA_MASK = 1184;
	public static final int CHATBOX_TRANSPARENT_SCROLLBAR_ARROW_UP = 1185;
	public static final int CHATBOX_TRANSPARENT_SCROLLBAR_ARROW_DOWN = 1186;
	public static final int CHATBOX_TRANSPARENT_SCROLLBAR_THUMB_TOP = 1187;
	public static final int CHATBOX_TRANSPARENT_SCROLLBAR_THUMB_MIDDLE = 1188;
	public static final int CHATBOX_TRANSPARENT_SCROLLBAR_THUMB_BOTTOM = 1190;
	public static final int UNUSED_TAB_LOGOUT_1191 = 1191;
	public static final int ROUND_CHECK_BOX_CHECKED_RED_HOVERED = 1192;
	public static final int OPTIONS_DISABLED_OPTION_OVERLAY = 1193;
	public static final int DUEL_ARENA_SAVE_PRESET = 1194;
	public static final int DUEL_ARENA_LOAD_PRESET = 1195;
	public static final int MINIMAP_ORB_XP = 1196;
	public static final int MINIMAP_ORB_XP_ACTIVATED = 1197;
	public static final int MINIMAP_ORB_XP_HOVERED = 1198;
	public static final int MINIMAP_ORB_XP_ACTIVATED_HOVERED = 1199;
	public static final int MINIMAP_CLICK_MASK = 1200;
	public static final int OPTIONS_ZOOM_SLIDER_THUMB = 1201;
	public static final int EMOTE_SIT_UP = 1202;
	public static final int EMOTE_STAR_JUMP = 1203;
	public static final int EMOTE_PUSH_UP = 1204;
	public static final int EMOTE_HYPERMOBILE_DRINKER = 1205;
	public static final int EMOTE_SIT_UP_LOCKED = 1206;
	public static final int EMOTE_STAR_JUMP_LOCKED = 1207;
	public static final int EMOTE_PUSH_UP_LOCKED = 1208;
	public static final int EMOTE_HYPERMOBILE_DRINKER_LOCKED = 1209;
	public static final int STASH_UNITS_GREEN_CHECK_MARK = 1210;
	public static final int ROUND_CHECK_BOX = 1211;
	public static final int ROUND_CHECK_BOX_CROSSED = 1212;
	public static final int ROUND_CHECK_BOX_CHECKED_GREEN = 1213;
	public static final int ROUND_CHECK_BOX_CHECKED_RED = 1214;
	public static final int SQUARE_CHECK_BOX = 1215;
	public static final int SQUARE_CHECK_BOX_CROSSED = 1216;
	public static final int SQUARE_CHECK_BOX_CHECKED = 1217;
	public static final int SQUARE_CHECK_BOX_HOVERED = 1218;
	public static final int SQUARE_CHECK_BOX_CROSSED_HOVERED = 1219;
	public static final int SQUARE_CHECK_BOX_CHECKED_HOVERED = 1220;
	public static final int DEADMAN_BANK_KEYS_4 = 1221;
	public static final int DEADMAN_BANK_KEYS_3 = 1222;
	public static final int DEADMAN_BANK_KEYS_2 = 1223;
	public static final int DEADMAN_BANK_KEYS_1 = 1224;
	/* Unmapped: 1225 */
	public static final int BANK_RAID_SEND_TO_INVENTORY = 1226;
	public static final int BANK_RAID_SEND_TO_BANK = 1227;
	public static final int LOGIN_SCREEN_DEADMAN_MODE_LOGO = 1228;
	public static final int NARROW_BUTTON_EDGE_LEFT = 1229;
	public static final int NARROW_BUTTON_MIDDLE = 1230;
	public static final int NARROW_BUTTON_EDGE_RIGHT = 1231;
	public static final int NARROW_BUTTON_EDGE_LEFT_SELECTED = 1232;
	public static final int NARROW_BUTTON_MIDDLE_SELECTED = 1233;
	public static final int NARROW_BUTTON_EDGE_RIGHT_SELECTED = 1234;
	public static final int BANK_RAID_SEND_TO_TRASH = 1235;
	public static final int UNKNOWN_INFORMATION_I = 1236;
	public static final int WORLD_SWITCHER_WORLD_STAR_PVP = 1237;
	public static final int WORLD_SWITCHER_WORLD_STAR_DEADMAN = 1238;
	public static final int DEADMAN_EXCLAMATION_MARK_SKULLED_WARNING = 1239;
	public static final int KOUREND_FAVOUR_OPEN_TASK_LIST = 1240;
	public static final int KOUREND_FAVOUR_OPEN_TASK_LIST_HOVERED = 1241;
	public static final int KOUREND_FAVOUR_ARCEUUS_ICON = 1242;
	public static final int KOUREND_FAVOUR_HOSIDIUS_ICON = 1243;
	public static final int KOUREND_FAVOUR_LOVAKENGJ_ICON = 1244;
	public static final int KOUREND_FAVOUR_PISCARILIUS_ICON = 1245;
	public static final int KOUREND_FAVOUR_SHAYZIEN_ICON = 1246;
	public static final int SPELL_BASIC_REANIMATION = 1247;
	public static final int SPELL_ADEPT_REANIMATION = 1248;
	public static final int SPELL_EXPERT_REANIMATION = 1249;
	public static final int SPELL_MASTER_REANIMATION = 1250;
	/* Unmapped: 1251 */
	public static final int SPELL_ARCEUUS_LIBRARY_TELEPORT = 1252;
	public static final int SPELL_DRAYNOR_MANOR_TELEPORT = 1253;
	public static final int SPELL_SALVE_GRAVEYARD_TELEPORT = 1254;
	/* Unmapped: 1255 */
	public static final int SPELL_MIND_ALTAR_TELEPORT = 1256;
	public static final int SPELL_RESPAWN_TELEPORT = 1257;
	/* Unmapped 1258 */
	public static final int SPELL_FENKENSTRAINS_CASTLE_TELEPORT = 1259;
	public static final int SPELL_WEST_ARDOUGNE_TELEPORT = 1260;
	public static final int SPELL_HARMONY_ISLAND_TELEPORT = 1261;
	public static final int SPELL_BARROWS_TELEPORT = 1262;
	public static final int SPELL_APE_ATOLL_TELEPORT = 1263;
	public static final int SPELL_CEMETERY_TELEPORT = 1264;
	/* Unmapped: 1265 */
	public static final int SPELL_RESURRECT_CROPS = 1266;
	public static final int SPELL_GHOSTLY_GRASP = 1267;
	public static final int SPELL_SKELETAL_GRASP = 1268;
	public static final int SPELL_UNDEAD_GRASP = 1269;
	public static final int SPELL_RESURRECT_LESSER_GHOST = 1270;
	/* Unmapped: 1271~1296 */
	public static final int QUESTS_PAGE_ICON_PURPLE_KOUREND = 1297;
	public static final int UNUSED_TAB_QUESTS_GREEN_ACHIEVEMENT_DIARIES = 1298;
	public static final int TAB_QUESTS_GREEN_ACHIEVEMENT_DIARIES = 1299;
	/* Unmapped: 1300~1301 */
	public static final int SPELL_INFERIOR_DEMONBANE = 1302;
	public static final int SPELL_SUPERIOR_DEMONBANE = 1303;
	public static final int SPELL_DARK_DEMONBANE = 1304;
	public static final int SPELL_MARK_OF_DARKNESS = 1305;
	public static final int SPELL_WARD_OF_ARCEUUS = 1306;
	public static final int SPELL_LESSER_CORRUPTION = 1307;
	public static final int SPELL_GREATER_CORRUPTION = 1308;
	/* Unmapped: 1309 */
	public static final int SPELL_DEATH_CHARGE = 1310;
	public static final int SPELL_DEMONIC_OFFERING = 1311;
	public static final int SPELL_SINISTER_OFFERING = 1312;
	/* Unmapped: 1313~1314 */
	public static final int SPELL_SHADOW_VEIL = 1315;
	public static final int SPELL_DARK_LURE = 1316;
	public static final int SPELL_VILE_VIGOUR = 1317;
	public static final int SPELL_DEGRIME = 1318;
	/* Unmapped: 1319~1324 */
	public static final int SPELL_WARD_OF_ARCEUUS_DISABLED = 1325;
	/* Unmapped: 1326 */
	public static final int SPELL_GREATER_CORRUPTION_DISABLED = 1327;
	/* Unmapped: 1328 */
	public static final int SPELL_DEATH_CHARGE_DISABLED = 1329;
	/* Unmapped: 1330~1333 */
	public static final int SPELL_SHADOW_VEIL_DISABLED = 1334;
	/* Unmapped: 1335~1337 */
	public static final int WORLD_SWITCHER_WORLD_STAR_BLUE = 1338;
	/* Unmapped: 1339 */
	public static final int FAIRY_RING_REMOVE_FAVOURITE = 1340;
	public static final int FAIRY_RING_ADD_FAVOURITE = 1341;
	public static final int BANK_PLACEHOLDERS_LOCK = 1342;
	public static final int EQUIPMENT_CALL_FOLLOWER = 1343;
	public static final int UNKNOWN_BUTTON_HALF_TOP = 1344;
	public static final int UNKNOWN_BUTTON_HALF_TOP_1345 = 1345;
	public static final int DEADMAN_TAB_ITEMS_LOST_ON_DEATH_TO_PVM = 1346;
	public static final int DEADMAN_TAB_ITEMS_LOST_ON_DEATH_TO_PVP = 1347;
	public static final int DEADMAN_TAB_ITEMS_LOST_ON_DEATH_WHILE_SKULLED = 1348;
	public static final int DEADMAN_TAB_ITEMS_LOST_ON_DEATH_WHILE_SKULLED_IN_SAFE_ZONE = 1349;
	public static final int EMOTE_URI_TRANSFORM = 1350;
	public static final int EMOTE_SMOOTH_DANCE = 1351;
	public static final int EMOTE_CRAZY_DANCE = 1352;
	public static final int EMOTE_PREMIER_SHIELD = 1353;
	public static final int EMOTE_URI_TRANSFORM_LOCKED = 1354;
	public static final int EMOTE_SMOOTH_DANCE_LOCKED = 1355;
	public static final int EMOTE_CRAZY_DANCE_LOCKED = 1356;
	public static final int EMOTE_PREMIER_SHIELD_LOCKED = 1357;
	/* Unmapped: 1358~1359 */
	public static final int HITSPLAT_GREEN_POISON = 1360;
	/* Unmapped: 1361~1363 */
	public static final int BOUNTY_HUNTER_SKIP_TARGET = 1364;
	public static final int BOUNTY_HUNTER_SKIP_TARGET_HOVERED = 1365;
	public static final int HOUSE_VIEWER_ROTATE_CLOCKWISE = 1366;
	public static final int HOUSE_VIEWER_ROTATE_ANTICLOCKWISE = 1367;
	public static final int HOUSE_VIEWER_PARLOUR = 1368;
	public static final int HOUSE_VIEWER_GARDEN = 1369;
	public static final int HOUSE_VIEWER_KITCHEN = 1370;
	public static final int HOUSE_VIEWER_DINING_ROOM = 1371;
	public static final int HOUSE_VIEWER_BEDROOM = 1372;
	public static final int HOUSE_VIEWER_GAMES_ROOM = 1373;
	public static final int HOUSE_VIEWER_SKILL_HALL = 1374;
	public static final int HOUSE_VIEWER_QUEST_HALL = 1375;
	public static final int HOUSE_VIEWER_CHAPEL = 1376;
	public static final int HOUSE_VIEWER_WORKSHOP = 1377;
	public static final int HOUSE_VIEWER_STUDY = 1378;
	public static final int HOUSE_VIEWER_PORTAL_CHAMBER = 1379;
	public static final int HOUSE_VIEWER_THRONE_ROOM = 1380;
	public static final int HOUSE_VIEWER_OUBLIETTE = 1381;
	public static final int HOUSE_VIEWER_DUNGEON_CORRIDOR = 1382;
	public static final int HOUSE_VIEWER_DUNGEON_JUNCTION = 1383;
	public static final int HOUSE_VIEWER_DUNGEON_STAIRS_ROOM = 1384;
	public static final int HOUSE_VIEWER_TREASURE_ROOM = 1385;
	public static final int HOUSE_VIEWER_FORMAL_GARDEN = 1386;
	public static final int HOUSE_VIEWER_COMBAT_ROOM = 1387;
	public static final int HOUSE_VIEWER_COSTUME_ROOM = 1388;
	public static final int HOUSE_VIEWER_MENAGERIE_INDOORS = 1389;
	public static final int HOUSE_VIEWER_MENAGERIE_OUTDOORS = 1390;
	public static final int HOUSE_VIEWER_SUPERIOR_GARDEN = 1391;
	public static final int HOUSE_VIEWER_ACHIEVEMENT_GALLERY = 1392;
	/* Unmapped: 1393, 1394, 1395 */
	public static final int UNUSED_TEXTURE_LEAVES_WITH_RED_X = 1396;
	public static final int SKILL_CRAFTING_1397 = 1397;
	public static final int SKILL_FIREMAKING_LOGS = 1398;
	public static final int SKILL_FIREMAKING_1399 = 1399;
	public static final int SKILL_MAGIC_RED = 1400;
	public static final int SKILL_ATTACK_SQUASHED = 1401;
	public static final int SKILL_STRENGTH_SQUASHED = 1402;
	public static final int SKILL_DEFENCE_SQUASHED = 1403;
	public static final int SKILL_RANGED_SQUASHED = 1404;
	public static final int SKILL_PRAYER_SQUASHED = 1405;
	public static final int SKILL_MAGIC_SQUASHED = 1406;
	public static final int SKILL_HITPOINTS_SQUASHED = 1407;
	public static final int PREMIER_CLUB_BRONZE = 1408;
	public static final int PREMIER_CLUB_SILVER = 1409;
	public static final int PREMIER_CLUB_GOLD = 1410;
	public static final int UNKNOWN_DIAGONAL_COMING_SOON_TEXT = 1411;
	public static final int UNKNOWN_WHITE_REFRESH_ARROWS = 1412;
	public static final int TAB_QUESTS_PURPLE_KOUREND_UNUSED = 1413;
	public static final int TAB_QUESTS_PURPLE_KOUREND = 1414;
	public static final int HEALTHBAR_COX_GREEN = 1415;
	public static final int HEALTHBAR_COX_BLUE = 1416;
	public static final int HEALTHBAR_COX_YELLOW = 1417;
	public static final int HEALTHBAR_COX_RED = 1418;
	/* Unmapped: 1419 */
	public static final int PRAYER_RIGOUR = 1420;
	public static final int PRAYER_AUGURY = 1421;
	/* Unmapped: 1422, 1423 */
	public static final int PRAYER_RIGOUR_DISABLED = 1424;
	public static final int PRAYER_AUGURY_DISABLED = 1425;
	/* Unmapped: 1426, 1427 */
	public static final int UNKNOWN_BLACK_ANTICLOCKWISE_ARROW_SHADOWED = 1428;
	public static final int UNKNOWN_BLACK_ANTICLOCKWISE_ARROW = 1429;
	public static final int YELLOW_CLICK_ANIMATION_1_1430 = 1430;
	public static final int YELLOW_CLICK_ANIMATION_2_1431 = 1431;
	public static final int YELLOW_CLICK_ANIMATION_3_1432 = 1432;
	public static final int YELLOW_CLICK_ANIMATION_4_1433 = 1433;
	public static final int RED_CLICK_ANIMATION_1_1434 = 1434;
	public static final int RED_CLICK_ANIMATION_2_1435 = 1435;
	public static final int RED_CLICK_ANIMATION_3_1436 = 1436;
	public static final int RED_CLICK_ANIMATION_4_1437 = 1437;
	public static final int MINIMAP_ORB_WORLD_MAP_FRAME = 1438;
	public static final int MINIMAP_ORB_WORLD_MAP_PLANET = 1439;
	public static final int MINIMAP_ORB_WORLD_MAP_PLANET_HOVERED = 1440;
	public static final int FIXED_MODE_TOP_RIGHT_CORNER = 1441;
	/* Unmapped: 1442~1447 */
	public static final int MAP_ICON_GENERAL_STORE = 1448;
	public static final int MAP_ICON_SWORD_SHOP = 1449;
	public static final int MAP_ICON_MAGIC_SHOP = 1450;
	public static final int MAP_ICON_AXE_SHOP = 1451;
	public static final int MAP_ICON_HELMET_SHOP = 1452;
	public static final int MAP_ICON_BANK = 1453;
	public static final int MAP_ICON_QUEST_START = 1454;
	public static final int MAP_ICON_AMULET_SHOP = 1455;
	public static final int MAP_ICON_MINING_SITE = 1456;
	public static final int MAP_ICON_FURNACE = 1457;
	public static final int MAP_ICON_ANVIL = 1458;
	public static final int MAP_ICON_COMBAT_TRAINING = 1459;
	public static final int MAP_ICON_DUNGEON = 1460;
	public static final int MAP_ICON_STAFF_SHOP = 1461;
	public static final int MAP_ICON_PLATEBODY_SHOP = 1462;
	public static final int MAP_ICON_PLATELEGS_SHOP = 1463;
	public static final int MAP_ICON_SCIMITAR_SHOP = 1464;
	public static final int MAP_ICON_ARCHERY_SHOP = 1465;
	public static final int MAP_ICON_SHIELD_SHOP = 1466;
	public static final int MAP_ICON_ALTAR = 1467;
	public static final int MAP_ICON_HERBALIST = 1468;
	public static final int MAP_ICON_JEWELLERY_SHOP = 1469;
	public static final int MAP_ICON_GEM_SHOP = 1470;
	public static final int MAP_ICON_CRAFTING_SHOP = 1471;
	public static final int MAP_ICON_CANDLE_SHOP = 1472;
	public static final int MAP_ICON_FISHING_SHOP = 1473;
	public static final int MAP_ICON_FISHING_SPOT = 1474;
	public static final int MAP_ICON_CLOTHES_SHOP = 1475;
	public static final int MAP_ICON_APOTHECARY = 1476;
	public static final int MAP_ICON_SILK_TRADER = 1477;
	public static final int MAP_ICON_FOOD_SHOP_KEBAB = 1478;
	public static final int MAP_ICON_PUB = 1479;
	public static final int MAP_ICON_MACE_SHOP = 1480;
	public static final int MAP_ICON_TANNERY = 1481;
	public static final int MAP_ICON_RARE_TREES = 1482;
	public static final int MAP_ICON_SPINNING_WHEEL = 1483;
	public static final int MAP_ICON_FOOD_SHOP = 1484;
	public static final int MAP_ICON_FOOD_SHOP_CUTLERY = 1485;
	public static final int MAP_ICON_MINIGAME = 1486;
	public static final int MAP_ICON_WATER_SOURCE = 1487;
	public static final int MAP_ICON_COOKING_RANGE = 1488;
	public static final int MAP_ICON_PLATESKIRT_SHOP = 1489;
	public static final int MAP_ICON_POTTERY_WHEEL = 1490;
	public static final int MAP_ICON_WINDMILL = 1491;
	public static final int MAP_ICON_MINING_SHOP = 1492;
	public static final int MAP_ICON_CHAINMAIL_SHOP = 1493;
	public static final int MAP_ICON_SILVER_SHOP = 1494;
	public static final int MAP_ICON_FUR_TRADER = 1495;
	public static final int MAP_ICON_SPICE_SHOP = 1496;
	public static final int MAP_ICON_AGILITY_TRAINING = 1497;
	public static final int MAP_ICON_FOOD_SHOP_FRUIT = 1498;
	public static final int MAP_ICON_SLAYER_MASTER = 1499;
	public static final int MAP_ICON_HAIRDRESSER = 1500;
	public static final int MAP_ICON_FARMING_PATCH = 1501;
	public static final int MAP_ICON_MAKEOVER_MAGE = 1502;
	public static final int MAP_ICON_GUIDE = 1503;
	public static final int MAP_ICON_TRANSPORTATION = 1504;
	public static final int MAP_ICON_HOUSE_PORTAL = 1505;
	public static final int MAP_ICON_FARMING_SHOP = 1506;
	public static final int MAP_ICON_LOOM = 1507;
	public static final int MAP_ICON_BREWERY = 1508;
	public static final int MAP_ICON_DAIRY_CHURN = 1509;
	public static final int MAP_ICON_STAGNANT_WATER_SOURCE = 1510;
	public static final int MAP_ICON_HUNTER_TRAINING = 1511;
	public static final int MAP_ICON_POLL_BOOTH = 1512;
	public static final int MAP_ICON_HUNTER_SHOP = 1513;
	public static final int UNKNOWN_MAP_ICON_INFORMATION_I_1514 = 1514;
	public static final int MAP_ICON_ESTATE_AGENT = 1515;
	public static final int MAP_ICON_SAWMILL = 1516;
	public static final int MAP_ICON_STONEMASON = 1517;
	public static final int MAP_ICON_AGILITY_SHORT_CUT = 1518;
	public static final int MAP_ICON_WOODCUTTING_STUMP = 1519;
	public static final int MAP_ICON_HOLIDAY_EVENT = 1520;
	public static final int MAP_ICON_SANDPIT = 1521;
	public static final int MAP_ICON_TASK_MASTER = 1522;
	public static final int MAP_ICON_PET_SHOP = 1523;
	public static final int MAP_ICON_BOUNTY_HUNTER_TRADER = 1524;
	public static final int MAP_ICON_IRON_MAN_TUTORS = 1525;
	public static final int MAP_ICON_PRICING_EXPERT_WEAPONS_AND_ARMOUR = 1526;
	public static final int MAP_ICON_PRICING_EXPERT_LOGS = 1527;
	public static final int MAP_ICON_PRICING_EXPERT_HERBS = 1528;
	public static final int MAP_ICON_PRICING_EXPERT_RUNES = 1529;
	public static final int MAP_ICON_PRICING_EXPERT_ORES_AND_BARS = 1530;
	public static final int MAP_ICON_GRAND_EXCHANGE = 1531;
	public static final int MAP_ICON_KOUREND_TASK = 1532;
	public static final int MAP_ICON_RAIDS_LOBBY = 1533;
	public static final int MAP_ICON_MAP_LINK_DOWNSTAIRS = 1534;
	public static final int MAP_ICON_MAP_LINK_UPSTAIRS = 1535;
	/* Unmapped: 1536, 1537, 1538 */
	public static final int WORLD_MAP_YOU_ARE_HERE = 1539;
	public static final int WORLD_MAP_OFFSCREEN_FOCUS_ARROW = 1540;
	public static final int WORLD_MAP_OFFSCREEN_FOCUS_ARROW_DIAGONAL = 1541;
	public static final int UNKNOWN_BROWN_ARROW_UP = 1542;
	public static final int UNKNOWN_YELLOW_ARROW_UP = 1543;
	public static final int DEADMAN_SPECIAL_LOGO = 1544;
	public static final int UNKNOWN_BACKGROUND_BEIGE = 1545;
	public static final int BUTTON_METAL_CORNER_TOP_LEFT_HOVERED = 1546;
	public static final int BUTTON_METAL_CORNER_TOP_RIGHT_HOVERED = 1547;
	public static final int BUTTON_METAL_CORNER_BOTTOM_LEFT_HOVERED = 1548;
	public static final int BUTTON_METAL_CORNER_BOTTOM_RIGHT_HOVERED = 1549;
	public static final int BUTTON_METAL_EDGE_LEFT_HOVERED = 1550;
	public static final int BUTTON_METAL_EDGE_TOP_HOVERED = 1551;
	public static final int BUTTON_METAL_EDGE_RIGHT_HOVERED = 1552;
	public static final int BUTTON_METAL_EDGE_BOTTOM_HOVERED = 1553;
	public static final int BUTTON_METAL_CORNER_TOP_LEFT = 1554;
	public static final int BUTTON_METAL_CORNER_TOP_RIGHT = 1555;
	public static final int BUTTON_METAL_CORNER_BOTTOM_LEFT = 1556;
	public static final int BUTTON_METAL_CORNER_BOTTOM_RIGHT = 1557;
	public static final int BUTTON_METAL_EDGE_LEFT = 1558;
	public static final int BUTTON_METAL_EDGE_TOP = 1559;
	public static final int BUTTON_METAL_EDGE_RIGHT = 1560;
	public static final int BUTTON_METAL_EDGE_BOTTOM = 1561;
	public static final int OPTIONS_FINGER_POINTING_AT_MENU = 1562;
	public static final int OPTIONS_DISPLAY_ICON_VERTICAL = 1563;
	public static final int OPTIONS_F1_BUTTON_DISABLED = 1564;
	public static final int OPTIONS_TWO_FINGERS_POINTING_AT_MAGNIFYING_GLASS = 1565;
	public static final int OPTIONS_SHIFT_KEY_DISABLED = 1566;
	public static final int OPTIONS_FINGER_POINTING_AT_PET = 1567;
	public static final int OPTIONS_SCROLL = 1568;
	/* Unmapped: 1569, 1570, 1571 */
	public static final int OPTIONS_FIXED_MODE_ENABLED = 1572;
	public static final int OPTIONS_RESIZEABLE_MODE_ENABLED = 1573;
	public static final int OPTIONS_FIXED_MODE_ENABLED_VERTICAL = 1574;
	public static final int OPTIONS_RESIZEABLE_MODE_ENABLED_VERTICAL = 1575;
	public static final int OPTIONS_HOUSE_DOORS_CLOSED = 1576;
	public static final int OPTIONS_HOUSE_DOORS_OPEN = 1577;
	public static final int OPTIONS_HOUSE_DOORS_INVISIBLE = 1578;
	public static final int TAB_QUESTS_BROWN_RAIDING_PARTY_UNUSED = 1579;
	public static final int TAB_MAGIC_SPELLBOOK_ANCIENT_MAGICKS_UNUSED = 1580;
	public static final int TAB_MAGIC_SPELLBOOK_LUNAR_UNUSED = 1581;
	public static final int TAB_QUESTS_BROWN_RAIDING_PARTY = 1582;
	public static final int TAB_MAGIC_SPELLBOOK_ANCIENT_MAGICKS = 1583;
	public static final int TAB_MAGIC_SPELLBOOK_LUNAR = 1584;
	public static final int UNKNOWN_BUTTON_HALF_LEFT_1585 = 1585;
	public static final int UNKNOWN_BUTTON_HALF_LEFT_GREEN = 1586;
	public static final int UNKNOWN_BUTTON_HALF_LEFT_FADED_GREEN = 1587;
	public static final int UNKNOWN_BUTTON_HALF_LEFT_RED = 1588;
	public static final int UNKNOWN_BUTTON_HALF_LEFT_FADED_RED = 1589;
	public static final int UNKNOWN_BUTTON_HALF_RIGHT_1590 = 1590;
	public static final int UNKNOWN_BUTTON_HALF_RIGHT_GREEN = 1591;
	public static final int UNKNOWN_BUTTON_HALF_RIGHT_FADED_GREEN = 1592;
	public static final int UNKNOWN_BUTTON_HALF_RIGHT_RED = 1593;
	public static final int UNKNOWN_BUTTON_HALF_RIGHT_FADED_RED = 1594;
	public static final int UNKNOWN_SLIM_BUTTON = 1595;
	public static final int UNKNOWN_SLIM_BUTTON_SELECTED = 1596;
	/* Unmapped: 1597, 1598, 1599 */
	public static final int LOGOUT_THUMB_UP = 1600;
	public static final int LOGOUT_THUMB_DOWN = 1601;
	public static final int LOGOUT_THUMB_UP_HOVERED = 1602;
	public static final int LOGOUT_THUMB_DOWN_HOVERED = 1603;
	public static final int UNKNOWN_SMALL_HITPOINTS_ICON = 1604;
	public static final int UNKNOWN_SMALL_PRAYER_ICON = 1605;
	public static final int UNKNOWN_SMALL_SPECIAL_ICON = 1606;
	public static final int MINIMAP_ORB_SPECIAL = 1607;
	public static final int MINIMAP_ORB_SPECIAL_ACTIVATED = 1608;
	/* Unmapped: 1609 */
	public static final int MINIMAP_ORB_SPECIAL_ICON = 1610;
	public static final int FIXED_MODE_MINIMAP_FRAME_BOTTOM = 1611;
	/* Unmapped: 1612, 1613, 1614 */
	public static final int MOBILE_TUTORIAL_FUNCTION_MODE_BUTTON = 1615;
	public static final int MOBILE_TUTORIAL_CAMERA_MOVEMENT = 1616;
	public static final int MOBILE_CONCEPT_SKETCH_UI = 1617;
	public static final int MOBILE_TUTORIAL_NPC_GESTURE_PRESS = 1618;
	public static final int MOBILE_TUTORIAL_MINIMISE_WORLD_MAP = 1619;
	public static final int MOBILE_TUTORIAL_NPC_GESTURE_TAP = 1620;
	public static final int MOBILE_TUTORIAL_GESTURES_TAP_AND_PRESS = 1621;
	public static final int MOBILE_CONCEPT_SKETCH_DEVICE = 1622;
	public static final int MOBILE_FUNCTION_MODE_ENABLED = 1623;
	public static final int MOBILE_FUNCTION_MODE_DISABLED = 1624;
	public static final int MOBILE_YELLOW_TOUCH_ANIMATION_1 = 1625;
	public static final int MOBILE_YELLOW_TOUCH_ANIMATION_2 = 1626;
	/* Unmapped: 1627~1631 */
	public static final int HITSPLAT_DARK_GREEN_VENOM = 1632;
	/* Unmapped: 1633~1652 */
	public static final int MOBILE_FINGER_ON_INTERFACE = 1653;
	/* Unmapped: 1627~1701 */
	public static final int BUTTON_FRIENDS = 1702;
	public static final int BUTTON_IGNORES = 1703;
	/* Unmapped: 1704~1707 */
	public static final int TAB_MAGIC_SPELLBOOK_ARCEUUS_UNUSED = 1708;
	/* Unmapped: 1709, 1710 */
	public static final int TAB_MAGIC_SPELLBOOK_ARCEUUS = 1711;
	public static final int BIG_ASS_GUTHIX_SPELL = 1774;
	public static final int BIG_ASS_GREY_ENTANGLE = 1788;
	public static final int BIG_ASS_WHITE_ENTANGLE = 1789;
	public static final int BIG_SUPERHEAT = 1800;
	public static final int BIG_SPEC_TRANSFER = 1959;
	/* Unmapped: 1712 */
	public static final int TAB_QUESTS_ORANGE_ADVENTURE_PATHS = 1713;
	/* Unmapped: 1714~2175 */
	public static final int HEALTHBAR_DEFAULT_FRONT_30PX = 2176;
	public static final int HEALTHBAR_DEFAULT_BACK_30PX = 2177;
	public static final int HEALTHBAR_DEFAULT_FRONT_50PX = 2178;
	public static final int HEALTHBAR_DEFAULT_BACK_50PX = 2179;
	public static final int HEALTHBAR_DEFAULT_FRONT_60PX = 2180;
	public static final int HEALTHBAR_DEFAULT_BACK_60PX = 2181;
	public static final int HEALTHBAR_DEFAULT_FRONT_80PX = 2182;
	public static final int HEALTHBAR_DEFAULT_BACK_80PX = 2183;
	public static final int HEALTHBAR_DEFAULT_FRONT_100PX = 2184;
	public static final int HEALTHBAR_DEFAULT_BACK_100PX = 2185;
	public static final int HEALTHBAR_DEFAULT_FRONT_120PX = 2186;
	public static final int HEALTHBAR_DEFAULT_BACK_120PX = 2187;
	public static final int HEALTHBAR_DEFAULT_FRONT_140PX = 2188;
	public static final int HEALTHBAR_DEFAULT_BACK_140PX = 2189;
	public static final int HEALTHBAR_DEFAULT_FRONT_160PX = 2190;
	public static final int HEALTHBAR_DEFAULT_BACK_160PX = 2191;
	/* Unmapped: 2192~2218 */
	public static final int QUESTS_PAGE_ICON_ORANGE_ADVENTURE_PATHS = 2219;
	/* Unmapped: 2220~2275 */
	public static final int QUESTS_PAGE_ICON_BROWN_CHARACTER_SUMMARY = 2276;
	/* Unmapped: 2277~2306 */
	public static final int TAB_CLAN_CHAT = 2307;
	/* Unmapped: 2308 */
	public static final int TAB_QUESTS_BROWN_CHARACTER_SUMMARY = 2309;
	/* Unmapped: 2308~2419 */
	public static final int WIKI_DESELECTED = 2420;
	public static final int WIKI_SELECTED = 2421;
	/* Unmapped: 2422~2430 */
	public static final int HEALTHBAR_DEFAULT_FRONT_40PX = 2431;
	public static final int HEALTHBAR_DEFAULT_BACK_40PX = 2432;
	public static final int HEALTHBAR_CYAN_FRONT_30PX = 2433;
	public static final int HEALTHBAR_CYAN_BACK_30PX = 2434;
	public static final int HEALTHBAR_CYAN_FRONT_40PX = 2435;
	public static final int HEALTHBAR_CYAN_BACK_40PX = 2436;
	public static final int HEALTHBAR_CYAN_FRONT_50PX = 2437;
	public static final int HEALTHBAR_CYAN_BACK_50PX = 2438;
	public static final int HEALTHBAR_CYAN_FRONT_60PX = 2439;
	public static final int HEALTHBAR_CYAN_BACK_60PX = 2440;
	public static final int HEALTHBAR_CYAN_FRONT_80PX = 2441;
	public static final int HEALTHBAR_CYAN_BACK_80PX = 2442;
	public static final int HEALTHBAR_CYAN_FRONT_100PX = 2443;
	public static final int HEALTHBAR_CYAN_BACK_100PX = 2444;
	public static final int HEALTHBAR_CYAN_FRONT_120PX = 2445;
	public static final int HEALTHBAR_CYAN_BACK_120PX = 2446;
	public static final int HEALTHBAR_CYAN_FRONT_140PX = 2447;
	public static final int HEALTHBAR_CYAN_BACK_140PX = 2448;
	public static final int HEALTHBAR_CYAN_FRONT_160PX = 2449;
	public static final int HEALTHBAR_CYAN_BACK_160PX = 2450;
	public static final int HEALTHBAR_ORANGE_FRONT_30PX = 2451;
	public static final int HEALTHBAR_ORANGE_BACK_30PX = 2452;
	public static final int HEALTHBAR_ORANGE_FRONT_40PX = 2453;
	public static final int HEALTHBAR_ORANGE_BACK_40PX = 2454;
	public static final int HEALTHBAR_ORANGE_FRONT_50PX = 2455;
	public static final int HEALTHBAR_ORANGE_BACK_50PX = 2456;
	public static final int HEALTHBAR_ORANGE_FRONT_60PX = 2457;
	public static final int HEALTHBAR_ORANGE_BACK_60PX = 2458;
	public static final int HEALTHBAR_ORANGE_FRONT_80PX = 2459;
	public static final int HEALTHBAR_ORANGE_BACK_80PX = 2460;
	public static final int HEALTHBAR_ORANGE_FRONT_100PX = 2461;
	public static final int HEALTHBAR_ORANGE_BACK_100PX = 2462;
	public static final int HEALTHBAR_ORANGE_FRONT_120PX = 2463;
	public static final int HEALTHBAR_ORANGE_BACK_120PX = 2464;
	public static final int HEALTHBAR_ORANGE_FRONT_140PX = 2465;
	public static final int HEALTHBAR_ORANGE_BACK_140PX = 2466;
	public static final int HEALTHBAR_ORANGE_FRONT_160PX = 2467;
	public static final int HEALTHBAR_ORANGE_BACK_160PX = 2468;
	public static final int HEALTHBAR_YELLOW_FRONT_30PX = 2469;
	public static final int HEALTHBAR_YELLOW_BACK_30PX = 2470;
	public static final int HEALTHBAR_YELLOW_FRONT_40PX = 2471;
	public static final int HEALTHBAR_YELLOW_BACK_40PX = 2472;
	public static final int HEALTHBAR_YELLOW_FRONT_50PX = 2473;
	public static final int HEALTHBAR_YELLOW_BACK_50PX = 2474;
	public static final int HEALTHBAR_YELLOW_FRONT_60PX = 2475;
	public static final int HEALTHBAR_YELLOW_BACK_60PX = 2476;
	public static final int HEALTHBAR_YELLOW_FRONT_80PX = 2477;
	public static final int HEALTHBAR_YELLOW_BACK_80PX = 2478;
	public static final int HEALTHBAR_YELLOW_FRONT_100PX = 2479;
	public static final int HEALTHBAR_YELLOW_BACK_100PX = 2480;
	public static final int HEALTHBAR_YELLOW_FRONT_120PX = 2481;
	public static final int HEALTHBAR_YELLOW_BACK_120PX = 2482;
	public static final int HEALTHBAR_YELLOW_FRONT_140PX = 2483;
	public static final int HEALTHBAR_YELLOW_BACK_140PX = 2484;
	public static final int HEALTHBAR_YELLOW_FRONT_160PX = 2485;
	public static final int HEALTHBAR_YELLOW_BACK_160PX = 2486;
	/* Unmapped: 2487~2824 */
	public static final int FRIENDS_CHAT_RANK_SMILEY_FRIEND = 2825;
	public static final int FRIENDS_CHAT_RANK_CROWN_JAGEX_MODERATOR = 2826;
	public static final int FRIENDS_CHAT_RANK_KEY_CHANNEL_OWNER = 2827;
	public static final int FRIENDS_CHAT_RANK_GOLD_STAR_GENERAL = 2828;
	public static final int FRIENDS_CHAT_RANK_SILVER_STAR_CAPTAIN = 2829;
	public static final int FRIENDS_CHAT_RANK_BRONZE_STAR_LIEUTENANT = 2830;
	public static final int FRIENDS_CHAT_RANK_TRIPLE_CHEVRON_SERGEANT = 2831;
	public static final int FRIENDS_CHAT_RANK_DOUBLE_CHEVRON_CORPORAL = 2832;
	public static final int FRIENDS_CHAT_RANK_SINGLE_CHEVRON_RECRUIT = 2833;
	/* Unmapped: 2834~2857 */
	public static final int SETTINGS_SLIDER_HANDLE_BLUE = 2858;
	public static final int SETTINGS_SLIDER_HANDLE_RED = 2859;
	public static final int SETTINGS_SLIDER_HANDLE_GREEN = 2860;
	/* Unmapped: 2861~2966 */
	public static final int HEALTHBAR_BLUE_FRONT_50PX = 2967;
	public static final int HEALTHBAR_BLUE_BACK_50PX = 2968;
	/* Unmapped: 2968~2970 */
	public static final int HEALTHBAR_DEFAULT_FRONT_70PX = 2971;
	public static final int HEALTHBAR_DEFAULT_BACK_70PX = 2972;
	public static final int HEALTHBAR_CYAN_FRONT_70PX = 2973;
	public static final int HEALTHBAR_CYAN_BACK_70PX = 2974;
	public static final int HEALTHBAR_ORANGE_FRONT_70PX = 2975;
	public static final int HEALTHBAR_ORANGE_BACK_70PX = 2976;
	public static final int HEALTHBAR_YELLOW_FRONT_70PX = 2977;
	public static final int HEALTHBAR_YELLOW_BACK_70PX = 2978;
	/* Unmapped: 2979, 2980 */
	public static final int SPELL_RESURRECT_SUPERIOR_SKELETON = 2981;
	/* Unmapped: 2982~2983 */
	public static final int SPELL_RESURRECT_GREATER_ZOMBIE = 2984;
	/* Unmapped: 2985~2986 */
	public static final int SPELL_RESURRECT_SUPERIOR_SKELETON_DISABLED = 2987;
	/* Unmapped: 2988~4707 */
	public static final int HEALTHBAR_PURPLE_FRONT_30PX = 4708;
	public static final int HEALTHBAR_PURPLE_BACK_30PX = 4709;
	public static final int HEALTHBAR_PURPLE_FRONT_40PX = 4710;
	public static final int HEALTHBAR_PURPLE_BACK_40PX = 4711;
	public static final int HEALTHBAR_PURPLE_FRONT_50PX = 4712;
	public static final int HEALTHBAR_PURPLE_BACK_50PX = 4713;
	public static final int HEALTHBAR_PURPLE_FRONT_60PX = 4714;
	public static final int HEALTHBAR_PURPLE_BACK_60PX = 4715;
	public static final int HEALTHBAR_PURPLE_FRONT_70PX = 4716;
	public static final int HEALTHBAR_PURPLE_BACK_70PX = 4717;
	public static final int HEALTHBAR_PURPLE_FRONT_80PX = 4718;
	public static final int HEALTHBAR_PURPLE_BACK_80PX = 4719;
	public static final int HEALTHBAR_PURPLE_FRONT_100PX = 4720;
	public static final int HEALTHBAR_PURPLE_BACK_100PX = 4721;
	public static final int HEALTHBAR_PURPLE_FRONT_120PX = 4722;
	public static final int HEALTHBAR_PURPLE_BACK_120PX = 4723;
	public static final int HEALTHBAR_PURPLE_FRONT_140PX = 4724;
	public static final int HEALTHBAR_PURPLE_BACK_140PX = 4725;
	public static final int HEALTHBAR_PURPLE_FRONT_160PX = 4726;
	public static final int HEALTHBAR_PURPLE_BACK_160PX = 4727;
}
