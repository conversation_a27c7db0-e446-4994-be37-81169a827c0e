import net.runelite.mapping.Export;
import net.runelite.mapping.ObfuscatedName;
import net.runelite.mapping.ObfuscatedSignature;

@ObfuscatedName("nd")
public class class367 extends RuntimeException {
   @ObfuscatedName("je")
   @ObfuscatedSignature(
      descriptor = "[Lrs;"
   )
   @Export("headIconPrayerSprites")
   static SpritePixels[] headIconPrayerSprites;

   public class367(String var1, Object[] var2) {
      super(String.format(var1, var2));
   }
}
