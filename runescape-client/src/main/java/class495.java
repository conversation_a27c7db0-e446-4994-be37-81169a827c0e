import net.runelite.mapping.ObfuscatedName;

@ObfuscatedName("sj")
public abstract class class495 {
   @ObfuscatedName("c")
   static final String[] field5084 = new String[32];

   static {
      field5084[0] = "MEMBERS";
      field5084[1] = "QUICKCHAT";
      field5084[2] = "PVPWORLD";
      field5084[3] = "LOOTSHARE";
      field5084[4] = "DEDICATEDACTIVITY";
      field5084[5] = "BOUNTYWORLD";
      field5084[6] = "PVPARENA";
      field5084[7] = "HIGHLEVELONLY_1500";
      field5084[8] = "SPEEDRUN";
      field5084[9] = "EXISTINGPLAYERSONLY";
      field5084[10] = "EXTRAHARDWILDERNESS";
      field5084[11] = "DUNGEONEERING";
      field5084[12] = "INSTANCE_SHARD";
      field5084[13] = "RENTABLE";
      field5084[14] = "LASTMANSTANDING";
      field5084[15] = "NEW_PLAYERS";
      field5084[16] = "BETA_WORLD";
      field5084[17] = "STAFF_IP_ONLY";
      field5084[18] = "HIGHLEVELONLY_2000";
      field5084[19] = "HIGHLEVELONLY_2400";
      field5084[20] = "VIPS_ONLY";
      field5084[21] = "HIDDEN_WORLD";
      field5084[22] = "LEGACY_ONLY";
      field5084[23] = "EOC_ONLY";
      field5084[24] = "BEHIND_PROXY";
      field5084[25] = "NOSAVE_MODE";
      field5084[26] = "TOURNAMENT_WORLD";
      field5084[27] = "FRESHSTART";
      field5084[28] = "HIGHLEVELONLY_1750";
      field5084[29] = "DEADMAN";
      field5084[30] = "SEASONAL";
      field5084[31] = "EXTERNAL_PARTNER_ONLY";
   }
}
