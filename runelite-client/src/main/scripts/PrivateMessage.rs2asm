.id                 681
.int_stack_count    0
.string_stack_count 0
.int_var_count      2
.string_var_count   1
   get_varc_int           5
   iconst                 14
   if_icmpeq              LABEL7
   get_varc_int           5
   iconst                 17
   if_icmpeq              LABEL7
   jump                   LABEL10
LABEL7:
   iconst                 1
   set_varc_int           66
   return                
LABEL10:
   iconst                 -1
   istore                 0
   sconst                 ""
   sstore                 0
   get_varc_string        359
   string_length         
   istore                 1
   iload                  1
   iconst                 0
   if_icmpgt              LABEL21
   jump                   LABEL269
LABEL21:
   get_varc_int           5
   switch                
      1: LABEL24
      4: LABEL26
      5: LABEL26
      2: LABEL47
      3: LABEL47
      6: LABEL47
      7: LABEL111
      19: LABEL111
      8: LABEL117
      9: LABEL125
      15: LABEL125
      20: LABEL125
      21: LABEL125
      10: LABEL202
      12: LABEL219
      13: LABEL237
      11: LABEL260
      18: LABEL260
      16: LABEL266
   jump                   LABEL268
LABEL24:
   return                
   jump                   LABEL268
LABEL26:
   ignore_count          
   iconst                 0
   if_icmplt              LABEL30
   jump                   LABEL33
LABEL30:
   sconst                 "Unable to update ignore list - system busy."
   mes                   
   jump                   LABEL46
LABEL33:
   get_varc_int           5
   iconst                 4
   if_icmpeq              LABEL37
   jump                   LABEL40
LABEL37:
   get_varc_string        359
   ignore_add            
   jump                   LABEL46
LABEL40:
   get_varc_int           5
   iconst                 5
   if_icmpeq              LABEL44
   jump                   LABEL46
LABEL44:
   get_varc_string        359
   ignore_del            
LABEL46:
   jump                   LABEL268
LABEL47:
   friend_count          
   iconst                 0
   if_icmplt              LABEL51
   jump                   LABEL54
LABEL51:
   sconst                 "Unable to complete action - system busy."
   mes                   
   jump                   LABEL110
LABEL54:
   get_varc_int           5
   iconst                 2
   if_icmpeq              LABEL58
   jump                   LABEL61
LABEL58:
   get_varc_string        359
   friend_add            
   jump                   LABEL110
LABEL61:
   get_varc_int           5
   iconst                 3
   if_icmpeq              LABEL65
   jump                   LABEL68
LABEL65:
   get_varc_string        359
   friend_del            
   jump                   LABEL110
LABEL68:
   get_varc_int           5
   iconst                 6
   if_icmpeq              LABEL72
   jump                   LABEL110
LABEL72:
   get_varbit             8119
   iconst                 0
   if_icmpeq              LABEL76
   jump                   LABEL83
LABEL76:
   iconst                 1
   iconst                 1
   iconst                 1
   invoke                 299
   sconst                 "You must set a name before you can chat."
   mes                   
   return                
LABEL83:
   chat_getfilter_private
   iconst                 2
   if_icmpeq              LABEL87
   jump                   LABEL98
LABEL87:
   chat_getfilter_public 
   iconst                 1
   chat_getfilter_trade  
   chat_setfilter        
   invoke                 178
   invoke                 553
   istore                 0
   iload                  0
   invoke                 84
   iload                  0
   invoke                 89
LABEL98:
   get_varbit             4394
   iconst                 1
   if_icmpeq              LABEL102
   jump                   LABEL105
LABEL102:
   get_varc_string        360
   friend_del            
   jump                   LABEL108
LABEL105:
   get_varc_string        360
   get_varc_string        359
   sconst                 "privateMessage" ; load event name
   iconst                 0                ; whether or not to skip
   runelite_callback     ; invoke callback
   iconst                 1
   if_icmpeq              LABEL108         ; if skipped, do not message
   chat_sendprivate      
LABEL108:
   clientclock           
   set_varc_int           61
LABEL110:
   jump                   LABEL268
LABEL111:
   get_varc_string        359
   invoke                 212
   resume_countdialog    
   iconst                 0
   set_varc_int           5
   jump                   LABEL268
LABEL117:
   get_varc_string        359
   removetags            
   set_varc_string        361
   get_varc_string        359
   resume_namedialog     
   iconst                 0
   set_varc_int           5
   jump                   LABEL268
LABEL125:
   get_varc_int           5
   iconst                 20
   if_icmpeq              LABEL129
   jump                   LABEL133
LABEL129:
   get_varc_string        359
   removetags            
   invoke                 4394
   jump                   LABEL197
LABEL133:
   get_varc_int           5
   iconst                 21
   if_icmpeq              LABEL137
   jump                   LABEL197
LABEL137:
   get_varc_string        359
   sconst                 "jagex"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "jaqex"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "jagx"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "jgex"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "jgx"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "admin"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "staff"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "mod "
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "m0d "
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "-"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "_"
   sconst                 " "
   invoke                 4191
   set_varc_string        359
   get_varc_string        359
   sconst                 "  "
   sconst                 " "
   invoke                 4191
   set_varc_string        359
LABEL197:
   get_varc_string        359
   resume_stringdialog   
   iconst                 0
   set_varc_int           5
   jump                   LABEL268
LABEL202:
   get_varbit             8119
   iconst                 0
   if_icmpeq              LABEL206
   jump                   LABEL213
LABEL206:
   iconst                 1
   iconst                 1
   iconst                 1
   invoke                 299
   sconst                 "You must set a name before you can chat."
   mes                   
   return                
LABEL213:
   get_varc_string        359
   removetags            
   set_varc_string        362
   get_varc_string        359
   clan_joinchat         
   jump                   LABEL268
LABEL219:
   iload                  1
   iconst                 10
   if_icmpgt              LABEL223
   jump                   LABEL229
LABEL223:
   get_varc_string        359
   iconst                 0
   iconst                 9
   substring             
   sstore                 0
   jump                   LABEL231
LABEL229:
   get_varc_string        359
   sstore                 0
LABEL231:
   sload                  0
   lowercase             
   chat_setmessagefilter 
   invoke                 553
   invoke                 84
   jump                   LABEL268
LABEL237:
   get_varbit             8119
   iconst                 0
   if_icmpeq              LABEL241
   jump                   LABEL248
LABEL241:
   iconst                 1
   iconst                 1
   iconst                 1
   invoke                 299
   sconst                 "You must set a name before you can chat."
   mes                   
   return                
LABEL248:
   get_varc_string        359
   iconst                 0
   set_varc_int           62
   set_varc_string        358
   invoke                 95
   iconst                 552
   iconst                 -2147483645
   iconst                 1
   sconst                 "I1"
   iconst                 10616843
   if_setontimer         
   jump                   LABEL268
LABEL260:
   iconst                 0
   iconst                 1
   iconst                 1
   invoke                 299
   return                
   jump                   LABEL268
LABEL266:
   get_varc_string        359
   invoke                 2061
LABEL268:
   jump                   LABEL275
LABEL269:
   get_varc_int           5
   switch                
      7: LABEL272
      8: LABEL272
      9: LABEL272
      15: LABEL272
      20: LABEL272
      21: LABEL272
      16: LABEL274
   jump                   LABEL275
LABEL272:
   return                
   jump                   LABEL275
LABEL274:
   return                
LABEL275:
   iconst                 1
   iconst                 1
   iconst                 1
   invoke                 299
   return                
